import{Q as i,W as f,M as u,av as l,aw as p,v as s,D as o,w as h,y as E}from"./runtime.BoVB3PJd.js";function T(n){var t=document.createElement("template");return t.innerHTML=n,t.content}function r(n,t){var e=u;e.nodes_start===null&&(e.nodes_start=n,e.nodes_end=t)}function g(n,t){var e=(t&l)!==0,_=(t&p)!==0,a,c=!n.startsWith("<!>");return()=>{if(s)return r(o,null),o;a===void 0&&(a=T(c?n:"<!>"+n),e||(a=f(a)));var d=_?document.importNode(a,!0):a.cloneNode(!0);if(e){var m=f(d),v=d.lastChild;r(m,v)}else r(d,d);return d}}function M(n=""){if(!s){var t=i(n+"");return r(t,t),t}var e=o;return e.nodeType!==3&&(e.before(e=i()),E(e)),r(e,e),e}function N(){if(s)return r(o,null),o;var n=document.createDocumentFragment(),t=document.createComment(""),e=i();return n.append(t,e),r(t,e),n}function x(n,t){if(s){u.nodes_end=o,h();return}n!==null&&n.before(t)}const w="5";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(w);export{x as a,r as b,N as c,M as d,g as t};
