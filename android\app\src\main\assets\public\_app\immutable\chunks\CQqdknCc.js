import{s as u}from"./CZYSyPBs.js";import{a as L}from"./BeWv36fu.js";const S=()=>{if(u.user===null)throw new TypeError("Not logged in");return`Bearer ${u.user.supabaseAccessToken}`},d=async e=>{if(!e.ok)throw new Error(`${e.url} | ${e.status} ${e.statusText} | ${(await e.json()).message}`);return await e.json()},o=e=>new URL(e,L),h=async(e,a)=>await d(await fetch(o(e),a)),w=async(e,a)=>{const n=new Request(o(e),a);return n.headers.set("Authorization",S()),await d(await fetch(n))},i=(e,a)=>{const n=o(e),l=w;return(c,g)=>{const r=new URL(n);for(const[m,v]of Object.entries(c))r.searchParams.set(m,v);return l(r,g)}},s=(e,a,n="POST")=>{const l=o(e),c=a?w:h;return(g,r)=>{const m=new URL(l);return c(m,{body:JSON.stringify(g),headers:{"Content-Type":"application/json"},method:n,...r})}},t={listing:{bySeller:i("listing/by-seller"),details:i("listing/details"),list:i("listing/list"),edit:s("listing/edit",!0,"PATCH"),new:s("listing/new",!0,"PUT")},stream:{bySeller:i("livestream/by-seller"),details:i("livestream/details"),getHost:i("livestream/get-host"),list:i("livestream/list"),new:s("livestream/new",!0,"PUT"),start:s("livestream/start",!0),stop:s("livestream/stop",!0),setHostSession:s("livestream/set-host-session",!0,"PATCH"),edit:{details:s("livestream/edit/details",!0,"PATCH"),listing:{selection:s("livestream/edit/listing-selection",!0,"PATCH"),price:s("livestream/edit/listing-price",!0,"PATCH"),activation:s("livestream/edit/listing-activation",!0,"PATCH")}}},user:{login:s("user/login",!1)}},P=t.listing.details,H=t.listing.list,T=t.listing.bySeller,b=t.listing.edit,f=t.listing.new,A=t.stream.list,C=t.stream.getHost,U=t.stream.details,j=t.stream.bySeller,R=t.stream.new,O=t.stream.edit.details,$=t.stream.edit.listing.selection,k=t.stream.edit.listing.price,B=t.stream.edit.listing.activation,F=t.stream.setHostSession,I=t.stream.start,q=t.stream.stop,x=t.user.login;export{U as a,H as b,O as c,q as d,$ as e,j as f,T as g,A as h,F as i,k as j,B as k,C as l,P as m,R as n,b as o,f as p,I as s,x as u};
