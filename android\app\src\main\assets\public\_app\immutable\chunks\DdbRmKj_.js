import{d as l,b as o,g as a,e,u as c}from"./DvS_9Yw_.js";import{a as d}from"./CQqdknCc.js";import{g as m}from"./7czifHHs.js";let s=l(null);const D=t=>{e(s,t,!0),n(),m("/livestream")},S=t=>{e(s,t,!0)};let r=l(o({title:"",description:"",active:!1,listings:[]}));const n=()=>{e(r,{title:"",description:"",active:!1,listings:[]},!0),a(s)!==null&&(async()=>{const t=await d({streamId:a(s)});e(r,{title:t.title,description:t.description,active:t.active,listings:t.listings},!0)})()},v=n;let i=l(null);const b=t=>{e(i,t,!0)},I=t=>{a(i)!==null&&Object.assign(a(i),t)},u=c(()=>({id:a(s),data:a(r),callData:a(i)})),w=()=>a(u);export{S as a,n as b,b as c,I as d,D as e,v as r,w as s};
