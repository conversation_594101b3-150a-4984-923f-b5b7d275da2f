import"./CWj6FrbW.js";import{p as m,g as i,u as n,r as p,a as c}from"./DvS_9Yw_.js";import{f as d,a as f}from"./Ckyppc5t.js";import{e as g}from"./hasoi-S8.js";import{s as u}from"./PoYD5o0_.js";import{p as L}from"./Ctt2IVZk.js";import"./7czifHHs.js";import{L as h}from"./CDLiAs4l.js";var v=d("<listings-list></listings-list>",2);function S(o,s){m(s,!0);const a=L(s,"selectedIds",19,()=>new Set);var t=v();u(t,1,"svelte-13i9k66"),g(t,21,()=>s.listings,e=>e.id,(e,r)=>{const l=n(()=>a().has(i(r).id));h(e,{get title(){return i(r).title},onClick:()=>s.onClickListing(i(r)),get selected(){return i(l)}})}),p(t),f(o,t),c()}export{S as L};
