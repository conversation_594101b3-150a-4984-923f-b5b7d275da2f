const y="modulepreload",w=function(f,a){return new URL(f,a).href},E={},g=function(a,u,d){let h=Promise.resolve();if(u&&u.length>0){let s=function(e){return Promise.all(e.map(r=>Promise.resolve(r).then(l=>({status:"fulfilled",value:l}),l=>({status:"rejected",reason:l}))))};const t=document.getElementsByTagName("link"),o=document.querySelector("meta[property=csp-nonce]"),v=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));h=s(u.map(e=>{if(e=w(e,d),e in E)return;E[e]=!0;const r=e.endsWith(".css"),l=r?'[rel="stylesheet"]':"";if(!!d)for(let i=t.length-1;i>=0;i--){const c=t[i];if(c.href===e&&(!r||c.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${e}"]${l}`))return;const n=document.createElement("link");if(n.rel=r?"stylesheet":y,r||(n.as="script"),n.crossOrigin="",n.href=e,v&&n.setAttribute("nonce",v),document.head.appendChild(n),r)return new Promise((i,c)=>{n.addEventListener("load",i),n.addEventListener("error",()=>c(new Error(`Unable to preload CSS for ${e}`)))})}))}function m(s){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=s,window.dispatchEvent(t),!t.defaultPrevented)throw s}return h.then(s=>{for(const t of s||[])t.status==="rejected"&&m(t.reason);return a().catch(m)})};export{g as _};
