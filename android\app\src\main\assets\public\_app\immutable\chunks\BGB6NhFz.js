import{al as g,ad as S,aj as D,ac as I,am as y,q as c,o as R,i as L,A as _,a2 as O,an as V,ao as H,a7 as Y,a4 as j,ap as C,ae as M,y as P,h as E,p as W,E as $,W as k,a as q}from"./DvS_9Yw_.js";import{b as z,r as A,h}from"./CTtUbKlS.js";import{r as B}from"./DiITAvcd.js";import{b as F}from"./Ckyppc5t.js";const G=["touchstart","touchmove"];function J(t){return G.includes(t)}function x(t,e){var a=e==null?"":typeof e=="object"?e+"":e;a!==(t.__t??(t.__t=t.nodeValue))&&(t.__t=a,t.nodeValue=a+"")}function K(t,e){return N(t,e)}function ee(t,e){g(),e.intro=e.intro??!1;const a=e.target,u=E,l=_;try{for(var r=S(a);r&&(r.nodeType!==8||r.data!==D);)r=I(r);if(!r)throw y;c(!0),R(r),L();const d=N(t,{...e,anchor:r});if(_===null||_.nodeType!==8||_.data!==O)throw V(),y;return c(!1),d}catch(d){if(d===y)return e.recover===!1&&H(),g(),Y(a),c(!1),K(t,e);throw d}finally{c(u),R(l),B()}}const i=new Map;function N(t,{target:e,anchor:a,props:u={},events:l,context:r,intro:d=!0}){g();var m=new Set,p=o=>{for(var s=0;s<o.length;s++){var n=o[s];if(!m.has(n)){m.add(n);var f=J(n);e.addEventListener(n,h,{passive:f});var T=i.get(n);T===void 0?(document.addEventListener(n,h,{passive:f}),i.set(n,1)):i.set(n,T+1)}}};p(j(z)),A.add(p);var v=void 0,b=C(()=>{var o=a??e.appendChild(M());return P(()=>{if(r){W({});var s=$;s.c=r}l&&(u.$$events=l),E&&F(o,null),v=t(o,u)||{},E&&(k.nodes_end=_),r&&q()}),()=>{var f;for(var s of m){e.removeEventListener(s,h);var n=i.get(s);--n===0?(document.removeEventListener(s,h),i.delete(s)):i.set(s,n)}A.delete(p),o!==a&&((f=o.parentNode)==null||f.removeChild(o))}});return w.set(v,b),v}let w=new WeakMap;function te(t,e){const a=w.get(t);return a?(w.delete(t),a(e)):Promise.resolve()}export{ee as h,K as m,x as s,te as u};
