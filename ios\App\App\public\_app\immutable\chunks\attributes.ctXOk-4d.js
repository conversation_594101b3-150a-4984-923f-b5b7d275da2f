import{v as d,$ as v,a0 as i,a1 as c,O as u,a2 as g,a3 as h,M as y,a4 as m}from"./runtime.BoVB3PJd.js";let n=!1;function p(){n||(n=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{var s;if(!e.defaultPrevented)for(const r of e.target.elements)(s=r.__on_r)==null||s.call(r)})},{capture:!0}))}function N(e){if(d){var s=!1,r=()=>{if(!s){if(s=!0,e.hasAttribute("value")){var t=e.value;o(e,"value",null),e.value=t}if(e.hasAttribute("checked")){var a=e.checked;o(e,"checked",null),e.checked=a}}};e.__on_r=r,v(r),p()}}function o(e,s,r,t){var a=e.__attributes??(e.__attributes={});d&&(a[s]=e.getAttribute(s),s==="src"||s==="srcset"||s==="href"&&e.nodeName==="LINK")||a[s]!==(a[s]=r)&&(s==="style"&&"__styles"in e&&(e.__styles={}),s==="loading"&&(e[g]=r),r==null?e.removeAttribute(s):typeof r!="string"&&l(e).includes(s)?e[s]=r:e.setAttribute(s,r))}function k(e,s,r){var t=h,a=y;i(null),c(null);try{(_.has(e.nodeName)||!customElements||customElements.get(e.tagName.toLowerCase())?l(e).includes(s):r&&typeof r=="object")?e[s]=r:o(e,s,r==null?r:String(r))}finally{i(t),c(a)}}var _=new Map;function l(e){var s=_.get(e.nodeName);if(s)return s;_.set(e.nodeName,s=[]);for(var r,t=e,a=Element.prototype;a!==t;){r=m(t);for(var f in r)r[f].set&&s.push(f);t=u(t)}return s}export{k as a,p as b,N as r,o as s};
