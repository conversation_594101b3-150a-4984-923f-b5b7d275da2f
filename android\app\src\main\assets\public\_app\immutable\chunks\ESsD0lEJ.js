var y=r=>{throw TypeError(r)};var z=(r,t,e)=>t.has(r)||y("Cannot "+e);var s=(r,t,e)=>(z(r,t,"read from private field"),e?e.call(r):t.get(r)),d=(r,t,e)=>t.has(r)?y("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,e);var g=(r,t,e)=>(z(r,t,"access private method"),e);import{e as h,w as l,g as f}from"./DvS_9Yw_.js";function m(r){h(r,r.v+1)}var x=["forEach","isDisjointFrom","isSubsetOf","isSupersetOf"],D=["difference","intersection","symmetricDifference","union"],S=!1,p,a,u,c,w;const v=class v extends Set{constructor(e){super();d(this,c);d(this,p,new Map);d(this,a,l(0));d(this,u,l(0));if(e){for(var i of e)super.add(i);s(this,u).v=super.size}S||g(this,c,w).call(this)}has(e){var i=super.has(e),n=s(this,p),o=n.get(e);if(o===void 0){if(!i)return f(s(this,a)),!1;o=l(!0),n.set(e,o)}return f(o),i}add(e){return super.has(e)||(super.add(e),h(s(this,u),super.size),m(s(this,a))),this}delete(e){var i=super.delete(e),n=s(this,p),o=n.get(e);return o!==void 0&&(n.delete(e),h(o,!1)),i&&(h(s(this,u),super.size),m(s(this,a))),i}clear(){if(super.size!==0){super.clear();var e=s(this,p);for(var i of e.values())h(i,!1);e.clear(),h(s(this,u),0),m(s(this,a))}}keys(){return this.values()}values(){return f(s(this,a)),super.values()}entries(){return f(s(this,a)),super.entries()}[Symbol.iterator](){return this.keys()}get size(){return f(s(this,u))}};p=new WeakMap,a=new WeakMap,u=new WeakMap,c=new WeakSet,w=function(){S=!0;var e=v.prototype,i=Set.prototype;for(const n of x)e[n]=function(...o){return f(s(this,a)),i[n].apply(this,o)};for(const n of D)e[n]=function(...o){f(s(this,a));var b=i[n].apply(this,o);return new v(b)}};let k=v;export{k as S};
