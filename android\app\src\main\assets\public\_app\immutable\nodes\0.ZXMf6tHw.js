import{g as er,a as tr}from"../chunks/Bx2EM-6T.js";import{_ as de}from"../chunks/Dp1pzeXC.js";import{c as Ve,d as Qe}from"../chunks/BeWv36fu.js";import"../chunks/CWj6FrbW.js";import{p as rr,a as sr,n as ir,c as nr,r as or}from"../chunks/DvS_9Yw_.js";import{h as ar}from"../chunks/DiITAvcd.js";import{f as At,a as Xe}from"../chunks/Ckyppc5t.js";import{s as lr,a as cr}from"../chunks/CZYSyPBs.js";import{s as ur}from"../chunks/PoYD5o0_.js";const hr=i=>{let e;return i?e=i:typeof fetch>"u"?e=(...t)=>de(async()=>{const{default:r}=await Promise.resolve().then(()=>ie);return{default:r}},void 0,import.meta.url).then(({default:r})=>r(...t)):e=fetch,(...t)=>e(...t)};class We extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class dr extends We{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class fr extends We{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class gr extends We{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var Ue;(function(i){i.Any="any",i.ApNortheast1="ap-northeast-1",i.ApNortheast2="ap-northeast-2",i.ApSouth1="ap-south-1",i.ApSoutheast1="ap-southeast-1",i.ApSoutheast2="ap-southeast-2",i.CaCentral1="ca-central-1",i.EuCentral1="eu-central-1",i.EuWest1="eu-west-1",i.EuWest2="eu-west-2",i.EuWest3="eu-west-3",i.SaEast1="sa-east-1",i.UsEast1="us-east-1",i.UsWest1="us-west-1",i.UsWest2="us-west-2"})(Ue||(Ue={}));var pr=function(i,e,t,r){function s(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(u){try{c(r.next(u))}catch(h){o(h)}}function l(u){try{c(r.throw(u))}catch(h){o(h)}}function c(u){u.done?n(u.value):s(u.value).then(a,l)}c((r=r.apply(i,e||[])).next())})};class vr{constructor(e,{headers:t={},customFetch:r,region:s=Ue.Any}={}){this.url=e,this.headers=t,this.region=s,this.fetch=hr(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r;return pr(this,void 0,void 0,function*(){try{const{headers:s,method:n,body:o}=t;let a={},{region:l}=t;l||(l=this.region),l&&l!=="any"&&(a["x-region"]=l);let c;o&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&(typeof Blob<"u"&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",c=o):typeof o=="string"?(a["Content-Type"]="text/plain",c=o):typeof FormData<"u"&&o instanceof FormData?c=o:(a["Content-Type"]="application/json",c=JSON.stringify(o)));const u=yield this.fetch(`${this.url}/${e}`,{method:n||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),s),body:c}).catch(g=>{throw new dr(g)}),h=u.headers.get("x-relay-error");if(h&&h==="true")throw new fr(u);if(!u.ok)throw new gr(u);let f=((r=u.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),d;return f==="application/json"?d=yield u.json():f==="application/octet-stream"?d=yield u.blob():f==="text/event-stream"?d=u:f==="multipart/form-data"?d=yield u.formData():d=yield u.text(),{data:d,error:null}}catch(s){return{data:null,error:s}}})}}var O={},W={},H={},K={},J={},G={},_r=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},se=_r();const wr=se.fetch,jt=se.fetch.bind(se),Pt=se.Headers,yr=se.Request,mr=se.Response,ie=Object.freeze(Object.defineProperty({__proto__:null,Headers:Pt,Request:yr,Response:mr,default:jt,fetch:wr},Symbol.toStringTag,{value:"Module"})),br=er(ie);var pe={},Ye;function $t(){if(Ye)return pe;Ye=1,Object.defineProperty(pe,"__esModule",{value:!0});class i extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}}return pe.default=i,pe}var Ze;function xt(){if(Ze)return G;Ze=1;var i=G&&G.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(G,"__esModule",{value:!0});const e=i(br),t=i($t());class r{constructor(n){this.shouldThrowOnError=!1,this.method=n.method,this.url=n.url,this.headers=n.headers,this.schema=n.schema,this.body=n.body,this.shouldThrowOnError=n.shouldThrowOnError,this.signal=n.signal,this.isMaybeSingle=n.isMaybeSingle,n.fetch?this.fetch=n.fetch:typeof fetch>"u"?this.fetch=e.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(n,o){return this.headers=Object.assign({},this.headers),this.headers[n]=o,this}then(n,o){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const a=this.fetch;let l=a(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async c=>{var u,h,f;let d=null,g=null,p=null,v=c.status,k=c.statusText;if(c.ok){if(this.method!=="HEAD"){const S=await c.text();S===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?g=S:g=JSON.parse(S))}const _=(u=this.headers.Prefer)===null||u===void 0?void 0:u.match(/count=(exact|planned|estimated)/),y=(h=c.headers.get("content-range"))===null||h===void 0?void 0:h.split("/");_&&y&&y.length>1&&(p=parseInt(y[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(g)&&(g.length>1?(d={code:"PGRST116",details:`Results contain ${g.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},g=null,p=null,v=406,k="Not Acceptable"):g.length===1?g=g[0]:g=null)}else{const _=await c.text();try{d=JSON.parse(_),Array.isArray(d)&&c.status===404&&(g=[],d=null,v=200,k="OK")}catch{c.status===404&&_===""?(v=204,k="No Content"):d={message:_}}if(d&&this.isMaybeSingle&&(!((f=d==null?void 0:d.details)===null||f===void 0)&&f.includes("0 rows"))&&(d=null,v=200,k="OK"),d&&this.shouldThrowOnError)throw new t.default(d)}return{error:d,data:g,count:p,status:v,statusText:k}});return this.shouldThrowOnError||(l=l.catch(c=>{var u,h,f;return{error:{message:`${(u=c==null?void 0:c.name)!==null&&u!==void 0?u:"FetchError"}: ${c==null?void 0:c.message}`,details:`${(h=c==null?void 0:c.stack)!==null&&h!==void 0?h:""}`,hint:"",code:`${(f=c==null?void 0:c.code)!==null&&f!==void 0?f:""}`},data:null,count:null,status:0,statusText:""}})),l.then(n,o)}returns(){return this}overrideTypes(){return this}}return G.default=r,G}var et;function Ct(){if(et)return J;et=1;var i=J&&J.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(J,"__esModule",{value:!0});const e=i(xt());class t extends e.default{select(s){let n=!1;const o=(s??"*").split("").map(a=>/\s/.test(a)&&!n?"":(a==='"'&&(n=!n),a)).join("");return this.url.searchParams.set("select",o),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(s,{ascending:n=!0,nullsFirst:o,foreignTable:a,referencedTable:l=a}={}){const c=l?`${l}.order`:"order",u=this.url.searchParams.get(c);return this.url.searchParams.set(c,`${u?`${u},`:""}${s}.${n?"asc":"desc"}${o===void 0?"":o?".nullsfirst":".nullslast"}`),this}limit(s,{foreignTable:n,referencedTable:o=n}={}){const a=typeof o>"u"?"limit":`${o}.limit`;return this.url.searchParams.set(a,`${s}`),this}range(s,n,{foreignTable:o,referencedTable:a=o}={}){const l=typeof a>"u"?"offset":`${a}.offset`,c=typeof a>"u"?"limit":`${a}.limit`;return this.url.searchParams.set(l,`${s}`),this.url.searchParams.set(c,`${n-s+1}`),this}abortSignal(s){return this.signal=s,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:s=!1,verbose:n=!1,settings:o=!1,buffers:a=!1,wal:l=!1,format:c="text"}={}){var u;const h=[s?"analyze":null,n?"verbose":null,o?"settings":null,a?"buffers":null,l?"wal":null].filter(Boolean).join("|"),f=(u=this.headers.Accept)!==null&&u!==void 0?u:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${c}; for="${f}"; options=${h};`,c==="json"?this:this}rollback(){var s;return((s=this.headers.Prefer)!==null&&s!==void 0?s:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return J.default=t,J}var tt;function He(){if(tt)return K;tt=1;var i=K&&K.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(K,"__esModule",{value:!0});const e=i(Ct());class t extends e.default{eq(s,n){return this.url.searchParams.append(s,`eq.${n}`),this}neq(s,n){return this.url.searchParams.append(s,`neq.${n}`),this}gt(s,n){return this.url.searchParams.append(s,`gt.${n}`),this}gte(s,n){return this.url.searchParams.append(s,`gte.${n}`),this}lt(s,n){return this.url.searchParams.append(s,`lt.${n}`),this}lte(s,n){return this.url.searchParams.append(s,`lte.${n}`),this}like(s,n){return this.url.searchParams.append(s,`like.${n}`),this}likeAllOf(s,n){return this.url.searchParams.append(s,`like(all).{${n.join(",")}}`),this}likeAnyOf(s,n){return this.url.searchParams.append(s,`like(any).{${n.join(",")}}`),this}ilike(s,n){return this.url.searchParams.append(s,`ilike.${n}`),this}ilikeAllOf(s,n){return this.url.searchParams.append(s,`ilike(all).{${n.join(",")}}`),this}ilikeAnyOf(s,n){return this.url.searchParams.append(s,`ilike(any).{${n.join(",")}}`),this}is(s,n){return this.url.searchParams.append(s,`is.${n}`),this}in(s,n){const o=Array.from(new Set(n)).map(a=>typeof a=="string"&&new RegExp("[,()]").test(a)?`"${a}"`:`${a}`).join(",");return this.url.searchParams.append(s,`in.(${o})`),this}contains(s,n){return typeof n=="string"?this.url.searchParams.append(s,`cs.${n}`):Array.isArray(n)?this.url.searchParams.append(s,`cs.{${n.join(",")}}`):this.url.searchParams.append(s,`cs.${JSON.stringify(n)}`),this}containedBy(s,n){return typeof n=="string"?this.url.searchParams.append(s,`cd.${n}`):Array.isArray(n)?this.url.searchParams.append(s,`cd.{${n.join(",")}}`):this.url.searchParams.append(s,`cd.${JSON.stringify(n)}`),this}rangeGt(s,n){return this.url.searchParams.append(s,`sr.${n}`),this}rangeGte(s,n){return this.url.searchParams.append(s,`nxl.${n}`),this}rangeLt(s,n){return this.url.searchParams.append(s,`sl.${n}`),this}rangeLte(s,n){return this.url.searchParams.append(s,`nxr.${n}`),this}rangeAdjacent(s,n){return this.url.searchParams.append(s,`adj.${n}`),this}overlaps(s,n){return typeof n=="string"?this.url.searchParams.append(s,`ov.${n}`):this.url.searchParams.append(s,`ov.{${n.join(",")}}`),this}textSearch(s,n,{config:o,type:a}={}){let l="";a==="plain"?l="pl":a==="phrase"?l="ph":a==="websearch"&&(l="w");const c=o===void 0?"":`(${o})`;return this.url.searchParams.append(s,`${l}fts${c}.${n}`),this}match(s){return Object.entries(s).forEach(([n,o])=>{this.url.searchParams.append(n,`eq.${o}`)}),this}not(s,n,o){return this.url.searchParams.append(s,`not.${n}.${o}`),this}or(s,{foreignTable:n,referencedTable:o=n}={}){const a=o?`${o}.or`:"or";return this.url.searchParams.append(a,`(${s})`),this}filter(s,n,o){return this.url.searchParams.append(s,`${n}.${o}`),this}}return K.default=t,K}var rt;function Rt(){if(rt)return H;rt=1;var i=H&&H.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(H,"__esModule",{value:!0});const e=i(He());class t{constructor(s,{headers:n={},schema:o,fetch:a}){this.url=s,this.headers=n,this.schema=o,this.fetch=a}select(s,{head:n=!1,count:o}={}){const a=n?"HEAD":"GET";let l=!1;const c=(s??"*").split("").map(u=>/\s/.test(u)&&!l?"":(u==='"'&&(l=!l),u)).join("");return this.url.searchParams.set("select",c),o&&(this.headers.Prefer=`count=${o}`),new e.default({method:a,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(s,{count:n,defaultToNull:o=!0}={}){const a="POST",l=[];if(this.headers.Prefer&&l.push(this.headers.Prefer),n&&l.push(`count=${n}`),o||l.push("missing=default"),this.headers.Prefer=l.join(","),Array.isArray(s)){const c=s.reduce((u,h)=>u.concat(Object.keys(h)),[]);if(c.length>0){const u=[...new Set(c)].map(h=>`"${h}"`);this.url.searchParams.set("columns",u.join(","))}}return new e.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:s,fetch:this.fetch,allowEmpty:!1})}upsert(s,{onConflict:n,ignoreDuplicates:o=!1,count:a,defaultToNull:l=!0}={}){const c="POST",u=[`resolution=${o?"ignore":"merge"}-duplicates`];if(n!==void 0&&this.url.searchParams.set("on_conflict",n),this.headers.Prefer&&u.push(this.headers.Prefer),a&&u.push(`count=${a}`),l||u.push("missing=default"),this.headers.Prefer=u.join(","),Array.isArray(s)){const h=s.reduce((f,d)=>f.concat(Object.keys(d)),[]);if(h.length>0){const f=[...new Set(h)].map(d=>`"${d}"`);this.url.searchParams.set("columns",f.join(","))}}return new e.default({method:c,url:this.url,headers:this.headers,schema:this.schema,body:s,fetch:this.fetch,allowEmpty:!1})}update(s,{count:n}={}){const o="PATCH",a=[];return this.headers.Prefer&&a.push(this.headers.Prefer),n&&a.push(`count=${n}`),this.headers.Prefer=a.join(","),new e.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:s,fetch:this.fetch,allowEmpty:!1})}delete({count:s}={}){const n="DELETE",o=[];return s&&o.push(`count=${s}`),this.headers.Prefer&&o.unshift(this.headers.Prefer),this.headers.Prefer=o.join(","),new e.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}return H.default=t,H}var ne={},oe={},st;function kr(){return st||(st=1,Object.defineProperty(oe,"__esModule",{value:!0}),oe.version=void 0,oe.version="0.0.0-automated"),oe}var it;function Sr(){if(it)return ne;it=1,Object.defineProperty(ne,"__esModule",{value:!0}),ne.DEFAULT_HEADERS=void 0;const i=kr();return ne.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${i.version}`},ne}var nt;function Er(){if(nt)return W;nt=1;var i=W&&W.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(W,"__esModule",{value:!0});const e=i(Rt()),t=i(He()),r=Sr();class s{constructor(o,{headers:a={},schema:l,fetch:c}={}){this.url=o,this.headers=Object.assign(Object.assign({},r.DEFAULT_HEADERS),a),this.schemaName=l,this.fetch=c}from(o){const a=new URL(`${this.url}/${o}`);return new e.default(a,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(o){return new s(this.url,{headers:this.headers,schema:o,fetch:this.fetch})}rpc(o,a={},{head:l=!1,get:c=!1,count:u}={}){let h;const f=new URL(`${this.url}/rpc/${o}`);let d;l||c?(h=l?"HEAD":"GET",Object.entries(a).filter(([p,v])=>v!==void 0).map(([p,v])=>[p,Array.isArray(v)?`{${v.join(",")}}`:`${v}`]).forEach(([p,v])=>{f.searchParams.append(p,v)})):(h="POST",d=a);const g=Object.assign({},this.headers);return u&&(g.Prefer=`count=${u}`),new t.default({method:h,url:f,headers:g,schema:this.schemaName,body:d,fetch:this.fetch,allowEmpty:!1})}}return W.default=s,W}var ot;function Tr(){if(ot)return O;ot=1;var i=O&&O.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(O,"__esModule",{value:!0}),O.PostgrestError=O.PostgrestBuilder=O.PostgrestTransformBuilder=O.PostgrestFilterBuilder=O.PostgrestQueryBuilder=O.PostgrestClient=void 0;const e=i(Er());O.PostgrestClient=e.default;const t=i(Rt());O.PostgrestQueryBuilder=t.default;const r=i(He());O.PostgrestFilterBuilder=r.default;const s=i(Ct());O.PostgrestTransformBuilder=s.default;const n=i(xt());O.PostgrestBuilder=n.default;const o=i($t());return O.PostgrestError=o.default,O.default={PostgrestClient:e.default,PostgrestQueryBuilder:t.default,PostgrestFilterBuilder:r.default,PostgrestTransformBuilder:s.default,PostgrestBuilder:n.default,PostgrestError:o.default},O}var Or=Tr();const Ar=tr(Or),{PostgrestClient:jr,PostgrestQueryBuilder:Ii,PostgrestFilterBuilder:Ui,PostgrestTransformBuilder:Li,PostgrestBuilder:Di,PostgrestError:Bi}=Ar;let Le;typeof window>"u"?Le=require("ws"):Le=window.WebSocket;const Pr="2.11.10",$r={"X-Client-Info":`realtime-js/${Pr}`},xr="1.0.0",It=1e4,Cr=1e3;var te;(function(i){i[i.connecting=0]="connecting",i[i.open=1]="open",i[i.closing=2]="closing",i[i.closed=3]="closed"})(te||(te={}));var P;(function(i){i.closed="closed",i.errored="errored",i.joined="joined",i.joining="joining",i.leaving="leaving"})(P||(P={}));var C;(function(i){i.close="phx_close",i.error="phx_error",i.join="phx_join",i.reply="phx_reply",i.leave="phx_leave",i.access_token="access_token"})(C||(C={}));var De;(function(i){i.websocket="websocket"})(De||(De={}));var M;(function(i){i.Connecting="connecting",i.Open="open",i.Closing="closing",i.Closed="closed"})(M||(M={}));class Rr{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t(typeof e=="string"?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){const s=t.getUint8(1),n=t.getUint8(2);let o=this.HEADER_LENGTH+2;const a=r.decode(e.slice(o,o+s));o=o+s;const l=r.decode(e.slice(o,o+n));o=o+n;const c=JSON.parse(r.decode(e.slice(o,e.byteLength)));return{ref:null,topic:a,event:l,payload:c}}}class Ut{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var E;(function(i){i.abstime="abstime",i.bool="bool",i.date="date",i.daterange="daterange",i.float4="float4",i.float8="float8",i.int2="int2",i.int4="int4",i.int4range="int4range",i.int8="int8",i.int8range="int8range",i.json="json",i.jsonb="jsonb",i.money="money",i.numeric="numeric",i.oid="oid",i.reltime="reltime",i.text="text",i.time="time",i.timestamp="timestamp",i.timestamptz="timestamptz",i.timetz="timetz",i.tsrange="tsrange",i.tstzrange="tstzrange"})(E||(E={}));const at=(i,e,t={})=>{var r;const s=(r=t.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(e).reduce((n,o)=>(n[o]=Ir(o,i,e,s),n),{})},Ir=(i,e,t,r)=>{const s=e.find(a=>a.name===i),n=s==null?void 0:s.type,o=t[i];return n&&!r.includes(n)?Lt(n,o):Be(o)},Lt=(i,e)=>{if(i.charAt(0)==="_"){const t=i.slice(1,i.length);return Br(e,t)}switch(i){case E.bool:return Ur(e);case E.float4:case E.float8:case E.int2:case E.int4:case E.int8:case E.numeric:case E.oid:return Lr(e);case E.json:case E.jsonb:return Dr(e);case E.timestamp:return qr(e);case E.abstime:case E.date:case E.daterange:case E.int4range:case E.int8range:case E.money:case E.reltime:case E.text:case E.time:case E.timestamptz:case E.timetz:case E.tsrange:case E.tstzrange:return Be(e);default:return Be(e)}},Be=i=>i,Ur=i=>{switch(i){case"t":return!0;case"f":return!1;default:return i}},Lr=i=>{if(typeof i=="string"){const e=parseFloat(i);if(!Number.isNaN(e))return e}return i},Dr=i=>{if(typeof i=="string")try{return JSON.parse(i)}catch(e){return console.log(`JSON parse error: ${e}`),i}return i},Br=(i,e)=>{if(typeof i!="string")return i;const t=i.length-1,r=i[t];if(i[0]==="{"&&r==="}"){let n;const o=i.slice(1,t);try{n=JSON.parse("["+o+"]")}catch{n=o?o.split(","):[]}return n.map(a=>Lt(e,a))}return i},qr=i=>typeof i=="string"?i.replace(" ","T"):i,Dt=i=>{let e=i;return e=e.replace(/^ws/i,"http"),e=e.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),e.replace(/\/+$/,"")};class je{constructor(e,t,r={},s=It){this.channel=e,this.event=t,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const e=t=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=t,this._matchReceive(t)};this.channel._on(this.refEvent,{},e),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(r=>r.status===e).forEach(r=>r.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var lt;(function(i){i.SYNC="sync",i.JOIN="join",i.LEAVE="leave"})(lt||(lt={}));class le{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(t==null?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},s=>{const{onJoin:n,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=le.syncState(this.state,s,n,o),this.pendingDiffs.forEach(l=>{this.state=le.syncDiff(this.state,l,n,o)}),this.pendingDiffs=[],a()}),this.channel._on(r.diff,{},s=>{const{onJoin:n,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(s):(this.state=le.syncDiff(this.state,s,n,o),a())}),this.onJoin((s,n,o)=>{this.channel._trigger("presence",{event:"join",key:s,currentPresences:n,newPresences:o})}),this.onLeave((s,n,o)=>{this.channel._trigger("presence",{event:"leave",key:s,currentPresences:n,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,s){const n=this.cloneDeep(e),o=this.transformState(t),a={},l={};return this.map(n,(c,u)=>{o[c]||(l[c]=u)}),this.map(o,(c,u)=>{const h=n[c];if(h){const f=u.map(v=>v.presence_ref),d=h.map(v=>v.presence_ref),g=u.filter(v=>d.indexOf(v.presence_ref)<0),p=h.filter(v=>f.indexOf(v.presence_ref)<0);g.length>0&&(a[c]=g),p.length>0&&(l[c]=p)}else a[c]=u}),this.syncDiff(n,{joins:a,leaves:l},r,s)}static syncDiff(e,t,r,s){const{joins:n,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(n,(a,l)=>{var c;const u=(c=e[a])!==null&&c!==void 0?c:[];if(e[a]=this.cloneDeep(l),u.length>0){const h=e[a].map(d=>d.presence_ref),f=u.filter(d=>h.indexOf(d.presence_ref)<0);e[a].unshift(...f)}r(a,u,l)}),this.map(o,(a,l)=>{let c=e[a];if(!c)return;const u=l.map(h=>h.presence_ref);c=c.filter(h=>u.indexOf(h.presence_ref)<0),e[a]=c,s(a,c,l),c.length===0&&delete e[a]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,r)=>{const s=e[r];return"metas"in s?t[r]=s.metas.map(n=>(n.presence_ref=n.phx_ref,delete n.phx_ref,delete n.phx_ref_prev,n)):t[r]=s,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var ct;(function(i){i.ALL="*",i.INSERT="INSERT",i.UPDATE="UPDATE",i.DELETE="DELETE"})(ct||(ct={}));var ut;(function(i){i.BROADCAST="broadcast",i.PRESENCE="presence",i.POSTGRES_CHANGES="postgres_changes",i.SYSTEM="system"})(ut||(ut={}));var I;(function(i){i.SUBSCRIBED="SUBSCRIBED",i.TIMED_OUT="TIMED_OUT",i.CLOSED="CLOSED",i.CHANNEL_ERROR="CHANNEL_ERROR"})(I||(I={}));class Ke{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=P.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new je(this,C.join,this.params,this.timeout),this.rejoinTimer=new Ut(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=P.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(s=>s.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=P.closed,this.socket._remove(this)}),this._onError(s=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,s),this.state=P.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=P.errored,this.rejoinTimer.scheduleTimeout())}),this._on(C.reply,{},(s,n)=>{this._trigger(this._replyEventName(n),s)}),this.presence=new le(this),this.broadcastEndpointURL=Dt(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,s;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:n,presence:o,private:a}}=this.params;this._onError(u=>e==null?void 0:e(I.CHANNEL_ERROR,u)),this._onClose(()=>e==null?void 0:e(I.CLOSED));const l={},c={broadcast:n,presence:o,postgres_changes:(s=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(u=>u.filter))!==null&&s!==void 0?s:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:u})=>{var h;if(this.socket.setAuth(),u===void 0){e==null||e(I.SUBSCRIBED);return}else{const f=this.bindings.postgres_changes,d=(h=f==null?void 0:f.length)!==null&&h!==void 0?h:0,g=[];for(let p=0;p<d;p++){const v=f[p],{filter:{event:k,schema:w,table:_,filter:y}}=v,S=u&&u[p];if(S&&S.event===k&&S.schema===w&&S.table===_&&S.filter===y)g.push(Object.assign(Object.assign({},v),{id:S.id}));else{this.unsubscribe(),this.state=P.errored,e==null||e(I.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=g,e&&e(I.SUBSCRIBED);return}}).receive("error",u=>{this.state=P.errored,e==null||e(I.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(u).join(", ")||"error")))}).receive("timeout",()=>{e==null||e(I.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,s;if(!this._canPush()&&e.type==="broadcast"){const{event:n,payload:o}=e,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:n,payload:o,private:this.private}]})};try{const c=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(r=t.timeout)!==null&&r!==void 0?r:this.timeout);return await((s=c.body)===null||s===void 0?void 0:s.cancel()),c.ok?"ok":"error"}catch(c){return c.name==="AbortError"?"timed out":"error"}}else return new Promise(n=>{var o,a,l;const c=this._push(e.type,e,t.timeout||this.timeout);e.type==="broadcast"&&!(!((l=(a=(o=this.params)===null||o===void 0?void 0:o.config)===null||a===void 0?void 0:a.broadcast)===null||l===void 0)&&l.ack)&&n("ok"),c.receive("ok",()=>n("ok")),c.receive("error",()=>n("error")),c.receive("timeout",()=>n("timed out"))})}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=P.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(C.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise(r=>{const s=new je(this,C.leave,{},e);s.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),s.send(),this._canPush()||s.trigger("ok",{})})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){const s=new AbortController,n=setTimeout(()=>s.abort(),r),o=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:s.signal}));return clearTimeout(n),o}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new je(this,e,t,r);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var s,n;const o=e.toLocaleLowerCase(),{close:a,error:l,leave:c,join:u}=C;if(r&&[a,l,c,u].indexOf(o)>=0&&r!==this._joinRef())return;let f=this._onMessage(o,t,r);if(t&&!f)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(s=this.bindings.postgres_changes)===null||s===void 0||s.filter(d=>{var g,p,v;return((g=d.filter)===null||g===void 0?void 0:g.event)==="*"||((v=(p=d.filter)===null||p===void 0?void 0:p.event)===null||v===void 0?void 0:v.toLocaleLowerCase())===o}).map(d=>d.callback(f,r)):(n=this.bindings[o])===null||n===void 0||n.filter(d=>{var g,p,v,k,w,_;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in d){const y=d.id,S=(g=d.filter)===null||g===void 0?void 0:g.event;return y&&((p=t.ids)===null||p===void 0?void 0:p.includes(y))&&(S==="*"||(S==null?void 0:S.toLocaleLowerCase())===((v=t.data)===null||v===void 0?void 0:v.type.toLocaleLowerCase()))}else{const y=(w=(k=d==null?void 0:d.filter)===null||k===void 0?void 0:k.event)===null||w===void 0?void 0:w.toLocaleLowerCase();return y==="*"||y===((_=t==null?void 0:t.event)===null||_===void 0?void 0:_.toLocaleLowerCase())}else return d.type.toLocaleLowerCase()===o}).map(d=>{if(typeof f=="object"&&"ids"in f){const g=f.data,{schema:p,table:v,commit_timestamp:k,type:w,errors:_}=g;f=Object.assign(Object.assign({},{schema:p,table:v,commit_timestamp:k,eventType:w,new:{},old:{},errors:_}),this._getPayloadRecords(g))}d.callback(f,r)})}_isClosed(){return this.state===P.closed}_isJoined(){return this.state===P.joined}_isJoining(){return this.state===P.joining}_isLeaving(){return this.state===P.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){const s=e.toLocaleLowerCase(),n={type:s,filter:t,callback:r};return this.bindings[s]?this.bindings[s].push(n):this.bindings[s]=[n],this}_off(e,t){const r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(s=>{var n;return!(((n=s.type)===null||n===void 0?void 0:n.toLocaleLowerCase())===r&&Ke.isEqual(s.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(C.close,{},e)}_onError(e){this._on(C.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=P.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return(e.type==="INSERT"||e.type==="UPDATE")&&(t.new=at(e.columns,e.record)),(e.type==="UPDATE"||e.type==="DELETE")&&(t.old=at(e.columns,e.old_record)),t}}const ht=()=>{},Nr=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class Mr{constructor(e,t){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers=$r,this.params={},this.timeout=It,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=ht,this.ref=0,this.logger=ht,this.conn=null,this.sendBuffer=[],this.serializer=new Rr,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=n=>{let o;return n?o=n:typeof fetch>"u"?o=(...a)=>de(async()=>{const{default:l}=await Promise.resolve().then(()=>ie);return{default:l}},void 0,import.meta.url).then(({default:l})=>l(...a)):o=fetch,(...a)=>o(...a)},this.endPoint=`${e}/${De.websocket}`,this.httpEndpoint=Dt(e),t!=null&&t.transport?this.transport=t.transport:this.transport=null,t!=null&&t.params&&(this.params=t.params),t!=null&&t.headers&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),t!=null&&t.timeout&&(this.timeout=t.timeout),t!=null&&t.logger&&(this.logger=t.logger),(t!=null&&t.logLevel||t!=null&&t.log_level)&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),t!=null&&t.heartbeatIntervalMs&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const s=(r=t==null?void 0:t.params)===null||r===void 0?void 0:r.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=t!=null&&t.reconnectAfterMs?t.reconnectAfterMs:n=>[1e3,2e3,5e3,1e4][n-1]||1e4,this.encode=t!=null&&t.encode?t.encode:(n,o)=>o(JSON.stringify(n)),this.decode=t!=null&&t.decode?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Ut(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(t==null?void 0:t.fetch),t!=null&&t.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(t==null?void 0:t.worker)||!1,this.workerUrl=t==null?void 0:t.workerUrl}this.accessToken=(t==null?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=Le),this.transport){typeof window<"u"&&this.transport===window.WebSocket?this.conn=new this.transport(this.endpointURL()):this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection();return}this.conn=new Fr(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:xr}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,t??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(r=>r.teardown()))}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return this.channels=this.channels.filter(r=>r._joinRef!==e._joinRef),this.channels.length===0&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map(t=>t.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case te.connecting:return M.Connecting;case te.open:return M.Open;case te.closing:return M.Closing;default:return M.Closed}}isConnected(){return this.connectionState()===M.Open}channel(e,t={config:{}}){const r=`realtime:${e}`,s=this.getChannels().find(n=>n.topic===r);if(s)return s;{const n=new Ke(`realtime:${e}`,t,this);return this.channels.push(n),n}}push(e){const{topic:t,event:r,payload:s,ref:n}=e,o=()=>{this.encode(e,a=>{var l;(l=this.conn)===null||l===void 0||l.send(a)})};this.log("push",`${t} ${r} (${n})`,s),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(r=>{t&&r.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),r.joinedOnce&&r._isJoined()&&r._push(C.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(e=this.conn)===null||e===void 0||e.close(Cr,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(r=>r.topic===e&&(r._isJoined()||r._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,t=>{let{topic:r,event:s,payload:n,ref:o}=t;r==="phoenix"&&s==="phx_reply"&&this.heartbeatCallback(t.payload.status=="ok"?"ok":"error"),o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${n.status||""} ${r} ${s} ${o&&"("+o+")"||""}`,n),Array.from(this.channels).filter(a=>a._isMember(r)).forEach(a=>a._trigger(s,n,o)),this.stateChangeCallbacks.message.forEach(a=>a(t))})}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),!this.worker)this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);else{this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=t=>{this.log("worker","worker error",t.message),this.workerRef.terminate()},this.workerRef.onmessage=t=>{t.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(C.error))}_appendParams(e,t){if(Object.keys(t).length===0)return e;const r=e.match(/\?/)?"&":"?",s=new URLSearchParams(t);return`${e}${r}${s}`}_workerObjectUrl(e){let t;if(e)t=e;else{const r=new Blob([Nr],{type:"application/javascript"});t=URL.createObjectURL(r)}return t}}class Fr{constructor(e,t,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=te.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=r.close}}class Je extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function A(i){return typeof i=="object"&&i!==null&&"__isStorageError"in i}class zr extends Je{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class qe extends Je{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var Wr=function(i,e,t,r){function s(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(u){try{c(r.next(u))}catch(h){o(h)}}function l(u){try{c(r.throw(u))}catch(h){o(h)}}function c(u){u.done?n(u.value):s(u.value).then(a,l)}c((r=r.apply(i,e||[])).next())})};const Bt=i=>{let e;return i?e=i:typeof fetch>"u"?e=(...t)=>de(async()=>{const{default:r}=await Promise.resolve().then(()=>ie);return{default:r}},void 0,import.meta.url).then(({default:r})=>r(...t)):e=fetch,(...t)=>e(...t)},Hr=()=>Wr(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield de(()=>Promise.resolve().then(()=>ie),void 0,import.meta.url)).Response:Response}),Ne=i=>{if(Array.isArray(i))return i.map(t=>Ne(t));if(typeof i=="function"||i!==Object(i))return i;const e={};return Object.entries(i).forEach(([t,r])=>{const s=t.replace(/([-_][a-z])/gi,n=>n.toUpperCase().replace(/[-_]/g,""));e[s]=Ne(r)}),e};var z=function(i,e,t,r){function s(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(u){try{c(r.next(u))}catch(h){o(h)}}function l(u){try{c(r.throw(u))}catch(h){o(h)}}function c(u){u.done?n(u.value):s(u.value).then(a,l)}c((r=r.apply(i,e||[])).next())})};const Pe=i=>i.msg||i.message||i.error_description||i.error||JSON.stringify(i),Kr=(i,e,t)=>z(void 0,void 0,void 0,function*(){const r=yield Hr();i instanceof r&&!(t!=null&&t.noResolveJson)?i.json().then(s=>{e(new zr(Pe(s),i.status||500))}).catch(s=>{e(new qe(Pe(s),s))}):e(new qe(Pe(i),i))}),Jr=(i,e,t,r)=>{const s={method:i,headers:(e==null?void 0:e.headers)||{}};return i==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json"},e==null?void 0:e.headers),r&&(s.body=JSON.stringify(r)),Object.assign(Object.assign({},s),t))};function fe(i,e,t,r,s,n){return z(this,void 0,void 0,function*(){return new Promise((o,a)=>{i(t,Jr(e,r,s,n)).then(l=>{if(!l.ok)throw l;return r!=null&&r.noResolveJson?l:l.json()}).then(l=>o(l)).catch(l=>Kr(l,a,r))})})}function ke(i,e,t,r){return z(this,void 0,void 0,function*(){return fe(i,"GET",e,t,r)})}function L(i,e,t,r,s){return z(this,void 0,void 0,function*(){return fe(i,"POST",e,r,s,t)})}function Gr(i,e,t,r,s){return z(this,void 0,void 0,function*(){return fe(i,"PUT",e,r,s,t)})}function Vr(i,e,t,r){return z(this,void 0,void 0,function*(){return fe(i,"HEAD",e,Object.assign(Object.assign({},t),{noResolveJson:!0}),r)})}function qt(i,e,t,r,s){return z(this,void 0,void 0,function*(){return fe(i,"DELETE",e,r,s,t)})}var $=function(i,e,t,r){function s(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(u){try{c(r.next(u))}catch(h){o(h)}}function l(u){try{c(r.throw(u))}catch(h){o(h)}}function c(u){u.done?n(u.value):s(u.value).then(a,l)}c((r=r.apply(i,e||[])).next())})};const Qr={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},dt={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Xr{constructor(e,t={},r,s){this.url=e,this.headers=t,this.bucketId=r,this.fetch=Bt(s)}uploadOrUpdate(e,t,r,s){return $(this,void 0,void 0,function*(){try{let n;const o=Object.assign(Object.assign({},dt),s);let a=Object.assign(Object.assign({},this.headers),e==="POST"&&{"x-upsert":String(o.upsert)});const l=o.metadata;typeof Blob<"u"&&r instanceof Blob?(n=new FormData,n.append("cacheControl",o.cacheControl),l&&n.append("metadata",this.encodeMetadata(l)),n.append("",r)):typeof FormData<"u"&&r instanceof FormData?(n=r,n.append("cacheControl",o.cacheControl),l&&n.append("metadata",this.encodeMetadata(l))):(n=r,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,l&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),s!=null&&s.headers&&(a=Object.assign(Object.assign({},a),s.headers));const c=this._removeEmptyFolders(t),u=this._getFinalPath(c),h=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:e,body:n,headers:a},o!=null&&o.duplex?{duplex:o.duplex}:{})),f=yield h.json();return h.ok?{data:{path:c,id:f.Id,fullPath:f.Key},error:null}:{data:null,error:f}}catch(n){if(A(n))return{data:null,error:n};throw n}})}upload(e,t,r){return $(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,s){return $(this,void 0,void 0,function*(){const n=this._removeEmptyFolders(e),o=this._getFinalPath(n),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",t);try{let l;const c=Object.assign({upsert:dt.upsert},s),u=Object.assign(Object.assign({},this.headers),{"x-upsert":String(c.upsert)});typeof Blob<"u"&&r instanceof Blob?(l=new FormData,l.append("cacheControl",c.cacheControl),l.append("",r)):typeof FormData<"u"&&r instanceof FormData?(l=r,l.append("cacheControl",c.cacheControl)):(l=r,u["cache-control"]=`max-age=${c.cacheControl}`,u["content-type"]=c.contentType);const h=yield this.fetch(a.toString(),{method:"PUT",body:l,headers:u}),f=yield h.json();return h.ok?{data:{path:n,fullPath:f.Key},error:null}:{data:null,error:f}}catch(l){if(A(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(e,t){return $(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e);const s=Object.assign({},this.headers);t!=null&&t.upsert&&(s["x-upsert"]="true");const n=yield L(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),o=new URL(this.url+n.url),a=o.searchParams.get("token");if(!a)throw new Je("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:a},error:null}}catch(r){if(A(r))return{data:null,error:r};throw r}})}update(e,t,r){return $(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return $(this,void 0,void 0,function*(){try{return{data:yield L(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(A(s))return{data:null,error:s};throw s}})}copy(e,t,r){return $(this,void 0,void 0,function*(){try{return{data:{path:(yield L(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(A(s))return{data:null,error:s};throw s}})}createSignedUrl(e,t,r){return $(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),n=yield L(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:t},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return n={signedUrl:encodeURI(`${this.url}${n.signedURL}${o}`)},{data:n,error:null}}catch(s){if(A(s))return{data:null,error:s};throw s}})}createSignedUrls(e,t,r){return $(this,void 0,void 0,function*(){try{const s=yield L(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:s.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${n}`):null})),error:null}}catch(s){if(A(s))return{data:null,error:s};throw s}})}download(e,t){return $(this,void 0,void 0,function*(){const s=typeof(t==null?void 0:t.transform)<"u"?"render/image/authenticated":"object",n=this.transformOptsToQueryString((t==null?void 0:t.transform)||{}),o=n?`?${n}`:"";try{const a=this._getFinalPath(e);return{data:yield(yield ke(this.fetch,`${this.url}/${s}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(A(a))return{data:null,error:a};throw a}})}info(e){return $(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const r=yield ke(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:Ne(r),error:null}}catch(r){if(A(r))return{data:null,error:r};throw r}})}exists(e){return $(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield Vr(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(A(r)&&r instanceof qe){const s=r.originalError;if([400,404].includes(s==null?void 0:s.status))return{data:!1,error:r}}throw r}})}getPublicUrl(e,t){const r=this._getFinalPath(e),s=[],n=t!=null&&t.download?`download=${t.download===!0?"":t.download}`:"";n!==""&&s.push(n);const a=typeof(t==null?void 0:t.transform)<"u"?"render/image":"object",l=this.transformOptsToQueryString((t==null?void 0:t.transform)||{});l!==""&&s.push(l);let c=s.join("&");return c!==""&&(c=`?${c}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${r}${c}`)}}}remove(e){return $(this,void 0,void 0,function*(){try{return{data:yield qt(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(A(t))return{data:null,error:t};throw t}})}list(e,t,r){return $(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},Qr),t),{prefix:e||""});return{data:yield L(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(s){if(A(s))return{data:null,error:s};throw s}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return typeof Buffer<"u"?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const Yr="2.7.1",Zr={"X-Client-Info":`storage-js/${Yr}`};var V=function(i,e,t,r){function s(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(u){try{c(r.next(u))}catch(h){o(h)}}function l(u){try{c(r.throw(u))}catch(h){o(h)}}function c(u){u.done?n(u.value):s(u.value).then(a,l)}c((r=r.apply(i,e||[])).next())})};class es{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},Zr),t),this.fetch=Bt(r)}listBuckets(){return V(this,void 0,void 0,function*(){try{return{data:yield ke(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(A(e))return{data:null,error:e};throw e}})}getBucket(e){return V(this,void 0,void 0,function*(){try{return{data:yield ke(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(A(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return V(this,void 0,void 0,function*(){try{return{data:yield L(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(A(r))return{data:null,error:r};throw r}})}updateBucket(e,t){return V(this,void 0,void 0,function*(){try{return{data:yield Gr(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(A(r))return{data:null,error:r};throw r}})}emptyBucket(e){return V(this,void 0,void 0,function*(){try{return{data:yield L(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(A(t))return{data:null,error:t};throw t}})}deleteBucket(e){return V(this,void 0,void 0,function*(){try{return{data:yield qt(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(A(t))return{data:null,error:t};throw t}})}}class ts extends es{constructor(e,t={},r){super(e,t,r)}from(e){return new Xr(this.url,this.headers,e,this.fetch)}}const rs="2.50.0";let ae="";typeof Deno<"u"?ae="deno":typeof document<"u"?ae="web":typeof navigator<"u"&&navigator.product==="ReactNative"?ae="react-native":ae="node";const ss={"X-Client-Info":`supabase-js-${ae}/${rs}`},is={headers:ss},ns={schema:"public"},os={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},as={};var ls=function(i,e,t,r){function s(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(u){try{c(r.next(u))}catch(h){o(h)}}function l(u){try{c(r.throw(u))}catch(h){o(h)}}function c(u){u.done?n(u.value):s(u.value).then(a,l)}c((r=r.apply(i,e||[])).next())})};const cs=i=>{let e;return i?e=i:typeof fetch>"u"?e=jt:e=fetch,(...t)=>e(...t)},us=()=>typeof Headers>"u"?Pt:Headers,hs=(i,e,t)=>{const r=cs(t),s=us();return(n,o)=>ls(void 0,void 0,void 0,function*(){var a;const l=(a=yield e())!==null&&a!==void 0?a:i;let c=new s(o==null?void 0:o.headers);return c.has("apikey")||c.set("apikey",i),c.has("Authorization")||c.set("Authorization",`Bearer ${l}`),r(n,Object.assign(Object.assign({},o),{headers:c}))})};var ds=function(i,e,t,r){function s(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(u){try{c(r.next(u))}catch(h){o(h)}}function l(u){try{c(r.throw(u))}catch(h){o(h)}}function c(u){u.done?n(u.value):s(u.value).then(a,l)}c((r=r.apply(i,e||[])).next())})};function fs(i){return i.endsWith("/")?i:i+"/"}function gs(i,e){var t,r;const{db:s,auth:n,realtime:o,global:a}=i,{db:l,auth:c,realtime:u,global:h}=e,f={db:Object.assign(Object.assign({},l),s),auth:Object.assign(Object.assign({},c),n),realtime:Object.assign(Object.assign({},u),o),global:Object.assign(Object.assign(Object.assign({},h),a),{headers:Object.assign(Object.assign({},(t=h==null?void 0:h.headers)!==null&&t!==void 0?t:{}),(r=a==null?void 0:a.headers)!==null&&r!==void 0?r:{})}),accessToken:()=>ds(this,void 0,void 0,function*(){return""})};return i.accessToken?f.accessToken=i.accessToken:delete f.accessToken,f}const Nt="2.70.0",Z=30*1e3,Me=3,$e=Me*Z,ps="http://localhost:9999",vs="supabase.auth.token",_s={"X-Client-Info":`gotrue-js/${Nt}`},Fe="X-Supabase-Api-Version",Mt={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},ws=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,ys=6e5;class Ge extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function m(i){return typeof i=="object"&&i!==null&&"__isAuthError"in i}class ms extends Ge{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}function bs(i){return m(i)&&i.name==="AuthApiError"}class Ft extends Ge{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class B extends Ge{constructor(e,t,r,s){super(e,r,s),this.name=t,this.status=r}}class U extends B{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function ks(i){return m(i)&&i.name==="AuthSessionMissingError"}class ve extends B{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class _e extends B{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class we extends B{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function Ss(i){return m(i)&&i.name==="AuthImplicitGrantRedirectError"}class ft extends B{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class ze extends B{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function xe(i){return m(i)&&i.name==="AuthRetryableFetchError"}class gt extends B{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class ce extends B{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const Se="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),pt=` 	
\r=`.split(""),Es=(()=>{const i=new Array(128);for(let e=0;e<i.length;e+=1)i[e]=-1;for(let e=0;e<pt.length;e+=1)i[pt[e].charCodeAt(0)]=-2;for(let e=0;e<Se.length;e+=1)i[Se[e].charCodeAt(0)]=e;return i})();function vt(i,e,t){if(i!==null)for(e.queue=e.queue<<8|i,e.queuedBits+=8;e.queuedBits>=6;){const r=e.queue>>e.queuedBits-6&63;t(Se[r]),e.queuedBits-=6}else if(e.queuedBits>0)for(e.queue=e.queue<<6-e.queuedBits,e.queuedBits=6;e.queuedBits>=6;){const r=e.queue>>e.queuedBits-6&63;t(Se[r]),e.queuedBits-=6}}function zt(i,e,t){const r=Es[i];if(r>-1)for(e.queue=e.queue<<6|r,e.queuedBits+=6;e.queuedBits>=8;)t(e.queue>>e.queuedBits-8&255),e.queuedBits-=8;else{if(r===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(i)}"`)}}function _t(i){const e=[],t=o=>{e.push(String.fromCodePoint(o))},r={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},n=o=>{As(o,r,t)};for(let o=0;o<i.length;o+=1)zt(i.charCodeAt(o),s,n);return e.join("")}function Ts(i,e){if(i<=127){e(i);return}else if(i<=2047){e(192|i>>6),e(128|i&63);return}else if(i<=65535){e(224|i>>12),e(128|i>>6&63),e(128|i&63);return}else if(i<=1114111){e(240|i>>18),e(128|i>>12&63),e(128|i>>6&63),e(128|i&63);return}throw new Error(`Unrecognized Unicode codepoint: ${i.toString(16)}`)}function Os(i,e){for(let t=0;t<i.length;t+=1){let r=i.charCodeAt(t);if(r>55295&&r<=56319){const s=(r-55296)*1024&65535;r=(i.charCodeAt(t+1)-56320&65535|s)+65536,t+=1}Ts(r,e)}}function As(i,e,t){if(e.utf8seq===0){if(i<=127){t(i);return}for(let r=1;r<6;r+=1)if((i>>7-r&1)===0){e.utf8seq=r;break}if(e.utf8seq===2)e.codepoint=i&31;else if(e.utf8seq===3)e.codepoint=i&15;else if(e.utf8seq===4)e.codepoint=i&7;else throw new Error("Invalid UTF-8 sequence");e.utf8seq-=1}else if(e.utf8seq>0){if(i<=127)throw new Error("Invalid UTF-8 sequence");e.codepoint=e.codepoint<<6|i&63,e.utf8seq-=1,e.utf8seq===0&&t(e.codepoint)}}function js(i){const e=[],t={queue:0,queuedBits:0},r=s=>{e.push(s)};for(let s=0;s<i.length;s+=1)zt(i.charCodeAt(s),t,r);return new Uint8Array(e)}function Ps(i){const e=[];return Os(i,t=>e.push(t)),new Uint8Array(e)}function $s(i){const e=[],t={queue:0,queuedBits:0},r=s=>{e.push(s)};return i.forEach(s=>vt(s,t,r)),vt(null,t,r),e.join("")}function xs(i){return Math.round(Date.now()/1e3)+i}function Cs(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(i){const e=Math.random()*16|0;return(i=="x"?e:e&3|8).toString(16)})}const x=()=>typeof window<"u"&&typeof document<"u",q={tested:!1,writable:!1},ue=()=>{if(!x())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(q.tested)return q.writable;const i=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(i,i),globalThis.localStorage.removeItem(i),q.tested=!0,q.writable=!0}catch{q.tested=!0,q.writable=!1}return q.writable};function Rs(i){const e={},t=new URL(i);if(t.hash&&t.hash[0]==="#")try{new URLSearchParams(t.hash.substring(1)).forEach((s,n)=>{e[n]=s})}catch{}return t.searchParams.forEach((r,s)=>{e[s]=r}),e}const Wt=i=>{let e;return i?e=i:typeof fetch>"u"?e=(...t)=>de(async()=>{const{default:r}=await Promise.resolve().then(()=>ie);return{default:r}},void 0,import.meta.url).then(({default:r})=>r(...t)):e=fetch,(...t)=>e(...t)},Is=i=>typeof i=="object"&&i!==null&&"status"in i&&"ok"in i&&"json"in i&&typeof i.json=="function",Ht=async(i,e,t)=>{await i.setItem(e,JSON.stringify(t))},ye=async(i,e)=>{const t=await i.getItem(e);if(!t)return null;try{return JSON.parse(t)}catch{return t}},me=async(i,e)=>{await i.removeItem(e)};class Oe{constructor(){this.promise=new Oe.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}Oe.promiseConstructor=Promise;function Ce(i){const e=i.split(".");if(e.length!==3)throw new ce("Invalid JWT structure");for(let r=0;r<e.length;r++)if(!ws.test(e[r]))throw new ce("JWT not in base64url format");return{header:JSON.parse(_t(e[0])),payload:JSON.parse(_t(e[1])),signature:js(e[2]),raw:{header:e[0],payload:e[1]}}}async function Us(i){return await new Promise(e=>{setTimeout(()=>e(null),i)})}function Ls(i,e){return new Promise((r,s)=>{(async()=>{for(let n=0;n<1/0;n++)try{const o=await i(n);if(!e(n,null,o)){r(o);return}}catch(o){if(!e(n,o)){s(o);return}}})()})}function Ds(i){return("0"+i.toString(16)).substr(-2)}function Bs(){const e=new Uint32Array(56);if(typeof crypto>"u"){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=t.length;let s="";for(let n=0;n<56;n++)s+=t.charAt(Math.floor(Math.random()*r));return s}return crypto.getRandomValues(e),Array.from(e,Ds).join("")}async function qs(i){const t=new TextEncoder().encode(i),r=await crypto.subtle.digest("SHA-256",t),s=new Uint8Array(r);return Array.from(s).map(n=>String.fromCharCode(n)).join("")}async function Ns(i){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),i;const t=await qs(i);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function Q(i,e,t=!1){const r=Bs();let s=r;t&&(s+="/PASSWORD_RECOVERY"),await Ht(i,`${e}-code-verifier`,s);const n=await Ns(r);return[n,r===n?"plain":"s256"]}const Ms=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function Fs(i){const e=i.headers.get(Fe);if(!e||!e.match(Ms))return null;try{return new Date(`${e}T00:00:00.0Z`)}catch{return null}}function zs(i){if(!i)throw new Error("Missing exp claim");const e=Math.floor(Date.now()/1e3);if(i<=e)throw new Error("JWT has expired")}function Ws(i){switch(i){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const Hs=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function X(i){if(!Hs.test(i))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var Ks=function(i,e){var t={};for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&e.indexOf(r)<0&&(t[r]=i[r]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(i);s<r.length;s++)e.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(i,r[s])&&(t[r[s]]=i[r[s]]);return t};const N=i=>i.msg||i.message||i.error_description||i.error||JSON.stringify(i),Js=[502,503,504];async function wt(i){var e;if(!Is(i))throw new ze(N(i),0);if(Js.includes(i.status))throw new ze(N(i),i.status);let t;try{t=await i.json()}catch(n){throw new Ft(N(n),n)}let r;const s=Fs(i);if(s&&s.getTime()>=Mt["2024-01-01"].timestamp&&typeof t=="object"&&t&&typeof t.code=="string"?r=t.code:typeof t=="object"&&t&&typeof t.error_code=="string"&&(r=t.error_code),r){if(r==="weak_password")throw new gt(N(t),i.status,((e=t.weak_password)===null||e===void 0?void 0:e.reasons)||[]);if(r==="session_not_found")throw new U}else if(typeof t=="object"&&t&&typeof t.weak_password=="object"&&t.weak_password&&Array.isArray(t.weak_password.reasons)&&t.weak_password.reasons.length&&t.weak_password.reasons.reduce((n,o)=>n&&typeof o=="string",!0))throw new gt(N(t),i.status,t.weak_password.reasons);throw new ms(N(t),i.status||500,r)}const Gs=(i,e,t,r)=>{const s={method:i,headers:(e==null?void 0:e.headers)||{}};return i==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},e==null?void 0:e.headers),s.body=JSON.stringify(r),Object.assign(Object.assign({},s),t))};async function b(i,e,t,r){var s;const n=Object.assign({},r==null?void 0:r.headers);n[Fe]||(n[Fe]=Mt["2024-01-01"].name),r!=null&&r.jwt&&(n.Authorization=`Bearer ${r.jwt}`);const o=(s=r==null?void 0:r.query)!==null&&s!==void 0?s:{};r!=null&&r.redirectTo&&(o.redirect_to=r.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await Vs(i,e,t+a,{headers:n,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(l):{data:Object.assign({},l),error:null}}async function Vs(i,e,t,r,s,n){const o=Gs(e,r,s,n);let a;try{a=await i(t,Object.assign({},o))}catch(l){throw console.error(l),new ze(N(l),0)}if(a.ok||await wt(a),r!=null&&r.noResolveJson)return a;try{return await a.json()}catch(l){await wt(l)}}function R(i){var e;let t=null;Zs(i)&&(t=Object.assign({},i),i.expires_at||(t.expires_at=xs(i.expires_in)));const r=(e=i.user)!==null&&e!==void 0?e:i;return{data:{session:t,user:r},error:null}}function yt(i){const e=R(i);return!e.error&&i.weak_password&&typeof i.weak_password=="object"&&Array.isArray(i.weak_password.reasons)&&i.weak_password.reasons.length&&i.weak_password.message&&typeof i.weak_password.message=="string"&&i.weak_password.reasons.reduce((t,r)=>t&&typeof r=="string",!0)&&(e.data.weak_password=i.weak_password),e}function D(i){var e;return{data:{user:(e=i.user)!==null&&e!==void 0?e:i},error:null}}function Qs(i){return{data:i,error:null}}function Xs(i){const{action_link:e,email_otp:t,hashed_token:r,redirect_to:s,verification_type:n}=i,o=Ks(i,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:e,email_otp:t,hashed_token:r,redirect_to:s,verification_type:n},l=Object.assign({},o);return{data:{properties:a,user:l},error:null}}function Ys(i){return i}function Zs(i){return i.access_token&&i.refresh_token&&i.expires_in}const Re=["global","local","others"];var ei=function(i,e){var t={};for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&e.indexOf(r)<0&&(t[r]=i[r]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(i);s<r.length;s++)e.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(i,r[s])&&(t[r[s]]=i[r[s]]);return t};class ti{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=Wt(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=Re[0]){if(Re.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${Re.join(", ")}`);try{return await b(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(r){if(m(r))return{data:null,error:r};throw r}}async inviteUserByEmail(e,t={}){try{return await b(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:D})}catch(r){if(m(r))return{data:{user:null},error:r};throw r}}async generateLink(e){try{const{options:t}=e,r=ei(e,["options"]),s=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(s.new_email=r==null?void 0:r.newEmail,delete s.newEmail),await b(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:Xs,redirectTo:t==null?void 0:t.redirectTo})}catch(t){if(m(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await b(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:D})}catch(t){if(m(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,r,s,n,o,a,l;try{const c={nextPage:null,lastPage:0,total:0},u=await b(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(t=e==null?void 0:e.page)===null||t===void 0?void 0:t.toString())!==null&&r!==void 0?r:"",per_page:(n=(s=e==null?void 0:e.perPage)===null||s===void 0?void 0:s.toString())!==null&&n!==void 0?n:""},xform:Ys});if(u.error)throw u.error;const h=await u.json(),f=(o=u.headers.get("x-total-count"))!==null&&o!==void 0?o:0,d=(l=(a=u.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&l!==void 0?l:[];return d.length>0&&(d.forEach(g=>{const p=parseInt(g.split(";")[0].split("=")[1].substring(0,1)),v=JSON.parse(g.split(";")[1].split("=")[1]);c[`${v}Page`]=p}),c.total=parseInt(f)),{data:Object.assign(Object.assign({},h),c),error:null}}catch(c){if(m(c))return{data:{users:[]},error:c};throw c}}async getUserById(e){X(e);try{return await b(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:D})}catch(t){if(m(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){X(e);try{return await b(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:D})}catch(r){if(m(r))return{data:{user:null},error:r};throw r}}async deleteUser(e,t=!1){X(e);try{return await b(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:D})}catch(r){if(m(r))return{data:{user:null},error:r};throw r}}async _listFactors(e){X(e.userId);try{const{data:t,error:r}=await b(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:s=>({data:{factors:s},error:null})});return{data:t,error:r}}catch(t){if(m(t))return{data:null,error:t};throw t}}async _deleteFactor(e){X(e.userId),X(e.id);try{return{data:await b(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(m(t))return{data:null,error:t};throw t}}}const ri={getItem:i=>ue()?globalThis.localStorage.getItem(i):null,setItem:(i,e)=>{ue()&&globalThis.localStorage.setItem(i,e)},removeItem:i=>{ue()&&globalThis.localStorage.removeItem(i)}};function mt(i={}){return{getItem:e=>i[e]||null,setItem:(e,t)=>{i[e]=t},removeItem:e=>{delete i[e]}}}function si(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const Y={debug:!!(globalThis&&ue()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class Kt extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class ii extends Kt{}async function ni(i,e,t){Y.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",i,e);const r=new globalThis.AbortController;return e>0&&setTimeout(()=>{r.abort(),Y.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",i)},e),await Promise.resolve().then(()=>globalThis.navigator.locks.request(i,e===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async s=>{if(s){Y.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",i,s.name);try{return await t()}finally{Y.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",i,s.name)}}else{if(e===0)throw Y.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",i),new ii(`Acquiring an exclusive Navigator LockManager lock "${i}" immediately failed`);if(Y.debug)try{const n=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(n,null,"  "))}catch(n){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",n)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await t()}}))}si();const oi={url:ps,storageKey:vs,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:_s,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function bt(i,e,t){return await t()}class he{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=he.nextInstanceID,he.nextInstanceID+=1,this.instanceID>0&&x()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const s=Object.assign(Object.assign({},oi),e);if(this.logDebugMessages=!!s.debug,typeof s.debug=="function"&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new ti({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=Wt(s.fetch),this.lock=s.lock||bt,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:x()&&(!((t=globalThis==null?void 0:globalThis.navigator)===null||t===void 0)&&t.locks)?this.lock=ni:this.lock=bt,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?s.storage?this.storage=s.storage:ue()?this.storage=ri:(this.memoryStorage={},this.storage=mt(this.memoryStorage)):(this.memoryStorage={},this.storage=mt(this.memoryStorage)),x()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(n){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",n)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async n=>{this._debug("received broadcast notification from other tab or client",n),await this._notifyAllSubscribers(n.data.event,n.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Nt}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var e;try{const t=Rs(window.location.href);let r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),x()&&this.detectSessionInUrl&&r!=="none"){const{data:s,error:n}=await this._getSessionFromURL(t,r);if(n){if(this._debug("#_initialize()","error detecting session from URL",n),Ss(n)){const l=(e=n.details)===null||e===void 0?void 0:e.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:n}}return await this._removeSession(),{error:n}}const{session:o,redirectType:a}=s;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),await this._saveSession(o),setTimeout(async()=>{a==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return m(t)?{error:t}:{error:new Ft("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,s;try{const n=await b(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(t=e==null?void 0:e.options)===null||t===void 0?void 0:t.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(s=e==null?void 0:e.options)===null||s===void 0?void 0:s.captchaToken}},xform:R}),{data:o,error:a}=n;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,c=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(n){if(m(n))return{data:{user:null,session:null},error:n};throw n}}async signUp(e){var t,r,s;try{let n;if("email"in e){const{email:u,password:h,options:f}=e;let d=null,g=null;this.flowType==="pkce"&&([d,g]=await Q(this.storage,this.storageKey)),n=await b(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:f==null?void 0:f.emailRedirectTo,body:{email:u,password:h,data:(t=f==null?void 0:f.data)!==null&&t!==void 0?t:{},gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken},code_challenge:d,code_challenge_method:g},xform:R})}else if("phone"in e){const{phone:u,password:h,options:f}=e;n=await b(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:u,password:h,data:(r=f==null?void 0:f.data)!==null&&r!==void 0?r:{},channel:(s=f==null?void 0:f.channel)!==null&&s!==void 0?s:"sms",gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken}},xform:R})}else throw new _e("You must provide either an email or phone number and a password");const{data:o,error:a}=n;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,c=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(n){if(m(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithPassword(e){try{let t;if("email"in e){const{email:n,password:o,options:a}=e;t=await b(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:n,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:yt})}else if("phone"in e){const{phone:n,password:o,options:a}=e;t=await b(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:n,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:yt})}else throw new _e("You must provide either an email or phone number and a password");const{data:r,error:s}=t;return s?{data:{user:null,session:null},error:s}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new ve}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s})}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,r,s,n;return await this._handleProviderSignIn(e.provider,{redirectTo:(t=e.options)===null||t===void 0?void 0:t.redirectTo,scopes:(r=e.options)===null||r===void 0?void 0:r.scopes,queryParams:(s=e.options)===null||s===void 0?void 0:s.queryParams,skipBrowserRedirect:(n=e.options)===null||n===void 0?void 0:n.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){const{chain:t}=e;if(t==="solana")return await this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,s,n,o,a,l,c,u,h,f,d;let g,p;if("message"in e)g=e.message,p=e.signature;else{const{chain:v,wallet:k,statement:w,options:_}=e;let y;if(x())if(typeof k=="object")y=k;else{const T=window;if("solana"in T&&typeof T.solana=="object"&&("signIn"in T.solana&&typeof T.solana.signIn=="function"||"signMessage"in T.solana&&typeof T.solana.signMessage=="function"))y=T.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof k!="object"||!(_!=null&&_.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");y=k}const S=new URL((t=_==null?void 0:_.url)!==null&&t!==void 0?t:window.location.href);if("signIn"in y&&y.signIn){const T=await y.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},_==null?void 0:_.signInWithSolana),{version:"1",domain:S.host,uri:S.href}),w?{statement:w}:null));let j;if(Array.isArray(T)&&T[0]&&typeof T[0]=="object")j=T[0];else if(T&&typeof T=="object"&&"signedMessage"in T&&"signature"in T)j=T;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in j&&"signature"in j&&(typeof j.signedMessage=="string"||j.signedMessage instanceof Uint8Array)&&j.signature instanceof Uint8Array)g=typeof j.signedMessage=="string"?j.signedMessage:new TextDecoder().decode(j.signedMessage),p=j.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in y)||typeof y.signMessage!="function"||!("publicKey"in y)||typeof y!="object"||!y.publicKey||!("toBase58"in y.publicKey)||typeof y.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");g=[`${S.host} wants you to sign in with your Solana account:`,y.publicKey.toBase58(),...w?["",w,""]:[""],"Version: 1",`URI: ${S.href}`,`Issued At: ${(s=(r=_==null?void 0:_.signInWithSolana)===null||r===void 0?void 0:r.issuedAt)!==null&&s!==void 0?s:new Date().toISOString()}`,...!((n=_==null?void 0:_.signInWithSolana)===null||n===void 0)&&n.notBefore?[`Not Before: ${_.signInWithSolana.notBefore}`]:[],...!((o=_==null?void 0:_.signInWithSolana)===null||o===void 0)&&o.expirationTime?[`Expiration Time: ${_.signInWithSolana.expirationTime}`]:[],...!((a=_==null?void 0:_.signInWithSolana)===null||a===void 0)&&a.chainId?[`Chain ID: ${_.signInWithSolana.chainId}`]:[],...!((l=_==null?void 0:_.signInWithSolana)===null||l===void 0)&&l.nonce?[`Nonce: ${_.signInWithSolana.nonce}`]:[],...!((c=_==null?void 0:_.signInWithSolana)===null||c===void 0)&&c.requestId?[`Request ID: ${_.signInWithSolana.requestId}`]:[],...!((h=(u=_==null?void 0:_.signInWithSolana)===null||u===void 0?void 0:u.resources)===null||h===void 0)&&h.length?["Resources",..._.signInWithSolana.resources.map(j=>`- ${j}`)]:[]].join(`
`);const T=await y.signMessage(new TextEncoder().encode(g),"utf8");if(!T||!(T instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=T}}try{const{data:v,error:k}=await b(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:g,signature:$s(p)},!((f=e.options)===null||f===void 0)&&f.captchaToken?{gotrue_meta_security:{captcha_token:(d=e.options)===null||d===void 0?void 0:d.captchaToken}}:null),xform:R});if(k)throw k;return!v||!v.session||!v.user?{data:{user:null,session:null},error:new ve}:(v.session&&(await this._saveSession(v.session),await this._notifyAllSubscribers("SIGNED_IN",v.session)),{data:Object.assign({},v),error:k})}catch(v){if(m(v))return{data:{user:null,session:null},error:v};throw v}}async _exchangeCodeForSession(e){const t=await ye(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(t??"").split("/");try{const{data:n,error:o}=await b(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:R});if(await me(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!n||!n.session||!n.user?{data:{user:null,session:null,redirectType:null},error:new ve}:(n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:Object.assign(Object.assign({},n),{redirectType:s??null}),error:o})}catch(n){if(m(n))return{data:{user:null,session:null,redirectType:null},error:n};throw n}}async signInWithIdToken(e){try{const{options:t,provider:r,token:s,access_token:n,nonce:o}=e,a=await b(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:n,nonce:o,gotrue_meta_security:{captcha_token:t==null?void 0:t.captchaToken}},xform:R}),{data:l,error:c}=a;return c?{data:{user:null,session:null},error:c}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new ve}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:c})}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,r,s,n,o;try{if("email"in e){const{email:a,options:l}=e;let c=null,u=null;this.flowType==="pkce"&&([c,u]=await Q(this.storage,this.storageKey));const{error:h}=await b(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:a,data:(t=l==null?void 0:l.data)!==null&&t!==void 0?t:{},create_user:(r=l==null?void 0:l.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:c,code_challenge_method:u},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:h}}if("phone"in e){const{phone:a,options:l}=e,{data:c,error:u}=await b(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:(s=l==null?void 0:l.data)!==null&&s!==void 0?s:{},create_user:(n=l==null?void 0:l.shouldCreateUser)!==null&&n!==void 0?n:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(o=l==null?void 0:l.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:c==null?void 0:c.message_id},error:u}}throw new _e("You must provide either an email or phone number.")}catch(a){if(m(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(e){var t,r;try{let s,n;"options"in e&&(s=(t=e.options)===null||t===void 0?void 0:t.redirectTo,n=(r=e.options)===null||r===void 0?void 0:r.captchaToken);const{data:o,error:a}=await b(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:n}}),redirectTo:s,xform:R});if(a)throw a;if(!o)throw new Error("An error occurred on token verification.");const l=o.session,c=o.user;return l!=null&&l.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(e.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(s){if(m(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithSSO(e){var t,r,s;try{let n=null,o=null;return this.flowType==="pkce"&&([n,o]=await Q(this.storage,this.storageKey)),await b(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:(r=(t=e.options)===null||t===void 0?void 0:t.redirectTo)!==null&&r!==void 0?r:void 0}),!((s=e==null?void 0:e.options)===null||s===void 0)&&s.captchaToken?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:n,code_challenge_method:o}),headers:this.headers,xform:Qs})}catch(n){if(m(n))return{data:null,error:n};throw n}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{const{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new U;const{error:s}=await b(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:s}})}catch(e){if(m(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:r,type:s,options:n}=e,{error:o}=await b(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},redirectTo:n==null?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in e){const{phone:r,type:s,options:n}=e,{data:o,error:a}=await b(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:a}}throw new _e("You must provide either an email or phone number and a type")}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async t=>t))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await r,await t()))();return this.pendingInLock.push((async()=>{try{await s}catch{}})()),s}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=t();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const s=[...this.pendingInLock];await Promise.all(s),this.pendingInLock.splice(0,s.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let e=null;const t=await ye(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),t!==null&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const r=e.expires_at?e.expires_at*1e3-Date.now()<$e:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let o=this.suppressGetSessionWarning;e=new Proxy(e,{get:(l,c,u)=>(!o&&c==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),o=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,c,u))})}return{data:{session:e},error:null}}const{session:s,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{session:null},error:n}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{return e?await b(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:D}):await this._useSession(async t=>{var r,s,n;const{data:o,error:a}=t;if(a)throw a;return!(!((r=o.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new U}:await b(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(n=(s=o.session)===null||s===void 0?void 0:s.access_token)!==null&&n!==void 0?n:void 0,xform:D})})}catch(t){if(m(t))return ks(t)&&(await this._removeSession(),await me(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{const{data:s,error:n}=r;if(n)throw n;if(!s.session)throw new U;const o=s.session;let a=null,l=null;this.flowType==="pkce"&&e.email!=null&&([a,l]=await Q(this.storage,this.storageKey));const{data:c,error:u}=await b(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:t==null?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:l}),jwt:o.access_token,xform:D});if(u)throw u;return o.user=c.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(r){if(m(r))return{data:{user:null},error:r};throw r}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new U;const t=Date.now()/1e3;let r=t,s=!0,n=null;const{payload:o}=Ce(e.access_token);if(o.exp&&(r=o.exp,s=r<=t),s){const{session:a,error:l}=await this._callRefreshToken(e.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!a)return{data:{user:null,session:null},error:null};n=a}else{const{data:a,error:l}=await this._getUser(e.access_token);if(l)throw l;n={access_token:e.access_token,refresh_token:e.refresh_token,user:a.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(n),await this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(t){if(m(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){const{data:o,error:a}=t;if(a)throw a;e=(r=o.session)!==null&&r!==void 0?r:void 0}if(!(e!=null&&e.refresh_token))throw new U;const{session:s,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{user:null,session:null},error:n}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!x())throw new we("No browser detected.");if(e.error||e.error_description||e.error_code)throw new we(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if(this.flowType==="pkce")throw new ft("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new we("Not a valid implicit grant flow url.");break;default:}if(t==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new ft("No code detected.");const{data:w,error:_}=await this._exchangeCodeForSession(e.code);if(_)throw _;const y=new URL(window.location.href);return y.searchParams.delete("code"),window.history.replaceState(window.history.state,"",y.toString()),{data:{session:w.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:s,access_token:n,refresh_token:o,expires_in:a,expires_at:l,token_type:c}=e;if(!n||!a||!o||!c)throw new we("No session defined in URL");const u=Math.round(Date.now()/1e3),h=parseInt(a);let f=u+h;l&&(f=parseInt(l));const d=f-u;d*1e3<=Z&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${h}s`);const g=f-h;u-g>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",g,f,u):u-g<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",g,f,u);const{data:p,error:v}=await this._getUser(n);if(v)throw v;const k={provider_token:r,provider_refresh_token:s,access_token:n,expires_in:h,expires_at:f,refresh_token:o,token_type:c,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:k,redirectType:e.type},error:null}}catch(r){if(m(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await ye(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;const{data:s,error:n}=t;if(n)return{error:n};const o=(r=s.session)===null||r===void 0?void 0:r.access_token;if(o){const{error:a}=await this.admin.signOut(o,e);if(a&&!(bs(a)&&(a.status===404||a.status===401||a.status===403)))return{error:a}}return e!=="others"&&(await this._removeSession(),await me(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){const t=Cs(),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})))(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,s;try{const{data:{session:n},error:o}=t;if(o)throw o;await((r=this.stateChangeEmitters.get(e))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",n)),this._debug("INITIAL_SESSION","callback id",e,"session",n)}catch(n){await((s=this.stateChangeEmitters.get(e))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",n),console.error(n)}})}async resetPasswordForEmail(e,t={}){let r=null,s=null;this.flowType==="pkce"&&([r,s]=await Q(this.storage,this.storageKey,!0));try{return await b(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(n){if(m(n))return{data:null,error:n};throw n}}async getUserIdentities(){var e;try{const{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(e=t.user.identities)!==null&&e!==void 0?e:[]},error:null}}catch(t){if(m(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:r,error:s}=await this._useSession(async n=>{var o,a,l,c,u;const{data:h,error:f}=n;if(f)throw f;const d=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:(o=e.options)===null||o===void 0?void 0:o.redirectTo,scopes:(a=e.options)===null||a===void 0?void 0:a.scopes,queryParams:(l=e.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await b(this.fetch,"GET",d,{headers:this.headers,jwt:(u=(c=h.session)===null||c===void 0?void 0:c.access_token)!==null&&u!==void 0?u:void 0})});if(s)throw s;return x()&&!(!((t=e.options)===null||t===void 0)&&t.skipBrowserRedirect)&&window.location.assign(r==null?void 0:r.url),{data:{provider:e.provider,url:r==null?void 0:r.url},error:null}}catch(r){if(m(r))return{data:{provider:e.provider,url:null},error:r};throw r}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,s;const{data:n,error:o}=t;if(o)throw o;return await b(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:(s=(r=n.session)===null||r===void 0?void 0:r.access_token)!==null&&s!==void 0?s:void 0})})}catch(t){if(m(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const r=Date.now();return await Ls(async s=>(s>0&&await Us(200*Math.pow(2,s-1)),this._debug(t,"refreshing attempt",s),await b(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:R})),(s,n)=>{const o=200*Math.pow(2,s);return n&&xe(n)&&Date.now()+o-r<Z})}catch(r){if(this._debug(t,"error",r),m(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(t,"end")}}_isValidSession(e){return typeof e=="object"&&e!==null&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),x()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const r=await ye(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),r!==null&&await this._removeSession();return}const s=((e=r.expires_at)!==null&&e!==void 0?e:1/0)*1e3-Date.now()<$e;if(this._debug(t,`session has${s?"":" not"} expired with margin of ${$e}s`),s){if(this.autoRefreshToken&&r.refresh_token){const{error:n}=await this._callRefreshToken(r.refresh_token);n&&(console.error(n),xe(n)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",n),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(t,"error",r),console.error(r);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new U;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new Oe;const{data:n,error:o}=await this._refreshAccessToken(e);if(o)throw o;if(!n.session)throw new U;await this._saveSession(n.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",n.session);const a={session:n.session,error:null};return this.refreshingDeferred.resolve(a),a}catch(n){if(this._debug(s,"error",n),m(n)){const o={session:null,error:n};return xe(n)||await this._removeSession(),(t=this.refreshingDeferred)===null||t===void 0||t.resolve(o),o}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(n),n}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(e,t,r=!0){const s=`#_notifyAllSubscribers(${e})`;this._debug(s,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});const n=[],o=Array.from(this.stateChangeEmitters.values()).map(async a=>{try{await a.callback(e,t)}catch(l){n.push(l)}});if(await Promise.all(o),n.length>0){for(let a=0;a<n.length;a+=1)console.error(n[a]);throw n[0]}}finally{this._debug(s,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await Ht(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await me(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&x()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),Z);this.autoRefreshTicker=e,e&&typeof e=="object"&&typeof e.unref=="function"?e.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const e=Date.now();try{return await this._useSession(async t=>{const{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const s=Math.floor((r.expires_at*1e3-e)/Z);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts ${Z}ms, refresh threshold is ${Me} ticks`),s<=Me&&await this._callRefreshToken(r.refresh_token)})}catch(t){console.error("Auto refresh tick failed with error. This is likely a transient error.",t)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof Kt)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!x()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){const s=[`provider=${encodeURIComponent(t)}`];if(r!=null&&r.redirectTo&&s.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r!=null&&r.scopes&&s.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[n,o]=await Q(this.storage,this.storageKey),a=new URLSearchParams({code_challenge:`${encodeURIComponent(n)}`,code_challenge_method:`${encodeURIComponent(o)}`});s.push(a.toString())}if(r!=null&&r.queryParams){const n=new URLSearchParams(r.queryParams);s.push(n.toString())}return r!=null&&r.skipBrowserRedirect&&s.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${s.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;const{data:s,error:n}=t;return n?{data:null,error:n}:await b(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token})})}catch(t){if(m(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession(async t=>{var r,s;const{data:n,error:o}=t;if(o)return{data:null,error:o};const a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},e.factorType==="phone"?{phone:e.phone}:{issuer:e.issuer}),{data:l,error:c}=await b(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:(r=n==null?void 0:n.session)===null||r===void 0?void 0:r.access_token});return c?{data:null,error:c}:(e.factorType==="totp"&&(!((s=l==null?void 0:l.totp)===null||s===void 0)&&s.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(t){if(m(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;const{data:s,error:n}=t;if(n)return{data:null,error:n};const{data:o,error:a}=await b(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:a})})}catch(t){if(m(t))return{data:null,error:t};throw t}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;const{data:s,error:n}=t;return n?{data:null,error:n}:await b(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token})})}catch(t){if(m(t))return{data:null,error:t};throw t}})}async _challengeAndVerify(e){const{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const r=(e==null?void 0:e.factors)||[],s=r.filter(o=>o.factor_type==="totp"&&o.status==="verified"),n=r.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:r,totp:s,phone:n},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;const{data:{session:s},error:n}=e;if(n)return{data:null,error:n};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=Ce(s.access_token);let a=null;o.aal&&(a=o.aal);let l=a;((r=(t=s.user.factors)===null||t===void 0?void 0:t.filter(h=>h.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(l="aal2");const u=o.amr||[];return{data:{currentLevel:a,nextLevel:l,currentAuthenticationMethods:u},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(o=>o.kid===e);if(r||(r=this.jwks.keys.find(o=>o.kid===e),r&&this.jwks_cached_at+ys>Date.now()))return r;const{data:s,error:n}=await b(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(n)throw n;if(!s.keys||s.keys.length===0)throw new ce("JWKS is empty");if(this.jwks=s,this.jwks_cached_at=Date.now(),r=s.keys.find(o=>o.kid===e),!r)throw new ce("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){const{data:d,error:g}=await this.getSession();if(g||!d.session)return{data:null,error:g};r=d.session.access_token}const{header:s,payload:n,signature:o,raw:{header:a,payload:l}}=Ce(r);if(zs(n.exp),!s.kid||s.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:d}=await this.getUser(r);if(d)throw d;return{data:{claims:n,header:s,signature:o},error:null}}const c=Ws(s.alg),u=await this.fetchJwk(s.kid,t),h=await crypto.subtle.importKey("jwk",u,c,!0,["verify"]);if(!await crypto.subtle.verify(c,h,o,Ps(`${a}.${l}`)))throw new ce("Invalid JWT signature");return{data:{claims:n,header:s,signature:o},error:null}}catch(r){if(m(r))return{data:null,error:r};throw r}}}he.nextInstanceID=0;const ai=he;class li extends ai{constructor(e){super(e)}}var ci=function(i,e,t,r){function s(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(u){try{c(r.next(u))}catch(h){o(h)}}function l(u){try{c(r.throw(u))}catch(h){o(h)}}function c(u){u.done?n(u.value):s(u.value).then(a,l)}c((r=r.apply(i,e||[])).next())})};class ui{constructor(e,t,r){var s,n,o;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const a=fs(e),l=new URL(a);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c=`sb-${l.hostname.split(".")[0]}-auth-token`,u={db:ns,realtime:as,auth:Object.assign(Object.assign({},os),{storageKey:c}),global:is},h=gs(r??{},u);this.storageKey=(s=h.auth.storageKey)!==null&&s!==void 0?s:"",this.headers=(n=h.global.headers)!==null&&n!==void 0?n:{},h.accessToken?(this.accessToken=h.accessToken,this.auth=new Proxy({},{get:(f,d)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(d)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=h.auth)!==null&&o!==void 0?o:{},this.headers,h.global.fetch),this.fetch=hs(t,this._getAccessToken.bind(this),h.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},h.realtime)),this.rest=new jr(new URL("rest/v1",l).href,{headers:this.headers,schema:h.db.schema,fetch:this.fetch}),h.accessToken||this._listenForAuthEvents()}get functions(){return new vr(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new ts(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return ci(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(t=(e=r.session)===null||e===void 0?void 0:e.access_token)!==null&&t!==void 0?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,storageKey:n,flowType:o,lock:a,debug:l},c,u){const h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new li({url:this.authUrl.href,headers:Object.assign(Object.assign({},h),c),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,flowType:o,lock:a,debug:l,fetch:u,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new Mr(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},e==null?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((t,r)=>{this._handleTokenChanged(t,"CLIENT",r==null?void 0:r.access_token)})}_handleTokenChanged(e,t,r){(e==="TOKEN_REFRESHED"||e==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:e==="SIGNED_OUT"&&(this.realtime.setAuth(),t=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const Jt=(i,e,t)=>new ui(i,e,t),Gt="0.5.2";var be={};/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var kt;function hi(){if(kt)return be;kt=1,be.parse=o,be.serialize=c;var i=Object.prototype.toString,e=Object.prototype.hasOwnProperty,t=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,r=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,n=/^[\u0020-\u003A\u003D-\u007E]*$/;function o(d,g){if(typeof d!="string")throw new TypeError("argument str must be a string");var p={},v=d.length;if(v<2)return p;var k=g&&g.decode||u,w=0,_=0,y=0;do{if(_=d.indexOf("=",w),_===-1)break;if(y=d.indexOf(";",w),y===-1)y=v;else if(_>y){w=d.lastIndexOf(";",_-1)+1;continue}var S=a(d,w,_),T=l(d,_,S),j=d.slice(S,T);if(!e.call(p,j)){var ge=a(d,_+1,y),Ae=l(d,y,ge);d.charCodeAt(ge)===34&&d.charCodeAt(Ae-1)===34&&(ge++,Ae--);var Zt=d.slice(ge,Ae);p[j]=f(Zt,k)}w=y+1}while(w<v);return p}function a(d,g,p){do{var v=d.charCodeAt(g);if(v!==32&&v!==9)return g}while(++g<p);return p}function l(d,g,p){for(;g>p;){var v=d.charCodeAt(--g);if(v!==32&&v!==9)return g+1}return p}function c(d,g,p){var v=p&&p.encode||encodeURIComponent;if(typeof v!="function")throw new TypeError("option encode is invalid");if(!t.test(d))throw new TypeError("argument name is invalid");var k=v(g);if(!r.test(k))throw new TypeError("argument val is invalid");var w=d+"="+k;if(!p)return w;if(p.maxAge!=null){var _=Math.floor(p.maxAge);if(!isFinite(_))throw new TypeError("option maxAge is invalid");w+="; Max-Age="+_}if(p.domain){if(!s.test(p.domain))throw new TypeError("option domain is invalid");w+="; Domain="+p.domain}if(p.path){if(!n.test(p.path))throw new TypeError("option path is invalid");w+="; Path="+p.path}if(p.expires){var y=p.expires;if(!h(y)||isNaN(y.valueOf()))throw new TypeError("option expires is invalid");w+="; Expires="+y.toUTCString()}if(p.httpOnly&&(w+="; HttpOnly"),p.secure&&(w+="; Secure"),p.partitioned&&(w+="; Partitioned"),p.priority){var S=typeof p.priority=="string"?p.priority.toLowerCase():p.priority;switch(S){case"low":w+="; Priority=Low";break;case"medium":w+="; Priority=Medium";break;case"high":w+="; Priority=High";break;default:throw new TypeError("option priority is invalid")}}if(p.sameSite){var T=typeof p.sameSite=="string"?p.sameSite.toLowerCase():p.sameSite;switch(T){case!0:w+="; SameSite=Strict";break;case"lax":w+="; SameSite=Lax";break;case"strict":w+="; SameSite=Strict";break;case"none":w+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return w}function u(d){return d.indexOf("%")!==-1?decodeURIComponent(d):d}function h(d){return i.call(d)==="[object Date]"}function f(d,g){try{return g(d)}catch{return d}}return be}var St=hi();function re(){return typeof window<"u"&&typeof window.document<"u"}const F={path:"/",sameSite:"lax",httpOnly:!1,maxAge:400*24*60*60},di=3180,fi=/^(.*)[.](0|[1-9][0-9]*)$/;function Ee(i,e){if(i===e)return!0;const t=i.match(fi);return!!(t&&t[1]===e)}function Vt(i,e,t){const r=di;let s=encodeURIComponent(e);if(s.length<=r)return[{name:i,value:e}];const n=[];for(;s.length>0;){let o=s.slice(0,r);const a=o.lastIndexOf("%");a>r-3&&(o=o.slice(0,a));let l="";for(;o.length>0;)try{l=decodeURIComponent(o);break}catch(c){if(c instanceof URIError&&o.at(-3)==="%"&&o.length>3)o=o.slice(0,o.length-3);else throw c}n.push(l),s=s.slice(o.length)}return n.map((o,a)=>({name:`${i}.${a}`,value:o}))}async function Et(i,e){const t=await e(i);if(t)return t;let r=[];for(let s=0;;s++){const n=`${i}.${s}`,o=await e(n);if(!o)break;r.push(o)}return r.length>0?r.join(""):null}const Te="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Tt=` 	
\r=`.split(""),gi=(()=>{const i=new Array(128);for(let e=0;e<i.length;e+=1)i[e]=-1;for(let e=0;e<Tt.length;e+=1)i[Tt[e].charCodeAt(0)]=-2;for(let e=0;e<Te.length;e+=1)i[Te[e].charCodeAt(0)]=e;return i})();function Qt(i){const e=[];let t=0,r=0;if(vi(i,n=>{for(t=t<<8|n,r+=8;r>=6;){const o=t>>r-6&63;e.push(Te[o]),r-=6}}),r>0)for(t=t<<6-r,r=6;r>=6;){const n=t>>r-6&63;e.push(Te[n]),r-=6}return e.join("")}function Ot(i){const e=[],t=o=>{e.push(String.fromCodePoint(o))},r={utf8seq:0,codepoint:0};let s=0,n=0;for(let o=0;o<i.length;o+=1){const a=i.charCodeAt(o),l=gi[a];if(l>-1)for(s=s<<6|l,n+=6;n>=8;)_i(s>>n-8&255,r,t),n-=8;else{if(l===-2)continue;throw new Error(`Invalid Base64-URL character "${i.at(o)}" at position ${o}`)}}return e.join("")}function pi(i,e){if(i<=127){e(i);return}else if(i<=2047){e(192|i>>6),e(128|i&63);return}else if(i<=65535){e(224|i>>12),e(128|i>>6&63),e(128|i&63);return}else if(i<=1114111){e(240|i>>18),e(128|i>>12&63),e(128|i>>6&63),e(128|i&63);return}throw new Error(`Unrecognized Unicode codepoint: ${i.toString(16)}`)}function vi(i,e){for(let t=0;t<i.length;t+=1){let r=i.charCodeAt(t);if(r>55295&&r<=56319){const s=(r-55296)*1024&65535;r=(i.charCodeAt(t+1)-56320&65535|s)+65536,t+=1}pi(r,e)}}function _i(i,e,t){if(e.utf8seq===0){if(i<=127){t(i);return}for(let r=1;r<6;r+=1)if((i>>7-r&1)===0){e.utf8seq=r;break}if(e.utf8seq===2)e.codepoint=i&31;else if(e.utf8seq===3)e.codepoint=i&15;else if(e.utf8seq===4)e.codepoint=i&7;else throw new Error("Invalid UTF-8 sequence");e.utf8seq-=1}else if(e.utf8seq>0){if(i<=127)throw new Error("Invalid UTF-8 sequence");e.codepoint=e.codepoint<<6|i&63,e.utf8seq-=1,e.utf8seq===0&&t(e.codepoint)}}const ee="base64-";function Xt(i,e){const t=i.cookies??null,r=i.cookieEncoding,s={},n={};let o,a;if(t)if("get"in t){const l=async c=>{const u=c.flatMap(f=>[f,...Array.from({length:5}).map((d,g)=>`${f}.${g}`)]),h=[];for(let f=0;f<u.length;f+=1){const d=await t.get(u[f]);!d&&typeof d!="string"||h.push({name:u[f],value:d})}return h};if(o=async c=>await l(c),"set"in t&&"remove"in t)a=async c=>{for(let u=0;u<c.length;u+=1){const{name:h,value:f,options:d}=c[u];f?await t.set(h,f,d):await t.remove(h,d)}};else if(e)a=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw new Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in t)if(o=async()=>await t.getAll(),"setAll"in t)a=t.setAll;else if(e)a=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw new Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw new Error(`@supabase/ssr: ${e?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${re()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!e&&re()){const l=()=>{const c=St.parse(document.cookie);return Object.keys(c).map(u=>({name:u,value:c[u]}))};o=()=>l(),a=c=>{c.forEach(({name:u,value:h,options:f})=>{document.cookie=St.serialize(u,h,f)})}}else{if(e)throw new Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");o=()=>[],a=()=>{throw new Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")}}return e?{getAll:o,setAll:a,setItems:s,removedItems:n,storage:{isServer:!0,getItem:async l=>{if(typeof s[l]=="string")return s[l];if(n[l])return null;const c=await o([l]),u=await Et(l,async f=>{const d=(c==null?void 0:c.find(({name:g})=>g===f))||null;return d?d.value:null});if(!u)return null;let h=u;return typeof u=="string"&&u.startsWith(ee)&&(h=Ot(u.substring(ee.length))),h},setItem:async(l,c)=>{l.endsWith("-code-verifier")&&await Yt({getAll:o,setAll:a,setItems:{[l]:c},removedItems:{}},{cookieOptions:(i==null?void 0:i.cookieOptions)??null,cookieEncoding:r}),s[l]=c,delete n[l]},removeItem:async l=>{delete s[l],n[l]=!0}}}:{getAll:o,setAll:a,setItems:s,removedItems:n,storage:{isServer:!1,getItem:async l=>{const c=await o([l]),u=await Et(l,async f=>{const d=(c==null?void 0:c.find(({name:g})=>g===f))||null;return d?d.value:null});if(!u)return null;let h=u;return u.startsWith(ee)&&(h=Ot(u.substring(ee.length))),h},setItem:async(l,c)=>{const u=await o([l]),h=(u==null?void 0:u.map(({name:w})=>w))||[],f=new Set(h.filter(w=>Ee(w,l)));let d=c;r==="base64url"&&(d=ee+Qt(c));const g=Vt(l,d);g.forEach(({name:w})=>{f.delete(w)});const p={...F,...i==null?void 0:i.cookieOptions,maxAge:0},v={...F,...i==null?void 0:i.cookieOptions,maxAge:F.maxAge};delete p.name,delete v.name;const k=[...[...f].map(w=>({name:w,value:"",options:p})),...g.map(({name:w,value:_})=>({name:w,value:_,options:v}))];k.length>0&&await a(k)},removeItem:async l=>{const c=await o([l]),h=((c==null?void 0:c.map(({name:d})=>d))||[]).filter(d=>Ee(d,l)),f={...F,...i==null?void 0:i.cookieOptions,maxAge:0};delete f.name,h.length>0&&await a(h.map(d=>({name:d,value:"",options:f})))}}}}async function Yt({getAll:i,setAll:e,setItems:t,removedItems:r},s){const n=s.cookieEncoding,o=s.cookieOptions??null,a=await i([...t?Object.keys(t):[],...r?Object.keys(r):[]]),l=(a==null?void 0:a.map(({name:d})=>d))||[],c=Object.keys(r).flatMap(d=>l.filter(g=>Ee(g,d))),u=Object.keys(t).flatMap(d=>{const g=new Set(l.filter(k=>Ee(k,d)));let p=t[d];n==="base64url"&&(p=ee+Qt(p));const v=Vt(d,p);return v.forEach(k=>{g.delete(k.name)}),c.push(...g),v}),h={...F,...o,maxAge:0},f={...F,...o,maxAge:F.maxAge};delete h.name,delete f.name,await e([...c.map(d=>({name:d,value:"",options:h})),...u.map(({name:d,value:g})=>({name:d,value:g,options:f}))])}let Ie;function wi(i,e,t){var o,a;const r=(t==null?void 0:t.isSingleton)===!0||(!t||!("isSingleton"in t))&&re();if(r&&Ie)return Ie;const{storage:s}=Xt({...t,cookieEncoding:(t==null?void 0:t.cookieEncoding)??"base64url"},!1),n=Jt(i,e,{...t,global:{...t==null?void 0:t.global,headers:{...(o=t==null?void 0:t.global)==null?void 0:o.headers,"X-Client-Info":`supabase-ssr/${Gt}`}},auth:{...t==null?void 0:t.auth,...(a=t==null?void 0:t.cookieOptions)!=null&&a.name?{storageKey:t.cookieOptions.name}:null,flowType:"pkce",autoRefreshToken:re(),detectSessionInUrl:re(),persistSession:!0,storage:s}});return r&&(Ie=n),n}function yi(i,e,t){var c,u;const{storage:r,getAll:s,setAll:n,setItems:o,removedItems:a}=Xt({...t,cookieEncoding:(t==null?void 0:t.cookieEncoding)??"base64url"},!0),l=Jt(i,e,{...t,global:{...t==null?void 0:t.global,headers:{...(c=t==null?void 0:t.global)==null?void 0:c.headers,"X-Client-Info":`supabase-ssr/${Gt}`}},auth:{...(u=t==null?void 0:t.cookieOptions)!=null&&u.name?{storageKey:t.cookieOptions.name}:null,...t==null?void 0:t.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:r}});return l.auth.onAuthStateChange(async h=>{(Object.keys(o).length>0||Object.keys(a).length>0)&&(h==="SIGNED_IN"||h==="TOKEN_REFRESHED"||h==="USER_UPDATED"||h==="PASSWORD_RECOVERY"||h==="SIGNED_OUT"||h==="MFA_CHALLENGE_VERIFIED")&&await Yt({getAll:s,setAll:n,setItems:o,removedItems:a},{cookieOptions:(t==null?void 0:t.cookieOptions)??null,cookieEncoding:(t==null?void 0:t.cookieEncoding)??"base64url"})}),l}const mi=!1,bi=!0,ki=async({data:i,depends:e,fetch:t})=>{e("supabase:auth");const r=re()?wi(Qe,Ve,{global:{fetch:t}}):yi(Qe,Ve,{global:{fetch:t},cookies:{getAll(){return i.cookies}}}),{data:{session:s}}=await r.auth.getSession(),{data:{user:n}}=await r.auth.getUser();return{session:s,supabase:r,user:n,BUILD_TYPE:i.BUILD_TYPE}},qi=Object.freeze(Object.defineProperty({__proto__:null,load:ki,prerender:bi,ssr:mi},Symbol.toStringTag,{value:"Module"}));var Si=At('<link rel="preconnect" href="https://fonts.googleapis.com"/> <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""/> <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&amp;family=Shanti&amp;display=swap"/>',1),Ei=At("<content-container><!></content-container>",2);function Ni(i,e){rr(e,!0),lr.buildType.resolve(e.data.BUILD_TYPE);var t=Ei();ar(s=>{var n=Si();ir(4),Xe(s,n)}),ur(t,1,"svelte-k59sdn");var r=nr(t);cr(r,()=>e.children),or(t),Xe(i,t),sr()}export{Ni as component,qi as universal};
