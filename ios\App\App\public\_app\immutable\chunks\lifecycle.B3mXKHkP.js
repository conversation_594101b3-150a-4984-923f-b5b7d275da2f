import{u as d,b as c,d as g,e as b,g as m,h as l,i as p,j as h,k}from"./runtime.BoVB3PJd.js";function x(n=!1){const s=g,e=s.l.u;if(!e)return;let r=()=>h(s.s);if(n){let o=0,t={};const _=k(()=>{let i=!1;const a=s.s;for(const f in a)a[f]!==t[f]&&(t[f]=a[f],i=!0);return i&&o++,o});r=()=>p(_)}e.b.length&&d(()=>{u(s,r),l(e.b)}),c(()=>{const o=b(()=>e.m.map(m));return()=>{for(const t of o)typeof t=="function"&&t()}}),e.a.length&&c(()=>{u(s,r),l(e.a)})}function u(n,s){if(n.l.s)for(const e of n.l.s)p(e);s()}export{x as i};
