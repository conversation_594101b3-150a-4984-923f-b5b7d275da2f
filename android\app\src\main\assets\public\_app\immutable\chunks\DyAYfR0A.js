import"./CWj6FrbW.js";import{o as Y}from"./B8_8MPyK.js";import{p as M,t as Q,a as W,s as D,c as x,r as U,g as X,u as Z}from"./DvS_9Yw_.js";import{f as F,a as z}from"./Ckyppc5t.js";import{g as I}from"./7czifHHs.js";import{a as N}from"./D9ToATF9.js";import{_ as ee}from"./Dp1pzeXC.js";import{b as te}from"./BeWv36fu.js";import{B as re}from"./DF_v5tP2.js";import{s as B}from"./CZYSyPBs.js";import{u as se}from"./CQqdknCc.js";/*! Capacitor: https://capacitorjs.com/ - MIT License */var L;(function(r){r.Unimplemented="UNIMPLEMENTED",r.Unavailable="UNAVAILABLE"})(L||(L={}));class _ extends Error{constructor(e,t,o){super(e),this.message=e,this.code=t,this.data=o}}const ne=r=>{var e,t;return r!=null&&r.androidBridge?"android":!((t=(e=r==null?void 0:r.webkit)===null||e===void 0?void 0:e.messageHandlers)===null||t===void 0)&&t.bridge?"ios":"web"},oe=r=>{const e=r.CapacitorCustomPlatform||null,t=r.Capacitor||{},o=t.Plugins=t.Plugins||{},n=()=>e!==null?e.name:ne(r),s=()=>n()!=="web",i=c=>{const l=u.get(c);return!!(l!=null&&l.platforms.has(n())||a(c))},a=c=>{var l;return(l=t.PluginHeaders)===null||l===void 0?void 0:l.find(C=>C.name===c)},d=c=>r.console.error(c),u=new Map,P=(c,l={})=>{const C=u.get(c);if(C)return console.warn(`Capacitor plugin "${c}" already registered. Cannot register plugins twice.`),C.proxy;const v=n(),y=a(c);let p;const K=async()=>(!p&&v in l?p=typeof l[v]=="function"?p=await l[v]():p=l[v]:e!==null&&!p&&"web"in l&&(p=typeof l.web=="function"?p=await l.web():p=l.web),p),V=(g,f)=>{var m,w;if(y){const b=y==null?void 0:y.methods.find(h=>f===h.name);if(b)return b.rtype==="promise"?h=>t.nativePromise(c,f.toString(),h):(h,E)=>t.nativeCallback(c,f.toString(),h,E);if(g)return(m=g[f])===null||m===void 0?void 0:m.bind(g)}else{if(g)return(w=g[f])===null||w===void 0?void 0:w.bind(g);throw new _(`"${c}" plugin is not implemented on ${v}`,L.Unimplemented)}},A=g=>{let f;const m=(...w)=>{const b=K().then(h=>{const E=V(h,g);if(E){const k=E(...w);return f=k==null?void 0:k.remove,k}else throw new _(`"${c}.${g}()" is not implemented on ${v}`,L.Unimplemented)});return g==="addListener"&&(b.remove=async()=>f()),b};return m.toString=()=>`${g.toString()}() { [capacitor code] }`,Object.defineProperty(m,"name",{value:g,writable:!1,configurable:!1}),m},$=A("addListener"),T=A("removeListener"),J=(g,f)=>{const m=$({eventName:g},f),w=async()=>{const h=await m;T({eventName:g,callbackId:h},f)},b=new Promise(h=>m.then(()=>h({remove:w})));return b.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await w()},b},O=new Proxy({},{get(g,f){switch(f){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return y?J:$;case"removeListener":return T;default:return A(f)}}});return o[c]=O,u.set(c,{name:c,proxy:O,platforms:new Set([...Object.keys(l),...y?[v]:[]])}),O};return t.convertFileSrc||(t.convertFileSrc=c=>c),t.getPlatform=n,t.handleError=d,t.isNativePlatform=s,t.isPluginAvailable=i,t.registerPlugin=P,t.Exception=_,t.DEBUG=!!t.DEBUG,t.isLoggingEnabled=!!t.isLoggingEnabled,t},ie=r=>r.Capacitor=oe(r),j=ie(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),S=j.registerPlugin;class G{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let o=!1;this.listeners[e]||(this.listeners[e]=[],o=!0),this.listeners[e].push(t);const s=this.windowListeners[e];s&&!s.registered&&this.addWindowListener(s),o&&this.sendRetainedArgumentsForEvent(e);const i=async()=>this.removeListener(e,t);return Promise.resolve({remove:i})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,o){const n=this.listeners[e];if(!n){if(o){let s=this.retainedEventArguments[e];s||(s=[]),s.push(t),this.retainedEventArguments[e]=s}return}n.forEach(s=>s(t))}hasListeners(e){var t;return!!(!((t=this.listeners[e])===null||t===void 0)&&t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:o=>{this.notifyListeners(t,o)}}}unimplemented(e="not implemented"){return new j.Exception(e,L.Unimplemented)}unavailable(e="not available"){return new j.Exception(e,L.Unavailable)}async removeListener(e,t){const o=this.listeners[e];if(!o)return;const n=o.indexOf(t);this.listeners[e].splice(n,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(o=>{this.notifyListeners(e,o)}))}}const H=r=>encodeURIComponent(r).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),R=r=>r.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class ae extends G{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach(o=>{if(o.length<=0)return;let[n,s]=o.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");n=R(n).trim(),s=R(s).trim(),t[n]=s}),t}async setCookie(e){try{const t=H(e.key),o=H(e.value),n=`; expires=${(e.expires||"").replace("expires=","")}`,s=(e.path||"/").replace("path=",""),i=e.url!=null&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${o||""}${n}; path=${s}; ${i};`}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}S("CapacitorCookies",{web:()=>new ae});const ce=async r=>new Promise((e,t)=>{const o=new FileReader;o.onload=()=>{const n=o.result;e(n.indexOf(",")>=0?n.split(",")[1]:n)},o.onerror=n=>t(n),o.readAsDataURL(r)}),le=(r={})=>{const e=Object.keys(r);return Object.keys(r).map(n=>n.toLocaleLowerCase()).reduce((n,s,i)=>(n[s]=r[e[i]],n),{})},de=(r,e=!0)=>r?Object.entries(r).reduce((o,n)=>{const[s,i]=n;let a,d;return Array.isArray(i)?(d="",i.forEach(u=>{a=e?encodeURIComponent(u):u,d+=`${s}=${a}&`}),d.slice(0,-1)):(a=e?encodeURIComponent(i):i,d=`${s}=${a}`),`${o}&${d}`},"").substr(1):null,ue=(r,e={})=>{const t=Object.assign({method:r.method||"GET",headers:r.headers},e),n=le(r.headers)["content-type"]||"";if(typeof r.data=="string")t.body=r.data;else if(n.includes("application/x-www-form-urlencoded")){const s=new URLSearchParams;for(const[i,a]of Object.entries(r.data||{}))s.set(i,a);t.body=s.toString()}else if(n.includes("multipart/form-data")||r.data instanceof FormData){const s=new FormData;if(r.data instanceof FormData)r.data.forEach((a,d)=>{s.append(d,a)});else for(const a of Object.keys(r.data))s.append(a,r.data[a]);t.body=s;const i=new Headers(t.headers);i.delete("content-type"),t.headers=i}else(n.includes("application/json")||typeof r.data=="object")&&(t.body=JSON.stringify(r.data));return t};class ge extends G{async request(e){const t=ue(e,e.webFetchExtra),o=de(e.params,e.shouldEncodeUrlParams),n=o?`${e.url}?${o}`:e.url,s=await fetch(n,t),i=s.headers.get("content-type")||"";let{responseType:a="text"}=s.ok?e:{};i.includes("application/json")&&(a="json");let d,u;switch(a){case"arraybuffer":case"blob":u=await s.blob(),d=await ce(u);break;case"json":d=await s.json();break;case"document":case"text":default:d=await s.text()}const P={};return s.headers.forEach((c,l)=>{P[l]=c}),{data:d,headers:P,status:s.status,url:s.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}S("CapacitorHttp",{web:()=>new ge});const q=S("SocialLogin",{web:()=>ee(()=>import("./vm71AKha.js"),[],import.meta.url).then(r=>new r.SocialLoginWeb)}),fe="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20height='24'%20viewBox='0%200%2024%2024'%20width='24'%3e%3cpath%20d='M22.56%2012.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26%201.37-1.04%202.53-2.21%203.31v2.77h3.57c2.08-1.92%203.28-4.74%203.28-8.09z'%20fill='%234285F4'/%3e%3cpath%20d='M12%2023c2.97%200%205.46-.98%207.28-2.66l-3.57-2.77c-.98.66-2.23%201.06-3.71%201.06-2.86%200-5.29-1.93-6.16-4.53H2.18v2.84C3.99%2020.53%207.7%2023%2012%2023z'%20fill='%2334A853'/%3e%3cpath%20d='M5.84%2014.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43%208.55%201%2010.22%201%2012s.43%203.45%201.18%204.93l2.85-2.22.81-.62z'%20fill='%23FBBC05'/%3e%3cpath%20d='M12%205.38c1.62%200%203.06.56%204.21%201.64l3.15-3.15C17.45%202.09%2014.97%201%2012%201%207.7%201%203.99%203.47%202.18%207.07l3.66%202.84c.87-2.6%203.3-4.53%206.16-4.53z'%20fill='%23EA4335'/%3e%3cpath%20d='M1%201h22v22H1z'%20fill='none'/%3e%3c/svg%3e";q.initialize({google:{webClientId:te}});var he=F('<div class="svelte-8jg49c"><span>Sign in with Google</span> <img alt="Google icon" class="svelte-8jg49c"/></div>');function me(r,e){M(e,!0);const t=async()=>{const n=await q.login({provider:"google",options:{}});return await e.supabase.auth.signInWithIdToken({provider:"google",token:n.result.idToken})},o=async()=>{if((await t()).error!==null)return;const s=await e.supabase.auth.getUser();if(s.error!==null)return;const i=await e.supabase.auth.getSession();i.error!==null||i.data.session===null||e.onLogin(s.data.user,i.data.session.access_token)};re(r,{onClick:()=>o(),children:(n,s)=>{var i=he(),a=D(x(i),2);U(i),Q(()=>N(a,"src",fe)),z(n,i)},$$slots:{default:!0}}),W()}var pe=F('<main class="svelte-1h2kea4"><h1>SLAY - demo</h1> <button-rack><!></button-rack></main>',2);function _e(r,e){M(e,!0);const t=Z(()=>e.data.supabase);Y(()=>{B.user!==null&&I("/")});const o=async(a,d)=>{const u=await se({},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${d}`}});B.user={supabaseUser:a,supabaseAccessToken:d,streamioAuth:{id:u.userId,name:u.userName,token:u.streamioUserToken},id:u.userId,name:u.userName,canSell:u.canSell},I("/now-live")};var n=pe(),s=D(x(n),2),i=x(s);me(i,{get supabase(){return X(t)},onLogin:o}),U(s),U(n),z(r,n),W()}export{G as W,_e as _};
