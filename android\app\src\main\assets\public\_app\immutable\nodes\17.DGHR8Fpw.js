import"../chunks/CWj6FrbW.js";import{o as F,a as G}from"../chunks/B8_8MPyK.js";import{p as W,c,r as d,s as k,n as J,t as Y,a as j,d as _,b as Q,e as w,g as e,u as X,f as V}from"../chunks/DvS_9Yw_.js";import{f as T,t as q,a as m,c as H}from"../chunks/Ckyppc5t.js";import{i as I}from"../chunks/DrBokXpg.js";import{s as g}from"../chunks/PoYD5o0_.js";import{T as $,S as tt}from"../chunks/Ce5yDgIc.js";import{P as et}from"../chunks/BeWv36fu.js";import{S as at,P as z}from"../chunks/Ddyo76Dh.js";import{B as Z}from"../chunks/DF_v5tP2.js";import{e as rt}from"../chunks/hasoi-S8.js";import{L as st}from"../chunks/BCBegGzd.js";import{a as it,l as nt}from"../chunks/CQqdknCc.js";import{s as R}from"../chunks/BGB6NhFz.js";import{L as lt}from"../chunks/CDLiAs4l.js";import{s as U}from"../chunks/CZYSyPBs.js";var ot=T("<watch-listing><listing-display><!></listing-display> <listing-top><h2> </h2> <!></listing-top> <listing-description> </listing-description></watch-listing>",2);function ut(p,t){W(t,!0);var i=ot();g(i,1,"svelte-1w9xg7a");var n=c(i);g(n,1,"svelte-1w9xg7a");var a=c(n);lt(a,{get title(){return t.listing.title}}),d(n);var o=k(n,2);g(o,1,"svelte-1w9xg7a");var u=c(o),L=c(u,!0);d(u);var f=k(u,2);Z(f,{onClick:()=>{},strong:!0,children:(A,s)=>{J();var v=q();Y(()=>R(v,`$${t.listing.price??""}`)),m(A,v)},$$slots:{default:!0}}),d(o);var h=k(o,2);g(h,1,"svelte-1w9xg7a");var N=c(h,!0);d(h),d(i),Y(()=>{R(L,t.listing.title),R(N,t.listing.description)}),m(p,i),j()}var ct=T("<watch-listings><!></watch-listings>",2),dt=T("<watch-menu><!> <!></watch-menu>",2);function mt(p,t){W(t,!0);const i={chat:"Chat",listings:"Listings"};let n=_(Q(i.chat)),a=_(null);(async()=>w(a,await it({streamId:t.streamId}),!0))();const o=s=>{if(e(a)===null||s.custom.type!==st.UpdateListing)return;const v=s.custom,C=e(a).listings.find(x=>x.id===v.data.listing.id);C!==void 0&&(C.active=!1)};F(()=>{t.call.on("custom",o)}),G(()=>{t.call.off("custom",o)});var u=dt(),L=c(u);{var f=s=>{at(s,{get userId(){return t.userId},get userName(){return t.userName},get call(){return t.call}})},h=(s,v)=>{{var C=x=>{var S=ct();g(S,1,"svelte-1onq0wg");var r=c(S);{var l=b=>{var M=H(),B=V(M);rt(B,17,()=>e(a).listings,P=>P.id,(P,y)=>{var O=H(),E=V(O);{var D=K=>{ut(K,{get listing(){return e(y)}})};I(E,K=>{e(y).active&&K(D)})}m(P,O)}),m(b,M)};I(r,b=>{e(a)!==null&&b(l)})}d(S),m(x,S)};I(s,x=>{e(n)===i.listings&&x(C)},v)}};I(L,s=>{e(n)===i.chat?s(f):s(h,!1)})}var N=k(L,2);const A=X(()=>Object.values(i));$(N,{get currentLabel(){return e(n)},get labels(){return e(A)},onClickTab:s=>w(n,s,!0)}),d(u),m(p,u),j()}var vt=T("<video-backdrop><!></video-backdrop> <video-main><!></video-main>",3),gt=T("<watch-container><!> <watch-overlays><watch-exit><!></watch-exit> <!></watch-overlays></watch-container>",2);function ft(p,t){W(t,!0);let i=X(()=>({id:t.userId,name:t.userName,image:`https://getstream.io/random_svg/?id=${t.userId}&name=${t.userName}`})),n=_(0),a=_(null),o=_(null),u=_(!0),L=_(Q([]));(async()=>{const r=tt.getOrCreateInstance({apiKey:et,token:t.userToken,user:e(i)});w(a,r.call("livestream",t.streamId),!0),await e(a).join(),await(async l=>{w(o,l.hostSessionId,!0)})(await nt({call_id:t.streamId})),e(a).state.participantCount$.subscribe(l=>{w(n,l||0,!0)}),e(a).state.participants$.subscribe(l=>{w(L,l,!0)}),e(a).state.endedAt$.subscribe(l=>{l!==void 0&&w(u,!1)})})(),G(()=>{var r;(r=e(a))==null||r.leave()});var f=gt();g(f,1,"svelte-1pdxdnd");var h=c(f);{var N=r=>{var l=H(),b=V(l);{var M=B=>{var P=vt(),y=V(P);g(y,1,"svelte-1pdxdnd");var O=c(y);z(O,{get call(){return e(a)},get sessionId(){return e(o)},isBackdrop:!0}),d(y);var E=k(y,2);g(E,1,"svelte-1pdxdnd");var D=c(E);z(D,{get call(){return e(a)},get sessionId(){return e(o)},hasShadow:!0}),d(E),m(B,P)};I(b,B=>{e(a)!==null&&e(o)!==null&&B(M)})}m(r,l)},A=r=>{var l=q("Call over! Thanks for watching!");m(r,l)};I(h,r=>{e(u)?r(N):r(A,!1)})}var s=k(h,2);g(s,1,"svelte-1pdxdnd");var v=c(s),C=c(v);Z(C,{onClick:()=>history.back(),children:(r,l)=>{J();var b=q("<");m(r,b)},$$slots:{default:!0}}),d(v);var x=k(v,2);{var S=r=>{mt(r,{get userId(){return t.userId},get userName(){return t.userName},get call(){return e(a)},get streamId(){return t.streamId}})};I(x,r=>{e(a)!==null&&r(S)})}d(s),d(f),m(p,f),j()}var ht=T('<main class="svelte-11nkcr9"><!></main>');function Et(p,t){W(t,!0);let i=_(null);F(()=>{w(i,new URLSearchParams(location.search).get("streamId")??null,!0)});var n=ht(),a=c(n);{var o=u=>{ft(u,{get streamId(){return e(i)},get userToken(){return U.user.streamioAuth.token},get userId(){return U.user.streamioAuth.id},get userName(){return U.user.streamioAuth.name}})};I(a,u=>{e(i)!==null&&U.user!==null&&u(o)})}d(n),m(p,n),j()}export{Et as component};
