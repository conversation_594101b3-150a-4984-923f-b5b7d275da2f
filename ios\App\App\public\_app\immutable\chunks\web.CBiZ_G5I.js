import{W as _}from"./2.D1wUj2ZB.js";class f extends _{constructor(){var e;if(super(),this.googleClientId=null,this.appleClientId=null,this.googleScriptLoaded=!1,this.googleLoginType="online",this.appleScriptLoaded=!1,this.appleScriptUrl="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js",this.GOOGLE_TOKEN_REQUEST_URL="https://www.googleapis.com/oauth2/v3/tokeninfo",this.facebookAppId=null,this.facebookScriptLoaded=!1,localStorage.getItem(f.OAUTH_STATE_KEY)){console.log("OAUTH_STATE_KEY found");const n=this.handleOAuthRedirect();n&&((e=window.opener)===null||e===void 0||e.postMessage(Object.assign({type:"oauth-response"},n.result),window.location.origin),window.close())}}handleOAuthRedirect(){const e=new URL(window.location.href).searchParams,n=e.get("code");if(n&&e.has("scope"))return{provider:"google",result:{provider:"google",result:{serverAuthCode:n}}};const o=window.location.hash.substring(1);if(console.log("handleOAuthRedirect",window.location.hash),!o)return;console.log("handleOAuthRedirect ok");const i=new URLSearchParams(o),t=i.get("access_token"),r=i.get("id_token");if(t&&r){localStorage.removeItem(f.OAUTH_STATE_KEY);const s=this.parseJwt(r);return{provider:"google",result:{accessToken:{token:t},idToken:r,profile:{email:s.email||null,familyName:s.family_name||null,givenName:s.given_name||null,id:s.sub||null,name:s.name||null,imageUrl:s.picture||null}}}}return null}async initialize(e){var n,o,i;!((n=e.google)===null||n===void 0)&&n.webClientId&&(this.googleClientId=e.google.webClientId,e.google.mode&&(this.googleLoginType=e.google.mode),await this.loadGoogleScript()),!((o=e.apple)===null||o===void 0)&&o.clientId&&(this.appleClientId=e.apple.clientId,await this.loadAppleScript()),!((i=e.facebook)===null||i===void 0)&&i.appId&&(this.facebookAppId=e.facebook.appId,await this.loadFacebookScript(),FB.init({appId:this.facebookAppId,version:"v17.0",xfbml:!0,cookie:!0}))}async login(e){switch(e.provider){case"google":return this.loginWithGoogle(e.options);case"apple":return this.loginWithApple(e.options);case"facebook":return this.loginWithFacebook(e.options);default:throw new Error(`Login for ${e.provider} is not implemented on web`)}}async logout(e){switch(e.provider){case"google":if(this.googleLoginType==="offline")return Promise.reject("Offline login doesn't store tokens. logout is not available");console.log("Google logout: Id token should be revoked on the client side if stored");const n=this.getGoogleState();if(!n)return;await this.rawLogoutGoogle(n.accessToken);break;case"apple":console.log("Apple logout: Session should be managed on the client side");break;case"facebook":return new Promise(o=>{FB.logout(()=>o())});default:throw new Error(`Logout for ${e.provider} is not implemented`)}}async accessTokenIsValid(e){const n=`${this.GOOGLE_TOKEN_REQUEST_URL}?access_token=${encodeURIComponent(e)}`;try{const o=await fetch(n);if(!o.ok)return console.log(`Invalid response from ${this.GOOGLE_TOKEN_REQUEST_URL}. Response not successful. Status code: ${o.status}. Assuming that the token is not valid`),!1;const i=await o.text();if(!i)throw console.error(`Invalid response from ${this.GOOGLE_TOKEN_REQUEST_URL}. Response body is null`),new Error(`Invalid response from ${this.GOOGLE_TOKEN_REQUEST_URL}. Response body is null`);let t;try{t=JSON.parse(i)}catch(l){throw console.error(`Invalid response from ${this.GOOGLE_TOKEN_REQUEST_URL}. Response body is not valid JSON. Error: ${l}`),new Error(`Invalid response from ${this.GOOGLE_TOKEN_REQUEST_URL}. Response body is not valid JSON. Error: ${l}`)}const r=t.expires_in;if(r==null)throw console.error(`Invalid response from ${this.GOOGLE_TOKEN_REQUEST_URL}. Response JSON does not include 'expires_in'.`),new Error(`Invalid response from ${this.GOOGLE_TOKEN_REQUEST_URL}. Response JSON does not include 'expires_in'.`);let s;try{if(s=parseInt(r,10),isNaN(s))throw new Error("'expires_in' is not a valid integer")}catch(l){throw console.error(`Invalid response from ${this.GOOGLE_TOKEN_REQUEST_URL}. 'expires_in': ${r} is not a valid integer. Error: ${l}`),new Error(`Invalid response from ${this.GOOGLE_TOKEN_REQUEST_URL}. 'expires_in': ${r} is not a valid integer. Error: ${l}`)}return s>5}catch(o){throw console.error(o),o}}idTokenValid(e){try{const n=this.parseJwt(e),o=Math.ceil(Date.now()/1e3)+5;return n.exp&&o<n.exp}catch{return!1}}async rawLogoutGoogle(e,n=null){if(n===null&&(n=await this.accessTokenIsValid(e)),n===!0)return new Promise((o,i)=>{try{google.accounts.oauth2.revoke(e,async()=>{this.clearStateGoogle(),o()})}catch(t){i(t)}});this.clearStateGoogle()}async isLoggedIn(e){switch(e.provider){case"google":if(this.googleLoginType==="offline")return Promise.reject("Offline login doesn't store tokens. isLoggedIn is not available");const n=this.getGoogleState();if(!n)return{isLoggedIn:!1};try{const o=await this.accessTokenIsValid(n.accessToken),i=this.idTokenValid(n.idToken);if(o&&i)return{isLoggedIn:!0};try{await this.rawLogoutGoogle(n.accessToken,!1)}catch(t){console.error("Access token is not valid, but cannot logout",t)}return{isLoggedIn:!1}}catch(o){return Promise.reject(o)}case"apple":return console.log("Apple login status should be managed on the client side"),{isLoggedIn:!1};case"facebook":return new Promise(o=>{FB.getLoginStatus(i=>{o({isLoggedIn:i.status==="connected"})})});default:throw new Error(`isLoggedIn for ${e.provider} is not implemented`)}}async getAuthorizationCode(e){switch(e.provider){case"google":if(this.googleLoginType==="offline")return Promise.reject("Offline login doesn't store tokens. getAuthorizationCode is not available");const n=this.getGoogleState();if(!n)throw new Error("No Google authorization code available");try{const o=await this.accessTokenIsValid(n.accessToken),i=this.idTokenValid(n.idToken);if(o&&i)return{accessToken:n.accessToken,jwt:n.idToken};try{await this.rawLogoutGoogle(n.accessToken,!1)}catch(t){console.error("Access token is not valid, but cannot logout",t)}throw new Error("No Google authorization code available")}catch(o){return Promise.reject(o)}case"apple":throw console.log("Apple authorization code should be stored during login"),new Error("Apple authorization code not available");case"facebook":return new Promise((o,i)=>{FB.getLoginStatus(t=>{var r;t.status==="connected"?o({jwt:((r=t.authResponse)===null||r===void 0?void 0:r.accessToken)||""}):i(new Error("No Facebook authorization code available"))})});default:throw new Error(`getAuthorizationCode for ${e.provider} is not implemented`)}}async refresh(e){switch(e.provider){case"google":return Promise.reject("Not implemented");case"apple":console.log("Apple refresh not available on web");break;case"facebook":await this.loginWithFacebook(e.options);break;default:throw new Error(`Refresh for ${e.provider} is not implemented`)}}loginWithGoogle(e){if(!this.googleClientId)throw new Error("Google Client ID not set. Call initialize() first.");let n=e.scopes||[];return n.length>0?(n.includes("https://www.googleapis.com/auth/userinfo.email")||n.push("https://www.googleapis.com/auth/userinfo.email"),n.includes("https://www.googleapis.com/auth/userinfo.profile")||n.push("https://www.googleapis.com/auth/userinfo.profile"),n.includes("openid")||n.push("openid")):n=["https://www.googleapis.com/auth/userinfo.email","https://www.googleapis.com/auth/userinfo.profile","openid"],n.length>3||this.googleLoginType==="offline"?this.fallbackToTraditionalOAuth(n):new Promise((o,i)=>{google.accounts.id.initialize({client_id:this.googleClientId,callback:t=>{if(console.log("google.accounts.id.initialize callback",t),t.error)i(t.error);else{const r=this.parseJwt(t.credential),s={accessToken:null,responseType:"online",idToken:t.credential,profile:{email:r.email||null,familyName:r.family_name||null,givenName:r.given_name||null,id:r.sub||null,name:r.name||null,imageUrl:r.picture||null}};o({provider:"google",result:s})}},auto_select:!0}),google.accounts.id.prompt(t=>{t.isNotDisplayed()||t.isSkippedMoment()?(console.log("OneTap is not displayed or skipped"),this.fallbackToTraditionalOAuth(n).then(r=>o({provider:"google",result:r.result})).catch(i)):console.log("OneTap is displayed")})})}parseJwt(e){const o=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),i=decodeURIComponent(atob(o).split("").map(t=>"%"+("00"+t.charCodeAt(0).toString(16)).slice(-2)).join(""));return JSON.parse(i)}async loadGoogleScript(){if(!this.googleScriptLoaded)return new Promise((e,n)=>{const o=document.createElement("script");o.src="https://accounts.google.com/gsi/client",o.async=!0,o.onload=()=>{this.googleScriptLoaded=!0,e()},o.onerror=n,document.body.appendChild(o)})}async loginWithApple(e){if(!this.appleClientId)throw new Error("Apple Client ID not set. Call initialize() first.");if(!this.appleScriptLoaded)throw new Error("Apple Sign-In script not loaded.");return new Promise((n,o)=>{var i;AppleID.auth.init({clientId:this.appleClientId,scope:((i=e.scopes)===null||i===void 0?void 0:i.join(" "))||"name email",redirectURI:e.redirectUrl||window.location.href,state:e.state,nonce:e.nonce,usePopup:!0}),AppleID.auth.signIn().then(t=>{var r,s,l,d,u,a,c;const p={profile:{user:!((s=(r=t.user)===null||r===void 0?void 0:r.name)===null||s===void 0)&&s.firstName?`${t.user.name.firstName} ${t.user.name.lastName}`:"",email:((l=t.user)===null||l===void 0?void 0:l.email)||null,givenName:((u=(d=t.user)===null||d===void 0?void 0:d.name)===null||u===void 0?void 0:u.firstName)||null,familyName:((c=(a=t.user)===null||a===void 0?void 0:a.name)===null||c===void 0?void 0:c.lastName)||null},accessToken:{token:t.authorization.code},idToken:t.authorization.id_token||null};n({provider:"apple",result:p})}).catch(t=>{o(t)})})}async loadAppleScript(){if(!this.appleScriptLoaded)return new Promise((e,n)=>{const o=document.createElement("script");o.src=this.appleScriptUrl,o.async=!0,o.onload=()=>{this.appleScriptLoaded=!0,e()},o.onerror=n,document.body.appendChild(o)})}persistStateGoogle(e,n){try{window.localStorage.setItem("capgo_social_login_google_state",JSON.stringify({accessToken:e,idToken:n}))}catch(o){console.error("Cannot persist state google",o)}}clearStateGoogle(){try{window.localStorage.removeItem("capgo_social_login_google_state")}catch(e){console.error("Cannot clear state google",e)}}getGoogleState(){try{const e=window.localStorage.getItem("capgo_social_login_google_state");if(!e)return null;const{accessToken:n,idToken:o}=JSON.parse(e);return{accessToken:n,idToken:o}}catch(e){return console.error("Cannot get state google",e),null}}async loadFacebookScript(){if(!this.facebookScriptLoaded)return new Promise((e,n)=>{const o=document.createElement("script");o.src="https://connect.facebook.net/en_US/sdk.js",o.async=!0,o.defer=!0,o.onload=()=>{this.facebookScriptLoaded=!0,e()},o.onerror=n,document.body.appendChild(o)})}async loginWithFacebook(e){if(!this.facebookAppId)throw new Error("Facebook App ID not set. Call initialize() first.");return new Promise((n,o)=>{FB.login(i=>{i.status==="connected"?FB.api("/me",{fields:"id,name,email,picture"},t=>{var r,s;const l={accessToken:{token:i.authResponse.accessToken,userId:i.authResponse.userID},profile:{userID:t.id,name:t.name,email:t.email||null,imageURL:((s=(r=t.picture)===null||r===void 0?void 0:r.data)===null||s===void 0?void 0:s.url)||null,friendIDs:[],birthday:null,ageRange:null,gender:null,location:null,hometown:null,profileURL:null},idToken:null};n({provider:"facebook",result:l})}):o(new Error("Facebook login failed"))},{scope:e.permissions.join(",")})})}async fallbackToTraditionalOAuth(e){const n=[...new Set([...e,"openid"])],i=`https://accounts.google.com/o/oauth2/v2/auth?${new URLSearchParams({client_id:this.googleClientId,redirect_uri:window.location.href,response_type:this.googleLoginType==="offline"?"code":"token id_token",scope:n.join(" "),nonce:Math.random().toString(36).substring(2),include_granted_scopes:"true",state:"popup"}).toString()}`,t=500,r=600,s=window.screenX+(window.outerWidth-t)/2,l=window.screenY+(window.outerHeight-r)/2;localStorage.setItem(f.OAUTH_STATE_KEY,"true");const d=window.open(i,"Google Sign In",`width=${t},height=${r},left=${s},top=${l},popup=1`);return new Promise((u,a)=>{if(!d){a(new Error("Failed to open popup"));return}const c=p=>{var w;if(p.origin===window.location.origin)if(((w=p.data)===null||w===void 0?void 0:w.type)==="oauth-response")if(window.removeEventListener("message",c),this.googleLoginType==="online"){const{accessToken:h,idToken:m}=p.data;if(h&&m){const g=this.parseJwt(m);this.persistStateGoogle(h.token,m),u({provider:"google",result:{accessToken:{token:h.token},idToken:m,profile:{email:g.email||null,familyName:g.family_name||null,givenName:g.given_name||null,id:g.sub||null,name:g.name||null,imageUrl:g.picture||null},responseType:"online"}})}}else{const{serverAuthCode:h}=p.data.result;u({provider:"google",result:{responseType:"offline",serverAuthCode:h}})}else a(new Error("Login failed"))};window.addEventListener("message",c),setTimeout(()=>{window.removeEventListener("message",c),d.close(),a(new Error("OAuth timeout"))},3e5)})}}f.OAUTH_STATE_KEY="social_login_oauth_pending";export{f as SocialLoginWeb};
