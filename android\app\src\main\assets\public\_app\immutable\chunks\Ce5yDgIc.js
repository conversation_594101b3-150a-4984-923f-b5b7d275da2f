import"./CWj6FrbW.js";import{p as Go,f as Wo,c as <PERSON>,r as <PERSON><PERSON>,s as <PERSON>,t as zo,g as ks,a as Yo}from"./DvS_9Yw_.js";import{s as Xo}from"./BGB6NhFz.js";import{f as Qs,a as rs}from"./Ckyppc5t.js";import{i as Zo}from"./DrBokXpg.js";import{e as Qo,i as ec}from"./hasoi-S8.js";import{d as tc}from"./CTtUbKlS.js";import{s as as}from"./PoYD5o0_.js";import{a as nc}from"./Bx2EM-6T.js";function sc(t,e){for(var s=0;s<e.length;s++){const n=e[s];if(typeof n!="string"&&!Array.isArray(n)){for(const i in n)if(i!=="default"&&!(i in t)){const r=Object.getOwnPropertyDescriptor(n,i);r&&Object.defineProperty(t,i,r.get?r:{enumerable:!0,get:()=>n[i]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}var ic=(t,e,s)=>e.onClickTab(ks(s)),rc=Qs("<tabber-separator></tabber-separator>",2),ac=Qs("<button> </button> <!>",1),oc=Qs("<tabber-tabs></tabber-tabs>",2);function Jg(t,e){Go(e,!0);var s=oc();as(s,1,"svelte-122ghjx"),Qo(s,21,()=>e.labels,ec,(n,i,r)=>{var a=ac(),o=Wo(a);o.__click=[ic,e,i];let l;var u=Jo(o,!0);Ui(o);var d=Ko(o,2);{var m=c=>{var h=rc();as(h,1,"svelte-122ghjx"),rs(c,h)};Zo(d,c=>{r<e.labels.length&&c(m)})}zo(c=>{l=as(o,1,"svelte-122ghjx",null,l,c),Xo(u,ks(i))},[()=>({selected:ks(i)===e.currentLabel})]),rs(n,a)}),Ui(s),rs(t,s),Yo()}tc(["click"]);let $r=!0,Br=!0;function Et(t,e,s){const n=t.match(e);return n&&n.length>=s&&parseFloat(n[s],10)}function Xe(t,e,s){if(!t.RTCPeerConnection)return;const n=t.RTCPeerConnection.prototype,i=n.addEventListener;n.addEventListener=function(a,o){if(a!==e)return i.apply(this,arguments);const l=u=>{const d=s(u);d&&(o.handleEvent?o.handleEvent(d):o(d))};return this._eventMap=this._eventMap||{},this._eventMap[e]||(this._eventMap[e]=new Map),this._eventMap[e].set(o,l),i.apply(this,[a,l])};const r=n.removeEventListener;n.removeEventListener=function(a,o){if(a!==e||!this._eventMap||!this._eventMap[e])return r.apply(this,arguments);if(!this._eventMap[e].has(o))return r.apply(this,arguments);const l=this._eventMap[e].get(o);return this._eventMap[e].delete(o),this._eventMap[e].size===0&&delete this._eventMap[e],Object.keys(this._eventMap).length===0&&delete this._eventMap,r.apply(this,[a,l])},Object.defineProperty(n,"on"+e,{get(){return this["_on"+e]},set(a){this["_on"+e]&&(this.removeEventListener(e,this["_on"+e]),delete this["_on"+e]),a&&this.addEventListener(e,this["_on"+e]=a)},enumerable:!0,configurable:!0})}function cc(t){return typeof t!="boolean"?new Error("Argument type: "+typeof t+". Please use a boolean."):($r=t,t?"adapter.js logging disabled":"adapter.js logging enabled")}function uc(t){return typeof t!="boolean"?new Error("Argument type: "+typeof t+". Please use a boolean."):(Br=!t,"adapter.js deprecation warnings "+(t?"disabled":"enabled"))}function Vr(){if(typeof window=="object"){if($r)return;typeof console<"u"&&typeof console.log=="function"&&console.log.apply(console,arguments)}}function ei(t,e){Br&&console.warn(t+" is deprecated, please use "+e+" instead.")}function lc(t){const e={browser:null,version:null};if(typeof t>"u"||!t.navigator||!t.navigator.userAgent)return e.browser="Not a browser.",e;const{navigator:s}=t;if(s.mozGetUserMedia)e.browser="firefox",e.version=parseInt(Et(s.userAgent,/Firefox\/(\d+)\./,1));else if(s.webkitGetUserMedia||t.isSecureContext===!1&&t.webkitRTCPeerConnection)e.browser="chrome",e.version=parseInt(Et(s.userAgent,/Chrom(e|ium)\/(\d+)\./,2));else if(t.RTCPeerConnection&&s.userAgent.match(/AppleWebKit\/(\d+)\./))e.browser="safari",e.version=parseInt(Et(s.userAgent,/AppleWebKit\/(\d+)\./,1)),e.supportsUnifiedPlan=t.RTCRtpTransceiver&&"currentDirection"in t.RTCRtpTransceiver.prototype,e._safariVersion=Et(s.userAgent,/Version\/(\d+(\.?\d+))/,1);else return e.browser="Not a supported browser.",e;return e}function Fi(t){return Object.prototype.toString.call(t)==="[object Object]"}function Hr(t){return Fi(t)?Object.keys(t).reduce(function(e,s){const n=Fi(t[s]),i=n?Hr(t[s]):t[s],r=n&&!Object.keys(i).length;return i===void 0||r?e:Object.assign(e,{[s]:i})},{}):t}function ws(t,e,s){!e||s.has(e.id)||(s.set(e.id,e),Object.keys(e).forEach(n=>{n.endsWith("Id")?ws(t,t.get(e[n]),s):n.endsWith("Ids")&&e[n].forEach(i=>{ws(t,t.get(i),s)})}))}function Mi(t,e,s){const n=s?"outbound-rtp":"inbound-rtp",i=new Map;if(e===null)return i;const r=[];return t.forEach(a=>{a.type==="track"&&a.trackIdentifier===e.id&&r.push(a)}),r.forEach(a=>{t.forEach(o=>{o.type===n&&o.trackId===a.id&&ws(t,o,i)})}),i}const ji=Vr;function qr(t,e){const s=t&&t.navigator;if(!s.mediaDevices)return;const n=function(o){if(typeof o!="object"||o.mandatory||o.optional)return o;const l={};return Object.keys(o).forEach(u=>{if(u==="require"||u==="advanced"||u==="mediaSource")return;const d=typeof o[u]=="object"?o[u]:{ideal:o[u]};d.exact!==void 0&&typeof d.exact=="number"&&(d.min=d.max=d.exact);const m=function(c,h){return c?c+h.charAt(0).toUpperCase()+h.slice(1):h==="deviceId"?"sourceId":h};if(d.ideal!==void 0){l.optional=l.optional||[];let c={};typeof d.ideal=="number"?(c[m("min",u)]=d.ideal,l.optional.push(c),c={},c[m("max",u)]=d.ideal,l.optional.push(c)):(c[m("",u)]=d.ideal,l.optional.push(c))}d.exact!==void 0&&typeof d.exact!="number"?(l.mandatory=l.mandatory||{},l.mandatory[m("",u)]=d.exact):["min","max"].forEach(c=>{d[c]!==void 0&&(l.mandatory=l.mandatory||{},l.mandatory[m(c,u)]=d[c])})}),o.advanced&&(l.optional=(l.optional||[]).concat(o.advanced)),l},i=function(o,l){if(e.version>=61)return l(o);if(o=JSON.parse(JSON.stringify(o)),o&&typeof o.audio=="object"){const u=function(d,m,c){m in d&&!(c in d)&&(d[c]=d[m],delete d[m])};o=JSON.parse(JSON.stringify(o)),u(o.audio,"autoGainControl","googAutoGainControl"),u(o.audio,"noiseSuppression","googNoiseSuppression"),o.audio=n(o.audio)}if(o&&typeof o.video=="object"){let u=o.video.facingMode;u=u&&(typeof u=="object"?u:{ideal:u});const d=e.version<66;if(u&&(u.exact==="user"||u.exact==="environment"||u.ideal==="user"||u.ideal==="environment")&&!(s.mediaDevices.getSupportedConstraints&&s.mediaDevices.getSupportedConstraints().facingMode&&!d)){delete o.video.facingMode;let m;if(u.exact==="environment"||u.ideal==="environment"?m=["back","rear"]:(u.exact==="user"||u.ideal==="user")&&(m=["front"]),m)return s.mediaDevices.enumerateDevices().then(c=>{c=c.filter(f=>f.kind==="videoinput");let h=c.find(f=>m.some(p=>f.label.toLowerCase().includes(p)));return!h&&c.length&&m.includes("back")&&(h=c[c.length-1]),h&&(o.video.deviceId=u.exact?{exact:h.deviceId}:{ideal:h.deviceId}),o.video=n(o.video),ji("chrome: "+JSON.stringify(o)),l(o)})}o.video=n(o.video)}return ji("chrome: "+JSON.stringify(o)),l(o)},r=function(o){return e.version>=64?o:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[o.name]||o.name,message:o.message,constraint:o.constraint||o.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}},a=function(o,l,u){i(o,d=>{s.webkitGetUserMedia(d,l,m=>{u&&u(r(m))})})};if(s.getUserMedia=a.bind(s),s.mediaDevices.getUserMedia){const o=s.mediaDevices.getUserMedia.bind(s.mediaDevices);s.mediaDevices.getUserMedia=function(l){return i(l,u=>o(u).then(d=>{if(u.audio&&!d.getAudioTracks().length||u.video&&!d.getVideoTracks().length)throw d.getTracks().forEach(m=>{m.stop()}),new DOMException("","NotFoundError");return d},d=>Promise.reject(r(d))))}}}function dc(t,e){if(!(t.navigator.mediaDevices&&"getDisplayMedia"in t.navigator.mediaDevices)&&t.navigator.mediaDevices){if(typeof e!="function"){console.error("shimGetDisplayMedia: getSourceId argument is not a function");return}t.navigator.mediaDevices.getDisplayMedia=function(n){return e(n).then(i=>{const r=n.video&&n.video.width,a=n.video&&n.video.height,o=n.video&&n.video.frameRate;return n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:i,maxFrameRate:o||3}},r&&(n.video.mandatory.maxWidth=r),a&&(n.video.mandatory.maxHeight=a),t.navigator.mediaDevices.getUserMedia(n)})}}}function Gr(t){t.MediaStream=t.MediaStream||t.webkitMediaStream}function Wr(t){if(typeof t=="object"&&t.RTCPeerConnection&&!("ontrack"in t.RTCPeerConnection.prototype)){Object.defineProperty(t.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(s){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=s)},enumerable:!0,configurable:!0});const e=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=n=>{n.stream.addEventListener("addtrack",i=>{let r;t.RTCPeerConnection.prototype.getReceivers?r=this.getReceivers().find(o=>o.track&&o.track.id===i.track.id):r={track:i.track};const a=new Event("track");a.track=i.track,a.receiver=r,a.transceiver={receiver:r},a.streams=[n.stream],this.dispatchEvent(a)}),n.stream.getTracks().forEach(i=>{let r;t.RTCPeerConnection.prototype.getReceivers?r=this.getReceivers().find(o=>o.track&&o.track.id===i.id):r={track:i};const a=new Event("track");a.track=i,a.receiver=r,a.transceiver={receiver:r},a.streams=[n.stream],this.dispatchEvent(a)})},this.addEventListener("addstream",this._ontrackpoly)),e.apply(this,arguments)}}else Xe(t,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e))}function Jr(t){if(typeof t=="object"&&t.RTCPeerConnection&&!("getSenders"in t.RTCPeerConnection.prototype)&&"createDTMFSender"in t.RTCPeerConnection.prototype){const e=function(i,r){return{track:r,get dtmf(){return this._dtmf===void 0&&(r.kind==="audio"?this._dtmf=i.createDTMFSender(r):this._dtmf=null),this._dtmf},_pc:i}};if(!t.RTCPeerConnection.prototype.getSenders){t.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const i=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addTrack=function(o,l){let u=i.apply(this,arguments);return u||(u=e(this,o),this._senders.push(u)),u};const r=t.RTCPeerConnection.prototype.removeTrack;t.RTCPeerConnection.prototype.removeTrack=function(o){r.apply(this,arguments);const l=this._senders.indexOf(o);l!==-1&&this._senders.splice(l,1)}}const s=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(r){this._senders=this._senders||[],s.apply(this,[r]),r.getTracks().forEach(a=>{this._senders.push(e(this,a))})};const n=t.RTCPeerConnection.prototype.removeStream;t.RTCPeerConnection.prototype.removeStream=function(r){this._senders=this._senders||[],n.apply(this,[r]),r.getTracks().forEach(a=>{const o=this._senders.find(l=>l.track===a);o&&this._senders.splice(this._senders.indexOf(o),1)})}}else if(typeof t=="object"&&t.RTCPeerConnection&&"getSenders"in t.RTCPeerConnection.prototype&&"createDTMFSender"in t.RTCPeerConnection.prototype&&t.RTCRtpSender&&!("dtmf"in t.RTCRtpSender.prototype)){const e=t.RTCPeerConnection.prototype.getSenders;t.RTCPeerConnection.prototype.getSenders=function(){const n=e.apply(this,[]);return n.forEach(i=>i._pc=this),n},Object.defineProperty(t.RTCRtpSender.prototype,"dtmf",{get(){return this._dtmf===void 0&&(this.track.kind==="audio"?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function Kr(t){if(!t.RTCPeerConnection)return;const e=t.RTCPeerConnection.prototype.getStats;t.RTCPeerConnection.prototype.getStats=function(){const[n,i,r]=arguments;if(arguments.length>0&&typeof n=="function")return e.apply(this,arguments);if(e.length===0&&(arguments.length===0||typeof n!="function"))return e.apply(this,[]);const a=function(l){const u={};return l.result().forEach(m=>{const c={id:m.id,timestamp:m.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[m.type]||m.type};m.names().forEach(h=>{c[h]=m.stat(h)}),u[c.id]=c}),u},o=function(l){return new Map(Object.keys(l).map(u=>[u,l[u]]))};if(arguments.length>=2){const l=function(u){i(o(a(u)))};return e.apply(this,[l,n])}return new Promise((l,u)=>{e.apply(this,[function(d){l(o(a(d)))},u])}).then(i,r)}}function zr(t){if(!(typeof t=="object"&&t.RTCPeerConnection&&t.RTCRtpSender&&t.RTCRtpReceiver))return;if(!("getStats"in t.RTCRtpSender.prototype)){const s=t.RTCPeerConnection.prototype.getSenders;s&&(t.RTCPeerConnection.prototype.getSenders=function(){const r=s.apply(this,[]);return r.forEach(a=>a._pc=this),r});const n=t.RTCPeerConnection.prototype.addTrack;n&&(t.RTCPeerConnection.prototype.addTrack=function(){const r=n.apply(this,arguments);return r._pc=this,r}),t.RTCRtpSender.prototype.getStats=function(){const r=this;return this._pc.getStats().then(a=>Mi(a,r.track,!0))}}if(!("getStats"in t.RTCRtpReceiver.prototype)){const s=t.RTCPeerConnection.prototype.getReceivers;s&&(t.RTCPeerConnection.prototype.getReceivers=function(){const i=s.apply(this,[]);return i.forEach(r=>r._pc=this),i}),Xe(t,"track",n=>(n.receiver._pc=n.srcElement,n)),t.RTCRtpReceiver.prototype.getStats=function(){const i=this;return this._pc.getStats().then(r=>Mi(r,i.track,!1))}}if(!("getStats"in t.RTCRtpSender.prototype&&"getStats"in t.RTCRtpReceiver.prototype))return;const e=t.RTCPeerConnection.prototype.getStats;t.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof t.MediaStreamTrack){const n=arguments[0];let i,r,a;return this.getSenders().forEach(o=>{o.track===n&&(i?a=!0:i=o)}),this.getReceivers().forEach(o=>(o.track===n&&(r?a=!0:r=o),o.track===n)),a||i&&r?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):i?i.getStats():r?r.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return e.apply(this,arguments)}}function Yr(t){t.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(a=>this._shimmedLocalStreams[a][0])};const e=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addTrack=function(a,o){if(!o)return e.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const l=e.apply(this,arguments);return this._shimmedLocalStreams[o.id]?this._shimmedLocalStreams[o.id].indexOf(l)===-1&&this._shimmedLocalStreams[o.id].push(l):this._shimmedLocalStreams[o.id]=[o,l],l};const s=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(a){this._shimmedLocalStreams=this._shimmedLocalStreams||{},a.getTracks().forEach(u=>{if(this.getSenders().find(m=>m.track===u))throw new DOMException("Track already exists.","InvalidAccessError")});const o=this.getSenders();s.apply(this,arguments);const l=this.getSenders().filter(u=>o.indexOf(u)===-1);this._shimmedLocalStreams[a.id]=[a].concat(l)};const n=t.RTCPeerConnection.prototype.removeStream;t.RTCPeerConnection.prototype.removeStream=function(a){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[a.id],n.apply(this,arguments)};const i=t.RTCPeerConnection.prototype.removeTrack;t.RTCPeerConnection.prototype.removeTrack=function(a){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},a&&Object.keys(this._shimmedLocalStreams).forEach(o=>{const l=this._shimmedLocalStreams[o].indexOf(a);l!==-1&&this._shimmedLocalStreams[o].splice(l,1),this._shimmedLocalStreams[o].length===1&&delete this._shimmedLocalStreams[o]}),i.apply(this,arguments)}}function Xr(t,e){if(!t.RTCPeerConnection)return;if(t.RTCPeerConnection.prototype.addTrack&&e.version>=65)return Yr(t);const s=t.RTCPeerConnection.prototype.getLocalStreams;t.RTCPeerConnection.prototype.getLocalStreams=function(){const d=s.apply(this);return this._reverseStreams=this._reverseStreams||{},d.map(m=>this._reverseStreams[m.id])};const n=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(d){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},d.getTracks().forEach(m=>{if(this.getSenders().find(h=>h.track===m))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[d.id]){const m=new t.MediaStream(d.getTracks());this._streams[d.id]=m,this._reverseStreams[m.id]=d,d=m}n.apply(this,[d])};const i=t.RTCPeerConnection.prototype.removeStream;t.RTCPeerConnection.prototype.removeStream=function(d){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[d.id]||d]),delete this._reverseStreams[this._streams[d.id]?this._streams[d.id].id:d.id],delete this._streams[d.id]},t.RTCPeerConnection.prototype.addTrack=function(d,m){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const c=[].slice.call(arguments,1);if(c.length!==1||!c[0].getTracks().find(p=>p===d))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(p=>p.track===d))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const f=this._streams[m.id];if(f)f.addTrack(d),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const p=new t.MediaStream([d]);this._streams[m.id]=p,this._reverseStreams[p.id]=m,this.addStream(p)}return this.getSenders().find(p=>p.track===d)};function r(u,d){let m=d.sdp;return Object.keys(u._reverseStreams||[]).forEach(c=>{const h=u._reverseStreams[c],f=u._streams[h.id];m=m.replace(new RegExp(f.id,"g"),h.id)}),new RTCSessionDescription({type:d.type,sdp:m})}function a(u,d){let m=d.sdp;return Object.keys(u._reverseStreams||[]).forEach(c=>{const h=u._reverseStreams[c],f=u._streams[h.id];m=m.replace(new RegExp(h.id,"g"),f.id)}),new RTCSessionDescription({type:d.type,sdp:m})}["createOffer","createAnswer"].forEach(function(u){const d=t.RTCPeerConnection.prototype[u],m={[u](){const c=arguments;return arguments.length&&typeof arguments[0]=="function"?d.apply(this,[f=>{const p=r(this,f);c[0].apply(null,[p])},f=>{c[1]&&c[1].apply(null,f)},arguments[2]]):d.apply(this,arguments).then(f=>r(this,f))}};t.RTCPeerConnection.prototype[u]=m[u]});const o=t.RTCPeerConnection.prototype.setLocalDescription;t.RTCPeerConnection.prototype.setLocalDescription=function(){return!arguments.length||!arguments[0].type?o.apply(this,arguments):(arguments[0]=a(this,arguments[0]),o.apply(this,arguments))};const l=Object.getOwnPropertyDescriptor(t.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(t.RTCPeerConnection.prototype,"localDescription",{get(){const u=l.get.apply(this);return u.type===""?u:r(this,u)}}),t.RTCPeerConnection.prototype.removeTrack=function(d){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!d._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(d._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};let c;Object.keys(this._streams).forEach(h=>{this._streams[h].getTracks().find(p=>d.track===p)&&(c=this._streams[h])}),c&&(c.getTracks().length===1?this.removeStream(this._reverseStreams[c.id]):c.removeTrack(d.track),this.dispatchEvent(new Event("negotiationneeded")))}}function Es(t,e){!t.RTCPeerConnection&&t.webkitRTCPeerConnection&&(t.RTCPeerConnection=t.webkitRTCPeerConnection),t.RTCPeerConnection&&e.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(s){const n=t.RTCPeerConnection.prototype[s],i={[s](){return arguments[0]=new(s==="addIceCandidate"?t.RTCIceCandidate:t.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};t.RTCPeerConnection.prototype[s]=i[s]})}function Zr(t,e){Xe(t,"negotiationneeded",s=>{const n=s.target;if(!((e.version<72||n.getConfiguration&&n.getConfiguration().sdpSemantics==="plan-b")&&n.signalingState!=="stable"))return s})}const $i=Object.freeze(Object.defineProperty({__proto__:null,fixNegotiationNeeded:Zr,shimAddTrackRemoveTrack:Xr,shimAddTrackRemoveTrackWithNative:Yr,shimGetDisplayMedia:dc,shimGetSendersWithDtmf:Jr,shimGetStats:Kr,shimGetUserMedia:qr,shimMediaStream:Gr,shimOnTrack:Wr,shimPeerConnection:Es,shimSenderReceiverGetStats:zr},Symbol.toStringTag,{value:"Module"}));function Qr(t,e){const s=t&&t.navigator,n=t&&t.MediaStreamTrack;if(s.getUserMedia=function(i,r,a){ei("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),s.mediaDevices.getUserMedia(i).then(r,a)},!(e.version>55&&"autoGainControl"in s.mediaDevices.getSupportedConstraints())){const i=function(a,o,l){o in a&&!(l in a)&&(a[l]=a[o],delete a[o])},r=s.mediaDevices.getUserMedia.bind(s.mediaDevices);if(s.mediaDevices.getUserMedia=function(a){return typeof a=="object"&&typeof a.audio=="object"&&(a=JSON.parse(JSON.stringify(a)),i(a.audio,"autoGainControl","mozAutoGainControl"),i(a.audio,"noiseSuppression","mozNoiseSuppression")),r(a)},n&&n.prototype.getSettings){const a=n.prototype.getSettings;n.prototype.getSettings=function(){const o=a.apply(this,arguments);return i(o,"mozAutoGainControl","autoGainControl"),i(o,"mozNoiseSuppression","noiseSuppression"),o}}if(n&&n.prototype.applyConstraints){const a=n.prototype.applyConstraints;n.prototype.applyConstraints=function(o){return this.kind==="audio"&&typeof o=="object"&&(o=JSON.parse(JSON.stringify(o)),i(o,"autoGainControl","mozAutoGainControl"),i(o,"noiseSuppression","mozNoiseSuppression")),a.apply(this,[o])}}}}function hc(t,e){t.navigator.mediaDevices&&"getDisplayMedia"in t.navigator.mediaDevices||t.navigator.mediaDevices&&(t.navigator.mediaDevices.getDisplayMedia=function(n){if(!(n&&n.video)){const i=new DOMException("getDisplayMedia without video constraints is undefined");return i.name="NotFoundError",i.code=8,Promise.reject(i)}return n.video===!0?n.video={mediaSource:e}:n.video.mediaSource=e,t.navigator.mediaDevices.getUserMedia(n)})}function ea(t){typeof t=="object"&&t.RTCTrackEvent&&"receiver"in t.RTCTrackEvent.prototype&&!("transceiver"in t.RTCTrackEvent.prototype)&&Object.defineProperty(t.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Rs(t,e){if(typeof t!="object"||!(t.RTCPeerConnection||t.mozRTCPeerConnection))return;!t.RTCPeerConnection&&t.mozRTCPeerConnection&&(t.RTCPeerConnection=t.mozRTCPeerConnection),e.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(i){const r=t.RTCPeerConnection.prototype[i],a={[i](){return arguments[0]=new(i==="addIceCandidate"?t.RTCIceCandidate:t.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};t.RTCPeerConnection.prototype[i]=a[i]});const s={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},n=t.RTCPeerConnection.prototype.getStats;t.RTCPeerConnection.prototype.getStats=function(){const[r,a,o]=arguments;return n.apply(this,[r||null]).then(l=>{if(e.version<53&&!a)try{l.forEach(u=>{u.type=s[u.type]||u.type})}catch(u){if(u.name!=="TypeError")throw u;l.forEach((d,m)=>{l.set(m,Object.assign({},d,{type:s[d.type]||d.type}))})}return l}).then(a,o)}}function ta(t){if(!(typeof t=="object"&&t.RTCPeerConnection&&t.RTCRtpSender)||t.RTCRtpSender&&"getStats"in t.RTCRtpSender.prototype)return;const e=t.RTCPeerConnection.prototype.getSenders;e&&(t.RTCPeerConnection.prototype.getSenders=function(){const i=e.apply(this,[]);return i.forEach(r=>r._pc=this),i});const s=t.RTCPeerConnection.prototype.addTrack;s&&(t.RTCPeerConnection.prototype.addTrack=function(){const i=s.apply(this,arguments);return i._pc=this,i}),t.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function na(t){if(!(typeof t=="object"&&t.RTCPeerConnection&&t.RTCRtpSender)||t.RTCRtpSender&&"getStats"in t.RTCRtpReceiver.prototype)return;const e=t.RTCPeerConnection.prototype.getReceivers;e&&(t.RTCPeerConnection.prototype.getReceivers=function(){const n=e.apply(this,[]);return n.forEach(i=>i._pc=this),n}),Xe(t,"track",s=>(s.receiver._pc=s.srcElement,s)),t.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function sa(t){!t.RTCPeerConnection||"removeStream"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.removeStream=function(s){ei("removeStream","removeTrack"),this.getSenders().forEach(n=>{n.track&&s.getTracks().includes(n.track)&&this.removeTrack(n)})})}function ia(t){t.DataChannel&&!t.RTCDataChannel&&(t.RTCDataChannel=t.DataChannel)}function ra(t){if(!(typeof t=="object"&&t.RTCPeerConnection))return;const e=t.RTCPeerConnection.prototype.addTransceiver;e&&(t.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let n=arguments[1]&&arguments[1].sendEncodings;n===void 0&&(n=[]),n=[...n];const i=n.length>0;i&&n.forEach(a=>{if("rid"in a&&!/^[a-z0-9]{0,16}$/i.test(a.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in a&&!(parseFloat(a.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in a&&!(parseFloat(a.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});const r=e.apply(this,arguments);if(i){const{sender:a}=r,o=a.getParameters();(!("encodings"in o)||o.encodings.length===1&&Object.keys(o.encodings[0]).length===0)&&(o.encodings=n,a.sendEncodings=n,this.setParametersPromises.push(a.setParameters(o).then(()=>{delete a.sendEncodings}).catch(()=>{delete a.sendEncodings})))}return r})}function aa(t){if(!(typeof t=="object"&&t.RTCRtpSender))return;const e=t.RTCRtpSender.prototype.getParameters;e&&(t.RTCRtpSender.prototype.getParameters=function(){const n=e.apply(this,arguments);return"encodings"in n||(n.encodings=[].concat(this.sendEncodings||[{}])),n})}function oa(t){if(!(typeof t=="object"&&t.RTCPeerConnection))return;const e=t.RTCPeerConnection.prototype.createOffer;t.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>e.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):e.apply(this,arguments)}}function ca(t){if(!(typeof t=="object"&&t.RTCPeerConnection))return;const e=t.RTCPeerConnection.prototype.createAnswer;t.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>e.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):e.apply(this,arguments)}}const Bi=Object.freeze(Object.defineProperty({__proto__:null,shimAddTransceiver:ra,shimCreateAnswer:ca,shimCreateOffer:oa,shimGetDisplayMedia:hc,shimGetParameters:aa,shimGetUserMedia:Qr,shimOnTrack:ea,shimPeerConnection:Rs,shimRTCDataChannel:ia,shimReceiverGetStats:na,shimRemoveStream:sa,shimSenderGetStats:ta},Symbol.toStringTag,{value:"Module"}));function ua(t){if(!(typeof t!="object"||!t.RTCPeerConnection)){if("getLocalStreams"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in t.RTCPeerConnection.prototype)){const e=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addStream=function(n){this._localStreams||(this._localStreams=[]),this._localStreams.includes(n)||this._localStreams.push(n),n.getAudioTracks().forEach(i=>e.call(this,i,n)),n.getVideoTracks().forEach(i=>e.call(this,i,n))},t.RTCPeerConnection.prototype.addTrack=function(n,...i){return i&&i.forEach(r=>{this._localStreams?this._localStreams.includes(r)||this._localStreams.push(r):this._localStreams=[r]}),e.apply(this,arguments)}}"removeStream"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.removeStream=function(s){this._localStreams||(this._localStreams=[]);const n=this._localStreams.indexOf(s);if(n===-1)return;this._localStreams.splice(n,1);const i=s.getTracks();this.getSenders().forEach(r=>{i.includes(r.track)&&this.removeTrack(r)})})}}function la(t){if(!(typeof t!="object"||!t.RTCPeerConnection)&&("getRemoteStreams"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in t.RTCPeerConnection.prototype))){Object.defineProperty(t.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(s){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=s),this.addEventListener("track",this._onaddstreampoly=n=>{n.streams.forEach(i=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(i))return;this._remoteStreams.push(i);const r=new Event("addstream");r.stream=i,this.dispatchEvent(r)})})}});const e=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){const n=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(i){i.streams.forEach(r=>{if(n._remoteStreams||(n._remoteStreams=[]),n._remoteStreams.indexOf(r)>=0)return;n._remoteStreams.push(r);const a=new Event("addstream");a.stream=r,n.dispatchEvent(a)})}),e.apply(n,arguments)}}}function da(t){if(typeof t!="object"||!t.RTCPeerConnection)return;const e=t.RTCPeerConnection.prototype,s=e.createOffer,n=e.createAnswer,i=e.setLocalDescription,r=e.setRemoteDescription,a=e.addIceCandidate;e.createOffer=function(u,d){const m=arguments.length>=2?arguments[2]:arguments[0],c=s.apply(this,[m]);return d?(c.then(u,d),Promise.resolve()):c},e.createAnswer=function(u,d){const m=arguments.length>=2?arguments[2]:arguments[0],c=n.apply(this,[m]);return d?(c.then(u,d),Promise.resolve()):c};let o=function(l,u,d){const m=i.apply(this,[l]);return d?(m.then(u,d),Promise.resolve()):m};e.setLocalDescription=o,o=function(l,u,d){const m=r.apply(this,[l]);return d?(m.then(u,d),Promise.resolve()):m},e.setRemoteDescription=o,o=function(l,u,d){const m=a.apply(this,[l]);return d?(m.then(u,d),Promise.resolve()):m},e.addIceCandidate=o}function ha(t){const e=t&&t.navigator;if(e.mediaDevices&&e.mediaDevices.getUserMedia){const s=e.mediaDevices,n=s.getUserMedia.bind(s);e.mediaDevices.getUserMedia=i=>n(fa(i))}!e.getUserMedia&&e.mediaDevices&&e.mediaDevices.getUserMedia&&(e.getUserMedia=(function(n,i,r){e.mediaDevices.getUserMedia(n).then(i,r)}).bind(e))}function fa(t){return t&&t.video!==void 0?Object.assign({},t,{video:Hr(t.video)}):t}function pa(t){if(!t.RTCPeerConnection)return;const e=t.RTCPeerConnection;t.RTCPeerConnection=function(n,i){if(n&&n.iceServers){const r=[];for(let a=0;a<n.iceServers.length;a++){let o=n.iceServers[a];o.urls===void 0&&o.url?(ei("RTCIceServer.url","RTCIceServer.urls"),o=JSON.parse(JSON.stringify(o)),o.urls=o.url,delete o.url,r.push(o)):r.push(n.iceServers[a])}n.iceServers=r}return new e(n,i)},t.RTCPeerConnection.prototype=e.prototype,"generateCertificate"in e&&Object.defineProperty(t.RTCPeerConnection,"generateCertificate",{get(){return e.generateCertificate}})}function ma(t){typeof t=="object"&&t.RTCTrackEvent&&"receiver"in t.RTCTrackEvent.prototype&&!("transceiver"in t.RTCTrackEvent.prototype)&&Object.defineProperty(t.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function ga(t){const e=t.RTCPeerConnection.prototype.createOffer;t.RTCPeerConnection.prototype.createOffer=function(n){if(n){typeof n.offerToReceiveAudio<"u"&&(n.offerToReceiveAudio=!!n.offerToReceiveAudio);const i=this.getTransceivers().find(a=>a.receiver.track.kind==="audio");n.offerToReceiveAudio===!1&&i?i.direction==="sendrecv"?i.setDirection?i.setDirection("sendonly"):i.direction="sendonly":i.direction==="recvonly"&&(i.setDirection?i.setDirection("inactive"):i.direction="inactive"):n.offerToReceiveAudio===!0&&!i&&this.addTransceiver("audio",{direction:"recvonly"}),typeof n.offerToReceiveVideo<"u"&&(n.offerToReceiveVideo=!!n.offerToReceiveVideo);const r=this.getTransceivers().find(a=>a.receiver.track.kind==="video");n.offerToReceiveVideo===!1&&r?r.direction==="sendrecv"?r.setDirection?r.setDirection("sendonly"):r.direction="sendonly":r.direction==="recvonly"&&(r.setDirection?r.setDirection("inactive"):r.direction="inactive"):n.offerToReceiveVideo===!0&&!r&&this.addTransceiver("video",{direction:"recvonly"})}return e.apply(this,arguments)}}function ba(t){typeof t!="object"||t.AudioContext||(t.AudioContext=t.webkitAudioContext)}const Vi=Object.freeze(Object.defineProperty({__proto__:null,shimAudioContext:ba,shimCallbacksAPI:da,shimConstraints:fa,shimCreateOfferLegacy:ga,shimGetUserMedia:ha,shimLocalStreamsAPI:ua,shimRTCIceServerUrls:pa,shimRemoteStreamsAPI:la,shimTrackEventTransceiver:ma},Symbol.toStringTag,{value:"Module"}));var os={exports:{}},Hi;function fc(){return Hi||(Hi=1,function(t){const e={};e.generateIdentifier=function(){return Math.random().toString(36).substring(2,12)},e.localCName=e.generateIdentifier(),e.splitLines=function(s){return s.trim().split(`
`).map(n=>n.trim())},e.splitSections=function(s){return s.split(`
m=`).map((i,r)=>(r>0?"m="+i:i).trim()+`\r
`)},e.getDescription=function(s){const n=e.splitSections(s);return n&&n[0]},e.getMediaSections=function(s){const n=e.splitSections(s);return n.shift(),n},e.matchPrefix=function(s,n){return e.splitLines(s).filter(i=>i.indexOf(n)===0)},e.parseCandidate=function(s){let n;s.indexOf("a=candidate:")===0?n=s.substring(12).split(" "):n=s.substring(10).split(" ");const i={foundation:n[0],component:{1:"rtp",2:"rtcp"}[n[1]]||n[1],protocol:n[2].toLowerCase(),priority:parseInt(n[3],10),ip:n[4],address:n[4],port:parseInt(n[5],10),type:n[7]};for(let r=8;r<n.length;r+=2)switch(n[r]){case"raddr":i.relatedAddress=n[r+1];break;case"rport":i.relatedPort=parseInt(n[r+1],10);break;case"tcptype":i.tcpType=n[r+1];break;case"ufrag":i.ufrag=n[r+1],i.usernameFragment=n[r+1];break;default:i[n[r]]===void 0&&(i[n[r]]=n[r+1]);break}return i},e.writeCandidate=function(s){const n=[];n.push(s.foundation);const i=s.component;i==="rtp"?n.push(1):i==="rtcp"?n.push(2):n.push(i),n.push(s.protocol.toUpperCase()),n.push(s.priority),n.push(s.address||s.ip),n.push(s.port);const r=s.type;return n.push("typ"),n.push(r),r!=="host"&&s.relatedAddress&&s.relatedPort&&(n.push("raddr"),n.push(s.relatedAddress),n.push("rport"),n.push(s.relatedPort)),s.tcpType&&s.protocol.toLowerCase()==="tcp"&&(n.push("tcptype"),n.push(s.tcpType)),(s.usernameFragment||s.ufrag)&&(n.push("ufrag"),n.push(s.usernameFragment||s.ufrag)),"candidate:"+n.join(" ")},e.parseIceOptions=function(s){return s.substring(14).split(" ")},e.parseRtpMap=function(s){let n=s.substring(9).split(" ");const i={payloadType:parseInt(n.shift(),10)};return n=n[0].split("/"),i.name=n[0],i.clockRate=parseInt(n[1],10),i.channels=n.length===3?parseInt(n[2],10):1,i.numChannels=i.channels,i},e.writeRtpMap=function(s){let n=s.payloadType;s.preferredPayloadType!==void 0&&(n=s.preferredPayloadType);const i=s.channels||s.numChannels||1;return"a=rtpmap:"+n+" "+s.name+"/"+s.clockRate+(i!==1?"/"+i:"")+`\r
`},e.parseExtmap=function(s){const n=s.substring(9).split(" ");return{id:parseInt(n[0],10),direction:n[0].indexOf("/")>0?n[0].split("/")[1]:"sendrecv",uri:n[1],attributes:n.slice(2).join(" ")}},e.writeExtmap=function(s){return"a=extmap:"+(s.id||s.preferredId)+(s.direction&&s.direction!=="sendrecv"?"/"+s.direction:"")+" "+s.uri+(s.attributes?" "+s.attributes:"")+`\r
`},e.parseFmtp=function(s){const n={};let i;const r=s.substring(s.indexOf(" ")+1).split(";");for(let a=0;a<r.length;a++)i=r[a].trim().split("="),n[i[0].trim()]=i[1];return n},e.writeFmtp=function(s){let n="",i=s.payloadType;if(s.preferredPayloadType!==void 0&&(i=s.preferredPayloadType),s.parameters&&Object.keys(s.parameters).length){const r=[];Object.keys(s.parameters).forEach(a=>{s.parameters[a]!==void 0?r.push(a+"="+s.parameters[a]):r.push(a)}),n+="a=fmtp:"+i+" "+r.join(";")+`\r
`}return n},e.parseRtcpFb=function(s){const n=s.substring(s.indexOf(" ")+1).split(" ");return{type:n.shift(),parameter:n.join(" ")}},e.writeRtcpFb=function(s){let n="",i=s.payloadType;return s.preferredPayloadType!==void 0&&(i=s.preferredPayloadType),s.rtcpFeedback&&s.rtcpFeedback.length&&s.rtcpFeedback.forEach(r=>{n+="a=rtcp-fb:"+i+" "+r.type+(r.parameter&&r.parameter.length?" "+r.parameter:"")+`\r
`}),n},e.parseSsrcMedia=function(s){const n=s.indexOf(" "),i={ssrc:parseInt(s.substring(7,n),10)},r=s.indexOf(":",n);return r>-1?(i.attribute=s.substring(n+1,r),i.value=s.substring(r+1)):i.attribute=s.substring(n+1),i},e.parseSsrcGroup=function(s){const n=s.substring(13).split(" ");return{semantics:n.shift(),ssrcs:n.map(i=>parseInt(i,10))}},e.getMid=function(s){const n=e.matchPrefix(s,"a=mid:")[0];if(n)return n.substring(6)},e.parseFingerprint=function(s){const n=s.substring(14).split(" ");return{algorithm:n[0].toLowerCase(),value:n[1].toUpperCase()}},e.getDtlsParameters=function(s,n){return{role:"auto",fingerprints:e.matchPrefix(s+n,"a=fingerprint:").map(e.parseFingerprint)}},e.writeDtlsParameters=function(s,n){let i="a=setup:"+n+`\r
`;return s.fingerprints.forEach(r=>{i+="a=fingerprint:"+r.algorithm+" "+r.value+`\r
`}),i},e.parseCryptoLine=function(s){const n=s.substring(9).split(" ");return{tag:parseInt(n[0],10),cryptoSuite:n[1],keyParams:n[2],sessionParams:n.slice(3)}},e.writeCryptoLine=function(s){return"a=crypto:"+s.tag+" "+s.cryptoSuite+" "+(typeof s.keyParams=="object"?e.writeCryptoKeyParams(s.keyParams):s.keyParams)+(s.sessionParams?" "+s.sessionParams.join(" "):"")+`\r
`},e.parseCryptoKeyParams=function(s){if(s.indexOf("inline:")!==0)return null;const n=s.substring(7).split("|");return{keyMethod:"inline",keySalt:n[0],lifeTime:n[1],mkiValue:n[2]?n[2].split(":")[0]:void 0,mkiLength:n[2]?n[2].split(":")[1]:void 0}},e.writeCryptoKeyParams=function(s){return s.keyMethod+":"+s.keySalt+(s.lifeTime?"|"+s.lifeTime:"")+(s.mkiValue&&s.mkiLength?"|"+s.mkiValue+":"+s.mkiLength:"")},e.getCryptoParameters=function(s,n){return e.matchPrefix(s+n,"a=crypto:").map(e.parseCryptoLine)},e.getIceParameters=function(s,n){const i=e.matchPrefix(s+n,"a=ice-ufrag:")[0],r=e.matchPrefix(s+n,"a=ice-pwd:")[0];return i&&r?{usernameFragment:i.substring(12),password:r.substring(10)}:null},e.writeIceParameters=function(s){let n="a=ice-ufrag:"+s.usernameFragment+`\r
a=ice-pwd:`+s.password+`\r
`;return s.iceLite&&(n+=`a=ice-lite\r
`),n},e.parseRtpParameters=function(s){const n={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=e.splitLines(s)[0].split(" ");n.profile=r[2];for(let o=3;o<r.length;o++){const l=r[o],u=e.matchPrefix(s,"a=rtpmap:"+l+" ")[0];if(u){const d=e.parseRtpMap(u),m=e.matchPrefix(s,"a=fmtp:"+l+" ");switch(d.parameters=m.length?e.parseFmtp(m[0]):{},d.rtcpFeedback=e.matchPrefix(s,"a=rtcp-fb:"+l+" ").map(e.parseRtcpFb),n.codecs.push(d),d.name.toUpperCase()){case"RED":case"ULPFEC":n.fecMechanisms.push(d.name.toUpperCase());break}}}e.matchPrefix(s,"a=extmap:").forEach(o=>{n.headerExtensions.push(e.parseExtmap(o))});const a=e.matchPrefix(s,"a=rtcp-fb:* ").map(e.parseRtcpFb);return n.codecs.forEach(o=>{a.forEach(l=>{o.rtcpFeedback.find(d=>d.type===l.type&&d.parameter===l.parameter)||o.rtcpFeedback.push(l)})}),n},e.writeRtpDescription=function(s,n){let i="";i+="m="+s+" ",i+=n.codecs.length>0?"9":"0",i+=" "+(n.profile||"UDP/TLS/RTP/SAVPF")+" ",i+=n.codecs.map(a=>a.preferredPayloadType!==void 0?a.preferredPayloadType:a.payloadType).join(" ")+`\r
`,i+=`c=IN IP4 0.0.0.0\r
`,i+=`a=rtcp:9 IN IP4 0.0.0.0\r
`,n.codecs.forEach(a=>{i+=e.writeRtpMap(a),i+=e.writeFmtp(a),i+=e.writeRtcpFb(a)});let r=0;return n.codecs.forEach(a=>{a.maxptime>r&&(r=a.maxptime)}),r>0&&(i+="a=maxptime:"+r+`\r
`),n.headerExtensions&&n.headerExtensions.forEach(a=>{i+=e.writeExtmap(a)}),i},e.parseRtpEncodingParameters=function(s){const n=[],i=e.parseRtpParameters(s),r=i.fecMechanisms.indexOf("RED")!==-1,a=i.fecMechanisms.indexOf("ULPFEC")!==-1,o=e.matchPrefix(s,"a=ssrc:").map(c=>e.parseSsrcMedia(c)).filter(c=>c.attribute==="cname"),l=o.length>0&&o[0].ssrc;let u;const d=e.matchPrefix(s,"a=ssrc-group:FID").map(c=>c.substring(17).split(" ").map(f=>parseInt(f,10)));d.length>0&&d[0].length>1&&d[0][0]===l&&(u=d[0][1]),i.codecs.forEach(c=>{if(c.name.toUpperCase()==="RTX"&&c.parameters.apt){let h={ssrc:l,codecPayloadType:parseInt(c.parameters.apt,10)};l&&u&&(h.rtx={ssrc:u}),n.push(h),r&&(h=JSON.parse(JSON.stringify(h)),h.fec={ssrc:l,mechanism:a?"red+ulpfec":"red"},n.push(h))}}),n.length===0&&l&&n.push({ssrc:l});let m=e.matchPrefix(s,"b=");return m.length&&(m[0].indexOf("b=TIAS:")===0?m=parseInt(m[0].substring(7),10):m[0].indexOf("b=AS:")===0?m=parseInt(m[0].substring(5),10)*1e3*.95-50*40*8:m=void 0,n.forEach(c=>{c.maxBitrate=m})),n},e.parseRtcpParameters=function(s){const n={},i=e.matchPrefix(s,"a=ssrc:").map(o=>e.parseSsrcMedia(o)).filter(o=>o.attribute==="cname")[0];i&&(n.cname=i.value,n.ssrc=i.ssrc);const r=e.matchPrefix(s,"a=rtcp-rsize");n.reducedSize=r.length>0,n.compound=r.length===0;const a=e.matchPrefix(s,"a=rtcp-mux");return n.mux=a.length>0,n},e.writeRtcpParameters=function(s){let n="";return s.reducedSize&&(n+=`a=rtcp-rsize\r
`),s.mux&&(n+=`a=rtcp-mux\r
`),s.ssrc!==void 0&&s.cname&&(n+="a=ssrc:"+s.ssrc+" cname:"+s.cname+`\r
`),n},e.parseMsid=function(s){let n;const i=e.matchPrefix(s,"a=msid:");if(i.length===1)return n=i[0].substring(7).split(" "),{stream:n[0],track:n[1]};const r=e.matchPrefix(s,"a=ssrc:").map(a=>e.parseSsrcMedia(a)).filter(a=>a.attribute==="msid");if(r.length>0)return n=r[0].value.split(" "),{stream:n[0],track:n[1]}},e.parseSctpDescription=function(s){const n=e.parseMLine(s),i=e.matchPrefix(s,"a=max-message-size:");let r;i.length>0&&(r=parseInt(i[0].substring(19),10)),isNaN(r)&&(r=65536);const a=e.matchPrefix(s,"a=sctp-port:");if(a.length>0)return{port:parseInt(a[0].substring(12),10),protocol:n.fmt,maxMessageSize:r};const o=e.matchPrefix(s,"a=sctpmap:");if(o.length>0){const l=o[0].substring(10).split(" ");return{port:parseInt(l[0],10),protocol:l[1],maxMessageSize:r}}},e.writeSctpDescription=function(s,n){let i=[];return s.protocol!=="DTLS/SCTP"?i=["m="+s.kind+" 9 "+s.protocol+" "+n.protocol+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctp-port:"+n.port+`\r
`]:i=["m="+s.kind+" 9 "+s.protocol+" "+n.port+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctpmap:"+n.port+" "+n.protocol+` 65535\r
`],n.maxMessageSize!==void 0&&i.push("a=max-message-size:"+n.maxMessageSize+`\r
`),i.join("")},e.generateSessionId=function(){return Math.random().toString().substr(2,22)},e.writeSessionBoilerplate=function(s,n,i){let r;const a=n!==void 0?n:2;return s?r=s:r=e.generateSessionId(),`v=0\r
o=`+(i||"thisisadapterortc")+" "+r+" "+a+` IN IP4 127.0.0.1\r
s=-\r
t=0 0\r
`},e.getDirection=function(s,n){const i=e.splitLines(s);for(let r=0;r<i.length;r++)switch(i[r]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return i[r].substring(2)}return n?e.getDirection(n):"sendrecv"},e.getKind=function(s){return e.splitLines(s)[0].split(" ")[0].substring(2)},e.isRejected=function(s){return s.split(" ",2)[1]==="0"},e.parseMLine=function(s){const i=e.splitLines(s)[0].substring(2).split(" ");return{kind:i[0],port:parseInt(i[1],10),protocol:i[2],fmt:i.slice(3).join(" ")}},e.parseOLine=function(s){const i=e.matchPrefix(s,"o=")[0].substring(2).split(" ");return{username:i[0],sessionId:i[1],sessionVersion:parseInt(i[2],10),netType:i[3],addressType:i[4],address:i[5]}},e.isValidSDP=function(s){if(typeof s!="string"||s.length===0)return!1;const n=e.splitLines(s);for(let i=0;i<n.length;i++)if(n[i].length<2||n[i].charAt(1)!=="=")return!1;return!0},t.exports=e}(os)),os.exports}var ya=fc();const rt=nc(ya),pc=sc({__proto__:null,default:rt},[ya]);function on(t){if(!t.RTCIceCandidate||t.RTCIceCandidate&&"foundation"in t.RTCIceCandidate.prototype)return;const e=t.RTCIceCandidate;t.RTCIceCandidate=function(n){if(typeof n=="object"&&n.candidate&&n.candidate.indexOf("a=")===0&&(n=JSON.parse(JSON.stringify(n)),n.candidate=n.candidate.substring(2)),n.candidate&&n.candidate.length){const i=new e(n),r=rt.parseCandidate(n.candidate);for(const a in r)a in i||Object.defineProperty(i,a,{value:r[a]});return i.toJSON=function(){return{candidate:i.candidate,sdpMid:i.sdpMid,sdpMLineIndex:i.sdpMLineIndex,usernameFragment:i.usernameFragment}},i}return new e(n)},t.RTCIceCandidate.prototype=e.prototype,Xe(t,"icecandidate",s=>(s.candidate&&Object.defineProperty(s,"candidate",{value:new t.RTCIceCandidate(s.candidate),writable:"false"}),s))}function _s(t){!t.RTCIceCandidate||t.RTCIceCandidate&&"relayProtocol"in t.RTCIceCandidate.prototype||Xe(t,"icecandidate",e=>{if(e.candidate){const s=rt.parseCandidate(e.candidate.candidate);s.type==="relay"&&(e.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[s.priority>>24])}return e})}function cn(t,e){if(!t.RTCPeerConnection)return;"sctp"in t.RTCPeerConnection.prototype||Object.defineProperty(t.RTCPeerConnection.prototype,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp}});const s=function(o){if(!o||!o.sdp)return!1;const l=rt.splitSections(o.sdp);return l.shift(),l.some(u=>{const d=rt.parseMLine(u);return d&&d.kind==="application"&&d.protocol.indexOf("SCTP")!==-1})},n=function(o){const l=o.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(l===null||l.length<2)return-1;const u=parseInt(l[1],10);return u!==u?-1:u},i=function(o){let l=65536;return e.browser==="firefox"&&(e.version<57?o===-1?l=16384:l=2147483637:e.version<60?l=e.version===57?65535:65536:l=2147483637),l},r=function(o,l){let u=65536;e.browser==="firefox"&&e.version===57&&(u=65535);const d=rt.matchPrefix(o.sdp,"a=max-message-size:");return d.length>0?u=parseInt(d[0].substring(19),10):e.browser==="firefox"&&l!==-1&&(u=2147483637),u},a=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,e.browser==="chrome"&&e.version>=76){const{sdpSemantics:l}=this.getConfiguration();l==="plan-b"&&Object.defineProperty(this,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp},enumerable:!0,configurable:!0})}if(s(arguments[0])){const l=n(arguments[0]),u=i(l),d=r(arguments[0],l);let m;u===0&&d===0?m=Number.POSITIVE_INFINITY:u===0||d===0?m=Math.max(u,d):m=Math.min(u,d);const c={};Object.defineProperty(c,"maxMessageSize",{get(){return m}}),this._sctp=c}return a.apply(this,arguments)}}function un(t){if(!(t.RTCPeerConnection&&"createDataChannel"in t.RTCPeerConnection.prototype))return;function e(n,i){const r=n.send;n.send=function(){const o=arguments[0],l=o.length||o.size||o.byteLength;if(n.readyState==="open"&&i.sctp&&l>i.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+i.sctp.maxMessageSize+" bytes)");return r.apply(n,arguments)}}const s=t.RTCPeerConnection.prototype.createDataChannel;t.RTCPeerConnection.prototype.createDataChannel=function(){const i=s.apply(this,arguments);return e(i,this),i},Xe(t,"datachannel",n=>(e(n.channel,n.target),n))}function Is(t){if(!t.RTCPeerConnection||"connectionState"in t.RTCPeerConnection.prototype)return;const e=t.RTCPeerConnection.prototype;Object.defineProperty(e,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(e,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(s){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),s&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=s)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(s=>{const n=e[s];e[s]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=i=>{const r=i.target;if(r._lastConnectionState!==r.connectionState){r._lastConnectionState=r.connectionState;const a=new Event("connectionstatechange",i);r.dispatchEvent(a)}return i},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),n.apply(this,arguments)}})}function Os(t,e){if(!t.RTCPeerConnection||e.browser==="chrome"&&e.version>=71||e.browser==="safari"&&e._safariVersion>=13.1)return;const s=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(i){if(i&&i.sdp&&i.sdp.indexOf(`
a=extmap-allow-mixed`)!==-1){const r=i.sdp.split(`
`).filter(a=>a.trim()!=="a=extmap-allow-mixed").join(`
`);t.RTCSessionDescription&&i instanceof t.RTCSessionDescription?arguments[0]=new t.RTCSessionDescription({type:i.type,sdp:r}):i.sdp=r}return s.apply(this,arguments)}}function ln(t,e){if(!(t.RTCPeerConnection&&t.RTCPeerConnection.prototype))return;const s=t.RTCPeerConnection.prototype.addIceCandidate;!s||s.length===0||(t.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?(e.browser==="chrome"&&e.version<78||e.browser==="firefox"&&e.version<68||e.browser==="safari")&&arguments[0]&&arguments[0].candidate===""?Promise.resolve():s.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function dn(t,e){if(!(t.RTCPeerConnection&&t.RTCPeerConnection.prototype))return;const s=t.RTCPeerConnection.prototype.setLocalDescription;!s||s.length===0||(t.RTCPeerConnection.prototype.setLocalDescription=function(){let i=arguments[0]||{};if(typeof i!="object"||i.type&&i.sdp)return s.apply(this,arguments);if(i={type:i.type,sdp:i.sdp},!i.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":i.type="offer";break;default:i.type="answer";break}return i.sdp||i.type!=="offer"&&i.type!=="answer"?s.apply(this,[i]):(i.type==="offer"?this.createOffer:this.createAnswer).apply(this).then(a=>s.apply(this,[a]))})}const mc=Object.freeze(Object.defineProperty({__proto__:null,removeExtmapAllowMixed:Os,shimAddIceCandidateNullOrEmpty:ln,shimConnectionState:Is,shimMaxMessageSize:cn,shimParameterlessSetLocalDescription:dn,shimRTCIceCandidate:on,shimRTCIceCandidateRelayProtocol:_s,shimSendThrowTypeError:un},Symbol.toStringTag,{value:"Module"}));function gc({window:t}={},e={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const s=Vr,n=lc(t),i={browserDetails:n,commonShim:mc,extractVersion:Et,disableLog:cc,disableWarnings:uc,sdp:pc};switch(n.browser){case"chrome":if(!$i||!Es||!e.shimChrome)return s("Chrome shim is not included in this adapter release."),i;if(n.version===null)return s("Chrome shim can not determine version, not shimming."),i;s("adapter.js shimming chrome."),i.browserShim=$i,ln(t,n),dn(t),qr(t,n),Gr(t),Es(t,n),Wr(t),Xr(t,n),Jr(t),Kr(t),zr(t),Zr(t,n),on(t),_s(t),Is(t),cn(t,n),un(t),Os(t,n);break;case"firefox":if(!Bi||!Rs||!e.shimFirefox)return s("Firefox shim is not included in this adapter release."),i;s("adapter.js shimming firefox."),i.browserShim=Bi,ln(t,n),dn(t),Qr(t,n),Rs(t,n),ea(t),sa(t),ta(t),na(t),ia(t),ra(t),aa(t),oa(t),ca(t),on(t),Is(t),cn(t,n),un(t);break;case"safari":if(!Vi||!e.shimSafari)return s("Safari shim is not included in this adapter release."),i;s("adapter.js shimming safari."),i.browserShim=Vi,ln(t,n),dn(t),pa(t),ga(t),da(t),ua(t),la(t),ma(t),ha(t),ba(t),on(t),_s(t),cn(t,n),un(t),Os(t,n);break;default:s("Unsupported browser!");break}return i}gc({window:typeof window>"u"?void 0:window});function ht(t){let e=typeof t;if(e=="object"){if(Array.isArray(t))return"array";if(t===null)return"null"}return e}function Sn(t){return t!==null&&typeof t=="object"&&!Array.isArray(t)}let Pe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),Fn=[];for(let t=0;t<Pe.length;t++)Fn[Pe[t].charCodeAt(0)]=t;Fn[45]=Pe.indexOf("+");Fn[95]=Pe.indexOf("/");function bc(t){let e=t.length*3/4;t[t.length-2]=="="?e-=2:t[t.length-1]=="="&&(e-=1);let s=new Uint8Array(e),n=0,i=0,r,a=0;for(let o=0;o<t.length;o++){if(r=Fn[t.charCodeAt(o)],r===void 0)switch(t[o]){case"=":i=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string.")}switch(i){case 0:a=r,i=1;break;case 1:s[n++]=a<<2|(r&48)>>4,a=r,i=2;break;case 2:s[n++]=(a&15)<<4|(r&60)>>2,a=r,i=3;break;case 3:s[n++]=(a&3)<<6|r,i=0;break}}if(i==1)throw Error("invalid base64 string.");return s.subarray(0,n)}function yc(t){let e="",s=0,n,i=0;for(let r=0;r<t.length;r++)switch(n=t[r],s){case 0:e+=Pe[n>>2],i=(n&3)<<4,s=1;break;case 1:e+=Pe[i|n>>4],i=(n&15)<<2,s=2;break;case 2:e+=Pe[i|n>>6],e+=Pe[n&63],s=0;break}return s&&(e+=Pe[i],e+="=",s==1&&(e+="=")),e}var vn;(function(t){t.symbol=Symbol.for("protobuf-ts/unknown"),t.onRead=(s,n,i,r,a)=>{(e(n)?n[t.symbol]:n[t.symbol]=[]).push({no:i,wireType:r,data:a})},t.onWrite=(s,n,i)=>{for(let{no:r,wireType:a,data:o}of t.list(n))i.tag(r,a).raw(o)},t.list=(s,n)=>{if(e(s)){let i=s[t.symbol];return n?i.filter(r=>r.no==n):i}return[]},t.last=(s,n)=>t.list(s,n).slice(-1)[0];const e=s=>s&&Array.isArray(s[t.symbol])})(vn||(vn={}));function Sc(t,e){return Object.assign(Object.assign({},t),e)}var J;(function(t){t[t.Varint=0]="Varint",t[t.Bit64=1]="Bit64",t[t.LengthDelimited=2]="LengthDelimited",t[t.StartGroup=3]="StartGroup",t[t.EndGroup=4]="EndGroup",t[t.Bit32=5]="Bit32"})(J||(J={}));function vc(){let t=0,e=0;for(let n=0;n<28;n+=7){let i=this.buf[this.pos++];if(t|=(i&127)<<n,(i&128)==0)return this.assertBounds(),[t,e]}let s=this.buf[this.pos++];if(t|=(s&15)<<28,e=(s&112)>>4,(s&128)==0)return this.assertBounds(),[t,e];for(let n=3;n<=31;n+=7){let i=this.buf[this.pos++];if(e|=(i&127)<<n,(i&128)==0)return this.assertBounds(),[t,e]}throw new Error("invalid varint")}function cs(t,e,s){for(let r=0;r<28;r=r+7){const a=t>>>r,o=!(!(a>>>7)&&e==0),l=(o?a|128:a)&255;if(s.push(l),!o)return}const n=t>>>28&15|(e&7)<<4,i=e>>3!=0;if(s.push((i?n|128:n)&255),!!i){for(let r=3;r<31;r=r+7){const a=e>>>r,o=!!(a>>>7),l=(o?a|128:a)&255;if(s.push(l),!o)return}s.push(e>>>31&1)}}const hn=65536*65536;function Sa(t){let e=t[0]=="-";e&&(t=t.slice(1));const s=1e6;let n=0,i=0;function r(a,o){const l=Number(t.slice(a,o));i*=s,n=n*s+l,n>=hn&&(i=i+(n/hn|0),n=n%hn)}return r(-24,-18),r(-18,-12),r(-12,-6),r(-6),[e,n,i]}function Ps(t,e){if(e>>>0<=2097151)return""+(hn*e+(t>>>0));let s=t&16777215,n=(t>>>24|e<<8)>>>0&16777215,i=e>>16&65535,r=s+n*6777216+i*6710656,a=n+i*8147497,o=i*2,l=1e7;r>=l&&(a+=Math.floor(r/l),r%=l),a>=l&&(o+=Math.floor(a/l),a%=l);function u(d,m){let c=d?String(d):"";return m?"0000000".slice(c.length)+c:c}return u(o,0)+u(a,o)+u(r,1)}function qi(t,e){if(t>=0){for(;t>127;)e.push(t&127|128),t=t>>>7;e.push(t)}else{for(let s=0;s<9;s++)e.push(t&127|128),t=t>>7;e.push(1)}}function Tc(){let t=this.buf[this.pos++],e=t&127;if((t&128)==0)return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(t&127)<<7,(t&128)==0)return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(t&127)<<14,(t&128)==0)return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(t&127)<<21,(t&128)==0)return this.assertBounds(),e;t=this.buf[this.pos++],e|=(t&15)<<28;for(let s=5;(t&128)!==0&&s<10;s++)t=this.buf[this.pos++];if((t&128)!=0)throw new Error("invalid varint");return this.assertBounds(),e>>>0}let B;function Cc(){const t=new DataView(new ArrayBuffer(8));B=globalThis.BigInt!==void 0&&typeof t.getBigInt64=="function"&&typeof t.getBigUint64=="function"&&typeof t.setBigInt64=="function"&&typeof t.setBigUint64=="function"?{MIN:BigInt("-9223372036854775808"),MAX:BigInt("9223372036854775807"),UMIN:BigInt("0"),UMAX:BigInt("18446744073709551615"),C:BigInt,V:t}:void 0}Cc();function va(t){if(!t)throw new Error("BigInt unavailable, see https://github.com/timostamm/protobuf-ts/blob/v1.0.8/MANUAL.md#bigint-support")}const Ta=/^-?[0-9]+$/,Tn=4294967296,Yt=2147483648;class Ca{constructor(e,s){this.lo=e|0,this.hi=s|0}isZero(){return this.lo==0&&this.hi==0}toNumber(){let e=this.hi*Tn+(this.lo>>>0);if(!Number.isSafeInteger(e))throw new Error("cannot convert to safe number");return e}}class ie extends Ca{static from(e){if(B)switch(typeof e){case"string":if(e=="0")return this.ZERO;if(e=="")throw new Error("string is no integer");e=B.C(e);case"number":if(e===0)return this.ZERO;e=B.C(e);case"bigint":if(!e)return this.ZERO;if(e<B.UMIN)throw new Error("signed value for ulong");if(e>B.UMAX)throw new Error("ulong too large");return B.V.setBigUint64(0,e,!0),new ie(B.V.getInt32(0,!0),B.V.getInt32(4,!0))}else switch(typeof e){case"string":if(e=="0")return this.ZERO;if(e=e.trim(),!Ta.test(e))throw new Error("string is no integer");let[s,n,i]=Sa(e);if(s)throw new Error("signed value for ulong");return new ie(n,i);case"number":if(e==0)return this.ZERO;if(!Number.isSafeInteger(e))throw new Error("number is no integer");if(e<0)throw new Error("signed value for ulong");return new ie(e,e/Tn)}throw new Error("unknown value "+typeof e)}toString(){return B?this.toBigInt().toString():Ps(this.lo,this.hi)}toBigInt(){return va(B),B.V.setInt32(0,this.lo,!0),B.V.setInt32(4,this.hi,!0),B.V.getBigUint64(0,!0)}}ie.ZERO=new ie(0,0);class H extends Ca{static from(e){if(B)switch(typeof e){case"string":if(e=="0")return this.ZERO;if(e=="")throw new Error("string is no integer");e=B.C(e);case"number":if(e===0)return this.ZERO;e=B.C(e);case"bigint":if(!e)return this.ZERO;if(e<B.MIN)throw new Error("signed long too small");if(e>B.MAX)throw new Error("signed long too large");return B.V.setBigInt64(0,e,!0),new H(B.V.getInt32(0,!0),B.V.getInt32(4,!0))}else switch(typeof e){case"string":if(e=="0")return this.ZERO;if(e=e.trim(),!Ta.test(e))throw new Error("string is no integer");let[s,n,i]=Sa(e);if(s){if(i>Yt||i==Yt&&n!=0)throw new Error("signed long too small")}else if(i>=Yt)throw new Error("signed long too large");let r=new H(n,i);return s?r.negate():r;case"number":if(e==0)return this.ZERO;if(!Number.isSafeInteger(e))throw new Error("number is no integer");return e>0?new H(e,e/Tn):new H(-e,-e/Tn).negate()}throw new Error("unknown value "+typeof e)}isNegative(){return(this.hi&Yt)!==0}negate(){let e=~this.hi,s=this.lo;return s?s=~s+1:e+=1,new H(s,e)}toString(){if(B)return this.toBigInt().toString();if(this.isNegative()){let e=this.negate();return"-"+Ps(e.lo,e.hi)}return Ps(this.lo,this.hi)}toBigInt(){return va(B),B.V.setInt32(0,this.lo,!0),B.V.setInt32(4,this.hi,!0),B.V.getBigInt64(0,!0)}}H.ZERO=new H(0,0);const Gi={readUnknownField:!0,readerFactory:t=>new wc(t)};function kc(t){return t?Object.assign(Object.assign({},Gi),t):Gi}class wc{constructor(e,s){this.varint64=vc,this.uint32=Tc,this.buf=e,this.len=e.length,this.pos=0,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength),this.textDecoder=s??new TextDecoder("utf-8",{fatal:!0,ignoreBOM:!0})}tag(){let e=this.uint32(),s=e>>>3,n=e&7;if(s<=0||n<0||n>5)throw new Error("illegal tag: field no "+s+" wire type "+n);return[s,n]}skip(e){let s=this.pos;switch(e){case J.Varint:for(;this.buf[this.pos++]&128;);break;case J.Bit64:this.pos+=4;case J.Bit32:this.pos+=4;break;case J.LengthDelimited:let n=this.uint32();this.pos+=n;break;case J.StartGroup:let i;for(;(i=this.tag()[1])!==J.EndGroup;)this.skip(i);break;default:throw new Error("cant skip wire type "+e)}return this.assertBounds(),this.buf.subarray(s,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return this.uint32()|0}sint32(){let e=this.uint32();return e>>>1^-(e&1)}int64(){return new H(...this.varint64())}uint64(){return new ie(...this.varint64())}sint64(){let[e,s]=this.varint64(),n=-(e&1);return e=(e>>>1|(s&1)<<31)^n,s=s>>>1^n,new H(e,s)}bool(){let[e,s]=this.varint64();return e!==0||s!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return new ie(this.sfixed32(),this.sfixed32())}sfixed64(){return new H(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let e=this.uint32(),s=this.pos;return this.pos+=e,this.assertBounds(),this.buf.subarray(s,s+e)}string(){return this.textDecoder.decode(this.bytes())}}function U(t,e){if(!t)throw new Error(e)}const Ec=34028234663852886e22,Rc=-34028234663852886e22,_c=4294967295,Ic=2147483647,Oc=-2147483648;function Ot(t){if(typeof t!="number")throw new Error("invalid int 32: "+typeof t);if(!Number.isInteger(t)||t>Ic||t<Oc)throw new Error("invalid int 32: "+t)}function Cn(t){if(typeof t!="number")throw new Error("invalid uint 32: "+typeof t);if(!Number.isInteger(t)||t>_c||t<0)throw new Error("invalid uint 32: "+t)}function ti(t){if(typeof t!="number")throw new Error("invalid float 32: "+typeof t);if(Number.isFinite(t)&&(t>Ec||t<Rc))throw new Error("invalid float 32: "+t)}const Wi={writeUnknownFields:!0,writerFactory:()=>new Ac};function Pc(t){return t?Object.assign(Object.assign({},Wi),t):Wi}class Ac{constructor(e){this.stack=[],this.textEncoder=e??new TextEncoder,this.chunks=[],this.buf=[]}finish(){this.chunks.push(new Uint8Array(this.buf));let e=0;for(let i=0;i<this.chunks.length;i++)e+=this.chunks[i].length;let s=new Uint8Array(e),n=0;for(let i=0;i<this.chunks.length;i++)s.set(this.chunks[i],n),n+=this.chunks[i].length;return this.chunks=[],s}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let e=this.finish(),s=this.stack.pop();if(!s)throw new Error("invalid state, fork stack empty");return this.chunks=s.chunks,this.buf=s.buf,this.uint32(e.byteLength),this.raw(e)}tag(e,s){return this.uint32((e<<3|s)>>>0)}raw(e){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(e),this}uint32(e){for(Cn(e);e>127;)this.buf.push(e&127|128),e=e>>>7;return this.buf.push(e),this}int32(e){return Ot(e),qi(e,this.buf),this}bool(e){return this.buf.push(e?1:0),this}bytes(e){return this.uint32(e.byteLength),this.raw(e)}string(e){let s=this.textEncoder.encode(e);return this.uint32(s.byteLength),this.raw(s)}float(e){ti(e);let s=new Uint8Array(4);return new DataView(s.buffer).setFloat32(0,e,!0),this.raw(s)}double(e){let s=new Uint8Array(8);return new DataView(s.buffer).setFloat64(0,e,!0),this.raw(s)}fixed32(e){Cn(e);let s=new Uint8Array(4);return new DataView(s.buffer).setUint32(0,e,!0),this.raw(s)}sfixed32(e){Ot(e);let s=new Uint8Array(4);return new DataView(s.buffer).setInt32(0,e,!0),this.raw(s)}sint32(e){return Ot(e),e=(e<<1^e>>31)>>>0,qi(e,this.buf),this}sfixed64(e){let s=new Uint8Array(8),n=new DataView(s.buffer),i=H.from(e);return n.setInt32(0,i.lo,!0),n.setInt32(4,i.hi,!0),this.raw(s)}fixed64(e){let s=new Uint8Array(8),n=new DataView(s.buffer),i=ie.from(e);return n.setInt32(0,i.lo,!0),n.setInt32(4,i.hi,!0),this.raw(s)}int64(e){let s=H.from(e);return cs(s.lo,s.hi,this.buf),this}sint64(e){let s=H.from(e),n=s.hi>>31,i=s.lo<<1^n,r=(s.hi<<1|s.lo>>>31)^n;return cs(i,r,this.buf),this}uint64(e){let s=ie.from(e);return cs(s.lo,s.hi,this.buf),this}}const Ji={emitDefaultValues:!1,enumAsInteger:!1,useProtoFieldName:!1,prettySpaces:0},Ki={ignoreUnknownFields:!1};function Nc(t){return t?Object.assign(Object.assign({},Ki),t):Ki}function Dc(t){return t?Object.assign(Object.assign({},Ji),t):Ji}function xc(t,e){var s,n;let i=Object.assign(Object.assign({},t),e);return i.typeRegistry=[...(s=t==null?void 0:t.typeRegistry)!==null&&s!==void 0?s:[],...(n=e==null?void 0:e.typeRegistry)!==null&&n!==void 0?n:[]],i}const ka=Symbol.for("protobuf-ts/message-type");function kn(t){let e=!1;const s=[];for(let n=0;n<t.length;n++){let i=t.charAt(n);i=="_"?e=!0:/\d/.test(i)?(s.push(i),e=!0):e?(s.push(i.toUpperCase()),e=!1):n==0?s.push(i.toLowerCase()):s.push(i)}return s.join("")}var v;(function(t){t[t.DOUBLE=1]="DOUBLE",t[t.FLOAT=2]="FLOAT",t[t.INT64=3]="INT64",t[t.UINT64=4]="UINT64",t[t.INT32=5]="INT32",t[t.FIXED64=6]="FIXED64",t[t.FIXED32=7]="FIXED32",t[t.BOOL=8]="BOOL",t[t.STRING=9]="STRING",t[t.BYTES=12]="BYTES",t[t.UINT32=13]="UINT32",t[t.SFIXED32=15]="SFIXED32",t[t.SFIXED64=16]="SFIXED64",t[t.SINT32=17]="SINT32",t[t.SINT64=18]="SINT64"})(v||(v={}));var we;(function(t){t[t.BIGINT=0]="BIGINT",t[t.STRING=1]="STRING",t[t.NUMBER=2]="NUMBER"})(we||(we={}));var wn;(function(t){t[t.NO=0]="NO",t[t.PACKED=1]="PACKED",t[t.UNPACKED=2]="UNPACKED"})(wn||(wn={}));function Lc(t){var e,s,n,i;return t.localName=(e=t.localName)!==null&&e!==void 0?e:kn(t.name),t.jsonName=(s=t.jsonName)!==null&&s!==void 0?s:kn(t.name),t.repeat=(n=t.repeat)!==null&&n!==void 0?n:wn.NO,t.opt=(i=t.opt)!==null&&i!==void 0?i:t.repeat||t.oneof?!1:t.kind=="message",t}function Uc(t){if(typeof t!="object"||t===null||!t.hasOwnProperty("oneofKind"))return!1;switch(typeof t.oneofKind){case"string":return t[t.oneofKind]===void 0?!1:Object.keys(t).length==2;case"undefined":return Object.keys(t).length==1;default:return!1}}class Fc{constructor(e){var s;this.fields=(s=e.fields)!==null&&s!==void 0?s:[]}prepare(){if(this.data)return;const e=[],s=[],n=[];for(let i of this.fields)if(i.oneof)n.includes(i.oneof)||(n.push(i.oneof),e.push(i.oneof),s.push(i.oneof));else switch(s.push(i.localName),i.kind){case"scalar":case"enum":(!i.opt||i.repeat)&&e.push(i.localName);break;case"message":i.repeat&&e.push(i.localName);break;case"map":e.push(i.localName);break}this.data={req:e,known:s,oneofs:Object.values(n)}}is(e,s,n=!1){if(s<0)return!0;if(e==null||typeof e!="object")return!1;this.prepare();let i=Object.keys(e),r=this.data;if(i.length<r.req.length||r.req.some(a=>!i.includes(a))||!n&&i.some(a=>!r.known.includes(a)))return!1;if(s<1)return!0;for(const a of r.oneofs){const o=e[a];if(!Uc(o))return!1;if(o.oneofKind===void 0)continue;const l=this.fields.find(u=>u.localName===o.oneofKind);if(!l||!this.field(o[o.oneofKind],l,n,s))return!1}for(const a of this.fields)if(a.oneof===void 0&&!this.field(e[a.localName],a,n,s))return!1;return!0}field(e,s,n,i){let r=s.repeat;switch(s.kind){case"scalar":return e===void 0?s.opt:r?this.scalars(e,s.T,i,s.L):this.scalar(e,s.T,s.L);case"enum":return e===void 0?s.opt:r?this.scalars(e,v.INT32,i):this.scalar(e,v.INT32);case"message":return e===void 0?!0:r?this.messages(e,s.T(),n,i):this.message(e,s.T(),n,i);case"map":if(typeof e!="object"||e===null)return!1;if(i<2)return!0;if(!this.mapKeys(e,s.K,i))return!1;switch(s.V.kind){case"scalar":return this.scalars(Object.values(e),s.V.T,i,s.V.L);case"enum":return this.scalars(Object.values(e),v.INT32,i);case"message":return this.messages(Object.values(e),s.V.T(),n,i)}break}return!0}message(e,s,n,i){return n?s.isAssignable(e,i):s.is(e,i)}messages(e,s,n,i){if(!Array.isArray(e))return!1;if(i<2)return!0;if(n){for(let r=0;r<e.length&&r<i;r++)if(!s.isAssignable(e[r],i-1))return!1}else for(let r=0;r<e.length&&r<i;r++)if(!s.is(e[r],i-1))return!1;return!0}scalar(e,s,n){let i=typeof e;switch(s){case v.UINT64:case v.FIXED64:case v.INT64:case v.SFIXED64:case v.SINT64:switch(n){case we.BIGINT:return i=="bigint";case we.NUMBER:return i=="number"&&!isNaN(e);default:return i=="string"}case v.BOOL:return i=="boolean";case v.STRING:return i=="string";case v.BYTES:return e instanceof Uint8Array;case v.DOUBLE:case v.FLOAT:return i=="number"&&!isNaN(e);default:return i=="number"&&Number.isInteger(e)}}scalars(e,s,n,i){if(!Array.isArray(e))return!1;if(n<2)return!0;if(Array.isArray(e)){for(let r=0;r<e.length&&r<n;r++)if(!this.scalar(e[r],s,i))return!1}return!0}mapKeys(e,s,n){let i=Object.keys(e);switch(s){case v.INT32:case v.FIXED32:case v.SFIXED32:case v.SINT32:case v.UINT32:return this.scalars(i.slice(0,n).map(r=>parseInt(r)),s,n);case v.BOOL:return this.scalars(i.slice(0,n).map(r=>r=="true"?!0:r=="false"?!1:r),s,n);default:return this.scalars(i,s,n,we.STRING)}}}function be(t,e){switch(e){case we.BIGINT:return t.toBigInt();case we.NUMBER:return t.toNumber();default:return t.toString()}}class Mc{constructor(e){this.info=e}prepare(){var e;if(this.fMap===void 0){this.fMap={};const s=(e=this.info.fields)!==null&&e!==void 0?e:[];for(const n of s)this.fMap[n.name]=n,this.fMap[n.jsonName]=n,this.fMap[n.localName]=n}}assert(e,s,n){if(!e){let i=ht(n);throw(i=="number"||i=="boolean")&&(i=n.toString()),new Error(`Cannot parse JSON ${i} for ${this.info.typeName}#${s}`)}}read(e,s,n){this.prepare();const i=[];for(const[r,a]of Object.entries(e)){const o=this.fMap[r];if(!o){if(!n.ignoreUnknownFields)throw new Error(`Found unknown field while reading ${this.info.typeName} from JSON format. JSON key: ${r}`);continue}const l=o.localName;let u;if(o.oneof){if(a===null&&(o.kind!=="enum"||o.T()[0]!=="google.protobuf.NullValue"))continue;if(i.includes(o.oneof))throw new Error(`Multiple members of the oneof group "${o.oneof}" of ${this.info.typeName} are present in JSON.`);i.push(o.oneof),u=s[o.oneof]={oneofKind:l}}else u=s;if(o.kind=="map"){if(a===null)continue;this.assert(Sn(a),o.name,a);const d=u[l];for(const[m,c]of Object.entries(a)){this.assert(c!==null,o.name+" map value",null);let h;switch(o.V.kind){case"message":h=o.V.T().internalJsonRead(c,n);break;case"enum":if(h=this.enum(o.V.T(),c,o.name,n.ignoreUnknownFields),h===!1)continue;break;case"scalar":h=this.scalar(c,o.V.T,o.V.L,o.name);break}this.assert(h!==void 0,o.name+" map value",c);let f=m;o.K==v.BOOL&&(f=f=="true"?!0:f=="false"?!1:f),f=this.scalar(f,o.K,we.STRING,o.name).toString(),d[f]=h}}else if(o.repeat){if(a===null)continue;this.assert(Array.isArray(a),o.name,a);const d=u[l];for(const m of a){this.assert(m!==null,o.name,null);let c;switch(o.kind){case"message":c=o.T().internalJsonRead(m,n);break;case"enum":if(c=this.enum(o.T(),m,o.name,n.ignoreUnknownFields),c===!1)continue;break;case"scalar":c=this.scalar(m,o.T,o.L,o.name);break}this.assert(c!==void 0,o.name,a),d.push(c)}}else switch(o.kind){case"message":if(a===null&&o.T().typeName!="google.protobuf.Value"){this.assert(o.oneof===void 0,o.name+" (oneof member)",null);continue}u[l]=o.T().internalJsonRead(a,n,u[l]);break;case"enum":if(a===null)continue;let d=this.enum(o.T(),a,o.name,n.ignoreUnknownFields);if(d===!1)continue;u[l]=d;break;case"scalar":if(a===null)continue;u[l]=this.scalar(a,o.T,o.L,o.name);break}}}enum(e,s,n,i){if(e[0]=="google.protobuf.NullValue"&&U(s===null||s==="NULL_VALUE",`Unable to parse field ${this.info.typeName}#${n}, enum ${e[0]} only accepts null.`),s===null)return 0;switch(typeof s){case"number":return U(Number.isInteger(s),`Unable to parse field ${this.info.typeName}#${n}, enum can only be integral number, got ${s}.`),s;case"string":let r=s;e[2]&&s.substring(0,e[2].length)===e[2]&&(r=s.substring(e[2].length));let a=e[1][r];return typeof a>"u"&&i?!1:(U(typeof a=="number",`Unable to parse field ${this.info.typeName}#${n}, enum ${e[0]} has no value for "${s}".`),a)}U(!1,`Unable to parse field ${this.info.typeName}#${n}, cannot parse enum value from ${typeof s}".`)}scalar(e,s,n,i){let r;try{switch(s){case v.DOUBLE:case v.FLOAT:if(e===null)return 0;if(e==="NaN")return Number.NaN;if(e==="Infinity")return Number.POSITIVE_INFINITY;if(e==="-Infinity")return Number.NEGATIVE_INFINITY;if(e===""){r="empty string";break}if(typeof e=="string"&&e.trim().length!==e.length){r="extra whitespace";break}if(typeof e!="string"&&typeof e!="number")break;let a=Number(e);if(Number.isNaN(a)){r="not a number";break}if(!Number.isFinite(a)){r="too large or small";break}return s==v.FLOAT&&ti(a),a;case v.INT32:case v.FIXED32:case v.SFIXED32:case v.SINT32:case v.UINT32:if(e===null)return 0;let o;if(typeof e=="number"?o=e:e===""?r="empty string":typeof e=="string"&&(e.trim().length!==e.length?r="extra whitespace":o=Number(e)),o===void 0)break;return s==v.UINT32?Cn(o):Ot(o),o;case v.INT64:case v.SFIXED64:case v.SINT64:if(e===null)return be(H.ZERO,n);if(typeof e!="number"&&typeof e!="string")break;return be(H.from(e),n);case v.FIXED64:case v.UINT64:if(e===null)return be(ie.ZERO,n);if(typeof e!="number"&&typeof e!="string")break;return be(ie.from(e),n);case v.BOOL:if(e===null)return!1;if(typeof e!="boolean")break;return e;case v.STRING:if(e===null)return"";if(typeof e!="string"){r="extra whitespace";break}try{encodeURIComponent(e)}catch(l){l="invalid UTF8";break}return e;case v.BYTES:if(e===null||e==="")return new Uint8Array(0);if(typeof e!="string")break;return bc(e)}}catch(a){r=a.message}this.assert(!1,i+(r?" - "+r:""),e)}}class jc{constructor(e){var s;this.fields=(s=e.fields)!==null&&s!==void 0?s:[]}write(e,s){const n={},i=e;for(const r of this.fields){if(!r.oneof){let u=this.field(r,i[r.localName],s);u!==void 0&&(n[s.useProtoFieldName?r.name:r.jsonName]=u);continue}const a=i[r.oneof];if(a.oneofKind!==r.localName)continue;const o=r.kind=="scalar"||r.kind=="enum"?Object.assign(Object.assign({},s),{emitDefaultValues:!0}):s;let l=this.field(r,a[r.localName],o);U(l!==void 0),n[s.useProtoFieldName?r.name:r.jsonName]=l}return n}field(e,s,n){let i;if(e.kind=="map"){U(typeof s=="object"&&s!==null);const r={};switch(e.V.kind){case"scalar":for(const[l,u]of Object.entries(s)){const d=this.scalar(e.V.T,u,e.name,!1,!0);U(d!==void 0),r[l.toString()]=d}break;case"message":const a=e.V.T();for(const[l,u]of Object.entries(s)){const d=this.message(a,u,e.name,n);U(d!==void 0),r[l.toString()]=d}break;case"enum":const o=e.V.T();for(const[l,u]of Object.entries(s)){U(u===void 0||typeof u=="number");const d=this.enum(o,u,e.name,!1,!0,n.enumAsInteger);U(d!==void 0),r[l.toString()]=d}break}(n.emitDefaultValues||Object.keys(r).length>0)&&(i=r)}else if(e.repeat){U(Array.isArray(s));const r=[];switch(e.kind){case"scalar":for(let l=0;l<s.length;l++){const u=this.scalar(e.T,s[l],e.name,e.opt,!0);U(u!==void 0),r.push(u)}break;case"enum":const a=e.T();for(let l=0;l<s.length;l++){U(s[l]===void 0||typeof s[l]=="number");const u=this.enum(a,s[l],e.name,e.opt,!0,n.enumAsInteger);U(u!==void 0),r.push(u)}break;case"message":const o=e.T();for(let l=0;l<s.length;l++){const u=this.message(o,s[l],e.name,n);U(u!==void 0),r.push(u)}break}(n.emitDefaultValues||r.length>0||n.emitDefaultValues)&&(i=r)}else switch(e.kind){case"scalar":i=this.scalar(e.T,s,e.name,e.opt,n.emitDefaultValues);break;case"enum":i=this.enum(e.T(),s,e.name,e.opt,n.emitDefaultValues,n.enumAsInteger);break;case"message":i=this.message(e.T(),s,e.name,n);break}return i}enum(e,s,n,i,r,a){if(e[0]=="google.protobuf.NullValue")return!r&&!i?void 0:null;if(s===void 0){U(i);return}if(!(s===0&&!r&&!i))return U(typeof s=="number"),U(Number.isInteger(s)),a||!e[1].hasOwnProperty(s)?s:e[2]?e[2]+e[1][s]:e[1][s]}message(e,s,n,i){return s===void 0?i.emitDefaultValues?null:void 0:e.internalJsonWrite(s,i)}scalar(e,s,n,i,r){if(s===void 0){U(i);return}const a=r||i;switch(e){case v.INT32:case v.SFIXED32:case v.SINT32:return s===0?a?0:void 0:(Ot(s),s);case v.FIXED32:case v.UINT32:return s===0?a?0:void 0:(Cn(s),s);case v.FLOAT:ti(s);case v.DOUBLE:return s===0?a?0:void 0:(U(typeof s=="number"),Number.isNaN(s)?"NaN":s===Number.POSITIVE_INFINITY?"Infinity":s===Number.NEGATIVE_INFINITY?"-Infinity":s);case v.STRING:return s===""?a?"":void 0:(U(typeof s=="string"),s);case v.BOOL:return s===!1?a?!1:void 0:(U(typeof s=="boolean"),s);case v.UINT64:case v.FIXED64:U(typeof s=="number"||typeof s=="string"||typeof s=="bigint");let o=ie.from(s);return o.isZero()&&!a?void 0:o.toString();case v.INT64:case v.SFIXED64:case v.SINT64:U(typeof s=="number"||typeof s=="string"||typeof s=="bigint");let l=H.from(s);return l.isZero()&&!a?void 0:l.toString();case v.BYTES:return U(s instanceof Uint8Array),s.byteLength?yc(s):a?"":void 0}}}function As(t,e=we.STRING){switch(t){case v.BOOL:return!1;case v.UINT64:case v.FIXED64:return be(ie.ZERO,e);case v.INT64:case v.SFIXED64:case v.SINT64:return be(H.ZERO,e);case v.DOUBLE:case v.FLOAT:return 0;case v.BYTES:return new Uint8Array(0);case v.STRING:return"";default:return 0}}class $c{constructor(e){this.info=e}prepare(){var e;if(!this.fieldNoToField){const s=(e=this.info.fields)!==null&&e!==void 0?e:[];this.fieldNoToField=new Map(s.map(n=>[n.no,n]))}}read(e,s,n,i){this.prepare();const r=i===void 0?e.len:e.pos+i;for(;e.pos<r;){const[a,o]=e.tag(),l=this.fieldNoToField.get(a);if(!l){let c=n.readUnknownField;if(c=="throw")throw new Error(`Unknown field ${a} (wire type ${o}) for ${this.info.typeName}`);let h=e.skip(o);c!==!1&&(c===!0?vn.onRead:c)(this.info.typeName,s,a,o,h);continue}let u=s,d=l.repeat,m=l.localName;switch(l.oneof&&(u=u[l.oneof],u.oneofKind!==m&&(u=s[l.oneof]={oneofKind:m})),l.kind){case"scalar":case"enum":let c=l.kind=="enum"?v.INT32:l.T,h=l.kind=="scalar"?l.L:void 0;if(d){let g=u[m];if(o==J.LengthDelimited&&c!=v.STRING&&c!=v.BYTES){let b=e.uint32()+e.pos;for(;e.pos<b;)g.push(this.scalar(e,c,h))}else g.push(this.scalar(e,c,h))}else u[m]=this.scalar(e,c,h);break;case"message":if(d){let g=u[m],b=l.T().internalBinaryRead(e,e.uint32(),n);g.push(b)}else u[m]=l.T().internalBinaryRead(e,e.uint32(),n,u[m]);break;case"map":let[f,p]=this.mapEntry(l,e,n);u[m][f]=p;break}}}mapEntry(e,s,n){let i=s.uint32(),r=s.pos+i,a,o;for(;s.pos<r;){let[l,u]=s.tag();switch(l){case 1:e.K==v.BOOL?a=s.bool().toString():a=this.scalar(s,e.K,we.STRING);break;case 2:switch(e.V.kind){case"scalar":o=this.scalar(s,e.V.T,e.V.L);break;case"enum":o=s.int32();break;case"message":o=e.V.T().internalBinaryRead(s,s.uint32(),n);break}break;default:throw new Error(`Unknown field ${l} (wire type ${u}) in map entry for ${this.info.typeName}#${e.name}`)}}if(a===void 0){let l=As(e.K);a=e.K==v.BOOL?l.toString():l}if(o===void 0)switch(e.V.kind){case"scalar":o=As(e.V.T,e.V.L);break;case"enum":o=0;break;case"message":o=e.V.T().create();break}return[a,o]}scalar(e,s,n){switch(s){case v.INT32:return e.int32();case v.STRING:return e.string();case v.BOOL:return e.bool();case v.DOUBLE:return e.double();case v.FLOAT:return e.float();case v.INT64:return be(e.int64(),n);case v.UINT64:return be(e.uint64(),n);case v.FIXED64:return be(e.fixed64(),n);case v.FIXED32:return e.fixed32();case v.BYTES:return e.bytes();case v.UINT32:return e.uint32();case v.SFIXED32:return e.sfixed32();case v.SFIXED64:return be(e.sfixed64(),n);case v.SINT32:return e.sint32();case v.SINT64:return be(e.sint64(),n)}}}class Bc{constructor(e){this.info=e}prepare(){if(!this.fields){const e=this.info.fields?this.info.fields.concat():[];this.fields=e.sort((s,n)=>s.no-n.no)}}write(e,s,n){this.prepare();for(const r of this.fields){let a,o,l=r.repeat,u=r.localName;if(r.oneof){const d=e[r.oneof];if(d.oneofKind!==u)continue;a=d[u],o=!0}else a=e[u],o=!1;switch(r.kind){case"scalar":case"enum":let d=r.kind=="enum"?v.INT32:r.T;if(l)if(U(Array.isArray(a)),l==wn.PACKED)this.packed(s,d,r.no,a);else for(const m of a)this.scalar(s,d,r.no,m,!0);else a===void 0?U(r.opt):this.scalar(s,d,r.no,a,o||r.opt);break;case"message":if(l){U(Array.isArray(a));for(const m of a)this.message(s,n,r.T(),r.no,m)}else this.message(s,n,r.T(),r.no,a);break;case"map":U(typeof a=="object"&&a!==null);for(const[m,c]of Object.entries(a))this.mapEntry(s,n,r,m,c);break}}let i=n.writeUnknownFields;i!==!1&&(i===!0?vn.onWrite:i)(this.info.typeName,e,s)}mapEntry(e,s,n,i,r){e.tag(n.no,J.LengthDelimited),e.fork();let a=i;switch(n.K){case v.INT32:case v.FIXED32:case v.UINT32:case v.SFIXED32:case v.SINT32:a=Number.parseInt(i);break;case v.BOOL:U(i=="true"||i=="false"),a=i=="true";break}switch(this.scalar(e,n.K,1,a,!0),n.V.kind){case"scalar":this.scalar(e,n.V.T,2,r,!0);break;case"enum":this.scalar(e,v.INT32,2,r,!0);break;case"message":this.message(e,s,n.V.T(),2,r);break}e.join()}message(e,s,n,i,r){r!==void 0&&(n.internalBinaryWrite(r,e.tag(i,J.LengthDelimited).fork(),s),e.join())}scalar(e,s,n,i,r){let[a,o,l]=this.scalarInfo(s,i);(!l||r)&&(e.tag(n,a),e[o](i))}packed(e,s,n,i){if(!i.length)return;U(s!==v.BYTES&&s!==v.STRING),e.tag(n,J.LengthDelimited),e.fork();let[,r]=this.scalarInfo(s);for(let a=0;a<i.length;a++)e[r](i[a]);e.join()}scalarInfo(e,s){let n=J.Varint,i,r=s===void 0,a=s===0;switch(e){case v.INT32:i="int32";break;case v.STRING:a=r||!s.length,n=J.LengthDelimited,i="string";break;case v.BOOL:a=s===!1,i="bool";break;case v.UINT32:i="uint32";break;case v.DOUBLE:n=J.Bit64,i="double";break;case v.FLOAT:n=J.Bit32,i="float";break;case v.INT64:a=r||H.from(s).isZero(),i="int64";break;case v.UINT64:a=r||ie.from(s).isZero(),i="uint64";break;case v.FIXED64:a=r||ie.from(s).isZero(),n=J.Bit64,i="fixed64";break;case v.BYTES:a=r||!s.byteLength,n=J.LengthDelimited,i="bytes";break;case v.FIXED32:n=J.Bit32,i="fixed32";break;case v.SFIXED32:n=J.Bit32,i="sfixed32";break;case v.SFIXED64:a=r||H.from(s).isZero(),n=J.Bit64,i="sfixed64";break;case v.SINT32:i="sint32";break;case v.SINT64:a=r||H.from(s).isZero(),i="sint64";break}return[n,i,r||a]}}function Vc(t){const e=t.messagePrototype?Object.create(t.messagePrototype):Object.defineProperty({},ka,{value:t});for(let s of t.fields){let n=s.localName;if(!s.opt)if(s.oneof)e[s.oneof]={oneofKind:void 0};else if(s.repeat)e[n]=[];else switch(s.kind){case"scalar":e[n]=As(s.T,s.L);break;case"enum":e[n]=0;break;case"map":e[n]={};break}}return e}function us(t,e,s){let n,i=s,r;for(let a of t.fields){let o=a.localName;if(a.oneof){const l=i[a.oneof];if((l==null?void 0:l.oneofKind)==null)continue;if(n=l[o],r=e[a.oneof],r.oneofKind=l.oneofKind,n==null){delete r[o];continue}}else if(n=i[o],r=e,n==null)continue;switch(a.repeat&&(r[o].length=n.length),a.kind){case"scalar":case"enum":if(a.repeat)for(let u=0;u<n.length;u++)r[o][u]=n[u];else r[o]=n;break;case"message":let l=a.T();if(a.repeat)for(let u=0;u<n.length;u++)r[o][u]=l.create(n[u]);else r[o]===void 0?r[o]=l.create(n):l.mergePartial(r[o],n);break;case"map":switch(a.V.kind){case"scalar":case"enum":Object.assign(r[o],n);break;case"message":let u=a.V.T();for(let d of Object.keys(n))r[o][d]=u.create(n[d]);break}break}}}function Hc(t,e,s){if(e===s)return!0;if(!e||!s)return!1;for(let n of t.fields){let i=n.localName,r=n.oneof?e[n.oneof][i]:e[i],a=n.oneof?s[n.oneof][i]:s[i];switch(n.kind){case"enum":case"scalar":let o=n.kind=="enum"?v.INT32:n.T;if(!(n.repeat?zi(o,r,a):wa(o,r,a)))return!1;break;case"map":if(!(n.V.kind=="message"?Yi(n.V.T(),Xt(r),Xt(a)):zi(n.V.kind=="enum"?v.INT32:n.V.T,Xt(r),Xt(a))))return!1;break;case"message":let l=n.T();if(!(n.repeat?Yi(l,r,a):l.equals(r,a)))return!1;break}}return!0}const Xt=Object.values;function wa(t,e,s){if(e===s)return!0;if(t!==v.BYTES)return!1;let n=e,i=s;if(n.length!==i.length)return!1;for(let r=0;r<n.length;r++)if(n[r]!=i[r])return!1;return!0}function zi(t,e,s){if(e.length!==s.length)return!1;for(let n=0;n<e.length;n++)if(!wa(t,e[n],s[n]))return!1;return!0}function Yi(t,e,s){if(e.length!==s.length)return!1;for(let n=0;n<e.length;n++)if(!t.equals(e[n],s[n]))return!1;return!0}const qc=Object.getOwnPropertyDescriptors(Object.getPrototypeOf({}));class k{constructor(e,s,n){this.defaultCheckDepth=16,this.typeName=e,this.fields=s.map(Lc),this.options=n??{},this.messagePrototype=Object.create(null,Object.assign(Object.assign({},qc),{[ka]:{value:this}})),this.refTypeCheck=new Fc(this),this.refJsonReader=new Mc(this),this.refJsonWriter=new jc(this),this.refBinReader=new $c(this),this.refBinWriter=new Bc(this)}create(e){let s=Vc(this);return e!==void 0&&us(this,s,e),s}clone(e){let s=this.create();return us(this,s,e),s}equals(e,s){return Hc(this,e,s)}is(e,s=this.defaultCheckDepth){return this.refTypeCheck.is(e,s,!1)}isAssignable(e,s=this.defaultCheckDepth){return this.refTypeCheck.is(e,s,!0)}mergePartial(e,s){us(this,e,s)}fromBinary(e,s){let n=kc(s);return this.internalBinaryRead(n.readerFactory(e),e.byteLength,n)}fromJson(e,s){return this.internalJsonRead(e,Nc(s))}fromJsonString(e,s){let n=JSON.parse(e);return this.fromJson(n,s)}toJson(e,s){return this.internalJsonWrite(e,Dc(s))}toJsonString(e,s){var n;let i=this.toJson(e,s);return JSON.stringify(i,null,(n=s==null?void 0:s.prettySpaces)!==null&&n!==void 0?n:0)}toBinary(e,s){let n=Pc(s);return this.internalBinaryWrite(e,n.writerFactory(),n).finish()}internalJsonRead(e,s,n){if(e!==null&&typeof e=="object"&&!Array.isArray(e)){let i=n??this.create();return this.refJsonReader.read(e,i,s),i}throw new Error(`Unable to parse message ${this.typeName} from JSON ${ht(e)}.`)}internalJsonWrite(e,s){return this.refJsonWriter.write(e,s)}internalBinaryWrite(e,s,n){return this.refBinWriter.write(e,s,n),s}internalBinaryRead(e,s,n,i){let r=i??this.create();return this.refBinReader.read(e,r,n,s),r}}function Gc(t,e){var s,n,i;let r=t;return r.service=e,r.localName=(s=r.localName)!==null&&s!==void 0?s:kn(r.name),r.serverStreaming=!!r.serverStreaming,r.clientStreaming=!!r.clientStreaming,r.options=(n=r.options)!==null&&n!==void 0?n:{},r.idempotency=(i=r.idempotency)!==null&&i!==void 0?i:void 0,r}class Wc{constructor(e,s,n){this.typeName=e,this.methods=s.map(i=>Gc(i,this)),this.options=n??{}}}class ce extends Error{constructor(e,s="UNKNOWN",n){super(e),this.name="RpcError",Object.setPrototypeOf(this,new.target.prototype),this.code=s,this.meta=n??{}}toString(){const e=[this.name+": "+this.message];this.code&&(e.push(""),e.push("Code: "+this.code)),this.serviceName&&this.methodName&&e.push("Method: "+this.serviceName+"/"+this.methodName);let s=Object.entries(this.meta);if(s.length){e.push(""),e.push("Meta:");for(let[n,i]of s)e.push(`  ${n}: ${i}`)}return e.join(`
`)}}function Jc(t,e){if(!e)return t;let s={};Zt(t,s),Zt(e,s);for(let n of Object.keys(e)){let i=e[n];switch(n){case"jsonOptions":s.jsonOptions=xc(t.jsonOptions,s.jsonOptions);break;case"binaryOptions":s.binaryOptions=Sc(t.binaryOptions,s.binaryOptions);break;case"meta":s.meta={},Zt(t.meta,s.meta),Zt(e.meta,s.meta);break;case"interceptors":s.interceptors=t.interceptors?t.interceptors.concat(i):i.concat();break}}return s}function Zt(t,e){if(!t)return;let s=e;for(let[n,i]of Object.entries(t))i instanceof Date?s[n]=new Date(i.getTime()):Array.isArray(i)?s[n]=i.concat():s[n]=i}var ge;(function(t){t[t.PENDING=0]="PENDING",t[t.REJECTED=1]="REJECTED",t[t.RESOLVED=2]="RESOLVED"})(ge||(ge={}));class Qt{constructor(e=!0){this._state=ge.PENDING,this._promise=new Promise((s,n)=>{this._resolve=s,this._reject=n}),e&&this._promise.catch(s=>{})}get state(){return this._state}get promise(){return this._promise}resolve(e){if(this.state!==ge.PENDING)throw new Error(`cannot resolve ${ge[this.state].toLowerCase()}`);this._resolve(e),this._state=ge.RESOLVED}reject(e){if(this.state!==ge.PENDING)throw new Error(`cannot reject ${ge[this.state].toLowerCase()}`);this._reject(e),this._state=ge.REJECTED}resolvePending(e){this._state===ge.PENDING&&this.resolve(e)}rejectPending(e){this._state===ge.PENDING&&this.reject(e)}}var Kc=function(t,e,s,n){function i(r){return r instanceof s?r:new s(function(a){a(r)})}return new(s||(s=Promise))(function(r,a){function o(d){try{u(n.next(d))}catch(m){a(m)}}function l(d){try{u(n.throw(d))}catch(m){a(m)}}function u(d){d.done?r(d.value):i(d.value).then(o,l)}u((n=n.apply(t,e||[])).next())})};class zc{constructor(e,s,n,i,r,a,o){this.method=e,this.requestHeaders=s,this.request=n,this.headers=i,this.response=r,this.status=a,this.trailers=o}then(e,s){return this.promiseFinished().then(n=>e?Promise.resolve(e(n)):n,n=>s?Promise.resolve(s(n)):Promise.reject(n))}promiseFinished(){return Kc(this,void 0,void 0,function*(){let[e,s,n,i]=yield Promise.all([this.headers,this.response,this.status,this.trailers]);return{method:this.method,requestHeaders:this.requestHeaders,request:this.request,headers:e,response:s,status:n,trailers:i}})}}function Oe(t,e,s,n,i){var r;{let a=(o,l,u)=>e.unary(o,l,u);for(const o of((r=n.interceptors)!==null&&r!==void 0?r:[]).filter(l=>l.interceptUnary).reverse()){const l=a;a=(u,d,m)=>o.interceptUnary(l,u,d,m)}return a(s,i,n)}}function Ea(t,e){return function(){return t.apply(e,arguments)}}const{toString:Yc}=Object.prototype,{getPrototypeOf:ni}=Object,{iterator:Mn,toStringTag:Ra}=Symbol,jn=(t=>e=>{const s=Yc.call(e);return t[s]||(t[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Se=t=>(t=t.toLowerCase(),e=>jn(e)===t),$n=t=>e=>typeof e===t,{isArray:ft}=Array,Pt=$n("undefined");function Xc(t){return t!==null&&!Pt(t)&&t.constructor!==null&&!Pt(t.constructor)&&ue(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const _a=Se("ArrayBuffer");function Zc(t){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(t):e=t&&t.buffer&&_a(t.buffer),e}const Qc=$n("string"),ue=$n("function"),Ia=$n("number"),Bn=t=>t!==null&&typeof t=="object",eu=t=>t===!0||t===!1,fn=t=>{if(jn(t)!=="object")return!1;const e=ni(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Ra in t)&&!(Mn in t)},tu=Se("Date"),nu=Se("File"),su=Se("Blob"),iu=Se("FileList"),ru=t=>Bn(t)&&ue(t.pipe),au=t=>{let e;return t&&(typeof FormData=="function"&&t instanceof FormData||ue(t.append)&&((e=jn(t))==="formdata"||e==="object"&&ue(t.toString)&&t.toString()==="[object FormData]"))},ou=Se("URLSearchParams"),[cu,uu,lu,du]=["ReadableStream","Request","Response","Headers"].map(Se),hu=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function jt(t,e,{allOwnKeys:s=!1}={}){if(t===null||typeof t>"u")return;let n,i;if(typeof t!="object"&&(t=[t]),ft(t))for(n=0,i=t.length;n<i;n++)e.call(null,t[n],n,t);else{const r=s?Object.getOwnPropertyNames(t):Object.keys(t),a=r.length;let o;for(n=0;n<a;n++)o=r[n],e.call(null,t[o],o,t)}}function Oa(t,e){e=e.toLowerCase();const s=Object.keys(t);let n=s.length,i;for(;n-- >0;)if(i=s[n],e===i.toLowerCase())return i;return null}const He=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Pa=t=>!Pt(t)&&t!==He;function Ns(){const{caseless:t}=Pa(this)&&this||{},e={},s=(n,i)=>{const r=t&&Oa(e,i)||i;fn(e[r])&&fn(n)?e[r]=Ns(e[r],n):fn(n)?e[r]=Ns({},n):ft(n)?e[r]=n.slice():e[r]=n};for(let n=0,i=arguments.length;n<i;n++)arguments[n]&&jt(arguments[n],s);return e}const fu=(t,e,s,{allOwnKeys:n}={})=>(jt(e,(i,r)=>{s&&ue(i)?t[r]=Ea(i,s):t[r]=i},{allOwnKeys:n}),t),pu=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),mu=(t,e,s,n)=>{t.prototype=Object.create(e.prototype,n),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),s&&Object.assign(t.prototype,s)},gu=(t,e,s,n)=>{let i,r,a;const o={};if(e=e||{},t==null)return e;do{for(i=Object.getOwnPropertyNames(t),r=i.length;r-- >0;)a=i[r],(!n||n(a,t,e))&&!o[a]&&(e[a]=t[a],o[a]=!0);t=s!==!1&&ni(t)}while(t&&(!s||s(t,e))&&t!==Object.prototype);return e},bu=(t,e,s)=>{t=String(t),(s===void 0||s>t.length)&&(s=t.length),s-=e.length;const n=t.indexOf(e,s);return n!==-1&&n===s},yu=t=>{if(!t)return null;if(ft(t))return t;let e=t.length;if(!Ia(e))return null;const s=new Array(e);for(;e-- >0;)s[e]=t[e];return s},Su=(t=>e=>t&&e instanceof t)(typeof Uint8Array<"u"&&ni(Uint8Array)),vu=(t,e)=>{const n=(t&&t[Mn]).call(t);let i;for(;(i=n.next())&&!i.done;){const r=i.value;e.call(t,r[0],r[1])}},Tu=(t,e)=>{let s;const n=[];for(;(s=t.exec(e))!==null;)n.push(s);return n},Cu=Se("HTMLFormElement"),ku=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,n,i){return n.toUpperCase()+i}),Xi=(({hasOwnProperty:t})=>(e,s)=>t.call(e,s))(Object.prototype),wu=Se("RegExp"),Aa=(t,e)=>{const s=Object.getOwnPropertyDescriptors(t),n={};jt(s,(i,r)=>{let a;(a=e(i,r,t))!==!1&&(n[r]=a||i)}),Object.defineProperties(t,n)},Eu=t=>{Aa(t,(e,s)=>{if(ue(t)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const n=t[s];if(ue(n)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},Ru=(t,e)=>{const s={},n=i=>{i.forEach(r=>{s[r]=!0})};return ft(t)?n(t):n(String(t).split(e)),s},_u=()=>{},Iu=(t,e)=>t!=null&&Number.isFinite(t=+t)?t:e;function Ou(t){return!!(t&&ue(t.append)&&t[Ra]==="FormData"&&t[Mn])}const Pu=t=>{const e=new Array(10),s=(n,i)=>{if(Bn(n)){if(e.indexOf(n)>=0)return;if(!("toJSON"in n)){e[i]=n;const r=ft(n)?[]:{};return jt(n,(a,o)=>{const l=s(a,i+1);!Pt(l)&&(r[o]=l)}),e[i]=void 0,r}}return n};return s(t,0)},Au=Se("AsyncFunction"),Nu=t=>t&&(Bn(t)||ue(t))&&ue(t.then)&&ue(t.catch),Na=((t,e)=>t?setImmediate:e?((s,n)=>(He.addEventListener("message",({source:i,data:r})=>{i===He&&r===s&&n.length&&n.shift()()},!1),i=>{n.push(i),He.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",ue(He.postMessage)),Du=typeof queueMicrotask<"u"?queueMicrotask.bind(He):typeof process<"u"&&process.nextTick||Na,xu=t=>t!=null&&ue(t[Mn]),y={isArray:ft,isArrayBuffer:_a,isBuffer:Xc,isFormData:au,isArrayBufferView:Zc,isString:Qc,isNumber:Ia,isBoolean:eu,isObject:Bn,isPlainObject:fn,isReadableStream:cu,isRequest:uu,isResponse:lu,isHeaders:du,isUndefined:Pt,isDate:tu,isFile:nu,isBlob:su,isRegExp:wu,isFunction:ue,isStream:ru,isURLSearchParams:ou,isTypedArray:Su,isFileList:iu,forEach:jt,merge:Ns,extend:fu,trim:hu,stripBOM:pu,inherits:mu,toFlatObject:gu,kindOf:jn,kindOfTest:Se,endsWith:bu,toArray:yu,forEachEntry:vu,matchAll:Tu,isHTMLForm:Cu,hasOwnProperty:Xi,hasOwnProp:Xi,reduceDescriptors:Aa,freezeMethods:Eu,toObjectSet:Ru,toCamelCase:ku,noop:_u,toFiniteNumber:Iu,findKey:Oa,global:He,isContextDefined:Pa,isSpecCompliantForm:Ou,toJSONObject:Pu,isAsyncFn:Au,isThenable:Nu,setImmediate:Na,asap:Du,isIterable:xu};function P(t,e,s,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),s&&(this.config=s),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}y.inherits(P,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:y.toJSONObject(this.config),code:this.code,status:this.status}}});const Da=P.prototype,xa={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{xa[t]={value:t}});Object.defineProperties(P,xa);Object.defineProperty(Da,"isAxiosError",{value:!0});P.from=(t,e,s,n,i,r)=>{const a=Object.create(Da);return y.toFlatObject(t,a,function(l){return l!==Error.prototype},o=>o!=="isAxiosError"),P.call(a,t.message,e,s,n,i),a.cause=t,a.name=t.name,r&&Object.assign(a,r),a};const Lu=null;function Ds(t){return y.isPlainObject(t)||y.isArray(t)}function La(t){return y.endsWith(t,"[]")?t.slice(0,-2):t}function Zi(t,e,s){return t?t.concat(e).map(function(i,r){return i=La(i),!s&&r?"["+i+"]":i}).join(s?".":""):e}function Uu(t){return y.isArray(t)&&!t.some(Ds)}const Fu=y.toFlatObject(y,{},null,function(e){return/^is[A-Z]/.test(e)});function Vn(t,e,s){if(!y.isObject(t))throw new TypeError("target must be an object");e=e||new FormData,s=y.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(p,g){return!y.isUndefined(g[p])});const n=s.metaTokens,i=s.visitor||d,r=s.dots,a=s.indexes,l=(s.Blob||typeof Blob<"u"&&Blob)&&y.isSpecCompliantForm(e);if(!y.isFunction(i))throw new TypeError("visitor must be a function");function u(f){if(f===null)return"";if(y.isDate(f))return f.toISOString();if(!l&&y.isBlob(f))throw new P("Blob is not supported. Use a Buffer instead.");return y.isArrayBuffer(f)||y.isTypedArray(f)?l&&typeof Blob=="function"?new Blob([f]):Buffer.from(f):f}function d(f,p,g){let b=f;if(f&&!g&&typeof f=="object"){if(y.endsWith(p,"{}"))p=n?p:p.slice(0,-2),f=JSON.stringify(f);else if(y.isArray(f)&&Uu(f)||(y.isFileList(f)||y.endsWith(p,"[]"))&&(b=y.toArray(f)))return p=La(p),b.forEach(function(S,w){!(y.isUndefined(S)||S===null)&&e.append(a===!0?Zi([p],w,r):a===null?p:p+"[]",u(S))}),!1}return Ds(f)?!0:(e.append(Zi(g,p,r),u(f)),!1)}const m=[],c=Object.assign(Fu,{defaultVisitor:d,convertValue:u,isVisitable:Ds});function h(f,p){if(!y.isUndefined(f)){if(m.indexOf(f)!==-1)throw Error("Circular reference detected in "+p.join("."));m.push(f),y.forEach(f,function(b,T){(!(y.isUndefined(b)||b===null)&&i.call(e,b,y.isString(T)?T.trim():T,p,c))===!0&&h(b,p?p.concat(T):[T])}),m.pop()}}if(!y.isObject(t))throw new TypeError("data must be an object");return h(t),e}function Qi(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(n){return e[n]})}function si(t,e){this._pairs=[],t&&Vn(t,this,e)}const Ua=si.prototype;Ua.append=function(e,s){this._pairs.push([e,s])};Ua.toString=function(e){const s=e?function(n){return e.call(this,n,Qi)}:Qi;return this._pairs.map(function(i){return s(i[0])+"="+s(i[1])},"").join("&")};function Mu(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Fa(t,e,s){if(!e)return t;const n=s&&s.encode||Mu;y.isFunction(s)&&(s={serialize:s});const i=s&&s.serialize;let r;if(i?r=i(e,s):r=y.isURLSearchParams(e)?e.toString():new si(e,s).toString(n),r){const a=t.indexOf("#");a!==-1&&(t=t.slice(0,a)),t+=(t.indexOf("?")===-1?"?":"&")+r}return t}class er{constructor(){this.handlers=[]}use(e,s,n){return this.handlers.push({fulfilled:e,rejected:s,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){y.forEach(this.handlers,function(n){n!==null&&e(n)})}}const Ma={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ju=typeof URLSearchParams<"u"?URLSearchParams:si,$u=typeof FormData<"u"?FormData:null,Bu=typeof Blob<"u"?Blob:null,Vu={isBrowser:!0,classes:{URLSearchParams:ju,FormData:$u,Blob:Bu},protocols:["http","https","file","blob","url","data"]},ii=typeof window<"u"&&typeof document<"u",xs=typeof navigator=="object"&&navigator||void 0,Hu=ii&&(!xs||["ReactNative","NativeScript","NS"].indexOf(xs.product)<0),qu=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Gu=ii&&window.location.href||"http://localhost",Wu=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ii,hasStandardBrowserEnv:Hu,hasStandardBrowserWebWorkerEnv:qu,navigator:xs,origin:Gu},Symbol.toStringTag,{value:"Module"})),se={...Wu,...Vu};function Ju(t,e){return Vn(t,new se.classes.URLSearchParams,Object.assign({visitor:function(s,n,i,r){return se.isNode&&y.isBuffer(s)?(this.append(n,s.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},e))}function Ku(t){return y.matchAll(/\w+|\[(\w*)]/g,t).map(e=>e[0]==="[]"?"":e[1]||e[0])}function zu(t){const e={},s=Object.keys(t);let n;const i=s.length;let r;for(n=0;n<i;n++)r=s[n],e[r]=t[r];return e}function ja(t){function e(s,n,i,r){let a=s[r++];if(a==="__proto__")return!0;const o=Number.isFinite(+a),l=r>=s.length;return a=!a&&y.isArray(i)?i.length:a,l?(y.hasOwnProp(i,a)?i[a]=[i[a],n]:i[a]=n,!o):((!i[a]||!y.isObject(i[a]))&&(i[a]=[]),e(s,n,i[a],r)&&y.isArray(i[a])&&(i[a]=zu(i[a])),!o)}if(y.isFormData(t)&&y.isFunction(t.entries)){const s={};return y.forEachEntry(t,(n,i)=>{e(Ku(n),i,s,0)}),s}return null}function Yu(t,e,s){if(y.isString(t))try{return(e||JSON.parse)(t),y.trim(t)}catch(n){if(n.name!=="SyntaxError")throw n}return(s||JSON.stringify)(t)}const $t={transitional:Ma,adapter:["xhr","http","fetch"],transformRequest:[function(e,s){const n=s.getContentType()||"",i=n.indexOf("application/json")>-1,r=y.isObject(e);if(r&&y.isHTMLForm(e)&&(e=new FormData(e)),y.isFormData(e))return i?JSON.stringify(ja(e)):e;if(y.isArrayBuffer(e)||y.isBuffer(e)||y.isStream(e)||y.isFile(e)||y.isBlob(e)||y.isReadableStream(e))return e;if(y.isArrayBufferView(e))return e.buffer;if(y.isURLSearchParams(e))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(r){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Ju(e,this.formSerializer).toString();if((o=y.isFileList(e))||n.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Vn(o?{"files[]":e}:e,l&&new l,this.formSerializer)}}return r||i?(s.setContentType("application/json",!1),Yu(e)):e}],transformResponse:[function(e){const s=this.transitional||$t.transitional,n=s&&s.forcedJSONParsing,i=this.responseType==="json";if(y.isResponse(e)||y.isReadableStream(e))return e;if(e&&y.isString(e)&&(n&&!this.responseType||i)){const a=!(s&&s.silentJSONParsing)&&i;try{return JSON.parse(e)}catch(o){if(a)throw o.name==="SyntaxError"?P.from(o,P.ERR_BAD_RESPONSE,this,null,this.response):o}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:se.classes.FormData,Blob:se.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};y.forEach(["delete","get","head","post","put","patch"],t=>{$t.headers[t]={}});const Xu=y.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Zu=t=>{const e={};let s,n,i;return t&&t.split(`
`).forEach(function(a){i=a.indexOf(":"),s=a.substring(0,i).trim().toLowerCase(),n=a.substring(i+1).trim(),!(!s||e[s]&&Xu[s])&&(s==="set-cookie"?e[s]?e[s].push(n):e[s]=[n]:e[s]=e[s]?e[s]+", "+n:n)}),e},tr=Symbol("internals");function Tt(t){return t&&String(t).trim().toLowerCase()}function pn(t){return t===!1||t==null?t:y.isArray(t)?t.map(pn):String(t)}function Qu(t){const e=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=s.exec(t);)e[n[1]]=n[2];return e}const el=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function ls(t,e,s,n,i){if(y.isFunction(n))return n.call(this,e,s);if(i&&(e=s),!!y.isString(e)){if(y.isString(n))return e.indexOf(n)!==-1;if(y.isRegExp(n))return n.test(e)}}function tl(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,s,n)=>s.toUpperCase()+n)}function nl(t,e){const s=y.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(t,n+s,{value:function(i,r,a){return this[n].call(this,e,i,r,a)},configurable:!0})})}let le=class{constructor(e){e&&this.set(e)}set(e,s,n){const i=this;function r(o,l,u){const d=Tt(l);if(!d)throw new Error("header name must be a non-empty string");const m=y.findKey(i,d);(!m||i[m]===void 0||u===!0||u===void 0&&i[m]!==!1)&&(i[m||l]=pn(o))}const a=(o,l)=>y.forEach(o,(u,d)=>r(u,d,l));if(y.isPlainObject(e)||e instanceof this.constructor)a(e,s);else if(y.isString(e)&&(e=e.trim())&&!el(e))a(Zu(e),s);else if(y.isObject(e)&&y.isIterable(e)){let o={},l,u;for(const d of e){if(!y.isArray(d))throw TypeError("Object iterator must return a key-value pair");o[u=d[0]]=(l=o[u])?y.isArray(l)?[...l,d[1]]:[l,d[1]]:d[1]}a(o,s)}else e!=null&&r(s,e,n);return this}get(e,s){if(e=Tt(e),e){const n=y.findKey(this,e);if(n){const i=this[n];if(!s)return i;if(s===!0)return Qu(i);if(y.isFunction(s))return s.call(this,i,n);if(y.isRegExp(s))return s.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,s){if(e=Tt(e),e){const n=y.findKey(this,e);return!!(n&&this[n]!==void 0&&(!s||ls(this,this[n],n,s)))}return!1}delete(e,s){const n=this;let i=!1;function r(a){if(a=Tt(a),a){const o=y.findKey(n,a);o&&(!s||ls(n,n[o],o,s))&&(delete n[o],i=!0)}}return y.isArray(e)?e.forEach(r):r(e),i}clear(e){const s=Object.keys(this);let n=s.length,i=!1;for(;n--;){const r=s[n];(!e||ls(this,this[r],r,e,!0))&&(delete this[r],i=!0)}return i}normalize(e){const s=this,n={};return y.forEach(this,(i,r)=>{const a=y.findKey(n,r);if(a){s[a]=pn(i),delete s[r];return}const o=e?tl(r):String(r).trim();o!==r&&delete s[r],s[o]=pn(i),n[o]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const s=Object.create(null);return y.forEach(this,(n,i)=>{n!=null&&n!==!1&&(s[i]=e&&y.isArray(n)?n.join(", "):n)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,s])=>e+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...s){const n=new this(e);return s.forEach(i=>n.set(i)),n}static accessor(e){const n=(this[tr]=this[tr]={accessors:{}}).accessors,i=this.prototype;function r(a){const o=Tt(a);n[o]||(nl(i,a),n[o]=!0)}return y.isArray(e)?e.forEach(r):r(e),this}};le.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);y.reduceDescriptors(le.prototype,({value:t},e)=>{let s=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(n){this[s]=n}}});y.freezeMethods(le);function ds(t,e){const s=this||$t,n=e||s,i=le.from(n.headers);let r=n.data;return y.forEach(t,function(o){r=o.call(s,r,i.normalize(),e?e.status:void 0)}),i.normalize(),r}function $a(t){return!!(t&&t.__CANCEL__)}function pt(t,e,s){P.call(this,t??"canceled",P.ERR_CANCELED,e,s),this.name="CanceledError"}y.inherits(pt,P,{__CANCEL__:!0});function Ba(t,e,s){const n=s.config.validateStatus;!s.status||!n||n(s.status)?t(s):e(new P("Request failed with status code "+s.status,[P.ERR_BAD_REQUEST,P.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function sl(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}function il(t,e){t=t||10;const s=new Array(t),n=new Array(t);let i=0,r=0,a;return e=e!==void 0?e:1e3,function(l){const u=Date.now(),d=n[r];a||(a=u),s[i]=l,n[i]=u;let m=r,c=0;for(;m!==i;)c+=s[m++],m=m%t;if(i=(i+1)%t,i===r&&(r=(r+1)%t),u-a<e)return;const h=d&&u-d;return h?Math.round(c*1e3/h):void 0}}function rl(t,e){let s=0,n=1e3/e,i,r;const a=(u,d=Date.now())=>{s=d,i=null,r&&(clearTimeout(r),r=null),t.apply(null,u)};return[(...u)=>{const d=Date.now(),m=d-s;m>=n?a(u,d):(i=u,r||(r=setTimeout(()=>{r=null,a(i)},n-m)))},()=>i&&a(i)]}const En=(t,e,s=3)=>{let n=0;const i=il(50,250);return rl(r=>{const a=r.loaded,o=r.lengthComputable?r.total:void 0,l=a-n,u=i(l),d=a<=o;n=a;const m={loaded:a,total:o,progress:o?a/o:void 0,bytes:l,rate:u||void 0,estimated:u&&o&&d?(o-a)/u:void 0,event:r,lengthComputable:o!=null,[e?"download":"upload"]:!0};t(m)},s)},nr=(t,e)=>{const s=t!=null;return[n=>e[0]({lengthComputable:s,total:t,loaded:n}),e[1]]},sr=t=>(...e)=>y.asap(()=>t(...e)),al=se.hasStandardBrowserEnv?((t,e)=>s=>(s=new URL(s,se.origin),t.protocol===s.protocol&&t.host===s.host&&(e||t.port===s.port)))(new URL(se.origin),se.navigator&&/(msie|trident)/i.test(se.navigator.userAgent)):()=>!0,ol=se.hasStandardBrowserEnv?{write(t,e,s,n,i,r){const a=[t+"="+encodeURIComponent(e)];y.isNumber(s)&&a.push("expires="+new Date(s).toGMTString()),y.isString(n)&&a.push("path="+n),y.isString(i)&&a.push("domain="+i),r===!0&&a.push("secure"),document.cookie=a.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function cl(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function ul(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}function Va(t,e,s){let n=!cl(e);return t&&(n||s==!1)?ul(t,e):e}const ir=t=>t instanceof le?{...t}:t;function Je(t,e){e=e||{};const s={};function n(u,d,m,c){return y.isPlainObject(u)&&y.isPlainObject(d)?y.merge.call({caseless:c},u,d):y.isPlainObject(d)?y.merge({},d):y.isArray(d)?d.slice():d}function i(u,d,m,c){if(y.isUndefined(d)){if(!y.isUndefined(u))return n(void 0,u,m,c)}else return n(u,d,m,c)}function r(u,d){if(!y.isUndefined(d))return n(void 0,d)}function a(u,d){if(y.isUndefined(d)){if(!y.isUndefined(u))return n(void 0,u)}else return n(void 0,d)}function o(u,d,m){if(m in e)return n(u,d);if(m in t)return n(void 0,u)}const l={url:r,method:r,data:r,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:o,headers:(u,d,m)=>i(ir(u),ir(d),m,!0)};return y.forEach(Object.keys(Object.assign({},t,e)),function(d){const m=l[d]||i,c=m(t[d],e[d],d);y.isUndefined(c)&&m!==o||(s[d]=c)}),s}const Ha=t=>{const e=Je({},t);let{data:s,withXSRFToken:n,xsrfHeaderName:i,xsrfCookieName:r,headers:a,auth:o}=e;e.headers=a=le.from(a),e.url=Fa(Va(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),o&&a.set("Authorization","Basic "+btoa((o.username||"")+":"+(o.password?unescape(encodeURIComponent(o.password)):"")));let l;if(y.isFormData(s)){if(se.hasStandardBrowserEnv||se.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((l=a.getContentType())!==!1){const[u,...d]=l?l.split(";").map(m=>m.trim()).filter(Boolean):[];a.setContentType([u||"multipart/form-data",...d].join("; "))}}if(se.hasStandardBrowserEnv&&(n&&y.isFunction(n)&&(n=n(e)),n||n!==!1&&al(e.url))){const u=i&&r&&ol.read(r);u&&a.set(i,u)}return e},ll=typeof XMLHttpRequest<"u",dl=ll&&function(t){return new Promise(function(s,n){const i=Ha(t);let r=i.data;const a=le.from(i.headers).normalize();let{responseType:o,onUploadProgress:l,onDownloadProgress:u}=i,d,m,c,h,f;function p(){h&&h(),f&&f(),i.cancelToken&&i.cancelToken.unsubscribe(d),i.signal&&i.signal.removeEventListener("abort",d)}let g=new XMLHttpRequest;g.open(i.method.toUpperCase(),i.url,!0),g.timeout=i.timeout;function b(){if(!g)return;const S=le.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders()),E={data:!o||o==="text"||o==="json"?g.responseText:g.response,status:g.status,statusText:g.statusText,headers:S,config:t,request:g};Ba(function(D){s(D),p()},function(D){n(D),p()},E),g=null}"onloadend"in g?g.onloadend=b:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout(b)},g.onabort=function(){g&&(n(new P("Request aborted",P.ECONNABORTED,t,g)),g=null)},g.onerror=function(){n(new P("Network Error",P.ERR_NETWORK,t,g)),g=null},g.ontimeout=function(){let w=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const E=i.transitional||Ma;i.timeoutErrorMessage&&(w=i.timeoutErrorMessage),n(new P(w,E.clarifyTimeoutError?P.ETIMEDOUT:P.ECONNABORTED,t,g)),g=null},r===void 0&&a.setContentType(null),"setRequestHeader"in g&&y.forEach(a.toJSON(),function(w,E){g.setRequestHeader(E,w)}),y.isUndefined(i.withCredentials)||(g.withCredentials=!!i.withCredentials),o&&o!=="json"&&(g.responseType=i.responseType),u&&([c,f]=En(u,!0),g.addEventListener("progress",c)),l&&g.upload&&([m,h]=En(l),g.upload.addEventListener("progress",m),g.upload.addEventListener("loadend",h)),(i.cancelToken||i.signal)&&(d=S=>{g&&(n(!S||S.type?new pt(null,t,g):S),g.abort(),g=null)},i.cancelToken&&i.cancelToken.subscribe(d),i.signal&&(i.signal.aborted?d():i.signal.addEventListener("abort",d)));const T=sl(i.url);if(T&&se.protocols.indexOf(T)===-1){n(new P("Unsupported protocol "+T+":",P.ERR_BAD_REQUEST,t));return}g.send(r||null)})},hl=(t,e)=>{const{length:s}=t=t?t.filter(Boolean):[];if(e||s){let n=new AbortController,i;const r=function(u){if(!i){i=!0,o();const d=u instanceof Error?u:this.reason;n.abort(d instanceof P?d:new pt(d instanceof Error?d.message:d))}};let a=e&&setTimeout(()=>{a=null,r(new P(`timeout ${e} of ms exceeded`,P.ETIMEDOUT))},e);const o=()=>{t&&(a&&clearTimeout(a),a=null,t.forEach(u=>{u.unsubscribe?u.unsubscribe(r):u.removeEventListener("abort",r)}),t=null)};t.forEach(u=>u.addEventListener("abort",r));const{signal:l}=n;return l.unsubscribe=()=>y.asap(o),l}},fl=function*(t,e){let s=t.byteLength;if(s<e){yield t;return}let n=0,i;for(;n<s;)i=n+e,yield t.slice(n,i),n=i},pl=async function*(t,e){for await(const s of ml(t))yield*fl(s,e)},ml=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const e=t.getReader();try{for(;;){const{done:s,value:n}=await e.read();if(s)break;yield n}}finally{await e.cancel()}},rr=(t,e,s,n)=>{const i=pl(t,e);let r=0,a,o=l=>{a||(a=!0,n&&n(l))};return new ReadableStream({async pull(l){try{const{done:u,value:d}=await i.next();if(u){o(),l.close();return}let m=d.byteLength;if(s){let c=r+=m;s(c)}l.enqueue(new Uint8Array(d))}catch(u){throw o(u),u}},cancel(l){return o(l),i.return()}},{highWaterMark:2})},Hn=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",qa=Hn&&typeof ReadableStream=="function",gl=Hn&&(typeof TextEncoder=="function"?(t=>e=>t.encode(e))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),Ga=(t,...e)=>{try{return!!t(...e)}catch{return!1}},bl=qa&&Ga(()=>{let t=!1;const e=new Request(se.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),ar=64*1024,Ls=qa&&Ga(()=>y.isReadableStream(new Response("").body)),Rn={stream:Ls&&(t=>t.body)};Hn&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!Rn[e]&&(Rn[e]=y.isFunction(t[e])?s=>s[e]():(s,n)=>{throw new P(`Response type '${e}' is not supported`,P.ERR_NOT_SUPPORT,n)})})})(new Response);const yl=async t=>{if(t==null)return 0;if(y.isBlob(t))return t.size;if(y.isSpecCompliantForm(t))return(await new Request(se.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(y.isArrayBufferView(t)||y.isArrayBuffer(t))return t.byteLength;if(y.isURLSearchParams(t)&&(t=t+""),y.isString(t))return(await gl(t)).byteLength},Sl=async(t,e)=>{const s=y.toFiniteNumber(t.getContentLength());return s??yl(e)},vl=Hn&&(async t=>{let{url:e,method:s,data:n,signal:i,cancelToken:r,timeout:a,onDownloadProgress:o,onUploadProgress:l,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:c}=Ha(t);u=u?(u+"").toLowerCase():"text";let h=hl([i,r&&r.toAbortSignal()],a),f;const p=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(l&&bl&&s!=="get"&&s!=="head"&&(g=await Sl(d,n))!==0){let E=new Request(e,{method:"POST",body:n,duplex:"half"}),_;if(y.isFormData(n)&&(_=E.headers.get("content-type"))&&d.setContentType(_),E.body){const[D,F]=nr(g,En(sr(l)));n=rr(E.body,ar,D,F)}}y.isString(m)||(m=m?"include":"omit");const b="credentials"in Request.prototype;f=new Request(e,{...c,signal:h,method:s.toUpperCase(),headers:d.normalize().toJSON(),body:n,duplex:"half",credentials:b?m:void 0});let T=await fetch(f);const S=Ls&&(u==="stream"||u==="response");if(Ls&&(o||S&&p)){const E={};["status","statusText","headers"].forEach(G=>{E[G]=T[G]});const _=y.toFiniteNumber(T.headers.get("content-length")),[D,F]=o&&nr(_,En(sr(o),!0))||[];T=new Response(rr(T.body,ar,D,()=>{F&&F(),p&&p()}),E)}u=u||"text";let w=await Rn[y.findKey(Rn,u)||"text"](T,t);return!S&&p&&p(),await new Promise((E,_)=>{Ba(E,_,{data:w,headers:le.from(T.headers),status:T.status,statusText:T.statusText,config:t,request:f})})}catch(b){throw p&&p(),b&&b.name==="TypeError"&&/Load failed|fetch/i.test(b.message)?Object.assign(new P("Network Error",P.ERR_NETWORK,t,f),{cause:b.cause||b}):P.from(b,b&&b.code,t,f)}}),Us={http:Lu,xhr:dl,fetch:vl};y.forEach(Us,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch{}Object.defineProperty(t,"adapterName",{value:e})}});const or=t=>`- ${t}`,Tl=t=>y.isFunction(t)||t===null||t===!1,Wa={getAdapter:t=>{t=y.isArray(t)?t:[t];const{length:e}=t;let s,n;const i={};for(let r=0;r<e;r++){s=t[r];let a;if(n=s,!Tl(s)&&(n=Us[(a=String(s)).toLowerCase()],n===void 0))throw new P(`Unknown adapter '${a}'`);if(n)break;i[a||"#"+r]=n}if(!n){const r=Object.entries(i).map(([o,l])=>`adapter ${o} `+(l===!1?"is not supported by the environment":"is not available in the build"));let a=e?r.length>1?`since :
`+r.map(or).join(`
`):" "+or(r[0]):"as no adapter specified";throw new P("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return n},adapters:Us};function hs(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new pt(null,t)}function cr(t){return hs(t),t.headers=le.from(t.headers),t.data=ds.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),Wa.getAdapter(t.adapter||$t.adapter)(t).then(function(n){return hs(t),n.data=ds.call(t,t.transformResponse,n),n.headers=le.from(n.headers),n},function(n){return $a(n)||(hs(t),n&&n.response&&(n.response.data=ds.call(t,t.transformResponse,n.response),n.response.headers=le.from(n.response.headers))),Promise.reject(n)})}const Ja="1.9.0",qn={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{qn[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}});const ur={};qn.transitional=function(e,s,n){function i(r,a){return"[Axios v"+Ja+"] Transitional option '"+r+"'"+a+(n?". "+n:"")}return(r,a,o)=>{if(e===!1)throw new P(i(a," has been removed"+(s?" in "+s:"")),P.ERR_DEPRECATED);return s&&!ur[a]&&(ur[a]=!0,console.warn(i(a," has been deprecated since v"+s+" and will be removed in the near future"))),e?e(r,a,o):!0}};qn.spelling=function(e){return(s,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};function Cl(t,e,s){if(typeof t!="object")throw new P("options must be an object",P.ERR_BAD_OPTION_VALUE);const n=Object.keys(t);let i=n.length;for(;i-- >0;){const r=n[i],a=e[r];if(a){const o=t[r],l=o===void 0||a(o,r,t);if(l!==!0)throw new P("option "+r+" must be "+l,P.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new P("Unknown option "+r,P.ERR_BAD_OPTION)}}const mn={assertOptions:Cl,validators:qn},Ce=mn.validators;let Ge=class{constructor(e){this.defaults=e||{},this.interceptors={request:new er,response:new er}}async request(e,s){try{return await this._request(e,s)}catch(n){if(n instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const r=i.stack?i.stack.replace(/^.+\n/,""):"";try{n.stack?r&&!String(n.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+r):n.stack=r}catch{}}throw n}}_request(e,s){typeof e=="string"?(s=s||{},s.url=e):s=e||{},s=Je(this.defaults,s);const{transitional:n,paramsSerializer:i,headers:r}=s;n!==void 0&&mn.assertOptions(n,{silentJSONParsing:Ce.transitional(Ce.boolean),forcedJSONParsing:Ce.transitional(Ce.boolean),clarifyTimeoutError:Ce.transitional(Ce.boolean)},!1),i!=null&&(y.isFunction(i)?s.paramsSerializer={serialize:i}:mn.assertOptions(i,{encode:Ce.function,serialize:Ce.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),mn.assertOptions(s,{baseUrl:Ce.spelling("baseURL"),withXsrfToken:Ce.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let a=r&&y.merge(r.common,r[s.method]);r&&y.forEach(["delete","get","head","post","put","patch","common"],f=>{delete r[f]}),s.headers=le.concat(a,r);const o=[];let l=!0;this.interceptors.request.forEach(function(p){typeof p.runWhen=="function"&&p.runWhen(s)===!1||(l=l&&p.synchronous,o.unshift(p.fulfilled,p.rejected))});const u=[];this.interceptors.response.forEach(function(p){u.push(p.fulfilled,p.rejected)});let d,m=0,c;if(!l){const f=[cr.bind(this),void 0];for(f.unshift.apply(f,o),f.push.apply(f,u),c=f.length,d=Promise.resolve(s);m<c;)d=d.then(f[m++],f[m++]);return d}c=o.length;let h=s;for(m=0;m<c;){const f=o[m++],p=o[m++];try{h=f(h)}catch(g){p.call(this,g);break}}try{d=cr.call(this,h)}catch(f){return Promise.reject(f)}for(m=0,c=u.length;m<c;)d=d.then(u[m++],u[m++]);return d}getUri(e){e=Je(this.defaults,e);const s=Va(e.baseURL,e.url,e.allowAbsoluteUrls);return Fa(s,e.params,e.paramsSerializer)}};y.forEach(["delete","get","head","options"],function(e){Ge.prototype[e]=function(s,n){return this.request(Je(n||{},{method:e,url:s,data:(n||{}).data}))}});y.forEach(["post","put","patch"],function(e){function s(n){return function(r,a,o){return this.request(Je(o||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:r,data:a}))}}Ge.prototype[e]=s(),Ge.prototype[e+"Form"]=s(!0)});let kl=class Ka{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(r){s=r});const n=this;this.promise.then(i=>{if(!n._listeners)return;let r=n._listeners.length;for(;r-- >0;)n._listeners[r](i);n._listeners=null}),this.promise.then=i=>{let r;const a=new Promise(o=>{n.subscribe(o),r=o}).then(i);return a.cancel=function(){n.unsubscribe(r)},a},e(function(r,a,o){n.reason||(n.reason=new pt(r,a,o),s(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const s=this._listeners.indexOf(e);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const e=new AbortController,s=n=>{e.abort(n)};return this.subscribe(s),e.signal.unsubscribe=()=>this.unsubscribe(s),e.signal}static source(){let e;return{token:new Ka(function(i){e=i}),cancel:e}}};function wl(t){return function(s){return t.apply(null,s)}}function El(t){return y.isObject(t)&&t.isAxiosError===!0}const Fs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Fs).forEach(([t,e])=>{Fs[e]=t});function za(t){const e=new Ge(t),s=Ea(Ge.prototype.request,e);return y.extend(s,Ge.prototype,e,{allOwnKeys:!0}),y.extend(s,e,null,{allOwnKeys:!0}),s.create=function(i){return za(Je(t,i))},s}const z=za($t);z.Axios=Ge;z.CanceledError=pt;z.CancelToken=kl;z.isCancel=$a;z.VERSION=Ja;z.toFormData=Vn;z.AxiosError=P;z.Cancel=z.CanceledError;z.all=function(e){return Promise.all(e)};z.spread=wl;z.isAxiosError=El;z.mergeConfig=Je;z.AxiosHeaders=le;z.formToJSON=t=>ja(y.isHTMLForm(t)?new FormData(t):t);z.getAdapter=Wa.getAdapter;z.HttpStatusCode=Fs;z.default=z;const{Axios:Yg,AxiosError:Xg,CanceledError:Zg,isCancel:Qg,CancelToken:eb,VERSION:tb,all:nb,Cancel:sb,isAxiosError:ib,spread:rb,toFormData:ab,AxiosHeaders:ob,HttpStatusCode:cb,formToJSON:ub,getAdapter:lb,mergeConfig:db}=z;var q;(function(t){t[t.cancelled=0]="cancelled",t[t.unknown=1]="unknown",t[t.invalid_argument=2]="invalid_argument",t[t.malformed=3]="malformed",t[t.deadline_exceeded=4]="deadline_exceeded",t[t.not_found=5]="not_found",t[t.bad_route=6]="bad_route",t[t.already_exists=7]="already_exists",t[t.permission_denied=8]="permission_denied",t[t.unauthenticated=9]="unauthenticated",t[t.resource_exhausted=10]="resource_exhausted",t[t.failed_precondition=11]="failed_precondition",t[t.aborted=12]="aborted",t[t.out_of_range=13]="out_of_range",t[t.unimplemented=14]="unimplemented",t[t.internal=15]="internal",t[t.unavailable=16]="unavailable",t[t.dataloss=17]="dataloss"})(q||(q={}));function Rl(t,e,s){if(s)for(let[n,i]of Object.entries(s))if(typeof i=="string")t.append(n,i);else for(let r of i)t.append(n,r);return t.set("Content-Type",e?"application/json":"application/protobuf"),t.set("Accept",e?"application/json":"application/protobuf, application/json"),t}function _l(t){if(!Sn(t)||typeof t.code!="string"||typeof t.msg!="string")return new ce("cannot read twirp error response",q[q.internal]);let e={};if(Sn(t.meta))for(let[s,n]of Object.entries(t.meta))typeof n=="string"&&(e[s]=n);return new ce(t.msg,t.code,e)}function Il(t){let e={};return t.forEach((s,n)=>{n.toLowerCase()!=="content-type"&&n.toLowerCase()!=="content-length"&&(e.hasOwnProperty(n)?e[n].push(s):e[n]=s)}),e}class Ol{constructor(e){this.defaultOptions=e}mergeOptions(e){return Jc(this.defaultOptions,e)}unary(e,s,n){var i,r,a;let o=n,l=this.makeUrl(e,o),u=(i=o.fetchInit)!==null&&i!==void 0?i:{},d=o.sendJson?e.I.toJsonString(s,o.jsonOptions):e.I.toBinary(s,o.binaryOptions),m=new Qt,c=new Qt,h=new Qt,f=new Qt;return globalThis.fetch(l,Object.assign(Object.assign({},u),{method:"POST",headers:Rl(new globalThis.Headers,!!o.sendJson,o.meta),body:d,signal:(r=n.abort)!==null&&r!==void 0?r:null})).then(p=>{m.resolve(Il(p.headers));let g;try{g=p.type}catch{}switch(g){case"error":case"opaque":case"opaqueredirect":throw new ce(`fetch response type ${p.type}`,q[q.unknown])}return p.ok?o.sendJson?p.json().then(b=>e.O.fromJson(b,o.jsonOptions),()=>{throw new ce("unable to read response body as json",q[q.dataloss])}):p.arrayBuffer().then(b=>e.O.fromBinary(new Uint8Array(b),o.binaryOptions),()=>{throw new ce("unable to read response body",q[q.dataloss])}):p.json().then(b=>{throw _l(b)},()=>{throw new ce("received HTTP "+p.status+", unable to read response body as json",q[q.internal])})},p=>{throw p instanceof Error&&p.name==="AbortError"?new ce(p.message,q[q.cancelled]):new ce(p instanceof Error?p.message:p)}).then(p=>{c.resolve(p),h.resolve({code:"OK",detail:""}),f.resolve({})}).catch(p=>{let g=p instanceof ce?p:new ce(p instanceof Error?p.message:p,q[q.internal]);g.methodName=e.name,g.serviceName=e.service.typeName,m.rejectPending(g),c.rejectPending(g),h.rejectPending(g),f.rejectPending(g)}),new zc(e,(a=o.meta)!==null&&a!==void 0?a:{},s,m.promise,c.promise,h.promise,f.promise)}makeUrl(e,s){let n=s.baseUrl;n.endsWith("/")&&(n=n.substring(0,n.length-1));let i=e.name;return s.useProtoMethodName!==!0&&(i=kn(i),i=i.substring(0,1).toUpperCase()+i.substring(1)),`${n}/${e.service.typeName}/${i}`}clientStreaming(e){const s=new ce("Client streaming is not supported by Twirp",q[q.unimplemented]);throw s.methodName=e.name,s.serviceName=e.service.typeName,s}duplex(e){const s=new ce("Duplex streaming is not supported by Twirp",q[q.unimplemented]);throw s.methodName=e.name,s.serviceName=e.service.typeName,s}serverStreaming(e){const s=new ce("Server streaming is not supported by Twirp",q[q.unimplemented]);throw s.methodName=e.name,s.serviceName=e.service.typeName,s}}var Ms=function(t,e){return Ms=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,n){s.__proto__=n}||function(s,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(s[i]=n[i])},Ms(t,e)};function Ee(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");Ms(t,e);function s(){this.constructor=t}t.prototype=e===null?Object.create(e):(s.prototype=e.prototype,new s)}function Pl(t,e,s,n){function i(r){return r instanceof s?r:new s(function(a){a(r)})}return new(s||(s=Promise))(function(r,a){function o(d){try{u(n.next(d))}catch(m){a(m)}}function l(d){try{u(n.throw(d))}catch(m){a(m)}}function u(d){d.done?r(d.value):i(d.value).then(o,l)}u((n=n.apply(t,e||[])).next())})}function Ya(t,e){var s={label:0,sent:function(){if(r[0]&1)throw r[1];return r[1]},trys:[],ops:[]},n,i,r,a=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return a.next=o(0),a.throw=o(1),a.return=o(2),typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function o(u){return function(d){return l([u,d])}}function l(u){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,u[0]&&(s=0)),s;)try{if(n=1,i&&(r=u[0]&2?i.return:u[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,u[1])).done)return r;switch(i=0,r&&(u=[u[0]&2,r.value]),u[0]){case 0:case 1:r=u;break;case 4:return s.label++,{value:u[1],done:!1};case 5:s.label++,i=u[1],u=[0];continue;case 7:u=s.ops.pop(),s.trys.pop();continue;default:if(r=s.trys,!(r=r.length>0&&r[r.length-1])&&(u[0]===6||u[0]===2)){s=0;continue}if(u[0]===3&&(!r||u[1]>r[0]&&u[1]<r[3])){s.label=u[1];break}if(u[0]===6&&s.label<r[1]){s.label=r[1],r=u;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(u);break}r[2]&&s.ops.pop(),s.trys.pop();continue}u=e.call(t,s)}catch(d){u=[6,d],i=0}finally{n=r=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function ct(t){var e=typeof Symbol=="function"&&Symbol.iterator,s=e&&t[e],n=0;if(s)return s.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function Ke(t,e){var s=typeof Symbol=="function"&&t[Symbol.iterator];if(!s)return t;var n=s.call(t),i,r=[],a;try{for(;(e===void 0||e-- >0)&&!(i=n.next()).done;)r.push(i.value)}catch(o){a={error:o}}finally{try{i&&!i.done&&(s=n.return)&&s.call(n)}finally{if(a)throw a.error}}return r}function ut(t,e,s){if(s||arguments.length===2)for(var n=0,i=e.length,r;n<i;n++)(r||!(n in e))&&(r||(r=Array.prototype.slice.call(e,0,n)),r[n]=e[n]);return t.concat(r||Array.prototype.slice.call(e))}function at(t){return this instanceof at?(this.v=t,this):new at(t)}function Al(t,e,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=s.apply(t,e||[]),i,r=[];return i=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",a),i[Symbol.asyncIterator]=function(){return this},i;function a(h){return function(f){return Promise.resolve(f).then(h,m)}}function o(h,f){n[h]&&(i[h]=function(p){return new Promise(function(g,b){r.push([h,p,g,b])>1||l(h,p)})},f&&(i[h]=f(i[h])))}function l(h,f){try{u(n[h](f))}catch(p){c(r[0][3],p)}}function u(h){h.value instanceof at?Promise.resolve(h.value.v).then(d,m):c(r[0][2],h)}function d(h){l("next",h)}function m(h){l("throw",h)}function c(h,f){h(f),r.shift(),r.length&&l(r[0][0],r[0][1])}}function Nl(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],s;return e?e.call(t):(t=typeof ct=="function"?ct(t):t[Symbol.iterator](),s={},n("next"),n("throw"),n("return"),s[Symbol.asyncIterator]=function(){return this},s);function n(r){s[r]=t[r]&&function(a){return new Promise(function(o,l){a=t[r](a),i(o,l,a.done,a.value)})}}function i(r,a,o,l){Promise.resolve(l).then(function(u){r({value:u,done:o})},a)}}function $(t){return typeof t=="function"}function Xa(t){var e=function(n){Error.call(n),n.stack=new Error().stack},s=t(e);return s.prototype=Object.create(Error.prototype),s.prototype.constructor=s,s}var fs=Xa(function(t){return function(s){t(this),this.message=s?s.length+` errors occurred during unsubscription:
`+s.map(function(n,i){return i+1+") "+n.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=s}});function _n(t,e){if(t){var s=t.indexOf(e);0<=s&&t.splice(s,1)}}var Bt=function(){function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){var e,s,n,i,r;if(!this.closed){this.closed=!0;var a=this._parentage;if(a)if(this._parentage=null,Array.isArray(a))try{for(var o=ct(a),l=o.next();!l.done;l=o.next()){var u=l.value;u.remove(this)}}catch(p){e={error:p}}finally{try{l&&!l.done&&(s=o.return)&&s.call(o)}finally{if(e)throw e.error}}else a.remove(this);var d=this.initialTeardown;if($(d))try{d()}catch(p){r=p instanceof fs?p.errors:[p]}var m=this._finalizers;if(m){this._finalizers=null;try{for(var c=ct(m),h=c.next();!h.done;h=c.next()){var f=h.value;try{lr(f)}catch(p){r=r??[],p instanceof fs?r=ut(ut([],Ke(r)),Ke(p.errors)):r.push(p)}}}catch(p){n={error:p}}finally{try{h&&!h.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}}if(r)throw new fs(r)}},t.prototype.add=function(e){var s;if(e&&e!==this)if(this.closed)lr(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=(s=this._finalizers)!==null&&s!==void 0?s:[]).push(e)}},t.prototype._hasParent=function(e){var s=this._parentage;return s===e||Array.isArray(s)&&s.includes(e)},t.prototype._addParent=function(e){var s=this._parentage;this._parentage=Array.isArray(s)?(s.push(e),s):s?[s,e]:e},t.prototype._removeParent=function(e){var s=this._parentage;s===e?this._parentage=null:Array.isArray(s)&&_n(s,e)},t.prototype.remove=function(e){var s=this._finalizers;s&&_n(s,e),e instanceof t&&e._removeParent(this)},t.EMPTY=function(){var e=new t;return e.closed=!0,e}(),t}(),Za=Bt.EMPTY;function Qa(t){return t instanceof Bt||t&&"closed"in t&&$(t.remove)&&$(t.add)&&$(t.unsubscribe)}function lr(t){$(t)?t():t.unsubscribe()}var Dl={Promise:void 0},xl={setTimeout:function(t,e){for(var s=[],n=2;n<arguments.length;n++)s[n-2]=arguments[n];return setTimeout.apply(void 0,ut([t,e],Ke(s)))},clearTimeout:function(t){return clearTimeout(t)},delegate:void 0};function eo(t){xl.setTimeout(function(){throw t})}function dr(){}function gn(t){t()}var ri=function(t){Ee(e,t);function e(s){var n=t.call(this)||this;return n.isStopped=!1,s?(n.destination=s,Qa(s)&&s.add(n)):n.destination=Fl,n}return e.create=function(s,n,i){return new At(s,n,i)},e.prototype.next=function(s){this.isStopped||this._next(s)},e.prototype.error=function(s){this.isStopped||(this.isStopped=!0,this._error(s))},e.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},e.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this),this.destination=null)},e.prototype._next=function(s){this.destination.next(s)},e.prototype._error=function(s){try{this.destination.error(s)}finally{this.unsubscribe()}},e.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},e}(Bt),Ll=function(){function t(e){this.partialObserver=e}return t.prototype.next=function(e){var s=this.partialObserver;if(s.next)try{s.next(e)}catch(n){en(n)}},t.prototype.error=function(e){var s=this.partialObserver;if(s.error)try{s.error(e)}catch(n){en(n)}else en(e)},t.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(s){en(s)}},t}(),At=function(t){Ee(e,t);function e(s,n,i){var r=t.call(this)||this,a;return $(s)||!s?a={next:s??void 0,error:n??void 0,complete:i??void 0}:a=s,r.destination=new Ll(a),r}return e}(ri);function en(t){eo(t)}function Ul(t){throw t}var Fl={closed:!0,next:dr,error:Ul,complete:dr},ai=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function Vt(t){return t}function Ml(t){return t.length===0?Vt:t.length===1?t[0]:function(s){return t.reduce(function(n,i){return i(n)},s)}}var oe=function(){function t(e){e&&(this._subscribe=e)}return t.prototype.lift=function(e){var s=new t;return s.source=this,s.operator=e,s},t.prototype.subscribe=function(e,s,n){var i=this,r=$l(e)?e:new At(e,s,n);return gn(function(){var a=i,o=a.operator,l=a.source;r.add(o?o.call(r,l):l?i._subscribe(r):i._trySubscribe(r))}),r},t.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(s){e.error(s)}},t.prototype.forEach=function(e,s){var n=this;return s=hr(s),new s(function(i,r){var a=new At({next:function(o){try{e(o)}catch(l){r(l),a.unsubscribe()}},error:r,complete:i});n.subscribe(a)})},t.prototype._subscribe=function(e){var s;return(s=this.source)===null||s===void 0?void 0:s.subscribe(e)},t.prototype[ai]=function(){return this},t.prototype.pipe=function(){for(var e=[],s=0;s<arguments.length;s++)e[s]=arguments[s];return Ml(e)(this)},t.prototype.toPromise=function(e){var s=this;return e=hr(e),new e(function(n,i){var r;s.subscribe(function(a){return r=a},function(a){return i(a)},function(){return n(r)})})},t.create=function(e){return new t(e)},t}();function hr(t){var e;return(e=t??Dl.Promise)!==null&&e!==void 0?e:Promise}function jl(t){return t&&$(t.next)&&$(t.error)&&$(t.complete)}function $l(t){return t&&t instanceof ri||jl(t)&&Qa(t)}function Bl(t){return $(t==null?void 0:t.lift)}function Re(t){return function(e){if(Bl(e))return e.lift(function(s){try{return t(s,this)}catch(n){this.error(n)}});throw new TypeError("Unable to lift unknown Observable type")}}function Ae(t,e,s,n,i){return new Vl(t,e,s,n,i)}var Vl=function(t){Ee(e,t);function e(s,n,i,r,a,o){var l=t.call(this,s)||this;return l.onFinalize=a,l.shouldUnsubscribe=o,l._next=n?function(u){try{n(u)}catch(d){s.error(d)}}:t.prototype._next,l._error=r?function(u){try{r(u)}catch(d){s.error(d)}finally{this.unsubscribe()}}:t.prototype._error,l._complete=i?function(){try{i()}catch(u){s.error(u)}finally{this.unsubscribe()}}:t.prototype._complete,l}return e.prototype.unsubscribe=function(){var s;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var n=this.closed;t.prototype.unsubscribe.call(this),!n&&((s=this.onFinalize)===null||s===void 0||s.call(this))}},e}(ri),Hl=Xa(function(t){return function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),Gn=function(t){Ee(e,t);function e(){var s=t.call(this)||this;return s.closed=!1,s.currentObservers=null,s.observers=[],s.isStopped=!1,s.hasError=!1,s.thrownError=null,s}return e.prototype.lift=function(s){var n=new fr(this,this);return n.operator=s,n},e.prototype._throwIfClosed=function(){if(this.closed)throw new Hl},e.prototype.next=function(s){var n=this;gn(function(){var i,r;if(n._throwIfClosed(),!n.isStopped){n.currentObservers||(n.currentObservers=Array.from(n.observers));try{for(var a=ct(n.currentObservers),o=a.next();!o.done;o=a.next()){var l=o.value;l.next(s)}}catch(u){i={error:u}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}}})},e.prototype.error=function(s){var n=this;gn(function(){if(n._throwIfClosed(),!n.isStopped){n.hasError=n.isStopped=!0,n.thrownError=s;for(var i=n.observers;i.length;)i.shift().error(s)}})},e.prototype.complete=function(){var s=this;gn(function(){if(s._throwIfClosed(),!s.isStopped){s.isStopped=!0;for(var n=s.observers;n.length;)n.shift().complete()}})},e.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(e.prototype,"observed",{get:function(){var s;return((s=this.observers)===null||s===void 0?void 0:s.length)>0},enumerable:!1,configurable:!0}),e.prototype._trySubscribe=function(s){return this._throwIfClosed(),t.prototype._trySubscribe.call(this,s)},e.prototype._subscribe=function(s){return this._throwIfClosed(),this._checkFinalizedStatuses(s),this._innerSubscribe(s)},e.prototype._innerSubscribe=function(s){var n=this,i=this,r=i.hasError,a=i.isStopped,o=i.observers;return r||a?Za:(this.currentObservers=null,o.push(s),new Bt(function(){n.currentObservers=null,_n(o,s)}))},e.prototype._checkFinalizedStatuses=function(s){var n=this,i=n.hasError,r=n.thrownError,a=n.isStopped;i?s.error(r):a&&s.complete()},e.prototype.asObservable=function(){var s=new oe;return s.source=this,s},e.create=function(s,n){return new fr(s,n)},e}(oe),fr=function(t){Ee(e,t);function e(s,n){var i=t.call(this)||this;return i.destination=s,i.source=n,i}return e.prototype.next=function(s){var n,i;(i=(n=this.destination)===null||n===void 0?void 0:n.next)===null||i===void 0||i.call(n,s)},e.prototype.error=function(s){var n,i;(i=(n=this.destination)===null||n===void 0?void 0:n.error)===null||i===void 0||i.call(n,s)},e.prototype.complete=function(){var s,n;(n=(s=this.destination)===null||s===void 0?void 0:s.complete)===null||n===void 0||n.call(s)},e.prototype._subscribe=function(s){var n,i;return(i=(n=this.source)===null||n===void 0?void 0:n.subscribe(s))!==null&&i!==void 0?i:Za},e}(Gn),A=function(t){Ee(e,t);function e(s){var n=t.call(this)||this;return n._value=s,n}return Object.defineProperty(e.prototype,"value",{get:function(){return this.getValue()},enumerable:!1,configurable:!0}),e.prototype._subscribe=function(s){var n=t.prototype._subscribe.call(this,s);return!n.closed&&s.next(this._value),n},e.prototype.getValue=function(){var s=this,n=s.hasError,i=s.thrownError,r=s._value;if(n)throw i;return this._throwIfClosed(),r},e.prototype.next=function(s){t.prototype.next.call(this,this._value=s)},e}(Gn),oi={now:function(){return(oi.delegate||Date).now()},delegate:void 0},js=function(t){Ee(e,t);function e(s,n,i){s===void 0&&(s=1/0),n===void 0&&(n=1/0),i===void 0&&(i=oi);var r=t.call(this)||this;return r._bufferSize=s,r._windowTime=n,r._timestampProvider=i,r._buffer=[],r._infiniteTimeWindow=!0,r._infiniteTimeWindow=n===1/0,r._bufferSize=Math.max(1,s),r._windowTime=Math.max(1,n),r}return e.prototype.next=function(s){var n=this,i=n.isStopped,r=n._buffer,a=n._infiniteTimeWindow,o=n._timestampProvider,l=n._windowTime;i||(r.push(s),!a&&r.push(o.now()+l)),this._trimBuffer(),t.prototype.next.call(this,s)},e.prototype._subscribe=function(s){this._throwIfClosed(),this._trimBuffer();for(var n=this._innerSubscribe(s),i=this,r=i._infiniteTimeWindow,a=i._buffer,o=a.slice(),l=0;l<o.length&&!s.closed;l+=r?1:2)s.next(o[l]);return this._checkFinalizedStatuses(s),n},e.prototype._trimBuffer=function(){var s=this,n=s._bufferSize,i=s._timestampProvider,r=s._buffer,a=s._infiniteTimeWindow,o=(a?1:2)*n;if(n<1/0&&o<r.length&&r.splice(0,r.length-o),!a){for(var l=i.now(),u=0,d=1;d<r.length&&r[d]<=l;d+=2)u=d;u&&r.splice(0,u+1)}},e}(Gn),ql=function(t){Ee(e,t);function e(s,n){return t.call(this)||this}return e.prototype.schedule=function(s,n){return this},e}(Bt),pr={setInterval:function(t,e){for(var s=[],n=2;n<arguments.length;n++)s[n-2]=arguments[n];return setInterval.apply(void 0,ut([t,e],Ke(s)))},clearInterval:function(t){return clearInterval(t)},delegate:void 0},Gl=function(t){Ee(e,t);function e(s,n){var i=t.call(this,s,n)||this;return i.scheduler=s,i.work=n,i.pending=!1,i}return e.prototype.schedule=function(s,n){var i;if(n===void 0&&(n=0),this.closed)return this;this.state=s;var r=this.id,a=this.scheduler;return r!=null&&(this.id=this.recycleAsyncId(a,r,n)),this.pending=!0,this.delay=n,this.id=(i=this.id)!==null&&i!==void 0?i:this.requestAsyncId(a,this.id,n),this},e.prototype.requestAsyncId=function(s,n,i){return i===void 0&&(i=0),pr.setInterval(s.flush.bind(s,this),i)},e.prototype.recycleAsyncId=function(s,n,i){if(i===void 0&&(i=0),i!=null&&this.delay===i&&this.pending===!1)return n;n!=null&&pr.clearInterval(n)},e.prototype.execute=function(s,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var i=this._execute(s,n);if(i)return i;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},e.prototype._execute=function(s,n){var i=!1,r;try{this.work(s)}catch(a){i=!0,r=a||new Error("Scheduled action threw falsy error")}if(i)return this.unsubscribe(),r},e.prototype.unsubscribe=function(){if(!this.closed){var s=this,n=s.id,i=s.scheduler,r=i.actions;this.work=this.state=this.scheduler=null,this.pending=!1,_n(r,this),n!=null&&(this.id=this.recycleAsyncId(i,n,null)),this.delay=null,t.prototype.unsubscribe.call(this)}},e}(ql),mr=function(){function t(e,s){s===void 0&&(s=t.now),this.schedulerActionCtor=e,this.now=s}return t.prototype.schedule=function(e,s,n){return s===void 0&&(s=0),new this.schedulerActionCtor(this,e).schedule(n,s)},t.now=oi.now,t}(),Wl=function(t){Ee(e,t);function e(s,n){n===void 0&&(n=mr.now);var i=t.call(this,s,n)||this;return i.actions=[],i._active=!1,i}return e.prototype.flush=function(s){var n=this.actions;if(this._active){n.push(s);return}var i;this._active=!0;do if(i=s.execute(s.state,s.delay))break;while(s=n.shift());if(this._active=!1,i){for(;s=n.shift();)s.unsubscribe();throw i}},e}(mr),Jl=new Wl(Gl),Kl=new oe(function(t){return t.complete()});function zl(t){return t&&$(t.schedule)}function ci(t){return t[t.length-1]}function Yl(t){return $(ci(t))?t.pop():void 0}function Ht(t){return zl(ci(t))?t.pop():void 0}function Xl(t,e){return typeof ci(t)=="number"?t.pop():e}var ui=function(t){return t&&typeof t.length=="number"&&typeof t!="function"};function to(t){return $(t==null?void 0:t.then)}function no(t){return $(t[ai])}function so(t){return Symbol.asyncIterator&&$(t==null?void 0:t[Symbol.asyncIterator])}function io(t){return new TypeError("You provided "+(t!==null&&typeof t=="object"?"an invalid object":"'"+t+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}function Zl(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var ro=Zl();function ao(t){return $(t==null?void 0:t[ro])}function oo(t){return Al(this,arguments,function(){var s,n,i,r;return Ya(this,function(a){switch(a.label){case 0:s=t.getReader(),a.label=1;case 1:a.trys.push([1,,9,10]),a.label=2;case 2:return[4,at(s.read())];case 3:return n=a.sent(),i=n.value,r=n.done,r?[4,at(void 0)]:[3,5];case 4:return[2,a.sent()];case 5:return[4,at(i)];case 6:return[4,a.sent()];case 7:return a.sent(),[3,2];case 8:return[3,10];case 9:return s.releaseLock(),[7];case 10:return[2]}})})}function co(t){return $(t==null?void 0:t.getReader)}function Ne(t){if(t instanceof oe)return t;if(t!=null){if(no(t))return Ql(t);if(ui(t))return ed(t);if(to(t))return td(t);if(so(t))return uo(t);if(ao(t))return nd(t);if(co(t))return sd(t)}throw io(t)}function Ql(t){return new oe(function(e){var s=t[ai]();if($(s.subscribe))return s.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function ed(t){return new oe(function(e){for(var s=0;s<t.length&&!e.closed;s++)e.next(t[s]);e.complete()})}function td(t){return new oe(function(e){t.then(function(s){e.closed||(e.next(s),e.complete())},function(s){return e.error(s)}).then(null,eo)})}function nd(t){return new oe(function(e){var s,n;try{for(var i=ct(t),r=i.next();!r.done;r=i.next()){var a=r.value;if(e.next(a),e.closed)return}}catch(o){s={error:o}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(s)throw s.error}}e.complete()})}function uo(t){return new oe(function(e){id(t,e).catch(function(s){return e.error(s)})})}function sd(t){return uo(oo(t))}function id(t,e){var s,n,i,r;return Pl(this,void 0,void 0,function(){var a,o;return Ya(this,function(l){switch(l.label){case 0:l.trys.push([0,5,6,11]),s=Nl(t),l.label=1;case 1:return[4,s.next()];case 2:if(n=l.sent(),!!n.done)return[3,4];if(a=n.value,e.next(a),e.closed)return[2];l.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return o=l.sent(),i={error:o},[3,11];case 6:return l.trys.push([6,,9,10]),n&&!n.done&&(r=s.return)?[4,r.call(s)]:[3,8];case 7:l.sent(),l.label=8;case 8:return[3,10];case 9:if(i)throw i.error;return[7];case 10:return[7];case 11:return e.complete(),[2]}})})}function Fe(t,e,s,n,i){n===void 0&&(n=0),i===void 0&&(i=!1);var r=e.schedule(function(){s(),i?t.add(this.schedule(null,n)):this.unsubscribe()},n);if(t.add(r),!i)return r}function lo(t,e){return e===void 0&&(e=0),Re(function(s,n){s.subscribe(Ae(n,function(i){return Fe(n,t,function(){return n.next(i)},e)},function(){return Fe(n,t,function(){return n.complete()},e)},function(i){return Fe(n,t,function(){return n.error(i)},e)}))})}function ho(t,e){return e===void 0&&(e=0),Re(function(s,n){n.add(t.schedule(function(){return s.subscribe(n)},e))})}function rd(t,e){return Ne(t).pipe(ho(e),lo(e))}function ad(t,e){return Ne(t).pipe(ho(e),lo(e))}function od(t,e){return new oe(function(s){var n=0;return e.schedule(function(){n===t.length?s.complete():(s.next(t[n++]),s.closed||this.schedule())})})}function cd(t,e){return new oe(function(s){var n;return Fe(s,e,function(){n=t[ro](),Fe(s,e,function(){var i,r,a;try{i=n.next(),r=i.value,a=i.done}catch(o){s.error(o);return}a?s.complete():s.next(r)},0,!0)}),function(){return $(n==null?void 0:n.return)&&n.return()}})}function fo(t,e){if(!t)throw new Error("Iterable cannot be null");return new oe(function(s){Fe(s,e,function(){var n=t[Symbol.asyncIterator]();Fe(s,e,function(){n.next().then(function(i){i.done?s.complete():s.next(i.value)})},0,!0)})})}function ud(t,e){return fo(oo(t),e)}function ld(t,e){if(t!=null){if(no(t))return rd(t,e);if(ui(t))return od(t,e);if(to(t))return ad(t,e);if(so(t))return fo(t,e);if(ao(t))return cd(t,e);if(co(t))return ud(t,e)}throw io(t)}function Ze(t,e){return e?ld(t,e):Ne(t)}function bn(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var s=Ht(t);return Ze(t,s)}function ne(t,e){return Re(function(s,n){var i=0;s.subscribe(Ae(n,function(r){n.next(t.call(e,r,i++))}))})}var dd=Array.isArray;function hd(t,e){return dd(e)?t.apply(void 0,ut([],Ke(e))):t(e)}function po(t){return ne(function(e){return hd(t,e)})}var fd=Array.isArray,pd=Object.getPrototypeOf,md=Object.prototype,gd=Object.keys;function bd(t){if(t.length===1){var e=t[0];if(fd(e))return{args:e,keys:null};if(yd(e)){var s=gd(e);return{args:s.map(function(n){return e[n]}),keys:s}}}return{args:t,keys:null}}function yd(t){return t&&typeof t=="object"&&pd(t)===md}function Sd(t,e){return t.reduce(function(s,n,i){return s[n]=e[i],s},{})}function ze(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var s=Ht(t),n=Yl(t),i=bd(t),r=i.args,a=i.keys;if(r.length===0)return Ze([],s);var o=new oe(vd(r,s,a?function(l){return Sd(a,l)}:Vt));return n?o.pipe(po(n)):o}function vd(t,e,s){return s===void 0&&(s=Vt),function(n){gr(e,function(){for(var i=t.length,r=new Array(i),a=i,o=i,l=function(d){gr(e,function(){var m=Ze(t[d],e),c=!1;m.subscribe(Ae(n,function(h){r[d]=h,c||(c=!0,o--),o||n.next(s(r.slice()))},function(){--a||n.complete()}))},n)},u=0;u<i;u++)l(u)},n)}}function gr(t,e,s){t?Fe(s,t,e):e()}function Td(t,e,s,n,i,r,a,o){var l=[],u=0,d=0,m=!1,c=function(){m&&!l.length&&!u&&e.complete()},h=function(p){return u<n?f(p):l.push(p)},f=function(p){u++;var g=!1;Ne(s(p,d++)).subscribe(Ae(e,function(b){e.next(b)},function(){g=!0},void 0,function(){if(g)try{u--;for(var b=function(){var T=l.shift();a||f(T)};l.length&&u<n;)b();c()}catch(T){e.error(T)}}))};return t.subscribe(Ae(e,h,function(){m=!0,c()})),function(){}}function Nt(t,e,s){return s===void 0&&(s=1/0),$(e)?Nt(function(n,i){return ne(function(r,a){return e(n,r,i,a)})(Ne(t(n,i)))},s):(typeof e=="number"&&(s=e),Re(function(n,i){return Td(n,i,t,s)}))}function mo(t){return t===void 0&&(t=1/0),Nt(Vt,t)}function Cd(){return mo(1)}function br(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Cd()(Ze(t,Ht(t)))}var kd=["addListener","removeListener"],wd=["addEventListener","removeEventListener"],Ed=["on","off"];function $s(t,e,s,n){if($(s)&&(n=s,s=void 0),n)return $s(t,e,s).pipe(po(n));var i=Ke(Id(t)?wd.map(function(o){return function(l){return t[o](e,l,s)}}):Rd(t)?kd.map(yr(t,e)):_d(t)?Ed.map(yr(t,e)):[],2),r=i[0],a=i[1];if(!r&&ui(t))return Nt(function(o){return $s(o,e,s)})(Ne(t));if(!r)throw new TypeError("Invalid event target");return new oe(function(o){var l=function(){for(var u=[],d=0;d<arguments.length;d++)u[d]=arguments[d];return o.next(1<u.length?u:u[0])};return r(l),function(){return a(l)}})}function yr(t,e){return function(s){return function(n){return t[s](e,n)}}}function Rd(t){return $(t.addListener)&&$(t.removeListener)}function _d(t){return $(t.on)&&$(t.off)}function Id(t){return $(t.addEventListener)&&$(t.removeEventListener)}function Od(t,e,s){return new oe(function(n){var i=function(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];return n.next(a.length===1?a[0]:a)},r=t(i);return $(e)?function(){return e(i,r)}:void 0})}function li(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var s=Ht(t),n=Xl(t,1/0),i=t;return i.length?i.length===1?Ne(i[0]):mo(n)(Ze(i,s)):Kl}function Wn(t,e){return $(e)?Nt(t,e,1):Nt(t,1)}function Pd(t,e){return e===void 0&&(e=Jl),Re(function(s,n){var i=null,r=null,a=null,o=function(){if(i){i.unsubscribe(),i=null;var u=r;r=null,n.next(u)}};function l(){var u=a+t,d=e.now();if(d<u){i=this.schedule(void 0,u-d),n.add(i);return}o()}s.subscribe(Ae(n,function(u){r=u,a=e.now(),i||(i=e.schedule(l,t),n.add(i))},function(){o(),n.complete()},void 0,function(){r=i=null}))})}function ae(t,e){return e===void 0&&(e=Vt),t=t??Ad,Re(function(s,n){var i,r=!0;s.subscribe(Ae(n,function(a){var o=e(a);(r||!t(i,o))&&(r=!1,i=o,n.next(a))}))})}function Ad(t,e){return t===e}function tn(t,e){return ae(function(s,n){return s[t]===n[t]})}function Nd(){return Re(function(t,e){var s,n=!1;t.subscribe(Ae(e,function(i){var r=s;s=i,n&&e.next([r,i]),n=!0}))})}function Dd(t){t===void 0&&(t={});var e=t.connector,s=e===void 0?function(){return new Gn}:e,n=t.resetOnError,i=n===void 0?!0:n,r=t.resetOnComplete,a=r===void 0?!0:r,o=t.resetOnRefCountZero,l=o===void 0?!0:o;return function(u){var d,m,c,h=0,f=!1,p=!1,g=function(){m==null||m.unsubscribe(),m=void 0},b=function(){g(),d=c=void 0,f=p=!1},T=function(){var S=d;b(),S==null||S.unsubscribe()};return Re(function(S,w){h++,!p&&!f&&g();var E=c=c??s();w.add(function(){h--,h===0&&!p&&!f&&(m=ps(T,l))}),E.subscribe(w),!d&&h>0&&(d=new At({next:function(_){return E.next(_)},error:function(_){p=!0,g(),m=ps(b,i,_),E.error(_)},complete:function(){f=!0,g(),m=ps(b,a),E.complete()}}),Ne(S).subscribe(d))})(u)}}function ps(t,e){for(var s=[],n=2;n<arguments.length;n++)s[n-2]=arguments[n];if(e===!0){t();return}if(e!==!1){var i=new At({next:function(){i.unsubscribe(),t()}});return Ne(e.apply(void 0,ut([],Ke(s)))).subscribe(i)}}function te(t,e,s){var n,i,r,a,o=!1;return t&&typeof t=="object"?(n=t.bufferSize,a=n===void 0?1/0:n,i=t.windowTime,e=i===void 0?1/0:i,r=t.refCount,o=r===void 0?!1:r,s=t.scheduler):a=t??1/0,Dd({connector:function(){return new js(a,e,s)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function Jn(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var s=Ht(t);return Re(function(n,i){(s?br(t,n,s):br(t,n)).subscribe(i)})}function Bs(t,e){return e===void 0&&(e=!1),Re(function(s,n){var i=0;s.subscribe(Ae(n,function(r){var a=t(r,i++);(a||e)&&n.next(r),!a&&n.complete()}))})}var Rt={exports:{}},xd=Rt.exports,Sr;function Ld(){return Sr||(Sr=1,function(t,e){(function(s,n){var i="1.0.40",r="",a="?",o="function",l="undefined",u="object",d="string",m="major",c="model",h="name",f="type",p="vendor",g="version",b="architecture",T="console",S="mobile",w="tablet",E="smarttv",_="wearable",D="embedded",F=500,G="Amazon",re="Apple",fe="ASUS",W="BlackBerry",$e="Browser",xe="Chrome",Gt="Edge",et="Firefox",Be="Google",Le="Huawei",gt="LG",bt="Microsoft",Ri="Motorola",yt="Opera",St="Samsung",_i="Sharp",Wt="Sony",ts="Xiaomi",ns="Zebra",Ii="Facebook",Oi="Chromium OS",Pi="Mac OS",Ai=" Browser",Vo=function(x,L){var O={};for(var V in x)L[V]&&L[V].length%2===0?O[V]=L[V].concat(x[V]):O[V]=x[V];return O},Jt=function(x){for(var L={},O=0;O<x.length;O++)L[x[O].toUpperCase()]=x[O];return L},Ni=function(x,L){return typeof x===d?tt(L).indexOf(tt(x))!==-1:!1},tt=function(x){return x.toLowerCase()},Ho=function(x){return typeof x===d?x.replace(/[^\d\.]/g,r).split(".")[0]:n},ss=function(x,L){if(typeof x===d)return x=x.replace(/^\s\s*/,r),typeof L===l?x:x.substring(0,F)},vt=function(x,L){for(var O=0,V,Ie,ve,M,I,Te;O<L.length&&!I;){var is=L[O],Li=L[O+1];for(V=Ie=0;V<is.length&&!I&&is[V];)if(I=is[V++].exec(x),I)for(ve=0;ve<Li.length;ve++)Te=I[++Ie],M=Li[ve],typeof M===u&&M.length>0?M.length===2?typeof M[1]==o?this[M[0]]=M[1].call(this,Te):this[M[0]]=M[1]:M.length===3?typeof M[1]===o&&!(M[1].exec&&M[1].test)?this[M[0]]=Te?M[1].call(this,Te,M[2]):n:this[M[0]]=Te?Te.replace(M[1],M[2]):n:M.length===4&&(this[M[0]]=Te?M[3].call(this,Te.replace(M[1],M[2])):n):this[M]=Te||n;O+=2}},Kt=function(x,L){for(var O in L)if(typeof L[O]===u&&L[O].length>0){for(var V=0;V<L[O].length;V++)if(Ni(L[O][V],x))return O===a?n:O}else if(Ni(L[O],x))return O===a?n:O;return L.hasOwnProperty("*")?L["*"]:x},qo={"1.0":"/8","1.2":"/1","1.3":"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},Di={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2","8.1":"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},xi={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[g,[h,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[g,[h,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[h,g],[/opios[\/ ]+([\w\.]+)/i],[g,[h,yt+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[g,[h,yt+" GX"]],[/\bopr\/([\w\.]+)/i],[g,[h,yt]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[g,[h,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[g,[h,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,/(heytap|ovi|115)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[h,g],[/quark(?:pc)?\/([-\w\.]+)/i],[g,[h,"Quark"]],[/\bddg\/([\w\.]+)/i],[g,[h,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[g,[h,"UC"+$e]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[g,[h,"WeChat"]],[/konqueror\/([\w\.]+)/i],[g,[h,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[g,[h,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[g,[h,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[g,[h,"Smart Lenovo "+$e]],[/(avast|avg)\/([\w\.]+)/i],[[h,/(.+)/,"$1 Secure "+$e],g],[/\bfocus\/([\w\.]+)/i],[g,[h,et+" Focus"]],[/\bopt\/([\w\.]+)/i],[g,[h,yt+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[g,[h,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[g,[h,"Dolphin"]],[/coast\/([\w\.]+)/i],[g,[h,yt+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[g,[h,"MIUI"+Ai]],[/fxios\/([\w\.-]+)/i],[g,[h,et]],[/\bqihoobrowser\/?([\w\.]*)/i],[g,[h,"360"]],[/\b(qq)\/([\w\.]+)/i],[[h,/(.+)/,"$1Browser"],g],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[h,/(.+)/,"$1"+Ai],g],[/samsungbrowser\/([\w\.]+)/i],[g,[h,St+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[g,[h,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[h,"Sogou Mobile"],g],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[h,g],[/(lbbrowser|rekonq)/i,/\[(linkedin)app\]/i],[h],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[g,h],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[h,Ii],g],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[h,g],[/\bgsa\/([\w\.]+) .*safari\//i],[g,[h,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[g,[h,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[g,[h,xe+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[h,xe+" WebView"],g],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[g,[h,"Android "+$e]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[h,g],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[g,[h,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[g,h],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[h,[g,Kt,qo]],[/(webkit|khtml)\/([\w\.]+)/i],[h,g],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[h,"Netscape"],g],[/(wolvic|librewolf)\/([\w\.]+)/i],[h,g],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[g,[h,et+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[h,[g,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[h,[g,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,tt]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,r,tt]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,tt]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[c,[p,St],[f,w]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[c,[p,St],[f,S]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[c,[p,re],[f,S]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[c,[p,re],[f,w]],[/(macintosh);/i],[c,[p,re]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[c,[p,_i],[f,S]],[/(?:honor)([-\w ]+)[;\)]/i],[c,[p,"Honor"],[f,S]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[c,[p,Le],[f,w]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[c,[p,Le],[f,S]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i],[[c,/_/g," "],[p,ts],[f,S]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[c,/_/g," "],[p,ts],[f,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[c,[p,"OPPO"],[f,S]],[/\b(opd2\d{3}a?) bui/i],[c,[p,"OPPO"],[f,w]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[c,[p,"Vivo"],[f,S]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[c,[p,"Realme"],[f,S]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[c,[p,Ri],[f,S]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[c,[p,Ri],[f,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[c,[p,gt],[f,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[c,[p,gt],[f,S]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[c,[p,"Lenovo"],[f,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[c,/_/g," "],[p,"Nokia"],[f,S]],[/(pixel c)\b/i],[c,[p,Be],[f,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[c,[p,Be],[f,S]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[c,[p,Wt],[f,S]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[c,"Xperia Tablet"],[p,Wt],[f,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[c,[p,"OnePlus"],[f,S]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[c,[p,G],[f,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[c,/(.+)/g,"Fire Phone $1"],[p,G],[f,S]],[/(playbook);[-\w\),; ]+(rim)/i],[c,p,[f,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[c,[p,W],[f,S]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[c,[p,fe],[f,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[c,[p,fe],[f,S]],[/(nexus 9)/i],[c,[p,"HTC"],[f,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[p,[c,/_/g," "],[f,S]],[/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\w*(\)| bui)/i],[c,[p,"TCL"],[f,w]],[/(itel) ((\w+))/i],[[p,tt],c,[f,Kt,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[c,[p,"Acer"],[f,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[c,[p,"Meizu"],[f,S]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[c,[p,"Ulefone"],[f,S]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[c,[p,"Energizer"],[f,S]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[c,[p,"Cat"],[f,S]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[c,[p,"Smartfren"],[f,S]],[/droid.+; (a(?:015|06[35]|142p?))/i],[c,[p,"Nothing"],[f,S]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (imo) ((?!tab)[\w ]+?)(?: bui|\))/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[p,c,[f,S]],[/(imo) (tab \w+)/i,/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[p,c,[f,w]],[/(surface duo)/i],[c,[p,bt],[f,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[c,[p,"Fairphone"],[f,S]],[/(u304aa)/i],[c,[p,"AT&T"],[f,S]],[/\bsie-(\w*)/i],[c,[p,"Siemens"],[f,S]],[/\b(rct\w+) b/i],[c,[p,"RCA"],[f,w]],[/\b(venue[\d ]{2,7}) b/i],[c,[p,"Dell"],[f,w]],[/\b(q(?:mv|ta)\w+) b/i],[c,[p,"Verizon"],[f,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[c,[p,"Barnes & Noble"],[f,w]],[/\b(tm\d{3}\w+) b/i],[c,[p,"NuVision"],[f,w]],[/\b(k88) b/i],[c,[p,"ZTE"],[f,w]],[/\b(nx\d{3}j) b/i],[c,[p,"ZTE"],[f,S]],[/\b(gen\d{3}) b.+49h/i],[c,[p,"Swiss"],[f,S]],[/\b(zur\d{3}) b/i],[c,[p,"Swiss"],[f,w]],[/\b((zeki)?tb.*\b) b/i],[c,[p,"Zeki"],[f,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[p,"Dragon Touch"],c,[f,w]],[/\b(ns-?\w{0,9}) b/i],[c,[p,"Insignia"],[f,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[c,[p,"NextBook"],[f,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[p,"Voice"],c,[f,S]],[/\b(lvtel\-)?(v1[12]) b/i],[[p,"LvTel"],c,[f,S]],[/\b(ph-1) /i],[c,[p,"Essential"],[f,S]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[c,[p,"Envizen"],[f,w]],[/\b(trio[-\w\. ]+) b/i],[c,[p,"MachSpeed"],[f,w]],[/\btu_(1491) b/i],[c,[p,"Rotor"],[f,w]],[/(shield[\w ]+) b/i],[c,[p,"Nvidia"],[f,w]],[/(sprint) (\w+)/i],[p,c,[f,S]],[/(kin\.[onetw]{3})/i],[[c,/\./g," "],[p,bt],[f,S]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[c,[p,ns],[f,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[c,[p,ns],[f,S]],[/smart-tv.+(samsung)/i],[p,[f,E]],[/hbbtv.+maple;(\d+)/i],[[c,/^/,"SmartTV"],[p,St],[f,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[p,gt],[f,E]],[/(apple) ?tv/i],[p,[c,re+" TV"],[f,E]],[/crkey/i],[[c,xe+"cast"],[p,Be],[f,E]],[/droid.+aft(\w+)( bui|\))/i],[c,[p,G],[f,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[c,[p,_i],[f,E]],[/(bravia[\w ]+)( bui|\))/i],[c,[p,Wt],[f,E]],[/(mitv-\w{5}) bui/i],[c,[p,ts],[f,E]],[/Hbbtv.*(technisat) (.*);/i],[p,c,[f,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[p,ss],[c,ss],[f,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[f,E]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[p,c,[f,T]],[/droid.+; (shield) bui/i],[c,[p,"Nvidia"],[f,T]],[/(playstation [345portablevi]+)/i],[c,[p,Wt],[f,T]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[c,[p,bt],[f,T]],[/\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i],[c,[p,St],[f,_]],[/((pebble))app/i],[p,c,[f,_]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[c,[p,re],[f,_]],[/droid.+; (glass) \d/i],[c,[p,Be],[f,_]],[/droid.+; (wt63?0{2,3})\)/i],[c,[p,ns],[f,_]],[/droid.+; (glass) \d/i],[c,[p,Be],[f,_]],[/(pico) (4|neo3(?: link|pro)?)/i],[p,c,[f,_]],[/; (quest( \d| pro)?)/i],[c,[p,Ii],[f,_]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[p,[f,D]],[/(aeobc)\b/i],[c,[p,G],[f,D]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[c,[f,S]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[c,[f,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[f,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[f,S]],[/(android[-\w\. ]{0,9});.+buil/i],[c,[p,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[g,[h,Gt+"HTML"]],[/(arkweb)\/([\w\.]+)/i],[h,g],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[g,[h,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[h,g],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[g,h]],os:[[/microsoft (windows) (vista|xp)/i],[h,g],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[h,[g,Kt,Di]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[g,Kt,Di],[h,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[g,/_/g,"."],[h,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[h,Pi],[g,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[g,h],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[h,g],[/\(bb(10);/i],[g,[h,W]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[g,[h,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[g,[h,et+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[g,[h,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[g,[h,"watchOS"]],[/crkey\/([\d\.]+)/i],[g,[h,xe+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[h,Oi],g],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[h,g],[/(sunos) ?([\w\.\d]*)/i],[[h,"Solaris"],g],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[h,g]]},pe=function(x,L){if(typeof x===u&&(L=x,x=n),!(this instanceof pe))return new pe(x,L).getResult();var O=typeof s!==l&&s.navigator?s.navigator:n,V=x||(O&&O.userAgent?O.userAgent:r),Ie=O&&O.userAgentData?O.userAgentData:n,ve=L?Vo(xi,L):xi,M=O&&O.userAgent==V;return this.getBrowser=function(){var I={};return I[h]=n,I[g]=n,vt.call(I,V,ve.browser),I[m]=Ho(I[g]),M&&O&&O.brave&&typeof O.brave.isBrave==o&&(I[h]="Brave"),I},this.getCPU=function(){var I={};return I[b]=n,vt.call(I,V,ve.cpu),I},this.getDevice=function(){var I={};return I[p]=n,I[c]=n,I[f]=n,vt.call(I,V,ve.device),M&&!I[f]&&Ie&&Ie.mobile&&(I[f]=S),M&&I[c]=="Macintosh"&&O&&typeof O.standalone!==l&&O.maxTouchPoints&&O.maxTouchPoints>2&&(I[c]="iPad",I[f]=w),I},this.getEngine=function(){var I={};return I[h]=n,I[g]=n,vt.call(I,V,ve.engine),I},this.getOS=function(){var I={};return I[h]=n,I[g]=n,vt.call(I,V,ve.os),M&&!I[h]&&Ie&&Ie.platform&&Ie.platform!="Unknown"&&(I[h]=Ie.platform.replace(/chrome os/i,Oi).replace(/macos/i,Pi)),I},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return V},this.setUA=function(I){return V=typeof I===d&&I.length>F?ss(I,F):I,this},this.setUA(V),this};pe.VERSION=i,pe.BROWSER=Jt([h,g,m]),pe.CPU=Jt([b]),pe.DEVICE=Jt([c,p,f,T,S,E,w,_,D]),pe.ENGINE=pe.OS=Jt([h,g]),t.exports&&(e=t.exports=pe),e.UAParser=pe;var nt=typeof s!==l&&(s.jQuery||s.Zepto);if(nt&&!nt.ua){var zt=new pe;nt.ua=zt.getResult(),nt.ua.get=function(){return zt.getUA()},nt.ua.set=function(x){zt.setUA(x);var L=zt.getResult();for(var O in L)nt.ua[O]=L[O]}}})(typeof window=="object"?window:xd)}(Rt,Rt.exports)),Rt.exports}var Ud=Ld(),me={},ms={},gs={exports:{}},vr;function di(){if(vr)return gs.exports;vr=1;var t=gs.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return e.address!=null?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return e.subtype!=null?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return e.sessionConfig!=null?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var s="candidate:%s %d %s %d %s %d typ %s";return s+=e.raddr!=null?" raddr %s rport %d":"%v%v",s+=e.tcptype!=null?" tcptype %s":"%v",e.generation!=null&&(s+=" generation %d"),s+=e["network-id"]!=null?" network-id %d":"%v",s+=e["network-cost"]!=null?" network-cost %d":"%v",s}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var s="ssrc:%d";return e.attribute!=null&&(s+=" %s",e.value!=null&&(s+=":%s")),s}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return e.maxMessageSize!=null?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(e.clksrcExt!=null?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var s="mediaclk:";return s+=e.id!=null?"id=%s %s":"%v%s",s+=e.mediaClockValue!=null?"=%s":"",s+=e.rateNumerator!=null?" rate=%s":"",s+=e.rateDenominator!=null?"/%s":"",s}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};return Object.keys(t).forEach(function(e){var s=t[e];s.forEach(function(n){n.reg||(n.reg=/(.*)/),n.format||(n.format="%s")})}),gs.exports}var Tr;function Fd(){return Tr||(Tr=1,function(t){var e=function(o){return String(Number(o))===o?Number(o):o},s=function(o,l,u,d){if(d&&!u)l[d]=e(o[1]);else for(var m=0;m<u.length;m+=1)o[m+1]!=null&&(l[u[m]]=e(o[m+1]))},n=function(o,l,u){var d=o.name&&o.names;o.push&&!l[o.push]?l[o.push]=[]:d&&!l[o.name]&&(l[o.name]={});var m=o.push?{}:d?l[o.name]:l;s(u.match(o.reg),m,o.names,o.name),o.push&&l[o.push].push(m)},i=di(),r=RegExp.prototype.test.bind(/^([a-z])=(.*)/);t.parse=function(o){var l={},u=[],d=l;return o.split(/(\r\n|\r|\n)/).filter(r).forEach(function(m){var c=m[0],h=m.slice(2);c==="m"&&(u.push({rtp:[],fmtp:[]}),d=u[u.length-1]);for(var f=0;f<(i[c]||[]).length;f+=1){var p=i[c][f];if(p.reg.test(h))return n(p,d,h)}}),l.media=u,l};var a=function(o,l){var u=l.split(/=(.+)/,2);return u.length===2?o[u[0]]=e(u[1]):u.length===1&&l.length>1&&(o[u[0]]=void 0),o};t.parseParams=function(o){return o.split(/;\s?/).reduce(a,{})},t.parseFmtpConfig=t.parseParams,t.parsePayloads=function(o){return o.toString().split(" ").map(Number)},t.parseRemoteCandidates=function(o){for(var l=[],u=o.split(" ").map(e),d=0;d<u.length;d+=3)l.push({component:u[d],ip:u[d+1],port:u[d+2]});return l},t.parseImageAttributes=function(o){return o.split(" ").map(function(l){return l.substring(1,l.length-1).split(",").reduce(a,{})})},t.parseSimulcastStreamList=function(o){return o.split(";").map(function(l){return l.split(",").map(function(u){var d,m=!1;return u[0]!=="~"?d=e(u):(d=e(u.substring(1,u.length)),m=!0),{scid:d,paused:m}})})}}(ms)),ms}var bs,Cr;function Md(){if(Cr)return bs;Cr=1;var t=di(),e=/%[sdv%]/g,s=function(a){var o=1,l=arguments,u=l.length;return a.replace(e,function(d){if(o>=u)return d;var m=l[o];switch(o+=1,d){case"%%":return"%";case"%s":return String(m);case"%d":return Number(m);case"%v":return""}})},n=function(a,o,l){var u=o.format instanceof Function?o.format(o.push?l:l[o.name]):o.format,d=[a+"="+u];if(o.names)for(var m=0;m<o.names.length;m+=1){var c=o.names[m];o.name?d.push(l[o.name][c]):d.push(l[o.names[m]])}else d.push(l[o.name]);return s.apply(null,d)},i=["v","o","s","i","u","e","p","c","b","t","r","z","a"],r=["i","c","b","a"];return bs=function(a,o){o=o||{},a.version==null&&(a.version=0),a.name==null&&(a.name=" "),a.media.forEach(function(m){m.payloads==null&&(m.payloads="")});var l=o.outerOrder||i,u=o.innerOrder||r,d=[];return l.forEach(function(m){t[m].forEach(function(c){c.name in a&&a[c.name]!=null?d.push(n(m,c,a)):c.push in a&&a[c.push]!=null&&a[c.push].forEach(function(h){d.push(n(m,c,h))})})}),a.media.forEach(function(m){d.push(n("m",t.m[0],m)),u.forEach(function(c){t[c].forEach(function(h){h.name in m&&m[h.name]!=null?d.push(n(c,h,m)):h.push in m&&m[h.push]!=null&&m[h.push].forEach(function(f){d.push(n(c,h,f))})})})}),d.join(`\r
`)+`\r
`},bs}var kr;function jd(){if(kr)return me;kr=1;var t=Fd(),e=Md(),s=di();return me.grammar=s,me.write=e,me.parse=t.parse,me.parseParams=t.parseParams,me.parseFmtpConfig=t.parseFmtpConfig,me.parsePayloads=t.parsePayloads,me.parseRemoteCandidates=t.parseRemoteCandidates,me.parseImageAttributes=t.parseImageAttributes,me.parseSimulcastStreamList=t.parseSimulcastStreamList,me}var $d=jd();const ys={AVAILABLE:"available",DISABLED:"disabled",AUTO_ON:"auto-on"},Y={BLOCK_USERS:"block-users",CHANGE_MAX_DURATION:"change-max-duration",CREATE_CALL:"create-call",CREATE_REACTION:"create-reaction",ENABLE_NOISE_CANCELLATION:"enable-noise-cancellation",END_CALL:"end-call",JOIN_BACKSTAGE:"join-backstage",JOIN_CALL:"join-call",JOIN_ENDED_CALL:"join-ended-call",MUTE_USERS:"mute-users",PIN_FOR_EVERYONE:"pin-for-everyone",READ_CALL:"read-call",REMOVE_CALL_MEMBER:"remove-call-member",SCREENSHARE:"screenshare",SEND_AUDIO:"send-audio",SEND_VIDEO:"send-video",START_BROADCAST_CALL:"start-broadcast-call",START_CLOSED_CAPTIONS_CALL:"start-closed-captions-call",START_FRAME_RECORD_CALL:"start-frame-record-call",START_RECORD_CALL:"start-record-call",START_TRANSCRIPTION_CALL:"start-transcription-call",STOP_BROADCAST_CALL:"stop-broadcast-call",STOP_CLOSED_CAPTIONS_CALL:"stop-closed-captions-call",STOP_FRAME_RECORD_CALL:"stop-frame-record-call",STOP_RECORD_CALL:"stop-record-call",STOP_TRANSCRIPTION_CALL:"stop-transcription-call",UPDATE_CALL:"update-call",UPDATE_CALL_MEMBER:"update-call-member",UPDATE_CALL_PERMISSIONS:"update-call-permissions",UPDATE_CALL_SETTINGS:"update-call-settings"};class go extends Error{constructor({message:e,code:s,status:n,response:i,unrecoverable:r}){super(e),this.name="ErrorFromResponse",this.code=s,this.response=i,this.status=n,this.unrecoverable=r}toJSON(){const e=[["status",this.status],["code",this.code],["unrecoverable",this.unrecoverable]],s=[];for(const[n,i]of e)typeof i<"u"&&i!==null&&s.push(`${n}: ${i}`);return{message:`(${s.join(", ")}) - ${this.message}`,stack:this.stack,name:this.name}}}var In;(function(t){t[t.NULL_VALUE=0]="NULL_VALUE"})(In||(In={}));class Bd extends k{constructor(){super("google.protobuf.Struct",[{no:1,name:"fields",kind:"map",K:9,V:{kind:"message",T:()=>ot}}])}internalJsonWrite(e,s){let n={};for(let[i,r]of Object.entries(e.fields))n[i]=ot.toJson(r);return n}internalJsonRead(e,s,n){if(!Sn(e))throw new globalThis.Error("Unable to parse message "+this.typeName+" from JSON "+ht(e)+".");n||(n=this.create());for(let[i,r]of globalThis.Object.entries(e))n.fields[i]=ot.fromJson(r);return n}}const On=new Bd;class Vd extends k{constructor(){super("google.protobuf.Value",[{no:1,name:"null_value",kind:"enum",oneof:"kind",T:()=>["google.protobuf.NullValue",In]},{no:2,name:"number_value",kind:"scalar",oneof:"kind",T:1},{no:3,name:"string_value",kind:"scalar",oneof:"kind",T:9},{no:4,name:"bool_value",kind:"scalar",oneof:"kind",T:8},{no:5,name:"struct_value",kind:"message",oneof:"kind",T:()=>On},{no:6,name:"list_value",kind:"message",oneof:"kind",T:()=>wr}])}internalJsonWrite(e,s){if(e.kind.oneofKind===void 0)throw new globalThis.Error;switch(e.kind.oneofKind){case void 0:throw new globalThis.Error;case"boolValue":return e.kind.boolValue;case"nullValue":return null;case"numberValue":let n=e.kind.numberValue;if(typeof n=="number"&&!Number.isFinite(n))throw new globalThis.Error;return n;case"stringValue":return e.kind.stringValue;case"listValue":let i=this.fields.find(a=>a.no===6);if((i==null?void 0:i.kind)!=="message")throw new globalThis.Error;return i.T().toJson(e.kind.listValue);case"structValue":let r=this.fields.find(a=>a.no===5);if((r==null?void 0:r.kind)!=="message")throw new globalThis.Error;return r.T().toJson(e.kind.structValue)}}internalJsonRead(e,s,n){switch(n||(n=this.create()),typeof e){case"number":n.kind={oneofKind:"numberValue",numberValue:e};break;case"string":n.kind={oneofKind:"stringValue",stringValue:e};break;case"boolean":n.kind={oneofKind:"boolValue",boolValue:e};break;case"object":e===null?n.kind={oneofKind:"nullValue",nullValue:In.NULL_VALUE}:globalThis.Array.isArray(e)?n.kind={oneofKind:"listValue",listValue:wr.fromJson(e)}:n.kind={oneofKind:"structValue",structValue:On.fromJson(e)};break;default:throw new globalThis.Error("Unable to parse "+this.typeName+" from JSON "+ht(e))}return n}}const ot=new Vd;class Hd extends k{constructor(){super("google.protobuf.ListValue",[{no:1,name:"values",kind:"message",repeat:1,T:()=>ot}])}internalJsonWrite(e,s){return e.values.map(n=>ot.toJson(n))}internalJsonRead(e,s,n){if(!globalThis.Array.isArray(e))throw new globalThis.Error("Unable to parse "+this.typeName+" from JSON "+ht(e));n||(n=this.create());let i=e.map(r=>ot.fromJson(r));return n.values.push(...i),n}}const wr=new Hd;class qd extends k{constructor(){super("google.protobuf.Timestamp",[{no:1,name:"seconds",kind:"scalar",T:3},{no:2,name:"nanos",kind:"scalar",T:5}])}now(){const e=this.create(),s=Date.now();return e.seconds=H.from(Math.floor(s/1e3)).toString(),e.nanos=s%1e3*1e6,e}toDate(e){return new Date(H.from(e.seconds).toNumber()*1e3+Math.ceil(e.nanos/1e6))}fromDate(e){const s=this.create(),n=e.getTime();return s.seconds=H.from(Math.floor(n/1e3)).toString(),s.nanos=(n%1e3+(n<0&&n%1e3!==0?1e3:0))*1e6,s}internalJsonWrite(e,s){let n=H.from(e.seconds).toNumber()*1e3;if(n<Date.parse("0001-01-01T00:00:00Z")||n>Date.parse("9999-12-31T23:59:59Z"))throw new Error("Unable to encode Timestamp to JSON. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.");if(e.nanos<0)throw new Error("Unable to encode invalid Timestamp to JSON. Nanos must not be negative.");let i="Z";if(e.nanos>0){let r=(e.nanos+1e9).toString().substring(1);r.substring(3)==="000000"?i="."+r.substring(0,3)+"Z":r.substring(6)==="000"?i="."+r.substring(0,6)+"Z":i="."+r+"Z"}return new Date(n).toISOString().replace(".000Z",i)}internalJsonRead(e,s,n){if(typeof e!="string")throw new Error("Unable to parse Timestamp from JSON "+ht(e)+".");let i=e.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:Z|\.([0-9]{3,9})Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!i)throw new Error("Unable to parse Timestamp from JSON. Invalid format.");let r=Date.parse(i[1]+"-"+i[2]+"-"+i[3]+"T"+i[4]+":"+i[5]+":"+i[6]+(i[8]?i[8]:"Z"));if(Number.isNaN(r))throw new Error("Unable to parse Timestamp from JSON. Invalid value.");if(r<Date.parse("0001-01-01T00:00:00Z")||r>Date.parse("9999-12-31T23:59:59Z"))throw new globalThis.Error("Unable to parse Timestamp from JSON. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.");return n||(n=this.create()),n.seconds=H.from(r/1e3).toString(),n.nanos=0,i[7]&&(n.nanos=parseInt("1"+i[7]+"0".repeat(9-i[7].length))-1e9),n}}const Dt=new qd;var ee;(function(t){t[t.PUBLISHER_UNSPECIFIED=0]="PUBLISHER_UNSPECIFIED",t[t.SUBSCRIBER=1]="SUBSCRIBER"})(ee||(ee={}));var Pn;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.POOR=1]="POOR",t[t.GOOD=2]="GOOD",t[t.EXCELLENT=3]="EXCELLENT"})(Pn||(Pn={}));var We;(function(t){t[t.LOW_UNSPECIFIED=0]="LOW_UNSPECIFIED",t[t.MID=1]="MID",t[t.HIGH=2]="HIGH",t[t.OFF=3]="OFF"})(We||(We={}));var C;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.AUDIO=1]="AUDIO",t[t.VIDEO=2]="VIDEO",t[t.SCREEN_SHARE=3]="SCREEN_SHARE",t[t.SCREEN_SHARE_AUDIO=4]="SCREEN_SHARE_AUDIO"})(C||(C={}));var xt;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.PUBLISH_TRACK_NOT_FOUND=100]="PUBLISH_TRACK_NOT_FOUND",t[t.PUBLISH_TRACKS_MISMATCH=101]="PUBLISH_TRACKS_MISMATCH",t[t.PUBLISH_TRACK_OUT_OF_ORDER=102]="PUBLISH_TRACK_OUT_OF_ORDER",t[t.PUBLISH_TRACK_VIDEO_LAYER_NOT_FOUND=103]="PUBLISH_TRACK_VIDEO_LAYER_NOT_FOUND",t[t.LIVE_ENDED=104]="LIVE_ENDED",t[t.PARTICIPANT_NOT_FOUND=200]="PARTICIPANT_NOT_FOUND",t[t.PARTICIPANT_MIGRATING_OUT=201]="PARTICIPANT_MIGRATING_OUT",t[t.PARTICIPANT_MIGRATION_FAILED=202]="PARTICIPANT_MIGRATION_FAILED",t[t.PARTICIPANT_MIGRATING=203]="PARTICIPANT_MIGRATING",t[t.PARTICIPANT_RECONNECT_FAILED=204]="PARTICIPANT_RECONNECT_FAILED",t[t.PARTICIPANT_MEDIA_TRANSPORT_FAILURE=205]="PARTICIPANT_MEDIA_TRANSPORT_FAILURE",t[t.CALL_NOT_FOUND=300]="CALL_NOT_FOUND",t[t.REQUEST_VALIDATION_FAILED=400]="REQUEST_VALIDATION_FAILED",t[t.UNAUTHENTICATED=401]="UNAUTHENTICATED",t[t.PERMISSION_DENIED=403]="PERMISSION_DENIED",t[t.TOO_MANY_REQUESTS=429]="TOO_MANY_REQUESTS",t[t.INTERNAL_SERVER_ERROR=500]="INTERNAL_SERVER_ERROR",t[t.SFU_SHUTTING_DOWN=600]="SFU_SHUTTING_DOWN",t[t.SFU_FULL=700]="SFU_FULL"})(xt||(xt={}));var Ye;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.REACT=1]="REACT",t[t.ANGULAR=2]="ANGULAR",t[t.ANDROID=3]="ANDROID",t[t.IOS=4]="IOS",t[t.FLUTTER=5]="FLUTTER",t[t.REACT_NATIVE=6]="REACT_NATIVE",t[t.UNITY=7]="UNITY",t[t.GO=8]="GO",t[t.PLAIN_JAVASCRIPT=9]="PLAIN_JAVASCRIPT"})(Ye||(Ye={}));var An;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.USER_MUTED=1]="USER_MUTED",t[t.PERMISSION_REVOKED=2]="PERMISSION_REVOKED",t[t.MODERATION=3]="MODERATION"})(An||(An={}));var Vs;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.SHUTTING_DOWN=1]="SHUTTING_DOWN",t[t.REBALANCE=2]="REBALANCE"})(Vs||(Vs={}));var Lt;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.ENDED=1]="ENDED",t[t.LIVE_ENDED=2]="LIVE_ENDED",t[t.KICKED=3]="KICKED",t[t.SESSION_ENDED=4]="SESSION_ENDED"})(Lt||(Lt={}));var N;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.DISCONNECT=1]="DISCONNECT",t[t.FAST=2]="FAST",t[t.REJOIN=3]="REJOIN",t[t.MIGRATE=4]="MIGRATE"})(N||(N={}));var Hs;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.NONE=1]="NONE",t[t.LIGHT=2]="LIGHT",t[t.MODERATE=3]="MODERATE",t[t.SEVERE=4]="SEVERE",t[t.CRITICAL=5]="CRITICAL",t[t.EMERGENCY=6]="EMERGENCY",t[t.SHUTDOWN=7]="SHUTDOWN"})(Hs||(Hs={}));var qs;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.NOMINAL=1]="NOMINAL",t[t.FAIR=2]="FAIR",t[t.SERIOUS=3]="SERIOUS",t[t.CRITICAL=4]="CRITICAL"})(qs||(qs={}));class Gd extends k{constructor(){super("stream.video.sfu.models.CallState",[{no:1,name:"participants",kind:"message",repeat:1,T:()=>mt},{no:2,name:"started_at",kind:"message",T:()=>Dt},{no:3,name:"participant_count",kind:"message",T:()=>bo},{no:4,name:"pins",kind:"message",repeat:1,T:()=>yo}])}}const Wd=new Gd;class Jd extends k{constructor(){super("stream.video.sfu.models.ParticipantCount",[{no:1,name:"total",kind:"scalar",T:13},{no:2,name:"anonymous",kind:"scalar",T:13}])}}const bo=new Jd;class Kd extends k{constructor(){super("stream.video.sfu.models.Pin",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9}])}}const yo=new Kd;class zd extends k{constructor(){super("stream.video.sfu.models.Participant",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"published_tracks",kind:"enum",repeat:1,T:()=>["stream.video.sfu.models.TrackType",C,"TRACK_TYPE_"]},{no:4,name:"joined_at",kind:"message",T:()=>Dt},{no:5,name:"track_lookup_prefix",kind:"scalar",T:9},{no:6,name:"connection_quality",kind:"enum",T:()=>["stream.video.sfu.models.ConnectionQuality",Pn,"CONNECTION_QUALITY_"]},{no:7,name:"is_speaking",kind:"scalar",T:8},{no:8,name:"is_dominant_speaker",kind:"scalar",T:8},{no:9,name:"audio_level",kind:"scalar",T:2},{no:10,name:"name",kind:"scalar",T:9},{no:11,name:"image",kind:"scalar",T:9},{no:12,name:"custom",kind:"message",T:()=>On},{no:13,name:"roles",kind:"scalar",repeat:2,T:9}])}}const mt=new zd;class Yd extends k{constructor(){super("stream.video.sfu.models.StreamQuality",[{no:1,name:"video_quality",kind:"enum",T:()=>["stream.video.sfu.models.VideoQuality",We,"VIDEO_QUALITY_"]},{no:2,name:"user_id",kind:"scalar",T:9}])}}new Yd;class Xd extends k{constructor(){super("stream.video.sfu.models.VideoDimension",[{no:1,name:"width",kind:"scalar",T:13},{no:2,name:"height",kind:"scalar",T:13}])}}const Kn=new Xd;class Zd extends k{constructor(){super("stream.video.sfu.models.VideoLayer",[{no:1,name:"rid",kind:"scalar",T:9},{no:2,name:"video_dimension",kind:"message",T:()=>Kn},{no:4,name:"bitrate",kind:"scalar",T:13},{no:5,name:"fps",kind:"scalar",T:13},{no:6,name:"quality",kind:"enum",T:()=>["stream.video.sfu.models.VideoQuality",We,"VIDEO_QUALITY_"]}])}}const Qd=new Zd;class eh extends k{constructor(){super("stream.video.sfu.models.SubscribeOption",[{no:1,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",C,"TRACK_TYPE_"]},{no:2,name:"codecs",kind:"message",repeat:1,T:()=>De}])}}const So=new eh;class th extends k{constructor(){super("stream.video.sfu.models.PublishOption",[{no:1,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",C,"TRACK_TYPE_"]},{no:2,name:"codec",kind:"message",T:()=>De},{no:3,name:"bitrate",kind:"scalar",T:5},{no:4,name:"fps",kind:"scalar",T:5},{no:5,name:"max_spatial_layers",kind:"scalar",T:5},{no:6,name:"max_temporal_layers",kind:"scalar",T:5},{no:7,name:"video_dimension",kind:"message",T:()=>Kn},{no:8,name:"id",kind:"scalar",T:5},{no:9,name:"use_single_layer",kind:"scalar",T:8}])}}const Ut=new th;class nh extends k{constructor(){super("stream.video.sfu.models.Codec",[{no:16,name:"payload_type",kind:"scalar",T:13},{no:10,name:"name",kind:"scalar",T:9},{no:14,name:"clock_rate",kind:"scalar",T:13},{no:15,name:"encoding_parameters",kind:"scalar",T:9},{no:12,name:"fmtp",kind:"scalar",T:9}])}}const De=new nh;let sh=class extends k{constructor(){super("stream.video.sfu.models.ICETrickle",[{no:1,name:"peer_type",kind:"enum",T:()=>["stream.video.sfu.models.PeerType",ee,"PEER_TYPE_"]},{no:2,name:"ice_candidate",kind:"scalar",T:9},{no:3,name:"session_id",kind:"scalar",T:9}])}};const vo=new sh;class ih extends k{constructor(){super("stream.video.sfu.models.TrackInfo",[{no:1,name:"track_id",kind:"scalar",T:9},{no:2,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",C,"TRACK_TYPE_"]},{no:5,name:"layers",kind:"message",repeat:1,T:()=>Qd},{no:6,name:"mid",kind:"scalar",T:9},{no:7,name:"dtx",kind:"scalar",T:8},{no:8,name:"stereo",kind:"scalar",T:8},{no:9,name:"red",kind:"scalar",T:8},{no:10,name:"muted",kind:"scalar",T:8},{no:11,name:"codec",kind:"message",T:()=>De},{no:12,name:"publish_option_id",kind:"scalar",T:5}])}}const hi=new ih;let rh=class extends k{constructor(){super("stream.video.sfu.models.Error",[{no:1,name:"code",kind:"enum",T:()=>["stream.video.sfu.models.ErrorCode",xt,"ERROR_CODE_"]},{no:2,name:"message",kind:"scalar",T:9},{no:3,name:"should_retry",kind:"scalar",T:8}])}};const _e=new rh;class ah extends k{constructor(){super("stream.video.sfu.models.ClientDetails",[{no:1,name:"sdk",kind:"message",T:()=>uh},{no:2,name:"os",kind:"message",T:()=>dh},{no:3,name:"browser",kind:"message",T:()=>fh},{no:4,name:"device",kind:"message",T:()=>bh}])}}const oh=new ah;class ch extends k{constructor(){super("stream.video.sfu.models.Sdk",[{no:1,name:"type",kind:"enum",T:()=>["stream.video.sfu.models.SdkType",Ye,"SDK_TYPE_"]},{no:2,name:"major",kind:"scalar",T:9},{no:3,name:"minor",kind:"scalar",T:9},{no:4,name:"patch",kind:"scalar",T:9}])}}const uh=new ch;class lh extends k{constructor(){super("stream.video.sfu.models.OS",[{no:1,name:"name",kind:"scalar",T:9},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"architecture",kind:"scalar",T:9}])}}const dh=new lh;class hh extends k{constructor(){super("stream.video.sfu.models.Browser",[{no:1,name:"name",kind:"scalar",T:9},{no:2,name:"version",kind:"scalar",T:9}])}}const fh=new hh;class ph extends k{constructor(){super("stream.video.sfu.models.RTMPIngress",[{no:1,name:"width",kind:"scalar",T:13},{no:2,name:"height",kind:"scalar",T:13},{no:3,name:"frame_rate",kind:"scalar",T:1},{no:4,name:"software",kind:"scalar",T:9},{no:5,name:"version",kind:"scalar",T:9},{no:6,name:"encoder",kind:"scalar",T:9},{no:7,name:"remote_addr",kind:"scalar",T:9}])}}const mh=new ph;class gh extends k{constructor(){super("stream.video.sfu.models.Device",[{no:1,name:"name",kind:"scalar",T:9},{no:2,name:"version",kind:"scalar",T:9}])}}const bh=new gh;class yh extends k{constructor(){super("stream.video.sfu.models.Call",[{no:1,name:"type",kind:"scalar",T:9},{no:2,name:"id",kind:"scalar",T:9},{no:3,name:"created_by_user_id",kind:"scalar",T:9},{no:4,name:"host_user_id",kind:"scalar",T:9},{no:5,name:"custom",kind:"message",T:()=>On},{no:6,name:"created_at",kind:"message",T:()=>Dt},{no:7,name:"updated_at",kind:"message",T:()=>Dt}])}}new yh;class Sh extends k{constructor(){super("stream.video.sfu.models.CallGrants",[{no:1,name:"can_publish_audio",kind:"scalar",T:8},{no:2,name:"can_publish_video",kind:"scalar",T:8},{no:3,name:"can_screenshare",kind:"scalar",T:8}])}}const vh=new Sh;class Th extends k{constructor(){super("stream.video.sfu.models.InputDevices",[{no:1,name:"available_devices",kind:"scalar",repeat:2,T:9},{no:2,name:"current_device",kind:"scalar",T:9},{no:3,name:"is_permitted",kind:"scalar",T:8}])}}const Er=new Th;class Ch extends k{constructor(){super("stream.video.sfu.models.AndroidState",[{no:1,name:"thermal_state",kind:"enum",T:()=>["stream.video.sfu.models.AndroidThermalState",Hs,"ANDROID_THERMAL_STATE_"]},{no:2,name:"is_power_saver_mode",kind:"scalar",T:8}])}}const kh=new Ch;class wh extends k{constructor(){super("stream.video.sfu.models.AppleState",[{no:1,name:"thermal_state",kind:"enum",T:()=>["stream.video.sfu.models.AppleThermalState",qs,"APPLE_THERMAL_STATE_"]},{no:2,name:"is_low_power_mode_enabled",kind:"scalar",T:8}])}}const Eh=new wh;class Rh extends k{constructor(){super("stream.video.sfu.models.PerformanceStats",[{no:1,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",C,"TRACK_TYPE_"]},{no:2,name:"codec",kind:"message",T:()=>De},{no:3,name:"avg_frame_time_ms",kind:"scalar",T:2},{no:4,name:"avg_fps",kind:"scalar",T:2},{no:5,name:"video_dimension",kind:"message",T:()=>Kn},{no:6,name:"target_bitrate",kind:"scalar",T:5}])}}const Gs=new Rh;class _h extends k{constructor(){super("stream.video.sfu.signal.StartNoiseCancellationRequest",[{no:1,name:"session_id",kind:"scalar",T:9}])}}const Ih=new _h;class Oh extends k{constructor(){super("stream.video.sfu.signal.StartNoiseCancellationResponse",[{no:1,name:"error",kind:"message",T:()=>_e}])}}const Ph=new Oh;class Ah extends k{constructor(){super("stream.video.sfu.signal.StopNoiseCancellationRequest",[{no:1,name:"session_id",kind:"scalar",T:9}])}}const Nh=new Ah;class Dh extends k{constructor(){super("stream.video.sfu.signal.StopNoiseCancellationResponse",[{no:1,name:"error",kind:"message",T:()=>_e}])}}const xh=new Dh;class Lh extends k{constructor(){super("stream.video.sfu.signal.Reconnection",[{no:1,name:"time_seconds",kind:"scalar",T:2},{no:2,name:"strategy",kind:"enum",T:()=>["stream.video.sfu.models.WebsocketReconnectStrategy",N,"WEBSOCKET_RECONNECT_STRATEGY_"]}])}}const Uh=new Lh;class Fh extends k{constructor(){super("stream.video.sfu.signal.Telemetry",[{no:1,name:"connection_time_seconds",kind:"scalar",oneof:"data",T:2},{no:2,name:"reconnection",kind:"message",oneof:"data",T:()=>Uh}])}}const Mh=new Fh;class jh extends k{constructor(){super("stream.video.sfu.signal.SendStatsRequest",[{no:1,name:"session_id",kind:"scalar",T:9},{no:2,name:"subscriber_stats",kind:"scalar",T:9},{no:3,name:"publisher_stats",kind:"scalar",T:9},{no:4,name:"webrtc_version",kind:"scalar",T:9},{no:5,name:"sdk",kind:"scalar",T:9},{no:6,name:"sdk_version",kind:"scalar",T:9},{no:7,name:"audio_devices",kind:"message",T:()=>Er},{no:8,name:"video_devices",kind:"message",T:()=>Er},{no:9,name:"android",kind:"message",oneof:"deviceState",T:()=>kh},{no:10,name:"apple",kind:"message",oneof:"deviceState",T:()=>Eh},{no:11,name:"telemetry",kind:"message",T:()=>Mh},{no:12,name:"rtmp",kind:"message",T:()=>mh},{no:13,name:"subscriber_rtc_stats",kind:"scalar",T:9},{no:14,name:"publisher_rtc_stats",kind:"scalar",T:9},{no:15,name:"rtc_stats",kind:"scalar",T:9},{no:16,name:"encode_stats",kind:"message",repeat:1,T:()=>Gs},{no:17,name:"decode_stats",kind:"message",repeat:1,T:()=>Gs},{no:18,name:"unified_session_id",kind:"scalar",T:9}])}}const $h=new jh;class Bh extends k{constructor(){super("stream.video.sfu.signal.SendStatsResponse",[{no:1,name:"error",kind:"message",T:()=>_e}])}}const Vh=new Bh;class Hh extends k{constructor(){super("stream.video.sfu.signal.ICERestartRequest",[{no:1,name:"session_id",kind:"scalar",T:9},{no:2,name:"peer_type",kind:"enum",T:()=>["stream.video.sfu.models.PeerType",ee,"PEER_TYPE_"]}])}}const qh=new Hh;class Gh extends k{constructor(){super("stream.video.sfu.signal.ICERestartResponse",[{no:1,name:"error",kind:"message",T:()=>_e}])}}const Wh=new Gh;class Jh extends k{constructor(){super("stream.video.sfu.signal.UpdateMuteStatesRequest",[{no:1,name:"session_id",kind:"scalar",T:9},{no:3,name:"mute_states",kind:"message",repeat:1,T:()=>Zh}])}}const Kh=new Jh;class zh extends k{constructor(){super("stream.video.sfu.signal.UpdateMuteStatesResponse",[{no:4,name:"error",kind:"message",T:()=>_e}])}}const Yh=new zh;class Xh extends k{constructor(){super("stream.video.sfu.signal.TrackMuteState",[{no:1,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",C,"TRACK_TYPE_"]},{no:2,name:"muted",kind:"scalar",T:8}])}}const Zh=new Xh;class Qh extends k{constructor(){super("stream.video.sfu.signal.AudioMuteChanged",[{no:1,name:"muted",kind:"scalar",T:8}])}}new Qh;class ef extends k{constructor(){super("stream.video.sfu.signal.VideoMuteChanged",[{no:2,name:"muted",kind:"scalar",T:8}])}}new ef;class tf extends k{constructor(){super("stream.video.sfu.signal.UpdateSubscriptionsRequest",[{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"tracks",kind:"message",repeat:1,T:()=>fi}])}}const nf=new tf;class sf extends k{constructor(){super("stream.video.sfu.signal.UpdateSubscriptionsResponse",[{no:4,name:"error",kind:"message",T:()=>_e}])}}const rf=new sf;class af extends k{constructor(){super("stream.video.sfu.signal.TrackSubscriptionDetails",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",C,"TRACK_TYPE_"]},{no:4,name:"dimension",kind:"message",T:()=>Kn}])}}const fi=new af;class of extends k{constructor(){super("stream.video.sfu.signal.SendAnswerRequest",[{no:1,name:"peer_type",kind:"enum",T:()=>["stream.video.sfu.models.PeerType",ee,"PEER_TYPE_"]},{no:2,name:"sdp",kind:"scalar",T:9},{no:3,name:"session_id",kind:"scalar",T:9}])}}const cf=new of;class uf extends k{constructor(){super("stream.video.sfu.signal.SendAnswerResponse",[{no:4,name:"error",kind:"message",T:()=>_e}])}}const lf=new uf;class df extends k{constructor(){super("stream.video.sfu.signal.ICETrickleResponse",[{no:4,name:"error",kind:"message",T:()=>_e}])}}const hf=new df;class ff extends k{constructor(){super("stream.video.sfu.signal.SetPublisherRequest",[{no:1,name:"sdp",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"tracks",kind:"message",repeat:1,T:()=>hi}])}}const pf=new ff;class mf extends k{constructor(){super("stream.video.sfu.signal.SetPublisherResponse",[{no:1,name:"sdp",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"ice_restart",kind:"scalar",T:8},{no:4,name:"error",kind:"message",T:()=>_e}])}}const gf=new mf,Ss=new Wc("stream.video.sfu.signal.SignalServer",[{name:"SetPublisher",options:{},I:pf,O:gf},{name:"SendAnswer",options:{},I:cf,O:lf},{name:"IceTrickle",options:{},I:vo,O:hf},{name:"UpdateSubscriptions",options:{},I:nf,O:rf},{name:"UpdateMuteStates",options:{},I:Kh,O:Yh},{name:"IceRestart",options:{},I:qh,O:Wh},{name:"SendStats",options:{},I:$h,O:Vh},{name:"StartNoiseCancellation",options:{},I:Ih,O:Ph},{name:"StopNoiseCancellation",options:{},I:Nh,O:xh}]);class bf extends k{constructor(){super("stream.video.sfu.event.SfuEvent",[{no:1,name:"subscriber_offer",kind:"message",oneof:"eventPayload",T:()=>tp},{no:2,name:"publisher_answer",kind:"message",oneof:"eventPayload",T:()=>sp},{no:3,name:"connection_quality_changed",kind:"message",oneof:"eventPayload",T:()=>rp},{no:4,name:"audio_level_changed",kind:"message",oneof:"eventPayload",T:()=>fp},{no:5,name:"ice_trickle",kind:"message",oneof:"eventPayload",T:()=>vo},{no:6,name:"change_publish_quality",kind:"message",oneof:"eventPayload",T:()=>Tp},{no:10,name:"participant_joined",kind:"message",oneof:"eventPayload",T:()=>zf},{no:11,name:"participant_left",kind:"message",oneof:"eventPayload",T:()=>Xf},{no:12,name:"dominant_speaker_changed",kind:"message",oneof:"eventPayload",T:()=>up},{no:13,name:"join_response",kind:"message",oneof:"eventPayload",T:()=>Jf},{no:14,name:"health_check_response",kind:"message",oneof:"eventPayload",T:()=>Uf},{no:16,name:"track_published",kind:"message",oneof:"eventPayload",T:()=>Mf},{no:17,name:"track_unpublished",kind:"message",oneof:"eventPayload",T:()=>$f},{no:18,name:"error",kind:"message",oneof:"eventPayload",T:()=>Rf},{no:19,name:"call_grants_updated",kind:"message",oneof:"eventPayload",T:()=>kp},{no:20,name:"go_away",kind:"message",oneof:"eventPayload",T:()=>Ep},{no:21,name:"ice_restart",kind:"message",oneof:"eventPayload",T:()=>Of},{no:22,name:"pins_updated",kind:"message",oneof:"eventPayload",T:()=>wf},{no:23,name:"call_ended",kind:"message",oneof:"eventPayload",T:()=>_p},{no:24,name:"participant_updated",kind:"message",oneof:"eventPayload",T:()=>Qf},{no:25,name:"participant_migration_complete",kind:"message",oneof:"eventPayload",T:()=>Cf},{no:27,name:"change_publish_options",kind:"message",oneof:"eventPayload",T:()=>Sf}])}}const Rr=new bf;class yf extends k{constructor(){super("stream.video.sfu.event.ChangePublishOptions",[{no:1,name:"publish_options",kind:"message",repeat:1,T:()=>Ut},{no:2,name:"reason",kind:"scalar",T:9}])}}const Sf=new yf;class vf extends k{constructor(){super("stream.video.sfu.event.ChangePublishOptionsComplete",[])}}new vf;class Tf extends k{constructor(){super("stream.video.sfu.event.ParticipantMigrationComplete",[])}}const Cf=new Tf;class kf extends k{constructor(){super("stream.video.sfu.event.PinsChanged",[{no:1,name:"pins",kind:"message",repeat:1,T:()=>yo}])}}const wf=new kf;class Ef extends k{constructor(){super("stream.video.sfu.event.Error",[{no:4,name:"error",kind:"message",T:()=>_e},{no:5,name:"reconnect_strategy",kind:"enum",T:()=>["stream.video.sfu.models.WebsocketReconnectStrategy",N,"WEBSOCKET_RECONNECT_STRATEGY_"]}])}}const Rf=new Ef;class _f extends k{constructor(){super("stream.video.sfu.event.ICETrickle",[{no:1,name:"peer_type",kind:"enum",T:()=>["stream.video.sfu.models.PeerType",ee,"PEER_TYPE_"]},{no:2,name:"ice_candidate",kind:"scalar",T:9}])}}new _f;class If extends k{constructor(){super("stream.video.sfu.event.ICERestart",[{no:1,name:"peer_type",kind:"enum",T:()=>["stream.video.sfu.models.PeerType",ee,"PEER_TYPE_"]}])}}const Of=new If;class Pf extends k{constructor(){super("stream.video.sfu.event.SfuRequest",[{no:1,name:"join_request",kind:"message",oneof:"requestPayload",T:()=>To},{no:2,name:"health_check_request",kind:"message",oneof:"requestPayload",T:()=>xf},{no:3,name:"leave_call_request",kind:"message",oneof:"requestPayload",T:()=>Nf}])}}const Ct=new Pf;class Af extends k{constructor(){super("stream.video.sfu.event.LeaveCallRequest",[{no:1,name:"session_id",kind:"scalar",T:9},{no:2,name:"reason",kind:"scalar",T:9}])}}const Nf=new Af;class Df extends k{constructor(){super("stream.video.sfu.event.HealthCheckRequest",[])}}const xf=new Df;class Lf extends k{constructor(){super("stream.video.sfu.event.HealthCheckResponse",[{no:1,name:"participant_count",kind:"message",T:()=>bo}])}}const Uf=new Lf;class Ff extends k{constructor(){super("stream.video.sfu.event.TrackPublished",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",C,"TRACK_TYPE_"]},{no:4,name:"participant",kind:"message",T:()=>mt}])}}const Mf=new Ff;class jf extends k{constructor(){super("stream.video.sfu.event.TrackUnpublished",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",C,"TRACK_TYPE_"]},{no:4,name:"cause",kind:"enum",T:()=>["stream.video.sfu.models.TrackUnpublishReason",An,"TRACK_UNPUBLISH_REASON_"]},{no:5,name:"participant",kind:"message",T:()=>mt}])}}const $f=new jf;class Bf extends k{constructor(){super("stream.video.sfu.event.JoinRequest",[{no:1,name:"token",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"subscriber_sdp",kind:"scalar",T:9},{no:8,name:"publisher_sdp",kind:"scalar",T:9},{no:4,name:"client_details",kind:"message",T:()=>oh},{no:5,name:"migration",kind:"message",T:()=>Gf},{no:6,name:"fast_reconnect",kind:"scalar",T:8},{no:7,name:"reconnect_details",kind:"message",T:()=>Hf},{no:9,name:"preferred_publish_options",kind:"message",repeat:1,T:()=>Ut},{no:10,name:"preferred_subscribe_options",kind:"message",repeat:1,T:()=>So}])}}const To=new Bf;class Vf extends k{constructor(){super("stream.video.sfu.event.ReconnectDetails",[{no:1,name:"strategy",kind:"enum",T:()=>["stream.video.sfu.models.WebsocketReconnectStrategy",N,"WEBSOCKET_RECONNECT_STRATEGY_"]},{no:3,name:"announced_tracks",kind:"message",repeat:1,T:()=>hi},{no:4,name:"subscriptions",kind:"message",repeat:1,T:()=>fi},{no:5,name:"reconnect_attempt",kind:"scalar",T:13},{no:6,name:"from_sfu_id",kind:"scalar",T:9},{no:7,name:"previous_session_id",kind:"scalar",T:9},{no:8,name:"reason",kind:"scalar",T:9}])}}const Hf=new Vf;class qf extends k{constructor(){super("stream.video.sfu.event.Migration",[{no:1,name:"from_sfu_id",kind:"scalar",T:9},{no:2,name:"announced_tracks",kind:"message",repeat:1,T:()=>hi},{no:3,name:"subscriptions",kind:"message",repeat:1,T:()=>fi}])}}const Gf=new qf;class Wf extends k{constructor(){super("stream.video.sfu.event.JoinResponse",[{no:1,name:"call_state",kind:"message",T:()=>Wd},{no:2,name:"reconnected",kind:"scalar",T:8},{no:3,name:"fast_reconnect_deadline_seconds",kind:"scalar",T:5},{no:4,name:"publish_options",kind:"message",repeat:1,T:()=>Ut}])}}const Jf=new Wf;class Kf extends k{constructor(){super("stream.video.sfu.event.ParticipantJoined",[{no:1,name:"call_cid",kind:"scalar",T:9},{no:2,name:"participant",kind:"message",T:()=>mt}])}}const zf=new Kf;class Yf extends k{constructor(){super("stream.video.sfu.event.ParticipantLeft",[{no:1,name:"call_cid",kind:"scalar",T:9},{no:2,name:"participant",kind:"message",T:()=>mt}])}}const Xf=new Yf;class Zf extends k{constructor(){super("stream.video.sfu.event.ParticipantUpdated",[{no:1,name:"call_cid",kind:"scalar",T:9},{no:2,name:"participant",kind:"message",T:()=>mt}])}}const Qf=new Zf;class ep extends k{constructor(){super("stream.video.sfu.event.SubscriberOffer",[{no:1,name:"ice_restart",kind:"scalar",T:8},{no:2,name:"sdp",kind:"scalar",T:9}])}}const tp=new ep;class np extends k{constructor(){super("stream.video.sfu.event.PublisherAnswer",[{no:1,name:"sdp",kind:"scalar",T:9}])}}const sp=new np;class ip extends k{constructor(){super("stream.video.sfu.event.ConnectionQualityChanged",[{no:1,name:"connection_quality_updates",kind:"message",repeat:1,T:()=>op}])}}const rp=new ip;class ap extends k{constructor(){super("stream.video.sfu.event.ConnectionQualityInfo",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"connection_quality",kind:"enum",T:()=>["stream.video.sfu.models.ConnectionQuality",Pn,"CONNECTION_QUALITY_"]}])}}const op=new ap;class cp extends k{constructor(){super("stream.video.sfu.event.DominantSpeakerChanged",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9}])}}const up=new cp;class lp extends k{constructor(){super("stream.video.sfu.event.AudioLevel",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"level",kind:"scalar",T:2},{no:4,name:"is_speaking",kind:"scalar",T:8}])}}const dp=new lp;class hp extends k{constructor(){super("stream.video.sfu.event.AudioLevelChanged",[{no:1,name:"audio_levels",kind:"message",repeat:1,T:()=>dp}])}}const fp=new hp;class pp extends k{constructor(){super("stream.video.sfu.event.AudioSender",[{no:2,name:"codec",kind:"message",T:()=>De},{no:3,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",C,"TRACK_TYPE_"]},{no:4,name:"publish_option_id",kind:"scalar",T:5}])}}const mp=new pp;class gp extends k{constructor(){super("stream.video.sfu.event.VideoLayerSetting",[{no:1,name:"name",kind:"scalar",T:9},{no:2,name:"active",kind:"scalar",T:8},{no:3,name:"max_bitrate",kind:"scalar",T:5},{no:4,name:"scale_resolution_down_by",kind:"scalar",T:2},{no:6,name:"codec",kind:"message",T:()=>De},{no:7,name:"max_framerate",kind:"scalar",T:13},{no:8,name:"scalability_mode",kind:"scalar",T:9}])}}const bp=new gp;class yp extends k{constructor(){super("stream.video.sfu.event.VideoSender",[{no:2,name:"codec",kind:"message",T:()=>De},{no:3,name:"layers",kind:"message",repeat:1,T:()=>bp},{no:4,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",C,"TRACK_TYPE_"]},{no:5,name:"publish_option_id",kind:"scalar",T:5}])}}const Sp=new yp;class vp extends k{constructor(){super("stream.video.sfu.event.ChangePublishQuality",[{no:1,name:"audio_senders",kind:"message",repeat:1,T:()=>mp},{no:2,name:"video_senders",kind:"message",repeat:1,T:()=>Sp}])}}const Tp=new vp;class Cp extends k{constructor(){super("stream.video.sfu.event.CallGrantsUpdated",[{no:1,name:"current_grants",kind:"message",T:()=>vh},{no:2,name:"message",kind:"scalar",T:9}])}}const kp=new Cp;class wp extends k{constructor(){super("stream.video.sfu.event.GoAway",[{no:1,name:"reason",kind:"enum",T:()=>["stream.video.sfu.models.GoAwayReason",Vs,"GO_AWAY_REASON_"]}])}}const Ep=new wp;class Rp extends k{constructor(){super("stream.video.sfu.event.CallEnded",[{no:1,name:"reason",kind:"enum",T:()=>["stream.video.sfu.models.CallEndedReason",Lt,"CALL_ENDED_REASON_"]}])}}const _p=new Rp;var K;(function(t){t.UNKNOWN="UNKNOWN",t.VISIBLE="VISIBLE",t.INVISIBLE="INVISIBLE"})(K||(K={}));var ke;(function(t){t[t.IMMEDIATE=20]="IMMEDIATE",t[t.FAST=100]="FAST",t[t.MEDIUM=600]="MEDIUM",t[t.SLOW=1200]="SLOW"})(ke||(ke={}));class Ip{constructor(e){this._transport=e,this.typeName=Ss.typeName,this.methods=Ss.methods,this.options=Ss.options}setPublisher(e,s){const n=this.methods[0],i=this._transport.mergeOptions(s);return Oe("unary",this._transport,n,i,e)}sendAnswer(e,s){const n=this.methods[1],i=this._transport.mergeOptions(s);return Oe("unary",this._transport,n,i,e)}iceTrickle(e,s){const n=this.methods[2],i=this._transport.mergeOptions(s);return Oe("unary",this._transport,n,i,e)}updateSubscriptions(e,s){const n=this.methods[3],i=this._transport.mergeOptions(s);return Oe("unary",this._transport,n,i,e)}updateMuteStates(e,s){const n=this.methods[4],i=this._transport.mergeOptions(s);return Oe("unary",this._transport,n,i,e)}iceRestart(e,s){const n=this.methods[5],i=this._transport.mergeOptions(s);return Oe("unary",this._transport,n,i,e)}sendStats(e,s){const n=this.methods[6],i=this._transport.mergeOptions(s);return Oe("unary",this._transport,n,i,e)}startNoiseCancellation(e,s){const n=this.methods[7],i=this._transport.mergeOptions(s);return Oe("unary",this._transport,n,i,e)}stopNoiseCancellation(e,s){const n=this.methods[8],i=this._transport.mergeOptions(s);return Oe("unary",this._transport,n,i,e)}}const Op={baseUrl:"",sendJson:!0,timeout:5*1e3,jsonOptions:{ignoreUnknownFields:!0}},Pp=t=>({interceptUnary(e,s,n,i){return i.meta={...i.meta,...t},e(s,n,i)}}),Ap=(t,e)=>({interceptUnary:(s,n,i,r)=>{const a=s(n,i,r);return t(e,`Invoked SFU RPC method ${n.name}`,{request:a.request,headers:a.requestHeaders,response:a.response}),a}}),Np=t=>{const e=(n,i,r)=>t(`${n}OnFailure`,[r,i]),s={SendStats:!0};return{interceptUnary(n,i,r,a){if(s[i.name])return n(i,r,a);t(i.name,r);const o=n(i,r,a);return o.then(l=>{var d;const u=(d=l.response)==null?void 0:d.error;u&&e(i.name,r,u)},l=>e(i.name,r,l)),o}}},Dp=t=>{const e=new Ol({...Op,...t});return new Ip(e)},Me=t=>new Promise(e=>setTimeout(e,t));function Ws(t){return t&&(Object.prototype.toString.call(t)==="[object Function]"||typeof t=="function"||t instanceof Function)}const st={TOKEN_EXPIRED:40,WS_CLOSED_SUCCESS:1e3};function qt(t){const e=Math.min(500+t*2e3,5e3),s=Math.min(Math.max(250,(t-1)*2e3),5e3);return Math.floor(Math.random()*(e-s)+s)}function kt(t){let e="";for(let s=0;s<t.length;s++)e+=t[s].toString(16).padStart(2,"0");return e}function Js(){const t=Lp(16);return t[6]=t[6]&15|64,t[8]=t[8]&191|128,[kt(t.subarray(0,4)),kt(t.subarray(4,6)),kt(t.subarray(6,8)),kt(t.subarray(8,10)),kt(t.subarray(10,16))].join("-")}const xp=typeof crypto<"u"&&crypto.getRandomValues?crypto.getRandomValues.bind(crypto):function(e){const s=Math.pow(2,8*e.byteLength/e.length);for(let n=0;n<e.length;n++)e[n]=Math.random()*s};function Lp(t){const e=new Uint8Array(t);return xp(e),e}function Ks(t){typeof window<"u"&&window.addEventListener&&(window.addEventListener("offline",t),window.addEventListener("online",t))}function Co(t){typeof window<"u"&&window.removeEventListener&&(window.removeEventListener("offline",t),window.removeEventListener("online",t))}function Up(t){return!t.status||t.status<200||300<=t.status}function Fp(t){return t.code!==void 0}const Z=()=>{var t;return typeof navigator>"u"?!1:((t=navigator.product)==null?void 0:t.toLowerCase())==="reactnative"},_r=Object.freeze({trace:0,debug:1,info:2,warn:3,error:4});let ko,pi="info";const wo=(t,e,...s)=>{let n;switch(t){case"error":if(Z()){e=`ERROR: ${e}`,n=console.info;break}n=console.error;break;case"warn":if(Z()){e=`WARN: ${e}`,n=console.info;break}n=console.warn;break;case"info":n=console.info;break;case"trace":n=console.trace;break;default:n=console.log;break}n(e,...s)},Mp=(t,e)=>{ko=t,jp(e)},jp=t=>{pi=t},zs=()=>pi,j=t=>{const e=ko||wo,s=(t||[]).filter(Boolean).join(":");return(i,r,...a)=>{_r[i]>=_r[pi]&&e(i,`[${s}]: ${r}`,...a)}},Ue=async(t,e)=>{var i;let s=0,n;do{s>0&&await Me(qt(s));try{n=await t()}catch(r){const a=r instanceof ce&&r.code===q[q.cancelled],o=(e==null?void 0:e.aborted)??!1;if(a||o)throw r;j(["sfu-client","rpc"])("debug",`rpc failed (${s})`,r),s++}}while(!n||(i=n.response.error)!=null&&i.shouldRetry);return n},Ir=async t=>{const e=new RTCPeerConnection;e.addTransceiver("video",{direction:t}),e.addTransceiver("audio",{direction:t});const n=(await e.createOffer()).sdp??"";return e.getTransceivers().forEach(i=>{var r;(r=i.stop)==null||r.call(i)}),e.close(),n},Ys=t=>t?(t=t.toLowerCase(),t==="vp9"||t==="av1"||t==="video/vp9"||t==="video/av1"):!1,$p={subscriberOffer:void 0,publisherAnswer:void 0,connectionQualityChanged:void 0,audioLevelChanged:void 0,iceTrickle:void 0,changePublishQuality:void 0,participantJoined:void 0,participantLeft:void 0,dominantSpeakerChanged:void 0,joinResponse:void 0,healthCheckResponse:void 0,trackPublished:void 0,trackUnpublished:void 0,error:void 0,callGrantsUpdated:void 0,goAway:void 0,iceRestart:void 0,pinsUpdated:void 0,callEnded:void 0,participantUpdated:void 0,participantMigrationComplete:void 0,changePublishOptions:void 0},Or=t=>Object.prototype.hasOwnProperty.call($p,t);class Bp{constructor(){this.logger=j(["Dispatcher"]),this.subscribers={},this.dispatch=(e,s="0")=>{const n=e.eventPayload.oneofKind;if(!n)return;const i=e.eventPayload[n];this.logger("debug",`Dispatching ${n}, tag=${s}`,i);const r=this.subscribers[n];if(r)for(const a of r)try{a(i)}catch(o){this.logger("warn","Listener failed with error",o)}},this.on=(e,s)=>{var n;return((n=this.subscribers)[e]??(n[e]=[])).push(s),()=>{this.off(e,s)}},this.off=(e,s)=>{this.subscribers[e]=(this.subscribers[e]||[]).filter(n=>n!==s)}}}class Vp{constructor(){this.subscriberCandidates=new js,this.publisherCandidates=new js,this.push=e=>{const s=Hp(e);s&&(e.peerType===ee.SUBSCRIBER?this.subscriberCandidates.next(s):e.peerType===ee.PUBLISHER_UNSPECIFIED?this.publisherCandidates.next(s):j(["sfu-client"])("warn","ICETrickle, Unknown peer type",e))},this.dispose=()=>{this.subscriberCandidates.complete(),this.publisherCandidates.complete()}}}const Hp=t=>{try{return JSON.parse(t.iceCandidate)}catch(e){j(["sfu-client"])("error","Failed to parse ICE Trickle",e,t);return}},de=Eo(Gp),vs=Eo(Wp),lt=new Map;function qp(t){return lt.has(t)}async function Ts(t){let e;for(;e=lt.get(t);)await e.promise}function Eo(t){return function(s,n){const{cb:i,onContinued:r}=t(s,n),a=lt.get(s);a==null||a.onContinued();const o=a?a.promise.then(i,i):i();return lt.set(s,{promise:o,onContinued:r}),o}}function Gp(t,e){let s=!1;return{cb:()=>e().finally(()=>{s||lt.delete(t)}),onContinued:()=>s=!0}}function Wp(t,e){const s=new AbortController;return{cb:()=>s.signal.aborted?Promise.resolve("canceled"):e(s.signal).finally(()=>{s.signal.aborted||lt.delete(t)}),onContinued:()=>s.abort()}}const Jp=t=>typeof t=="function",X=t=>{let e,s;if(ze([t]).subscribe({next:([n])=>{e=n},error:n=>{s=n}}).unsubscribe(),s)throw s;return e},Q=(t,e)=>{const s=Jp(e)?e(X(t)):e;return t.next(s),s},Kp=(t,e)=>{const s=t.getValue(),n=Q(t,e);return{lastValue:s,value:n,rollback:()=>Q(t,s)}},ye=(t,e,s=n=>j(["RxUtils"])("warn","An observable emitted an error",n))=>{const n=t.subscribe({next:e,error:s});return()=>{n.unsubscribe()}},mi=(t,e)=>{const s=Symbol();return ye(t,n=>{de(s,()=>e(n))})};var R;(function(t){t.UNKNOWN="unknown",t.IDLE="idle",t.RINGING="ringing",t.JOINING="joining",t.JOINED="joined",t.LEFT="left",t.RECONNECTING="reconnecting",t.MIGRATING="migrating",t.RECONNECTING_FAILED="reconnecting-failed",t.OFFLINE="offline"})(R||(R={}));class zp{constructor(){this.connectedUserSubject=new A(void 0),this.callsSubject=new A([]),this.setConnectedUser=e=>Q(this.connectedUserSubject,e),this.setCalls=e=>Q(this.callsSubject,e),this.registerCall=e=>{this.calls.find(s=>s.cid===e.cid)||this.setCalls(s=>[...s,e])},this.unregisterCall=e=>(j(["client-state"])("trace",`Unregistering call: ${e.cid}`),this.setCalls(n=>n.filter(i=>i!==e))),this.findCall=(e,s)=>this.calls.find(n=>n.type===e&&n.id===s),this.connectedUserSubject.subscribe(async e=>{if(!e){const s=j(["client-state"]);for(const n of this.calls)n.state.callingState!==R.LEFT&&(s("info",`User disconnected, leaving call: ${n.cid}`),await n.leave({message:"client.disconnectUser() called"}).catch(i=>{s("error",`Error leaving call: ${n.cid}`,i)}))}})}get connectedUser(){return X(this.connectedUserSubject)}get calls(){return X(this.callsSubject)}}class Yp{constructor(e){this.getCurrentValue=X,this.connectedUser$=e.connectedUserSubject.asObservable(),this.calls$=e.callsSubject.asObservable()}get connectedUser(){return X(this.connectedUser$)}get calls(){return X(this.calls$)}}const je=(...t)=>(e,s)=>{for(const n of t){const i=n(e,s);if(i!==0)return i}return 0},Ro=t=>e=>(s,n)=>t(s,n)?e(s,n):0,it=t=>t.publishedTracks.includes(C.VIDEO),nn=t=>t.publishedTracks.includes(C.AUDIO),qe=t=>t.publishedTracks.includes(C.SCREEN_SHARE),Xp=t=>t.publishedTracks.includes(C.SCREEN_SHARE_AUDIO),zn=(t,e)=>t.isDominantSpeaker&&!e.isDominantSpeaker?-1:!t.isDominantSpeaker&&e.isDominantSpeaker?1:0,Yn=(t,e)=>t.isSpeaking&&!e.isSpeaking?-1:!t.isSpeaking&&e.isSpeaking?1:0,_o=(t,e)=>qe(t)&&!qe(e)?-1:!qe(t)&&qe(e)?1:0,Xn=(t,e)=>it(t)&&!it(e)?-1:!it(t)&&it(e)?1:0,Zn=(t,e)=>nn(t)&&!nn(e)?-1:!nn(t)&&nn(e)?1:0,gi=(t,e)=>{if(t.pin&&e.pin){if(!t.pin.isLocalPin&&e.pin.isLocalPin)return-1;if(t.pin.isLocalPin&&!e.pin.isLocalPin)return 1;if(t.pin.pinnedAt>e.pin.pinnedAt)return-1;if(t.pin.pinnedAt<e.pin.pinnedAt)return 1}return t.pin&&!e.pin?-1:!t.pin&&e.pin?1:0},Qn=t=>(e,s)=>{var n,i,r,a;return((n=e.reaction)==null?void 0:n.type)===t&&((i=s.reaction)==null?void 0:i.type)!==t?-1:((r=e.reaction)==null?void 0:r.type)!==t&&((a=s.reaction)==null?void 0:a.type)===t?1:0},Zp=(...t)=>(e,s)=>sn(e,t)&&!sn(s,t)?-1:!sn(e,t)&&sn(s,t)?1:0,sn=(t,e)=>(t.roles||[]).some(s=>e.includes(s)),bi=Ro((t,e)=>{var s,n;return((s=t.viewportVisibilityState)==null?void 0:s.videoTrack)===K.INVISIBLE||((n=e.viewportVisibilityState)==null?void 0:n.videoTrack)===K.INVISIBLE}),Qp=Ro((t,e)=>{var s,n,i,r;return((s=t.viewportVisibilityState)==null?void 0:s.videoTrack)===K.INVISIBLE||((n=t.viewportVisibilityState)==null?void 0:n.videoTrack)===K.UNKNOWN||((i=e.viewportVisibilityState)==null?void 0:i.videoTrack)===K.INVISIBLE||((r=e.viewportVisibilityState)==null?void 0:r.videoTrack)===K.UNKNOWN}),Nn=je(gi,_o,bi(je(zn,Yn,Qn("raised-hand"),Xn,Zn)));je(gi,_o,zn,bi(je(Yn,Qn("raised-hand"),Xn,Zn)));je(gi,Qp(je(zn,Yn,Qn("raised-hand"),Xn,Zn)));const Pr=je(bi(je(zn,Yn,Qn("raised-hand"),Xn,Zn)),Zp("admin","host","speaker")),Ar={broadcasting:!1,hls:{playlist_url:"",status:""},rtmps:[]};class em{constructor(){this.backstageSubject=new A(!0),this.blockedUserIdsSubject=new A([]),this.createdAtSubject=new A(new Date),this.endedAtSubject=new A(void 0),this.startsAtSubject=new A(void 0),this.updatedAtSubject=new A(new Date),this.createdBySubject=new A(void 0),this.customSubject=new A({}),this.egressSubject=new A(void 0),this.ingressSubject=new A(void 0),this.recordingSubject=new A(!1),this.sessionSubject=new A(void 0),this.settingsSubject=new A(void 0),this.transcribingSubject=new A(!1),this.captioningSubject=new A(!1),this.endedBySubject=new A(void 0),this.thumbnailsSubject=new A(void 0),this.membersSubject=new A([]),this.ownCapabilitiesSubject=new A([]),this.callingStateSubject=new A(R.UNKNOWN),this.startedAtSubject=new A(void 0),this.participantCountSubject=new A(0),this.anonymousParticipantCountSubject=new A(0),this.participantsSubject=new A([]),this.callStatsReportSubject=new A(void 0),this.closedCaptionsSubject=new A([]),this.orphanedTracks=[],this.logger=j(["CallState"]),this.sortParticipantsBy=Nn,this.closedCaptionsTasks=new Map,this.dispose=()=>{for(const[n,i]of this.closedCaptionsTasks.entries())clearTimeout(i),this.closedCaptionsTasks.delete(n)},this.setSortParticipantsBy=n=>{this.sortParticipantsBy=n,this.setCurrentValue(this.participantsSubject,i=>i)},this.getCurrentValue=X,this.setCurrentValue=Q,this.setParticipantCount=n=>this.setCurrentValue(this.participantCountSubject,n),this.setStartedAt=n=>this.setCurrentValue(this.startedAtSubject,n),this.setCaptioning=n=>Kp(this.captioningSubject,n),this.setAnonymousParticipantCount=n=>this.setCurrentValue(this.anonymousParticipantCountSubject,n),this.setParticipants=n=>this.setCurrentValue(this.participantsSubject,n),this.setCallingState=n=>this.setCurrentValue(this.callingStateSubject,n),this.setCallStatsReport=n=>this.setCurrentValue(this.callStatsReportSubject,n),this.setMembers=n=>{this.setCurrentValue(this.membersSubject,n)},this.setOwnCapabilities=n=>this.setCurrentValue(this.ownCapabilitiesSubject,n),this.setBackstage=n=>this.setCurrentValue(this.backstageSubject,n),this.setEndedAt=n=>this.setCurrentValue(this.endedAtSubject,n),this.findParticipantBySessionId=n=>this.participants.find(i=>i.sessionId===n),this.getParticipantLookupBySessionId=()=>this.participants.reduce((n,i)=>(n[i.sessionId]=i,n),{}),this.updateParticipant=(n,i)=>{const r=this.findParticipantBySessionId(n);if(!r){this.logger("warn",`Participant with sessionId ${n} not found`);return}const a=typeof i=="function"?i(r):i,o={...r,...a};return this.setParticipants(l=>l.map(u=>u.sessionId===n?o:u))},this.updateOrAddParticipant=(n,i)=>this.setParticipants(r=>{let a=!0;const o=r.map(l=>l.sessionId===n?(a=!1,{...l,...i}):l);return a&&o.push(i),o}),this.updateParticipants=n=>Object.keys(n).length===0?this.participants:this.setParticipants(i=>i.map(r=>{const a=n[r.sessionId];return a?{...r,...a}:r})),this.updateParticipantTracks=(n,i)=>this.updateParticipants(Object.entries(i).reduce((r,[a,o])=>{o.dimension&&(o.dimension.height=Math.ceil(o.dimension.height),o.dimension.width=Math.ceil(o.dimension.width));const l=n==="videoTrack"?"videoDimension":n==="screenShareTrack"?"screenShareDimension":void 0;return l&&(r[a]={[l]:o.dimension}),r},{})),this.updateFromEvent=n=>{const i=this.eventHandlers[n.type];i&&i(n)},this.setServerSidePins=n=>{const i=n.reduce((r,a)=>(r[a.sessionId]=Date.now(),r),{});return this.setParticipants(r=>r.map(a=>{const o=i[a.sessionId];return o?{...a,pin:{isLocalPin:!1,pinnedAt:o}}:a.pin&&!a.pin.isLocalPin?{...a,pin:void 0}:a}))},this.registerOrphanedTrack=n=>{this.orphanedTracks.push(n)},this.removeOrphanedTrack=n=>{this.orphanedTracks=this.orphanedTracks.filter(i=>i.id!==n)},this.takeOrphanedTracks=n=>{const i=this.orphanedTracks.filter(r=>r.trackLookupPrefix===n);return i.length>0&&(this.orphanedTracks=this.orphanedTracks.filter(r=>r.trackLookupPrefix!==n)),i},this.updateClosedCaptionSettings=n=>{this.closedCaptionsSettings={...this.closedCaptionsSettings,...n}},this.updateFromCallResponse=n=>{this.setBackstage(n.backstage),this.setCurrentValue(this.blockedUserIdsSubject,n.blocked_user_ids),this.setCurrentValue(this.createdAtSubject,new Date(n.created_at)),this.setCurrentValue(this.updatedAtSubject,new Date(n.updated_at)),this.setCurrentValue(this.startsAtSubject,n.starts_at?new Date(n.starts_at):void 0),this.setEndedAt(n.ended_at?new Date(n.ended_at):void 0),this.setCurrentValue(this.createdBySubject,n.created_by),this.setCurrentValue(this.customSubject,n.custom),this.setCurrentValue(this.egressSubject,n.egress),this.setCurrentValue(this.ingressSubject,n.ingress),this.setCurrentValue(this.recordingSubject,n.recording);const i=this.setCurrentValue(this.sessionSubject,n.session);this.updateParticipantCountFromSession(i),this.setCurrentValue(this.settingsSubject,n.settings),this.setCurrentValue(this.transcribingSubject,n.transcribing),this.setCurrentValue(this.captioningSubject,n.captioning),this.setCurrentValue(this.thumbnailsSubject,n.thumbnails)},this.updateFromSfuCallState=(n,i,r)=>{const{participants:a,participantCount:o,startedAt:l,pins:u}=n,d=(r==null?void 0:r.announcedTracks.map(m=>m.trackType))??[];this.setParticipants(()=>{const m=this.getParticipantLookupBySessionId();return a.map(c=>{const h=m[c.sessionId],f=c.sessionId===i;return Object.assign({},h,c,{isLocalParticipant:f,publishedTracks:f?d:c.publishedTracks,viewportVisibilityState:(h==null?void 0:h.viewportVisibilityState)??{videoTrack:K.UNKNOWN,screenShareTrack:K.UNKNOWN}})})}),this.setParticipantCount((o==null?void 0:o.total)||0),this.setAnonymousParticipantCount((o==null?void 0:o.anonymous)||0),this.setStartedAt(l?Dt.toDate(l):new Date),this.setServerSidePins(u)},this.updateFromMemberRemoved=n=>{this.updateFromCallResponse(n.call),this.setCurrentValue(this.membersSubject,i=>i.filter(r=>n.members.indexOf(r.user_id)===-1))},this.updateFromMemberAdded=n=>{this.updateFromCallResponse(n.call),this.setCurrentValue(this.membersSubject,i=>[...i,...n.members])},this.updateFromHLSBroadcastStopped=()=>{this.setCurrentValue(this.egressSubject,(n=Ar)=>({...n,broadcasting:!1,hls:{...n.hls,status:""}}))},this.updateFromHLSBroadcastingFailed=()=>{this.setCurrentValue(this.egressSubject,(n=Ar)=>({...n,broadcasting:!1,hls:{...n.hls,status:""}}))},this.updateParticipantCountFromSession=n=>{if(!n||this.callingState===R.JOINED)return;const i=Object.values(n.participants_count_by_role).reduce((a,o)=>a+o,0),r=Math.max(i,n.participants.length);this.setParticipantCount(r),this.setAnonymousParticipantCount(n.anonymous_participant_count||0)},this.updateFromSessionParticipantCountUpdate=n=>{const i=this.setCurrentValue(this.sessionSubject,r=>r&&{...r,anonymous_participant_count:n.anonymous_participant_count,participants_count_by_role:n.participants_count_by_role});this.updateParticipantCountFromSession(i)},this.updateFromSessionParticipantLeft=n=>{const i=this.setCurrentValue(this.sessionSubject,r=>{if(!r)return r;const{participants:a,participants_count_by_role:o}=r,{user:l,user_session_id:u}=n.participant;return{...r,participants:a.filter(d=>d.user_session_id!==u),participants_count_by_role:{...o,[l.role]:Math.max(0,(o[l.role]||0)-1)}}});this.updateParticipantCountFromSession(i)},this.updateFromSessionParticipantJoined=n=>{const i=this.setCurrentValue(this.sessionSubject,r=>{if(!r)return r;const{participants:a,participants_count_by_role:o}=r,{user:l,user_session_id:u}=n.participant;let d=!0;const m=a.map(h=>h.user_session_id===u?(d=!1,n.participant):h);d&&m.push(n.participant);const c=d?1:0;return{...r,participants:m,participants_count_by_role:{...o,[l.role]:(o[l.role]||0)+c}}});this.updateParticipantCountFromSession(i)},this.updateMembers=n=>{this.updateFromCallResponse(n.call),this.setCurrentValue(this.membersSubject,i=>i.map(r=>{const a=n.members.find(o=>o.user_id===r.user_id);return a||r}))},this.updateParticipantReaction=n=>{const{user:i,custom:r,type:a,emoji_code:o}=n.reaction;this.setParticipants(l=>l.map(u=>u.userId!==i.id?u:{...u,reaction:{type:a,emoji_code:o,custom:r}}))},this.unblockUser=n=>{this.setCurrentValue(this.blockedUserIdsSubject,i=>i&&i.filter(r=>r!==n.user.id))},this.blockUser=n=>{this.setCurrentValue(this.blockedUserIdsSubject,i=>[...i||[],n.user.id])},this.updateOwnCapabilities=n=>{var i;n.user.id===((i=this.localParticipant)==null?void 0:i.userId)&&this.setCurrentValue(this.ownCapabilitiesSubject,n.own_capabilities)},this.updateFromClosedCaptions=n=>{this.setCurrentValue(this.closedCaptionsSubject,i=>{const{closed_caption:r}=n,a=c=>`${c.speaker_id}/${c.start_time}`,o=a(r);if(i.some(c=>a(c)===o))return i;const u=[...i,r],{visibilityDurationMs:d=2700,maxVisibleCaptions:m=2}=this.closedCaptionsSettings||{};if(d>0){const c=setTimeout(()=>{this.setCurrentValue(this.closedCaptionsSubject,h=>h.filter(f=>f!==r)),this.closedCaptionsTasks.delete(o)},d);this.closedCaptionsTasks.set(o,c);for(let h=0;h<u.length-m;h++){const f=a(u[h]),p=this.closedCaptionsTasks.get(f);clearTimeout(p),this.closedCaptionsTasks.delete(f)}}return u.slice(-m)})},this.rawParticipants$=this.participantsSubject.asObservable().pipe(te({bufferSize:1,refCount:!0})),this.participants$=this.participantsSubject.asObservable().pipe(ne(n=>n.sort(this.sortParticipantsBy)),te({bufferSize:1,refCount:!0})),this.localParticipant$=this.participants$.pipe(ne(n=>n.find(i=>i.isLocalParticipant)),te({bufferSize:1,refCount:!0})),this.remoteParticipants$=this.participants$.pipe(ne(n=>n.filter(i=>!i.isLocalParticipant)),te({bufferSize:1,refCount:!0})),this.pinnedParticipants$=this.participants$.pipe(ne(n=>n.filter(i=>!!i.pin)),te({bufferSize:1,refCount:!0})),this.dominantSpeaker$=this.participants$.pipe(ne(n=>n.find(i=>i.isDominantSpeaker)),te({bufferSize:1,refCount:!0})),this.hasOngoingScreenShare$=this.participants$.pipe(ne(n=>n.some(i=>qe(i))),ae(),te({bufferSize:1,refCount:!0})),this.createdAt$=this.createdAtSubject.asObservable(),this.endedAt$=this.endedAtSubject.asObservable(),this.startsAt$=this.startsAtSubject.asObservable(),this.startedAt$=this.startedAtSubject.asObservable(),this.updatedAt$=this.updatedAtSubject.asObservable(),this.callStatsReport$=this.callStatsReportSubject.asObservable(),this.members$=this.membersSubject.asObservable(),this.createdBy$=this.createdBySubject.asObservable(),this.custom$=this.customSubject.asObservable(),this.egress$=this.egressSubject.asObservable(),this.ingress$=this.ingressSubject.asObservable(),this.session$=this.sessionSubject.asObservable(),this.settings$=this.settingsSubject.asObservable(),this.endedBy$=this.endedBySubject.asObservable(),this.thumbnails$=this.thumbnailsSubject.asObservable(),this.closedCaptions$=this.closedCaptionsSubject.asObservable();const e=(n,i)=>{if(n.length!==i.length)return!1;for(const r of n)if(!i.includes(r))return!1;for(const r of i)if(!n.includes(r))return!1;return!0},s=(n,i)=>n.asObservable().pipe(ae(i));this.anonymousParticipantCount$=s(this.anonymousParticipantCountSubject),this.blockedUserIds$=s(this.blockedUserIdsSubject,e),this.backstage$=s(this.backstageSubject),this.callingState$=s(this.callingStateSubject),this.ownCapabilities$=s(this.ownCapabilitiesSubject,e),this.participantCount$=s(this.participantCountSubject),this.recording$=s(this.recordingSubject),this.transcribing$=s(this.transcribingSubject),this.captioning$=s(this.captioningSubject),this.eventHandlers={"call.frame_recording_ready":void 0,"call.permission_request":void 0,"call.recording_ready":void 0,"call.rtmp_broadcast_failed":void 0,"call.rtmp_broadcast_started":void 0,"call.rtmp_broadcast_stopped":void 0,"call.transcription_ready":void 0,"call.user_muted":void 0,"connection.error":void 0,"connection.ok":void 0,"health.check":void 0,"user.updated":void 0,custom:void 0,"call.accepted":n=>this.updateFromCallResponse(n.call),"call.blocked_user":this.blockUser,"call.closed_caption":this.updateFromClosedCaptions,"call.closed_captions_failed":()=>{this.setCurrentValue(this.captioningSubject,!1)},"call.closed_captions_started":()=>{this.setCurrentValue(this.captioningSubject,!0)},"call.closed_captions_stopped":()=>{this.setCurrentValue(this.captioningSubject,!1)},"call.created":n=>this.updateFromCallResponse(n.call),"call.deleted":n=>this.updateFromCallResponse(n.call),"call.ended":n=>{this.updateFromCallResponse(n.call),this.setCurrentValue(this.endedBySubject,n.user)},"call.frame_recording_failed":n=>{this.updateFromCallResponse(n.call)},"call.frame_recording_started":n=>{this.updateFromCallResponse(n.call)},"call.frame_recording_stopped":n=>{this.updateFromCallResponse(n.call)},"call.hls_broadcasting_failed":this.updateFromHLSBroadcastingFailed,"call.hls_broadcasting_started":n=>{this.updateFromCallResponse(n.call)},"call.hls_broadcasting_stopped":this.updateFromHLSBroadcastStopped,"call.live_started":n=>this.updateFromCallResponse(n.call),"call.member_added":this.updateFromMemberAdded,"call.member_removed":this.updateFromMemberRemoved,"call.member_updated_permission":this.updateMembers,"call.member_updated":this.updateMembers,"call.notification":n=>{this.updateFromCallResponse(n.call),this.setMembers(n.members)},"call.permissions_updated":this.updateOwnCapabilities,"call.reaction_new":this.updateParticipantReaction,"call.recording_started":()=>this.setCurrentValue(this.recordingSubject,!0),"call.recording_stopped":()=>this.setCurrentValue(this.recordingSubject,!1),"call.recording_failed":()=>this.setCurrentValue(this.recordingSubject,!1),"call.rejected":n=>this.updateFromCallResponse(n.call),"call.ring":n=>this.updateFromCallResponse(n.call),"call.missed":n=>this.updateFromCallResponse(n.call),"call.session_ended":n=>this.updateFromCallResponse(n.call),"call.session_participant_count_updated":this.updateFromSessionParticipantCountUpdate,"call.session_participant_joined":this.updateFromSessionParticipantJoined,"call.session_participant_left":this.updateFromSessionParticipantLeft,"call.session_started":n=>this.updateFromCallResponse(n.call),"call.transcription_started":()=>{this.setCurrentValue(this.transcribingSubject,!0)},"call.transcription_stopped":()=>{this.setCurrentValue(this.transcribingSubject,!1)},"call.transcription_failed":()=>{this.setCurrentValue(this.transcribingSubject,!1)},"call.unblocked_user":this.unblockUser,"call.updated":n=>this.updateFromCallResponse(n.call)}}get participantCount(){return this.getCurrentValue(this.participantCount$)}get startedAt(){return this.getCurrentValue(this.startedAt$)}get captioning(){return this.getCurrentValue(this.captioning$)}get anonymousParticipantCount(){return this.getCurrentValue(this.anonymousParticipantCount$)}get participants(){return this.getCurrentValue(this.participants$)}get rawParticipants(){return this.getCurrentValue(this.rawParticipants$)}get localParticipant(){return this.getCurrentValue(this.localParticipant$)}get remoteParticipants(){return this.getCurrentValue(this.remoteParticipants$)}get dominantSpeaker(){return this.getCurrentValue(this.dominantSpeaker$)}get pinnedParticipants(){return this.getCurrentValue(this.pinnedParticipants$)}get hasOngoingScreenShare(){return this.getCurrentValue(this.hasOngoingScreenShare$)}get callingState(){return this.getCurrentValue(this.callingState$)}get callStatsReport(){return this.getCurrentValue(this.callStatsReport$)}get members(){return this.getCurrentValue(this.members$)}get ownCapabilities(){return this.getCurrentValue(this.ownCapabilities$)}get backstage(){return this.getCurrentValue(this.backstage$)}get blockedUserIds(){return this.getCurrentValue(this.blockedUserIds$)}get createdAt(){return this.getCurrentValue(this.createdAt$)}get endedAt(){return this.getCurrentValue(this.endedAt$)}get startsAt(){return this.getCurrentValue(this.startsAt$)}get updatedAt(){return this.getCurrentValue(this.updatedAt$)}get createdBy(){return this.getCurrentValue(this.createdBy$)}get custom(){return this.getCurrentValue(this.custom$)}get egress(){return this.getCurrentValue(this.egress$)}get ingress(){return this.getCurrentValue(this.ingress$)}get recording(){return this.getCurrentValue(this.recording$)}get session(){return this.getCurrentValue(this.session$)}get settings(){return this.getCurrentValue(this.settings$)}get transcribing(){return this.getCurrentValue(this.transcribing$)}get endedBy(){return this.getCurrentValue(this.endedBy$)}get thumbnails(){return this.getCurrentValue(this.thumbnails$)}get closedCaptions(){return this.getCurrentValue(this.closedCaptions$)}}const Dn=t=>{const e=[];return t.forEach(s=>{e.push(s)}),e},Io=t=>({id:t.id,tracks:t.getTracks().map(e=>({id:e.id,kind:e.kind,label:e.label,enabled:e.enabled,muted:e.muted,readyState:e.readyState}))}),tm=t=>{const{sdk:e,...s}=t,n=Oo(e),i=Po(e);return{sdkName:n,sdkVersion:i,...s}},Oo=t=>t&&t.type===Ye.REACT?"stream-react":t&&t.type===Ye.REACT_NATIVE?"stream-react-native":"stream-js",Po=t=>t?`${t.major}.${t.minor}.${t.patch}`:"0.0.0-development",Nr=()=>typeof navigator>"u"?!1:/^((?!chrome|android).)*safari/i.test(navigator.userAgent||""),yi=()=>{var t;return typeof navigator>"u"?!1:(t=navigator.userAgent)==null?void 0:t.includes("Firefox")},nm=({subscriber:t,publisher:e,state:s,datacenter:n,pollingIntervalInMs:i=2e3})=>{const r=j(["stats"]),a=async(f,p)=>f==="subscriber"&&t?t.getStats(p):f==="publisher"&&e?e.getStats(p):void 0,o=async(f,p)=>{const g=f==="subscriber"?t:e;if(!g)return[];const b=[];for(const T of p){const S=await g.getStats(T),w=Dr(S,{trackKind:T.kind,kind:f,publisher:void 0});b.push(w)}return b},l=f=>{d.add(f),m()},u=f=>{d.delete(f),m()},d=new Set,m=async()=>{const f={};if(d.size>0){const w=new Set(d);for(const E of s.participants){if(!w.has(E.sessionId))continue;const{audioStream:_,isLocalParticipant:D,sessionId:F,userId:G,videoStream:re}=E,fe=D?"publisher":"subscriber";try{const W=D?(e==null?void 0:e.getPublishedTracks())||[]:[...(re==null?void 0:re.getVideoTracks())||[],...(_==null?void 0:_.getAudioTracks())||[]];f[F]=await o(fe,W)}catch(W){r("warn",`Failed to collect ${fe} stats for ${G}`,W)}}}const[p,g]=await Promise.all([a("subscriber"),e?a("publisher"):void 0]),b=(w,E)=>sm(Dr(w,{kind:E,trackKind:"video",publisher:e})),T=p?b(p,"subscriber"):Xs(),S=g?b(g,"publisher"):Xs();s.setCallStatsReport({datacenter:n,publisherStats:S,subscriberStats:T,subscriberRawStats:p,publisherRawStats:g,participants:f,timestamp:Date.now()})};let c;if(i>0){const f=async()=>{await m().catch(p=>{r("debug","Failed to collect stats",p)}),c=setTimeout(f,i)};f()}return{getRawStatsForTrack:a,getStatsForStream:o,startReportingStatsFor:l,stopReportingStatsFor:u,stop:()=>{c&&clearTimeout(c)}}},Dr=(t,e)=>{const{trackKind:s,kind:n,publisher:i}=e,r=n==="subscriber"?"inbound-rtp":"outbound-rtp",a=Dn(t),o=a.filter(l=>l.type===r&&l.kind===s).map(l=>{const u=l,d=a.find(f=>f.type==="codec"&&f.id===u.codecId),m=a.find(f=>f.type==="transport"&&f.id===u.transportId);let c;if(m&&m.dtlsState==="connected"){const f=a.find(p=>p.type==="candidate-pair"&&p.id===m.selectedCandidatePairId);c=f==null?void 0:f.currentRoundTripTime}let h;if(n==="publisher"&&i){const f=yi(),p=a.find(g=>g.type==="media-source"&&(f?!0:g.id===u.mediaSourceId));p&&(h=i.getTrackType(p.trackIdentifier))}return{bytesSent:u.bytesSent,bytesReceived:u.bytesReceived,codec:d==null?void 0:d.mimeType,currentRoundTripTime:c,frameHeight:u.frameHeight,frameWidth:u.frameWidth,framesPerSecond:u.framesPerSecond,jitter:u.jitter,kind:u.kind,mediaSourceId:u.mediaSourceId,qualityLimitationReason:u.qualityLimitationReason,rid:u.rid,ssrc:u.ssrc,trackType:h}});return{rawStats:t,streams:o,timestamp:Date.now()}},Xs=t=>({rawReport:t??{streams:[],timestamp:Date.now()},totalBytesSent:0,totalBytesReceived:0,averageJitterInMs:0,averageRoundTripTimeInMs:0,qualityLimitationReasons:"none",highestFrameWidth:0,highestFrameHeight:0,highestFramesPerSecond:0,codec:"",codecPerTrackType:{},timestamp:Date.now()}),sm=t=>{const e=Xs(t);let s=-1;const n=(l,u)=>l*u,i=new Set,r=t.streams,a=r.reduce((l,u)=>{l.totalBytesSent+=u.bytesSent||0,l.totalBytesReceived+=u.bytesReceived||0,l.averageJitterInMs+=u.jitter||0,l.averageRoundTripTimeInMs+=u.currentRoundTripTime||0;const d=n(u.frameWidth||0,u.frameHeight||0);return d>s&&(l.highestFrameWidth=u.frameWidth||0,l.highestFrameHeight=u.frameHeight||0,l.highestFramesPerSecond=u.framesPerSecond||0,s=d),i.add(u.qualityLimitationReason||""),l},e);r.length>0&&(a.averageJitterInMs=Math.round(a.averageJitterInMs/r.length*1e3),a.averageRoundTripTimeInMs=Math.round(a.averageRoundTripTimeInMs/r.length*1e3),a.codec=r[0].codec||"",a.codecPerTrackType=r.reduce((l,u)=>(u.trackType&&(l[u.trackType]=u.codec||""),l),{}));const o=[i.has("cpu")&&"cpu",i.has("bandwidth")&&"bandwidth",i.has("other")&&"other"].filter(Boolean).join(", ");return o&&(a.qualityLimitationReasons=o),a},im="1.23.5",[rm,am,om]=im.split(".");let Zs={type:Ye.PLAIN_JAVASCRIPT,major:rm,minor:am,patch:om},cm,um,lm={oneofKind:void 0};const dm=()=>Zs,hm=()=>lm,xr=async()=>{if(Z())return{sdk:Zs,os:cm,device:um};const t=navigator.userAgentData;let e;if(t&&t.getHighEntropyValues)try{e=await t.getHighEntropyValues(["platform","platformVersion"])}catch{}const s=new Ud.UAParser(navigator.userAgent),{browser:n,os:i,device:r,cpu:a}=s.getResult();return{sdk:Zs,browser:{name:n.name||navigator.userAgent,version:n.version||""},os:{name:(e==null?void 0:e.platform)||i.name||"",version:(e==null?void 0:e.platformVersion)||i.version||"",architecture:a.architecture||""},device:{name:[r.vendor,r.model,r.type].filter(Boolean).join(" "),version:""}}};class fm{constructor(e,{options:s,clientDetails:n,subscriber:i,publisher:r,microphone:a,camera:o,state:l,tracer:u,unifiedSessionId:d}){this.logger=j(["SfuStatsReporter"]),this.inputDevices=new Map,this.observeDevice=(h,f)=>{var g;const{browserPermissionState$:p}=h.state;(g=this.unsubscribeDevicePermissionsSubscription)==null||g.call(this),this.unsubscribeDevicePermissionsSubscription=ye(ze([p,this.state.ownCapabilities$]),([b,T])=>{var w;(w=this.unsubscribeListDevicesSubscription)==null||w.call(this);const S=f==="mic"?T.includes(Y.SEND_AUDIO):T.includes(Y.SEND_VIDEO);if(b!=="granted"||!S){this.inputDevices.set(f,{currentDevice:"",availableDevices:[],isPermitted:!1});return}this.unsubscribeListDevicesSubscription=ye(ze([h.listDevices(),h.state.selectedDevice$]),([E,_])=>{const D=E.find(F=>F.deviceId===_);this.inputDevices.set(f,{currentDevice:(D==null?void 0:D.label)||_||"",availableDevices:E.map(F=>F.label),isPermitted:!0})})})},this.sendConnectionTime=h=>{this.sendTelemetryData({data:{oneofKind:"connectionTimeSeconds",connectionTimeSeconds:h}})},this.sendReconnectionTime=(h,f)=>{this.sendTelemetryData({data:{oneofKind:"reconnection",reconnection:{strategy:h,timeSeconds:f}}})},this.sendTelemetryData=h=>{this.run(h).catch(f=>{this.logger("warn","Failed to send telemetry data",f)})},this.run=async h=>{var E,_,D,F,G,re,fe;const[f,p]=await Promise.all([this.subscriber.stats.get(),(E=this.publisher)==null?void 0:E.stats.get()]);(_=this.subscriber.tracer)==null||_.trace("getstats",f.delta),p&&((F=(D=this.publisher)==null?void 0:D.tracer)==null||F.trace("getstats",p.delta));const g=(G=this.subscriber.tracer)==null?void 0:G.take(),b=(fe=(re=this.publisher)==null?void 0:re.tracer)==null?void 0:fe.take(),T=this.tracer.take(),S=this.sfuClient.getTrace(),w=[...T.snapshot,...(S==null?void 0:S.snapshot)??[],...(b==null?void 0:b.snapshot)??[],...(g==null?void 0:g.snapshot)??[]];try{await this.sfuClient.sendStats({sdk:this.sdkName,sdkVersion:this.sdkVersion,webrtcVersion:this.webRTCVersion,subscriberStats:JSON.stringify(Dn(f.stats)),publisherStats:p?JSON.stringify(Dn(p.stats)):"[]",subscriberRtcStats:"",publisherRtcStats:"",rtcStats:JSON.stringify(w),encodeStats:(p==null?void 0:p.performanceStats)??[],decodeStats:f.performanceStats,audioDevices:this.inputDevices.get("mic"),videoDevices:this.inputDevices.get("camera"),unifiedSessionId:this.unifiedSessionId,deviceState:hm(),telemetry:h})}catch(W){throw b==null||b.rollback(),g==null||g.rollback(),T.rollback(),S==null||S.rollback(),W}},this.start=()=>{this.options.reporting_interval_ms<=0||(this.observeDevice(this.microphone,"mic"),this.observeDevice(this.camera,"camera"),clearInterval(this.intervalId),this.intervalId=setInterval(()=>{this.run().catch(h=>{this.logger("warn","Failed to report stats",h)})},this.options.reporting_interval_ms))},this.stop=()=>{var h,f;(h=this.unsubscribeDevicePermissionsSubscription)==null||h.call(this),this.unsubscribeDevicePermissionsSubscription=void 0,(f=this.unsubscribeListDevicesSubscription)==null||f.call(this),this.unsubscribeListDevicesSubscription=void 0,this.inputDevices.clear(),clearInterval(this.intervalId),this.intervalId=void 0,clearTimeout(this.timeoutId),this.timeoutId=void 0},this.flush=()=>{this.run().catch(h=>{this.logger("warn","Failed to flush report stats",h)})},this.scheduleOne=h=>{clearTimeout(this.timeoutId),this.timeoutId=setTimeout(()=>{this.run().catch(f=>{this.logger("warn","Failed to report stats",f)})},h)},this.sfuClient=e,this.options=s,this.subscriber=i,this.publisher=r,this.microphone=a,this.camera=o,this.state=l,this.tracer=u,this.unifiedSessionId=d;const{sdk:m,browser:c}=n;this.sdkName=Oo(m),this.sdkVersion=Po(m),this.webRTCVersion=`${(c==null?void 0:c.name)||""}-${(c==null?void 0:c.version)||""}`||"N/A"}}const pm=(t,e)=>{t.addEventListener("icecandidate",n=>{e("onicecandidate",n.candidate)}),t.addEventListener("track",n=>{const i=n.streams.map(r=>`stream:${r.id}`);e("ontrack",`${n.track.kind}:${n.track.id} ${i}`)}),t.addEventListener("signalingstatechange",()=>{e("signalingstatechange",t.signalingState)}),t.addEventListener("iceconnectionstatechange",()=>{e("iceconnectionstatechange",t.iceConnectionState)}),t.addEventListener("icegatheringstatechange",()=>{e("icegatheringstatechange",t.iceGatheringState)}),t.addEventListener("connectionstatechange",()=>{e("connectionstatechange",t.connectionState)}),t.addEventListener("negotiationneeded",()=>{e("negotiationneeded",void 0)}),t.addEventListener("datachannel",({channel:n})=>{e("datachannel",[n.id,n.label])});const s=t.close;t.close=function(){return e("close",void 0),s.call(this)};for(const n of["createOffer","createAnswer","setLocalDescription","setRemoteDescription","addIceCandidate"]){const i=t[n];i&&(t[n]=async function(...a){try{e(n,a);const o=await i.apply(this,a);return e(`${n}OnSuccess`,o),o}catch(o){throw e(`${n}OnFailure`,o.toString()),o}})}};class mm{constructor(e,s,n){this.previousStats={},this.frameTimeHistory=[],this.fpsHistory=[],this.get=async()=>{const i=await this.pc.getStats(),r=gm(i),a=this.withOverrides(this.peerType===ee.SUBSCRIBER?this.getDecodeStats(r):this.getEncodeStats(r)),o=bm(this.previousStats,r);return this.previousStats=r,this.frameTimeHistory=this.frameTimeHistory.slice(-2),this.fpsHistory=this.fpsHistory.slice(-2),{performanceStats:a,delta:o,stats:i}},this.getEncodeStats=i=>{const r=[];for(const a of Object.values(i)){if(a.type!=="outbound-rtp")continue;const{codecId:o,framesSent:l=0,kind:u,id:d,totalEncodeTime:m=0,framesPerSecond:c=0,frameHeight:h=0,frameWidth:f=0,targetBitrate:p=0,mediaSourceId:g}=a;if(u==="audio"||!this.previousStats[d])continue;const b=this.previousStats[d],T=m-(b.totalEncodeTime||0),S=l-(b.framesSent||0),w=S>0?T/S*1e3:0;this.frameTimeHistory.push(w),this.fpsHistory.push(c);let E=C.VIDEO;if(g&&i[g]){const _=i[g];E=this.trackIdToTrackType.get(_.trackIdentifier)||E}r.push({trackType:E,codec:Lr(i,o),avgFrameTimeMs:rn(this.frameTimeHistory),avgFps:rn(this.fpsHistory),targetBitrate:Math.round(p),videoDimension:{width:f,height:h}})}return r},this.getDecodeStats=i=>{let r,a=0;for(const g of Object.values(i)){if(g.type!=="inbound-rtp")continue;const b=g,{kind:T,frameWidth:S=0,frameHeight:w=0}=b,E=S*w;T==="video"&&E>a&&(r=b,a=E)}if(!r||!this.previousStats[r.id])return[];const o=this.previousStats[r.id],{framesDecoded:l=0,framesPerSecond:u=0,totalDecodeTime:d=0,trackIdentifier:m}=r,c=d-(o.totalDecodeTime||0),h=l-(o.framesDecoded||0),f=h>0?c/h*1e3:0;this.frameTimeHistory.push(f),this.fpsHistory.push(u);const p=this.trackIdToTrackType.get(m)||C.VIDEO;return[Gs.create({trackType:p,codec:Lr(i,r.codecId),avgFrameTimeMs:rn(this.frameTimeHistory),avgFps:rn(this.fpsHistory),videoDimension:{width:r.frameWidth,height:r.frameHeight}})]},this.withOverrides=i=>{if(this.costOverrides)for(const r of i){const a=this.costOverrides.get(r.trackType);a!==void 0&&(r.avgFrameTimeMs=a+(r.avgFrameTimeMs||0)/1e3)}return i},this.setCost=(i,r=C.VIDEO)=>{this.costOverrides||(this.costOverrides=new Map),this.costOverrides.set(r,i)},this.pc=e,this.peerType=s,this.trackIdToTrackType=n}}const gm=t=>{const e={};return t.forEach((s,n)=>{e[n]=s}),e},bm=(t,e)=>{e=JSON.parse(JSON.stringify(e));for(const[i,r]of Object.entries(e))if(delete r.id,!!t[i])for(const[a,o]of Object.entries(r))o===t[i][a]&&delete r[a];let s=-1/0;const n=Object.values(e);for(const i of n)i.timestamp>s&&(s=i.timestamp);for(const i of n)i.timestamp===s&&(i.timestamp=0);return e.timestamp=s,e},rn=t=>t.reduce((e,s)=>e+s,0)/t.length,Lr=(t,e)=>{if(!e||!t[e])return;const s=t[e];return De.create({name:s.mimeType.split("/").pop(),clockRate:s.clockRate,payloadType:s.payloadType,fmtp:s.sdpFmtpLine})};class Si{constructor(e){this.buffer=[],this.enabled=!0,this.setEnabled=s=>{this.enabled!==s&&(this.enabled=s,this.buffer=[])},this.trace=(s,n)=>{this.enabled&&this.buffer.push([s,this.id,n,Date.now()])},this.take=()=>{const s=this.buffer;return this.buffer=[],{snapshot:s,rollback:()=>{this.buffer.unshift(...s)}}},this.dispose=()=>{this.buffer=[]},this.id=e}}class Ao{constructor(e,{sfuClient:s,connectionConfig:n,state:i,dispatcher:r,onUnrecoverableError:a,logTag:o,enableTracing:l}){if(this.isIceRestarting=!1,this.isDisposed=!1,this.trackIdToTrackType=new Map,this.subscriptions=[],this.lock=Math.random().toString(36).slice(2),this.on=(u,d)=>{this.subscriptions.push(this.dispatcher.on(u,m=>{const c=`pc.${this.lock}.${u}`;de(c,async()=>d(m)).catch(h=>{this.isDisposed||this.logger("warn",`Error handling ${u}`,h)})}))},this.addTrickledIceCandidates=()=>{var m;const{iceTrickleBuffer:u}=this.sfuClient,d=this.peerType===ee.SUBSCRIBER?u.subscriberCandidates:u.publisherCandidates;(m=this.unsubscribeIceTrickle)==null||m.call(this),this.unsubscribeIceTrickle=mi(d,async c=>this.pc.addIceCandidate(c).catch(h=>{this.isDisposed||this.logger("warn","ICE candidate error",h,c)}))},this.setSfuClient=u=>{this.sfuClient=u},this.getStats=u=>this.pc.getStats(u),this.getTrackType=u=>this.trackIdToTrackType.get(u),this.onIceCandidate=u=>{const{candidate:d}=u;if(!d){this.logger("debug","null ice candidate");return}const m=this.asJSON(d);this.sfuClient.iceTrickle({peerType:this.peerType,iceCandidate:m}).catch(c=>{this.isDisposed||this.logger("warn","ICETrickle failed",c)})},this.asJSON=u=>{if(!u.usernameFragment){const d=u.candidate.split(" "),m=d.findIndex(h=>h==="ufrag")+1,c=d[m];return JSON.stringify({...u,usernameFragment:c})}return JSON.stringify(u.toJSON())},this.onConnectionStateChange=async()=>{const u=this.pc.connectionState;if(this.logger("debug","Connection state changed",u),!!this.tracer&&(u==="connected"||u==="failed"))try{const d=await this.stats.get();this.tracer.trace("getstats",d.delta)}catch(d){this.tracer.trace("getstatsOnFailure",d.toString())}},this.onIceConnectionStateChange=()=>{var d;const u=this.pc.iceConnectionState;this.logger("debug","ICE connection state changed",u),this.state.callingState!==R.OFFLINE&&this.state.callingState!==R.RECONNECTING&&(this.isIceRestarting||(u==="failed"?(d=this.onUnrecoverableError)==null||d.call(this,"ICE connection failed"):u==="disconnected"&&(this.logger("debug","Attempting to restart ICE"),this.restartIce().catch(m=>{var h;const c="ICE restart failed";this.logger("error",c,m),(h=this.onUnrecoverableError)==null||h.call(this,`${c}: ${m}`)}))))},this.onIceCandidateError=u=>{const d=u instanceof RTCPeerConnectionIceErrorEvent&&`${u.errorCode}: ${u.errorText}`,m=this.pc.iceConnectionState,c=m==="connected"||m==="checking"?"debug":"warn";this.logger(c,"ICE Candidate error",d)},this.onIceGatherChange=()=>{this.logger("debug","ICE Gathering State",this.pc.iceGatheringState)},this.onSignalingChange=()=>{this.logger("debug","Signaling state changed",this.pc.signalingState)},this.peerType=e,this.sfuClient=s,this.state=i,this.dispatcher=r,this.onUnrecoverableError=a,this.logger=j([e===ee.SUBSCRIBER?"Subscriber":"Publisher",o]),this.pc=new RTCPeerConnection(n),this.pc.addEventListener("icecandidate",this.onIceCandidate),this.pc.addEventListener("icecandidateerror",this.onIceCandidateError),this.pc.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.pc.addEventListener("icegatheringstatechange",this.onIceGatherChange),this.pc.addEventListener("signalingstatechange",this.onSignalingChange),this.pc.addEventListener("connectionstatechange",this.onConnectionStateChange),this.stats=new mm(this.pc,e,this.trackIdToTrackType),l){const u=`${o}-${e===ee.SUBSCRIBER?"sub":"pub"}`;this.tracer=new Si(u),this.tracer.trace("create",{url:s.edgeName,...n}),pm(this.pc,this.tracer.trace)}}dispose(){var e;this.onUnrecoverableError=void 0,this.isDisposed=!0,this.detachEventHandlers(),this.pc.close(),(e=this.tracer)==null||e.dispose()}detachEventHandlers(){var e;this.pc.removeEventListener("icecandidate",this.onIceCandidate),this.pc.removeEventListener("icecandidateerror",this.onIceCandidateError),this.pc.removeEventListener("signalingstatechange",this.onSignalingChange),this.pc.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.pc.removeEventListener("icegatheringstatechange",this.onIceGatherChange),(e=this.unsubscribeIceTrickle)==null||e.call(this),this.subscriptions.forEach(s=>s())}}class ym{constructor(){this.cache=[],this.layers=[],this.transceiverOrder=[],this.add=(e,s)=>{this.cache.push({publishOption:e,transceiver:s}),this.transceiverOrder.push(s)},this.get=e=>{var s;return(s=this.findTransceiver(e))==null?void 0:s.transceiver},this.has=e=>!!this.get(e),this.find=e=>this.cache.find(e),this.items=()=>this.cache,this.indexOf=e=>this.transceiverOrder.indexOf(e),this.getLayers=e=>{const s=this.layers.find(n=>n.publishOption.id===e.id&&n.publishOption.trackType===e.trackType);return s==null?void 0:s.layers},this.setLayers=(e,s=[])=>{const n=this.findLayer(e);n?n.layers=s:this.layers.push({publishOption:e,layers:s})},this.findTransceiver=e=>this.cache.find(s=>s.publishOption.id===e.id&&s.publishOption.trackType===e.trackType),this.findLayer=e=>this.layers.find(s=>s.publishOption.id===e.id&&s.publishOption.trackType===e.trackType)}}const Ft=(t,e)=>{j(["helpers"])("warn",e,t)},vi=t=>{switch(t){case C.SCREEN_SHARE:return"screenShareStream";case C.SCREEN_SHARE_AUDIO:return"screenShareAudioStream";case C.VIDEO:return"videoStream";case C.AUDIO:return"audioStream";case C.UNSPECIFIED:throw new Error("Track type is unspecified");default:Ft(t,"Unknown track type")}},Sm=t=>{switch(t){case"audio":return C.AUDIO;case"video":return C.VIDEO;case"screenshare":return C.SCREEN_SHARE;case"screenshare_audio":return C.SCREEN_SHARE_AUDIO;default:Ft(t,"Unknown mute type")}},vm=t=>{switch(t){case"TRACK_TYPE_AUDIO":return C.AUDIO;case"TRACK_TYPE_VIDEO":return C.VIDEO;case"TRACK_TYPE_SCREEN_SHARE":return C.SCREEN_SHARE;case"TRACK_TYPE_SCREEN_SHARE_AUDIO":return C.SCREEN_SHARE_AUDIO;default:return}},Ti=t=>t===C.AUDIO||t===C.SCREEN_SHARE_AUDIO,Tm={q:3e5,h:75e4,f:125e4},Cm=t=>{if(!t)return;const e=n=>i=>i.rid===n;return[{...t.find(e("f"))||t.find(e("h"))||t.find(e("q")),rid:"q"}]},km=t=>t==="q"?We.LOW_UNSPECIFIED:t==="h"?We.MID:We.HIGH,wm=(t=[])=>t.map(e=>({rid:e.rid||"",bitrate:e.maxBitrate||0,fps:e.maxFramerate||0,quality:km(e.rid||""),videoDimension:{width:e.width,height:e.height}})),Em=(t,e)=>`L${t}T${e}${t>1?"_KEY":""}`,Ur=(t,e)=>{if(Ti(e.trackType))return;const s=[],n=t.getSettings(),{width:i=0,height:r=0}=n,{bitrate:a,codec:o,fps:l,maxSpatialLayers:u=3,maxTemporalLayers:d=3,videoDimension:m={width:1280,height:720},useSingleLayer:c}=e,h=Rm(m,i,r,a);let f=1,p=1;const g=Ys(o==null?void 0:o.name);for(const b of["f","h","q"].slice(0,u)){const T={active:!0,rid:b,width:Math.round(i/f),height:Math.round(r/f),maxBitrate:Math.round(h/p)||Tm[b],maxFramerate:l};g?T.scalabilityMode=Em(c?1:u,d):T.scaleResolutionDownBy=f,f*=2,p*=2,s.unshift(T)}return _m(n,s,c)},Rm=(t,e,s,n)=>{const{width:i,height:r}=t;if(e<i||s<r){const a=e*s,o=i*r,l=a/o;return Math.round(n*l)}return n},_m=(t,e,s)=>{let n;const i=Math.max(t.width||0,t.height||0);i<=320?n=e.filter(a=>a.rid==="f"):i<=640?n=e.filter(a=>a.rid!=="q"):n=e;const r=["q","h","f"];return n.map((a,o,l)=>({...a,rid:r[o],active:s&&o<l.length-1?!1:a.active}))},Im=(t,e,s)=>{if(t.mid)return t.mid;if(!s)return String(e);const n=t.sender.track,r=$d.parse(s).media.find(a=>{var o;return a.type===n.kind&&(((o=a.msid)==null?void 0:o.includes(n.id))??!0)});return typeof(r==null?void 0:r.mid)<"u"?String(r.mid):e<0?"":String(e)};class Om extends Ao{constructor({publishOptions:e,...s}){super(ee.PUBLISHER_UNSPECIFIED,s),this.transceiverCache=new ym,this.clonedTracks=new Set,this.publish=async(n,i)=>{if(!this.publishOptions.some(r=>r.trackType===i))throw new Error(`No publish options found for ${C[i]}`);for(const r of this.publishOptions){if(r.trackType!==i)continue;const a=this.cloneTrack(n),o=this.transceiverCache.get(r);if(!o)await this.addTransceiver(a,r);else{const l=o.sender.track;await this.updateTransceiver(o,a,i),Z()||this.stopTrack(l)}}},this.addTransceiver=async(n,i)=>{var d;const r=Ur(n,i),a=Ys((d=i.codec)==null?void 0:d.name)?Cm(r):r,o=this.pc.addTransceiver(n,{direction:"sendonly",sendEncodings:a}),l=o.sender.getParameters();l.degradationPreference="maintain-framerate",await o.sender.setParameters(l);const u=i.trackType;this.logger("debug",`Added ${C[u]} transceiver`),this.transceiverCache.add(i,o),this.trackIdToTrackType.set(n.id,u),await this.negotiate()},this.updateTransceiver=async(n,i,r)=>{const a=n.sender;a.track&&this.trackIdToTrackType.delete(a.track.id),await a.replaceTrack(i),i&&this.trackIdToTrackType.set(i.id,r)},this.syncPublishOptions=async()=>{for(const n of this.publishOptions){const{trackType:i}=n;if(!this.isPublishing(i)||this.transceiverCache.has(n))continue;const r=this.transceiverCache.find(o=>!!o.transceiver.sender.track&&o.publishOption.trackType===i);if(!r||!r.transceiver)continue;const a=this.cloneTrack(r.transceiver.sender.track);await this.addTransceiver(a,n)}for(const n of this.transceiverCache.items()){const{publishOption:i,transceiver:r}=n;this.publishOptions.some(o=>o.id===i.id&&o.trackType===i.trackType)||(this.stopTrack(r.sender.track),await this.updateTransceiver(r,null,i.trackType))}},this.isPublishing=n=>{for(const i of this.transceiverCache.items()){if(i.publishOption.trackType!==n)continue;const r=i.transceiver.sender.track;if(r&&r.readyState==="live"&&r.enabled)return!0}return!1},this.stopTracks=(...n)=>{for(const i of this.transceiverCache.items()){const{publishOption:r,transceiver:a}=i;n.includes(r.trackType)&&this.stopTrack(a.sender.track)}},this.stopAllTracks=()=>{for(const{transceiver:n}of this.transceiverCache.items())this.stopTrack(n.sender.track);for(const n of this.clonedTracks)this.stopTrack(n)},this.changePublishQuality=async n=>{var g;const{trackType:i,layers:r,publishOptionId:a}=n,o=r.filter(b=>b.active),l="Update publish quality:";this.logger("info",`${l} requested layers by SFU:`,o);const u=this.transceiverCache.find(b=>b.publishOption.id===a&&b.publishOption.trackType===i),d=u==null?void 0:u.transceiver.sender;if(!d)return this.logger("warn",`${l} no video sender found.`);const m=d.getParameters();if(m.encodings.length===0)return this.logger("warn",`${l} there are no encodings set.`);const c=(g=u==null?void 0:u.publishOption.codec)==null?void 0:g.name,h=c&&Ys(c);let f=!1;for(const b of m.encodings){const T=h?o[0]:o.find(F=>F.name===b.rid)??(m.encodings.length===1?o[0]:void 0),S=!!(T!=null&&T.active);if(S!==b.active&&(b.active=S,f=!0),!T)continue;const{maxFramerate:w,scaleResolutionDownBy:E,maxBitrate:_,scalabilityMode:D}=T;E>=1&&E!==b.scaleResolutionDownBy&&(b.scaleResolutionDownBy=E,f=!0),_>0&&_!==b.maxBitrate&&(b.maxBitrate=_,f=!0),w>0&&w!==b.maxFramerate&&(b.maxFramerate=w,f=!0),D&&D!==b.scalabilityMode&&(b.scalabilityMode=D,f=!0)}const p=m.encodings.filter(b=>b.active);if(!f)return this.logger("info",`${l} no change:`,p);await d.setParameters(m),this.logger("info",`${l} enabled rids:`,p)},this.restartIce=async()=>{this.logger("debug","Restarting ICE connection");const n=this.pc.signalingState;if(this.isIceRestarting||n==="have-local-offer"){this.logger("debug","ICE restart is already in progress");return}await this.negotiate({iceRestart:!0})},this.negotiate=async n=>de(`publisher.negotiate.${this.lock}`,async()=>{const i=await this.pc.createOffer(n),r=this.getAnnouncedTracks(i.sdp);if(!r.length)throw new Error("Can't negotiate without any tracks");try{this.isIceRestarting=(n==null?void 0:n.iceRestart)??!1,await this.pc.setLocalDescription(i);const{sdp:a=""}=i,{response:o}=await this.sfuClient.setPublisher({sdp:a,tracks:r});if(o.error)throw new Error(o.error.message);const{sdp:l}=o;await this.pc.setRemoteDescription({type:"answer",sdp:l})}finally{this.isIceRestarting=!1}this.addTrickledIceCandidates()}),this.getPublishedTracks=()=>{const n=[];for(const{transceiver:i}of this.transceiverCache.items()){const r=i.sender.track;r&&r.readyState==="live"&&n.push(r)}return n},this.getAnnouncedTracks=n=>{const i=[];for(const r of this.transceiverCache.items()){const{transceiver:a,publishOption:o}=r;a.sender.track&&i.push(this.toTrackInfo(a,o,n))}return i},this.getAnnouncedTracksForReconnect=()=>{var r;const n=(r=this.pc.localDescription)==null?void 0:r.sdp,i=[];for(const a of this.publishOptions){const o=this.transceiverCache.get(a);!o||!o.sender.track||i.push(this.toTrackInfo(o,a,n))}return i},this.toTrackInfo=(n,i,r)=>{var h;const a=n.sender.track,o=a.readyState==="live",l=o?Ur(a,i):this.transceiverCache.getLayers(i);this.transceiverCache.setLayers(i,l);const u=Ti(i.trackType),d=u&&a.getSettings().channelCount===2,m=this.transceiverCache.indexOf(n),c=(h=this.state.settings)==null?void 0:h.audio;return{trackId:a.id,layers:wm(l),trackType:i.trackType,mid:Im(n,m,r),stereo:d,dtx:u&&!!(c!=null&&c.opus_dtx_enabled),red:u&&!!(c!=null&&c.redundant_coding_enabled),muted:!o,codec:i.codec,publishOptionId:i.id}},this.cloneTrack=n=>{const i=n.clone();return this.clonedTracks.add(i),i},this.stopTrack=n=>{n&&(n.stop(),this.clonedTracks.delete(n))},this.publishOptions=e,this.on("iceRestart",n=>{n.peerType===ee.PUBLISHER_UNSPECIFIED&&this.restartIce().catch(i=>{var a;const r="ICE restart failed";this.logger("warn",r,i),(a=this.onUnrecoverableError)==null||a.call(this,`${r}: ${i}`)})}),this.on("changePublishQuality",async n=>{for(const i of n.videoSenders)await this.changePublishQuality(i)}),this.on("changePublishOptions",n=>(this.publishOptions=n.publishOptions,this.syncPublishOptions()))}dispose(){super.dispose(),this.stopAllTracks(),this.clonedTracks.clear()}}class Pm extends Ao{constructor(e){super(ee.SUBSCRIBER,e),this.restartIce=async()=>{if(this.logger("debug","Restarting ICE connection"),this.pc.signalingState==="have-remote-offer"){this.logger("debug","ICE restart is already in progress");return}if(this.pc.connectionState==="new"){this.logger("debug","ICE connection is not yet established, skipping restart.");return}const s=this.isIceRestarting;try{this.isIceRestarting=!0,await this.sfuClient.iceRestart({peerType:ee.SUBSCRIBER})}catch(n){throw this.isIceRestarting=s,n}},this.handleOnTrack=s=>{const[n]=s.streams,[i,r]=n.id.split(":"),a=this.state.participants.find(m=>m.trackLookupPrefix===i);this.logger("debug",`[onTrack]: Got remote ${r} track for userId: ${a==null?void 0:a.userId}`,s.track.id,s.track);const o=`${a==null?void 0:a.userId} ${r}:${i}`;s.track.addEventListener("mute",()=>{this.logger("info",`[onTrack]: Track muted: ${o}`)}),s.track.addEventListener("unmute",()=>{this.logger("info",`[onTrack]: Track unmuted: ${o}`)}),s.track.addEventListener("ended",()=>{this.logger("info",`[onTrack]: Track ended: ${o}`),this.state.removeOrphanedTrack(n.id)});const l=vm(r);if(!l)return this.logger("error",`Unknown track type: ${r}`);if(this.trackIdToTrackType.set(s.track.id,l),!a){this.logger("warn",`[onTrack]: Received track for unknown participant: ${i}`,s),this.state.registerOrphanedTrack({id:n.id,trackLookupPrefix:i,track:n,trackType:l});return}const u=vi(l);if(!u){this.logger("error",`Unknown track type: ${r}`);return}const d=a[u];this.state.updateParticipant(a.sessionId,{[u]:n}),d&&(this.logger("info",`[onTrack]: Cleaning up previous remote ${s.track.kind} tracks for userId: ${a.userId}`),d.getTracks().forEach(m=>{m.stop(),d.removeTrack(m)}))},this.negotiate=async s=>{await this.pc.setRemoteDescription({type:"offer",sdp:s.sdp}),this.addTrickledIceCandidates();const n=await this.pc.createAnswer();await this.pc.setLocalDescription(n),await this.sfuClient.sendAnswer({peerType:ee.SUBSCRIBER,sdp:n.sdp||""}),this.isIceRestarting=!1},this.pc.addEventListener("track",this.handleOnTrack),this.on("subscriberOffer",async s=>this.negotiate(s).catch(n=>{this.logger("error","Negotiation failed.",n)}))}detachEventHandlers(){super.detachEventHandlers(),this.pc.removeEventListener("track",this.handleOnTrack)}}const Am=t=>{const{endpoint:e,onMessage:s,logTag:n}=t,i=j(["SfuClientWS",n]);i("debug","Creating signaling WS channel:",e);const r=new WebSocket(e);return r.binaryType="arraybuffer",r.addEventListener("error",a=>{i("error","Signaling WS channel error",a)}),r.addEventListener("close",a=>{i("info","Signaling WS channel is closed",a)}),r.addEventListener("open",a=>{i("info","Signaling WS channel is open",a)}),r.addEventListener("message",a=>{try{const o=a.data instanceof ArrayBuffer?Rr.fromBinary(new Uint8Array(a.data)):Rr.fromJsonString(a.data.toString());s(o)}catch(o){i("error","Failed to decode a message. Check whether the Proto models match.",{event:a,error:o})}}),r},Nm=t=>({bundlePolicy:"max-bundle",iceServers:t.map(e=>({urls:e.urls,username:e.username,credential:e.password}))});function Mt(t){let e=!0;const s=t.then(i=>({status:"resolved",result:i}),i=>({status:"rejected",error:i})).finally(()=>e=!1),n=()=>s.then(i=>{if(i.status==="rejected")throw i.error;return i.result});return n.checkPending=()=>e,n}const _t=()=>{let t,e;const s=new Promise((o,l)=>{t=o,e=l});let n=!1,i=!1;return{promise:s,resolve:o=>{n=!0,t(o)},reject:o=>{i=!0,e(o)},isResolved:()=>n,isRejected:()=>i}},Fr=Symbol("uninitialized");function Qe(t){let e=Fr;return()=>(e===Fr&&(e=t()),e)}const Dm={src:`const timerIdMapping = new Map();
self.addEventListener('message', (event) => {
    const request = event.data;
    switch (request.type) {
        case 'setTimeout':
        case 'setInterval':
            timerIdMapping.set(request.id, (request.type === 'setTimeout' ? setTimeout : setInterval)(() => {
                tick(request.id);
                if (request.type === 'setTimeout') {
                    timerIdMapping.delete(request.id);
                }
            }, request.timeout));
            break;
        case 'clearTimeout':
        case 'clearInterval':
            (request.type === 'clearTimeout' ? clearTimeout : clearInterval)(timerIdMapping.get(request.id));
            timerIdMapping.delete(request.id);
            break;
    }
});
function tick(id) {
    const message = { type: 'tick', id };
    self.postMessage(message);
}`};class xm{constructor(){this.currentTimerId=1,this.callbacks=new Map,this.fallback=!1}setup({useTimerWorker:e=!0}={}){if(!e){this.fallback=!0;return}try{const s=Dm.src,n=new Blob([s],{type:"application/javascript; charset=utf-8"}),i=URL.createObjectURL(n);this.worker=new Worker(i,{name:"str-timer-worker"}),this.worker.addEventListener("message",r=>{var l;const{type:a,id:o}=r.data;a==="tick"&&((l=this.callbacks.get(o))==null||l())})}catch(s){j(["timer-worker"])("error",s),this.fallback=!0}}destroy(){var e;this.callbacks.clear(),(e=this.worker)==null||e.terminate(),this.worker=void 0,this.fallback=!1}get ready(){return this.fallback||!!this.worker}setInterval(e,s){return this.setTimer("setInterval",e,s)}clearInterval(e){this.clearTimer("clearInterval",e)}setTimeout(e,s){return this.setTimer("setTimeout",e,s)}clearTimeout(e){this.clearTimer("clearTimeout",e)}setTimer(e,s,n){if(this.ready||this.setup(),this.fallback)return(e==="setTimeout"?setTimeout:setInterval)(s,n);const i=this.getTimerId();return this.callbacks.set(i,()=>{s(),e==="setTimeout"&&this.callbacks.delete(i)}),this.sendMessage({type:e,id:i,timeout:n}),i}clearTimer(e,s){if(s){if(this.ready||this.setup(),this.fallback){(e==="clearTimeout"?clearTimeout:clearInterval)(s);return}this.callbacks.delete(s),this.sendMessage({type:e,id:s})}}getTimerId(){return this.currentTimerId++}sendMessage(e){if(!this.worker)throw new Error("Cannot use timer worker before it's set up");this.worker.postMessage(e)}}let No=!1;const Lm=()=>{No=!0},xn=Qe(()=>{const t=new xm;return t.setup({useTimerWorker:No}),t});class he{constructor({dispatcher:e,credentials:s,sessionId:n,logTag:i,joinResponseTimeout:r=5e3,onSignalClose:a,streamClient:o,enableTracing:l}){this.iceTrickleBuffer=new Vp,this.isLeaving=!1,this.isClosing=!1,this.pingIntervalInMs=10*1e3,this.unhealthyTimeoutInMs=this.pingIntervalInMs+5*1e3,this.joinResponseTask=_t(),this.abortController=new AbortController,this.createWebSocket=()=>{const m={callEnded:!0,changePublishQuality:!0,changePublishOptions:!0,connectionQualityChanged:!0,error:!0,goAway:!0};this.signalWs=Am({logTag:this.logTag,endpoint:`${this.credentials.server.ws_endpoint}?tag=${this.logTag}`,onMessage:c=>{var f;this.lastMessageTimestamp=new Date,this.scheduleConnectionCheck();const h=c.eventPayload.oneofKind;m[h]&&((f=this.tracer)==null||f.trace(h,c)),this.dispatcher.dispatch(c,this.logTag)}}),this.signalReady=Mt(Promise.race([new Promise((c,h)=>{const f=()=>{this.signalWs.removeEventListener("open",f),c(this.signalWs)};this.signalWs.addEventListener("open",f),this.signalWs.addEventListener("close",p=>{this.handleWebSocketClose(p),h(new Error("SFU WS closed unexpectedly"))})}),new Promise((c,h)=>{setTimeout(()=>h(new Error("SFU WS connection timed out")),this.joinResponseTimeout)})]))},this.cleanUpWebSocket=()=>{this.signalWs.removeEventListener("close",this.handleWebSocketClose)},this.handleWebSocketClose=m=>{var c;this.signalWs.removeEventListener("close",this.handleWebSocketClose),xn().clearInterval(this.keepAliveInterval),clearTimeout(this.connectionCheckTimeout),(c=this.onSignalClose)==null||c.call(this,`${m.code} ${m.reason}`)},this.close=(m=he.NORMAL_CLOSURE,c)=>{this.isClosing=!0,this.signalWs.readyState===WebSocket.OPEN&&(this.logger("debug",`Closing SFU WS connection: ${m} - ${c}`),this.signalWs.close(m,`js-client: ${c}`),this.cleanUpWebSocket()),this.dispose()},this.dispose=()=>{var m;this.logger("debug","Disposing SFU client"),this.unsubscribeIceTrickle(),this.unsubscribeNetworkChanged(),clearInterval(this.keepAliveInterval),clearTimeout(this.connectionCheckTimeout),clearTimeout(this.migrateAwayTimeout),this.abortController.abort(),(m=this.migrationTask)==null||m.resolve(),this.iceTrickleBuffer.dispose()},this.getTrace=()=>{var m;return(m=this.tracer)==null?void 0:m.take()},this.leaveAndClose=async m=>{await this.joinTask;try{this.isLeaving=!0,await this.notifyLeave(m)}catch(c){this.logger("debug","Error notifying SFU about leaving call",c)}this.close(he.NORMAL_CLOSURE,m.substring(0,115))},this.updateSubscriptions=async m=>(await this.joinTask,Ue(()=>this.rpc.updateSubscriptions({sessionId:this.sessionId,tracks:m}),this.abortController.signal)),this.setPublisher=async m=>(await this.joinTask,Ue(()=>this.rpc.setPublisher({...m,sessionId:this.sessionId}),this.abortController.signal)),this.sendAnswer=async m=>(await this.joinTask,Ue(()=>this.rpc.sendAnswer({...m,sessionId:this.sessionId}),this.abortController.signal)),this.iceTrickle=async m=>(await this.joinTask,Ue(()=>this.rpc.iceTrickle({...m,sessionId:this.sessionId}),this.abortController.signal)),this.iceRestart=async m=>(await this.joinTask,Ue(()=>this.rpc.iceRestart({...m,sessionId:this.sessionId}),this.abortController.signal)),this.updateMuteStates=async m=>(await this.joinTask,Ue(()=>this.rpc.updateMuteStates({muteStates:m,sessionId:this.sessionId}),this.abortController.signal)),this.sendStats=async m=>(await this.joinTask,this.rpc.sendStats({...m,sessionId:this.sessionId})),this.startNoiseCancellation=async()=>(await this.joinTask,Ue(()=>this.rpc.startNoiseCancellation({sessionId:this.sessionId}),this.abortController.signal)),this.stopNoiseCancellation=async()=>(await this.joinTask,Ue(()=>this.rpc.stopNoiseCancellation({sessionId:this.sessionId}),this.abortController.signal)),this.enterMigration=async(m={})=>{var p;this.isLeaving=!0;const{timeout:c=7*1e3}=m;(p=this.migrationTask)==null||p.reject(new Error("Cancelled previous migration"));const h=this.migrationTask=_t(),f=this.dispatcher.on("participantMigrationComplete",()=>{f(),clearTimeout(this.migrateAwayTimeout),h.resolve()});return this.migrateAwayTimeout=setTimeout(()=>{f(),h.reject(new Error(`Migration (${this.logTag}) failed to complete in ${c}ms`))},c),h.promise},this.join=async m=>{var g;await this.signalReady(),(this.joinResponseTask.isResolved()||this.joinResponseTask.isRejected())&&(this.joinResponseTask=_t());const c=this.joinResponseTask;let h;const f=this.dispatcher.on("joinResponse",b=>{this.logger("debug","Received joinResponse",b),clearTimeout(h),f(),this.keepAlive(),c.resolve(b)});h=setTimeout(()=>{f(),c.reject(new Error('Waiting for "joinResponse" has timed out'))},this.joinResponseTimeout);const p=Ct.create({requestPayload:{oneofKind:"joinRequest",joinRequest:To.create({...m,sessionId:this.sessionId,token:this.credentials.token})}});return(g=this.tracer)==null||g.trace("joinRequest",p),await this.send(p),c.promise},this.ping=async()=>this.send(Ct.create({requestPayload:{oneofKind:"healthCheckRequest",healthCheckRequest:{}}})),this.notifyLeave=async m=>this.send(Ct.create({requestPayload:{oneofKind:"leaveCallRequest",leaveCallRequest:{sessionId:this.sessionId,reason:m}}})),this.send=async m=>{await this.signalReady();const c=Ct.toJson(m);if(this.signalWs.readyState!==WebSocket.OPEN){this.logger("debug","Signal WS is not open. Skipping message",c);return}this.logger("debug",`Sending message to: ${this.edgeName}`,c),this.signalWs.send(Ct.toBinary(m))},this.keepAlive=()=>{const m=xn();m.clearInterval(this.keepAliveInterval),this.keepAliveInterval=m.setInterval(()=>{this.ping().catch(c=>{this.logger("error","Error sending healthCheckRequest to SFU",c)})},this.pingIntervalInMs)},this.scheduleConnectionCheck=()=>{clearTimeout(this.connectionCheckTimeout),this.connectionCheckTimeout=setTimeout(()=>{this.lastMessageTimestamp&&new Date().getTime()-this.lastMessageTimestamp.getTime()>this.unhealthyTimeoutInMs&&this.close(he.ERROR_CONNECTION_UNHEALTHY,`SFU connection unhealthy. Didn't receive any message for ${this.unhealthyTimeoutInMs}ms`)},this.unhealthyTimeoutInMs)},this.dispatcher=e,this.sessionId=n||Js(),this.onSignalClose=a,this.credentials=s;const{server:u,token:d}=s;this.edgeName=u.edge_name,this.joinResponseTimeout=r,this.logTag=i,this.logger=j(["SfuClient",i]),this.tracer=l?new Si(`${i}-${this.edgeName}`):void 0,this.rpc=Dp({baseUrl:u.url,interceptors:[Pp({Authorization:`Bearer ${d}`}),this.tracer&&Np(this.tracer.trace),zs()==="trace"&&Ap(this.logger,"trace")].filter(m=>!!m)}),this.unsubscribeIceTrickle=e.on("iceTrickle",m=>{this.iceTrickleBuffer.push(m)}),this.unsubscribeNetworkChanged=o.on("network.changed",m=>{var c;m.online?(c=this.networkAvailableTask)==null||c.resolve():this.networkAvailableTask=_t()}),this.createWebSocket()}get isHealthy(){return this.signalWs.readyState===WebSocket.OPEN&&this.joinResponseTask.isResolved()}get joinTask(){return this.joinResponseTask.promise}}he.NORMAL_CLOSURE=1e3;he.ERROR_CONNECTION_UNHEALTHY=4001;he.DISPOSE_OLD_SOCKET=4002;const Um=t=>async function(s){if(s.user.id===t.currentUserId)return;const{state:n}=t;s.call.created_by.id===t.currentUserId&&n.callingState===R.RINGING&&await t.join()},Fm=t=>async function(s){if(s.user.id===t.currentUserId)return;const{call:n}=s,{session:i}=n;if(!i){t.logger("warn","No call session provided. Ignoring call.rejected event.",s);return}const r=i.rejected_by,{members:a,callingState:o}=t.state;if(o!==R.RINGING){t.logger("info","Call is not in ringing mode (it is either accepted or rejected already). Ignoring call.rejected event.",s);return}t.isCreatedByMe?a.filter(u=>u.user_id!==t.currentUserId).every(u=>r[u.user_id])&&(t.logger("info","everyone rejected, leaving the call"),await t.leave({reject:!0,reason:"cancel",message:"ring: everyone rejected"})):r[n.created_by.id]&&(t.logger("info","call creator rejected, leaving call"),await t.leave({message:"ring: creator rejected"}))},Mm=t=>function(){const{callingState:s}=t.state;s!==R.IDLE&&s!==R.LEFT&&t.leave({message:"call.ended event received",reject:!1}).catch(n=>{t.logger("error","Failed to leave call after call.ended ",n)})},jm=t=>t.on("callEnded",async e=>{if(t.state.callingState!==R.LEFT)try{if(e.reason===Lt.LIVE_ENDED){t.state.setBackstage(!0);const{hasPermission:n}=t.permissionsContext;if(n(Y.JOIN_BACKSTAGE))return}t.state.setEndedAt(new Date);const s=Lt[e.reason];await t.leave({message:`callEnded received: ${s}`})}catch(s){t.logger("error","Failed to leave call after being ended by the SFU",s)}}),$m=t=>function(s){const{currentGrants:n}=s;if(n){const{canPublishAudio:i,canPublishVideo:r,canScreenshare:a}=n,o={[Y.SEND_AUDIO]:i,[Y.SEND_VIDEO]:r,[Y.SCREENSHARE]:a},l=t.ownCapabilities.filter(u=>o[u]!==!1);Object.entries(o).forEach(([u,d])=>{d&&!l.includes(u)&&l.push(u)}),t.setOwnCapabilities(l)}},Bm=(t,e)=>t.on("connectionQualityChanged",s=>{const{connectionQualityUpdates:n}=s;n&&e.updateParticipants(n.reduce((i,r)=>{const{sessionId:a,connectionQuality:o}=r;return i[a]={connectionQuality:o},i},{}))}),Vm=(t,e)=>t.on("healthCheckResponse",s=>{const{participantCount:n}=s;n&&(e.setParticipantCount(n.total),e.setAnonymousParticipantCount(n.anonymous))}),Hm=(t,e)=>t.on("error",s=>{s.error&&s.error.code!==xt.LIVE_ENDED||(e.state.setBackstage(!0),e.permissionsContext.hasPermission(Y.JOIN_BACKSTAGE)||e.leave({message:"live ended"}).catch(n=>{e.logger("error","Failed to leave call after live ended",n)}))}),qm=t=>t.on("error",e=>{if(!e.error)return;const s=j(["SfuClient"]),{error:n,reconnectStrategy:i}=e;s("error","SFU reported error",{code:xt[n.code],reconnectStrategy:N[i],message:n.message,shouldRetry:n.shouldRetry})}),Gm=t=>function(s){const{pins:n}=s;t.setServerSidePins(n)},Wm=t=>t.on("trackUnpublished",async e=>{const{cause:s,type:n,sessionId:i}=e,{localParticipant:r}=t.state;if(s===An.MODERATION&&i===(r==null?void 0:r.sessionId)){const a=t.logger;a("info",`Local participant's ${C[n]} track is muted remotely`);try{n===C.VIDEO?await t.camera.disable():n===C.AUDIO?await t.microphone.disable():n===C.SCREEN_SHARE||n===C.SCREEN_SHARE_AUDIO?await t.screenShare.disable():a("warn","Unsupported track type to soft mute",C[n])}catch(o){a("error","Failed to stop publishing",o)}}}),yn=(t,...e)=>{for(const s of e)t.includes(s)||t.push(s);return t},Jm=t=>function(s){const{participant:n}=s;if(!n)return;const i=Ci(t,n);t.updateOrAddParticipant(n.sessionId,Object.assign(n,i,{viewportVisibilityState:{videoTrack:K.UNKNOWN,screenShareTrack:K.UNKNOWN}}))},Km=t=>function(s){const{participant:n}=s;n&&t.setParticipants(i=>i.filter(r=>r.sessionId!==n.sessionId))},zm=t=>function(s){const{participant:n}=s;n&&t.updateParticipant(n.sessionId,n)},Ym=t=>function(s){const{type:n,sessionId:i}=s;if(s.participant){const r=Ci(t,s.participant),a=Object.assign(s.participant,r);t.updateOrAddParticipant(i,a)}else t.updateParticipant(i,r=>({publishedTracks:yn([...r.publishedTracks],n)}))},Xm=t=>function(s){const{type:n,sessionId:i}=s;if(s.participant){const r=Ci(t,s.participant),a=Object.assign(s.participant,r);t.updateOrAddParticipant(i,a)}else t.updateParticipant(i,r=>({publishedTracks:r.publishedTracks.filter(a=>a!==n)}))},Ci=(t,e)=>{const s=t.takeOrphanedTracks(e.trackLookupPrefix);if(!s.length)return;const n={};for(const i of s){const r=vi(i.trackType);r&&(n[r]=i.track)}return n},Zm=(t,e)=>t.on("dominantSpeakerChanged",s=>{var i;const{sessionId:n}=s;n!==((i=e.dominantSpeaker)==null?void 0:i.sessionId)&&e.setParticipants(r=>r.map(a=>a.sessionId===n?{...a,isDominantSpeaker:!0}:a.isDominantSpeaker?{...a,isDominantSpeaker:!1}:a))}),Qm=(t,e)=>t.on("audioLevelChanged",s=>{const{audioLevels:n}=s;e.updateParticipants(n.reduce((i,r)=>(i[r.sessionId]={audioLevel:r.level,isSpeaking:r.isSpeaking},i),{}))}),eg=(t,e)=>{const s=t.state,n=[t.on("call.ended",Mm(t)),jm(t),Hm(e,t),qm(e),Bm(e,s),Vm(e,s),t.on("participantJoined",Jm(s)),t.on("participantLeft",Km(s)),t.on("participantUpdated",zm(s)),t.on("trackPublished",Ym(s)),t.on("trackUnpublished",Xm(s)),Qm(e,s),Zm(e,s),t.on("callGrantsUpdated",$m(s)),t.on("pinsUpdated",Gm(s)),Wm(t)];return t.ringing&&n.push(Do(t)),()=>{n.forEach(i=>i())}},Do=t=>{const e={"call.accepted":Um(t),"call.rejected":Fm(t)},s=Object.keys(e).map(n=>{const i=n;return t.on(i,e[i])});return()=>{s.forEach(n=>n())}},tg=.35;class ng{constructor(){this.elementHandlerMap=new Map,this.observer=null,this.queueSet=new Set,this.setViewport=(e,s)=>{const n=()=>{var i;(i=this.observer)==null||i.disconnect(),this.observer=null,this.elementHandlerMap.clear()};return this.observer=new IntersectionObserver(i=>{i.forEach(r=>{const a=this.elementHandlerMap.get(r.target);a==null||a(r)})},{root:e,...s,threshold:(s==null?void 0:s.threshold)??tg}),this.queueSet.size&&(this.queueSet.forEach(([i,r])=>{e.contains(i)&&(this.observer.observe(i),this.elementHandlerMap.set(i,r))}),this.queueSet.clear()),n},this.observe=(e,s)=>{const n=[e,s],i=()=>{var r;this.elementHandlerMap.delete(e),(r=this.observer)==null||r.unobserve(e),this.queueSet.delete(n)};return this.elementHandlerMap.has(e)?i:this.observer?(this.observer.root.contains(e)&&(this.elementHandlerMap.set(e,s),this.observer.observe(e)),i):(this.queueSet.add(n),i)}}}const Mr={videoTrack:K.UNKNOWN,screenShareTrack:K.UNKNOWN},an=Symbol("globalOverrideKey");class sg{constructor(e,s){this.viewportTracker=new ng,this.logger=j(["DynascaleManager"]),this.pendingSubscriptionsUpdate=null,this.videoTrackSubscriptionOverridesSubject=new A({}),this.videoTrackSubscriptionOverrides$=this.videoTrackSubscriptionOverridesSubject.asObservable(),this.incomingVideoSettings$=this.videoTrackSubscriptionOverrides$.pipe(ne(n=>{const{[an]:i,...r}=n;return{enabled:(i==null?void 0:i.enabled)!==!1,preferredResolution:i!=null&&i.enabled?i.dimension:void 0,participants:Object.fromEntries(Object.entries(r).map(([a,o])=>[a,{enabled:(o==null?void 0:o.enabled)!==!1,preferredResolution:o!=null&&o.enabled?o.dimension:void 0}])),isParticipantVideoEnabled:a=>{var o,l;return((o=n[a])==null?void 0:o.enabled)??((l=n[an])==null?void 0:l.enabled)??!0}}}),te(1)),this.dispose=async()=>{this.pendingSubscriptionsUpdate&&clearTimeout(this.pendingSubscriptionsUpdate);const n=this.getOrCreateAudioContext();n&&n.state!=="closed"&&(document.removeEventListener("click",this.resumeAudioContext),await n.close(),this.audioContext=void 0)},this.setVideoTrackSubscriptionOverrides=(n,i)=>i?Q(this.videoTrackSubscriptionOverridesSubject,r=>({...r,...Object.fromEntries(i.map(a=>[a,n]))})):Q(this.videoTrackSubscriptionOverridesSubject,n?{[an]:n}:{}),this.applyTrackSubscriptions=(n=ke.SLOW)=>{this.pendingSubscriptionsUpdate&&clearTimeout(this.pendingSubscriptionsUpdate);const i=()=>{var r;this.pendingSubscriptionsUpdate=null,(r=this.sfuClient)==null||r.updateSubscriptions(this.trackSubscriptions).catch(a=>{this.logger("debug","Failed to update track subscriptions",a)})};n?this.pendingSubscriptionsUpdate=setTimeout(i,n):i()},this.trackElementVisibility=(n,i,r)=>{const a=this.viewportTracker.observe(n,o=>{this.callState.updateParticipant(i,l=>{const u=l.viewportVisibilityState??Mr,d=o.isIntersecting||document.fullscreenElement===n?K.VISIBLE:K.INVISIBLE;return{...l,viewportVisibilityState:{...u,[r]:d}}})});return()=>{a(),this.callState.updateParticipant(i,o=>{const l=o.viewportVisibilityState??Mr;return{...o,viewportVisibilityState:{...l,[r]:K.UNKNOWN}}})}},this.setViewport=n=>this.viewportTracker.setViewport(n),this.bindVideoElement=(n,i,r)=>{const a=this.callState.findParticipantBySessionId(i);if(!a)return;const o=(p,g)=>{g&&(g.width===0||g.height===0)&&(this.logger("debug","Ignoring 0x0 dimension",a),g=void 0),this.callState.updateParticipantTracks(r,{[i]:{dimension:g}}),this.applyTrackSubscriptions(p)},l=this.callState.participants$.pipe(ne(p=>p.find(g=>g.sessionId===i)),Bs(p=>!!p),ae(),te({bufferSize:1,refCount:!0}));let u;const d=a.isLocalParticipant?null:l.pipe(ne(p=>{var g;return(g=p.viewportVisibilityState)==null?void 0:g[r]}),ae()).subscribe(p=>{if(!u){u=p??K.UNKNOWN;return}if(u=p??K.UNKNOWN,p===K.INVISIBLE)return o(ke.MEDIUM,void 0);o(ke.MEDIUM,{width:n.clientWidth,height:n.clientHeight})});let m;const c=a.isLocalParticipant?null:new ResizeObserver(()=>{const p={width:n.clientWidth,height:n.clientHeight};if(!m){m=p;return}if(m.width===p.width&&m.height===p.height||u===K.INVISIBLE)return;const b=Math.max(p.width/m.width,p.height/m.height)>1.2?ke.IMMEDIATE:ke.MEDIUM;o(b,{width:n.clientWidth,height:n.clientHeight}),m=p});c==null||c.observe(n);const h=a.isLocalParticipant?null:l.pipe(tn("publishedTracks"),ne(p=>r==="videoTrack"?it(p):qe(p)),ae()).subscribe(p=>{p?o(ke.IMMEDIATE,{width:n.clientWidth,height:n.clientHeight}):o(ke.FAST,void 0)});n.autoplay=!0,n.playsInline=!0,n.muted=!0;const f=l.pipe(tn(r==="videoTrack"?"videoStream":"screenShareStream")).subscribe(p=>{const g=r==="videoTrack"?p.videoStream:p.screenShareStream;n.srcObject!==g&&(n.srcObject=g??null,(Nr()||yi())&&setTimeout(()=>{n.srcObject=g??null,n.play().catch(b=>{this.logger("warn","Failed to play stream",b)})},25))});return()=>{o(ke.FAST,void 0),d==null||d.unsubscribe(),h==null||h.unsubscribe(),f.unsubscribe(),c==null||c.disconnect()}},this.bindAudioElement=(n,i,r)=>{const a=this.callState.findParticipantBySessionId(i);if(!a||a.isLocalParticipant)return;const o=this.callState.participants$.pipe(ne(f=>f.find(p=>p.sessionId===i)),Bs(f=>!!f),ae(),te({bufferSize:1,refCount:!0})),l=(f,p)=>{f&&("setSinkId"in n&&n.setSinkId(f).catch(g=>{this.logger("warn","Can't to set AudioElement sinkId",g)}),p&&"setSinkId"in p&&p.setSinkId(f).catch(g=>{this.logger("warn","Can't to set AudioContext sinkId",g)}))};let u,d;const m=o.pipe(tn(r==="screenShareAudioTrack"?"screenShareAudioStream":"audioStream")).subscribe(f=>{const p=r==="screenShareAudioTrack"?f.screenShareAudioStream:f.audioStream;n.srcObject!==p&&setTimeout(()=>{if(n.srcObject=p??null,!p)return;const g=this.getOrCreateAudioContext();g?(n.muted=!0,u==null||u.disconnect(),u=g.createMediaStreamSource(p),d??(d=g.createGain()),d.gain.value=f.audioVolume??this.speaker.state.volume,u.connect(d).connect(g.destination),this.resumeAudioContext()):(n.muted=!1,n.play().catch(T=>{this.logger("warn","Failed to play audio stream",T)}));const{selectedDevice:b}=this.speaker.state;b&&l(b,g)})}),c="setSinkId"in n?this.speaker.state.selectedDevice$.subscribe(f=>{const p=this.getOrCreateAudioContext();l(f,p)}):null,h=ze([this.speaker.state.volume$,o.pipe(tn("audioVolume"))]).subscribe(([f,p])=>{const g=p.audioVolume??f;n.volume=g,d&&(d.gain.value=g)});return n.autoplay=!0,()=>{c==null||c.unsubscribe(),h.unsubscribe(),m.unsubscribe(),n.srcObject=null,u==null||u.disconnect(),d==null||d.disconnect()}},this.getOrCreateAudioContext=()=>{if(this.audioContext||!Nr())return this.audioContext;const n=new AudioContext;return n.state==="suspended"&&document.addEventListener("click",this.resumeAudioContext),this.audioContext=n},this.resumeAudioContext=()=>{var n;((n=this.audioContext)==null?void 0:n.state)==="suspended"&&this.audioContext.resume().catch(i=>this.logger("warn","Can't resume audio context",i)).then(()=>{document.removeEventListener("click",this.resumeAudioContext)})},this.callState=e,this.speaker=s}setSfuClient(e){this.sfuClient=e}get trackSubscriptions(){const e=[];for(const s of this.callState.remoteParticipants){if(s.videoDimension&&it(s)){const n=this.videoTrackSubscriptionOverrides[s.sessionId]??this.videoTrackSubscriptionOverrides[an];(n==null?void 0:n.enabled)!==!1&&e.push({userId:s.userId,sessionId:s.sessionId,trackType:C.VIDEO,dimension:(n==null?void 0:n.dimension)??s.videoDimension})}s.screenShareDimension&&qe(s)&&e.push({userId:s.userId,sessionId:s.sessionId,trackType:C.SCREEN_SHARE,dimension:s.screenShareDimension}),Xp(s)&&e.push({userId:s.userId,sessionId:s.sessionId,trackType:C.SCREEN_SHARE_AUDIO})}return e}get videoTrackSubscriptionOverrides(){return X(this.videoTrackSubscriptionOverrides$)}}class ig{constructor(){this.permissions=[],this.setPermissions=e=>{this.permissions=e||[]},this.setCallSettings=e=>{this.settings=e},this.hasPermission=e=>this.permissions.includes(e),this.canPublish=e=>{switch(e){case C.AUDIO:return this.hasPermission(Y.SEND_AUDIO);case C.VIDEO:return this.hasPermission(Y.SEND_VIDEO);case C.SCREEN_SHARE:case C.SCREEN_SHARE_AUDIO:return this.hasPermission(Y.SCREENSHARE);case C.UNSPECIFIED:return!1;default:Ft(e,"Unknown track type")}},this.canRequest=(e,s=this.settings)=>{if(!s)return!1;const{audio:n,video:i,screensharing:r}=s;switch(e){case Y.SEND_AUDIO:return n.access_request_enabled;case Y.SEND_VIDEO:return i.access_request_enabled;case Y.SCREENSHARE:return r.access_request_enabled;default:return!1}}}}class It{constructor(e,s={sortParticipantsBy:Nn}){this.name=e,this.options=s}}class rg{constructor(e){this.register=s=>{this.callTypes[s.name]=s},this.unregister=s=>{delete this.callTypes[s]},this.get=s=>(this.callTypes[s]||this.register(new It(s)),this.callTypes[s]),this.callTypes=e.reduce((s,n)=>(s[n.name]=n,s),{})}}const ag=new rg([new It("default",{sortParticipantsBy:Nn}),new It("development",{sortParticipantsBy:Nn}),new It("livestream",{sortParticipantsBy:Pr}),new It("audio_room",{sortParticipantsBy:Pr})]);class xo{constructor(e){this.permission=e,this.disposeController=new AbortController,this.wasPrompted=!1,this.listeners=new Set,this.logger=j(["permissions"]);const s=this.disposeController.signal;this.ready=(async()=>{const n=()=>{Z()?this.setState("granted"):this.setState("prompt")};if(!og())return n();try{const i=await navigator.permissions.query({name:e.queryName});s.aborted||(this.setState(i.state),i.addEventListener("change",()=>this.setState(i.state),{signal:s}))}catch(i){this.logger("debug","Failed to query permission status",i),n()}})()}dispose(){this.state=void 0,this.disposeController.abort()}async getState(){if(await this.ready,!this.state)throw new Error("BrowserPermission instance possibly disposed");return this.state}async prompt({forcePrompt:e=!1,throwOnNotAllowed:s=!1}={}){return await de(`permission-prompt-${this.permission.queryName}`,async()=>{if(await this.getState()!=="prompt"||this.wasPrompted&&!e){const n=this.state==="granted";if(!n&&s)throw new Error("Permission was not granted previously, and prompting again is not allowed");return n}try{this.wasPrompted=!0,this.setState("prompting");const n=await navigator.mediaDevices.getUserMedia(this.permission.constraints);return mg(n),this.setState("granted"),!0}catch(n){if(n&&typeof n=="object"&&"name"in n&&(n.name==="NotAllowedError"||n.name==="SecurityError")){if(this.logger("info","Browser permission was not granted",{permission:this.permission}),this.setState("denied"),s)throw n;return!1}throw this.logger("error","Failed to getUserMedia",{error:n,permission:this.permission}),this.setState("prompt"),n}})}listen(e){return this.listeners.add(e),this.state&&e(this.state),()=>this.listeners.delete(e)}asObservable(){return this.getStateObservable().pipe(ne(e=>e!=="denied"))}asStateObservable(){return this.getStateObservable()}getIsPromptingObservable(){return this.getStateObservable().pipe(ne(e=>e==="prompting"))}getStateObservable(){return Od(e=>this.listen(e),(e,s)=>s())}setState(e){this.state!==e&&(this.state=e,this.listeners.forEach(s=>s(e)))}}function og(){var t;return!Z()&&typeof navigator<"u"&&!!((t=navigator.permissions)!=null&&t.query)}const ki=(t,e)=>Ze((async()=>{let s=await navigator.mediaDevices.enumerateDevices();return s.some(i=>i.kind===e&&i.label==="")&&await t.prompt()&&(s=await navigator.mediaDevices.enumerateDevices()),s.filter(i=>i.kind===e&&i.label!==""&&i.deviceId!=="default")})()),cg=()=>typeof document>"u"?!1:"setSinkId"in document.createElement("audio"),Lo={audio:{autoGainControl:!0,noiseSuppression:!0,echoCancellation:!0}},Uo={video:{width:1280,height:720}},dt=Qe(()=>new xo({constraints:Lo,queryName:"microphone"})),Ln=Qe(()=>new xo({constraints:Uo,queryName:"camera"})),es=Qe(()=>navigator.mediaDevices.addEventListener?$s(navigator.mediaDevices,"devicechange").pipe(ne(()=>{}),Pd(500)):Ze([])),ug=Qe(()=>li(es(),dt().asObservable()).pipe(Jn(void 0),Wn(()=>ki(dt(),"audioinput")),te(1))),lg=Qe(()=>li(es(),Ln().asObservable()).pipe(Jn(void 0),Wn(()=>ki(Ln(),"videoinput")),te(1))),dg=Qe(()=>li(es(),dt().asObservable()).pipe(Jn(void 0),Wn(()=>ki(dt(),"audiooutput")),te(1)));let hg=0;const Fo=async(t,e)=>{const s=`navigator.mediaDevices.getUserMedia.${hg++}.`;try{e==null||e.trace(s,t);const n=await navigator.mediaDevices.getUserMedia(t);if(e==null||e.trace(`${s}OnSuccess`,Io(n)),yi()&&navigator.mediaDevices.dispatchEvent(new Event("devicechange")),t.video){const[i]=n.getVideoTracks();if(i){const{width:r,height:a}=i.getSettings(),o=t.video;(r!==o.width||a!==o.height)&&(e==null||e.trace(`${s}Warn`,`Requested resolution ${o.width}x${o.height} but got ${r}x${a}`))}}return n}catch(n){throw e==null||e.trace(`${s}OnFailure`,n.name),n}};function Mo(t){if(!t||typeof t!="object")return!1;if("name"in t&&typeof t.name=="string"){const e=t.name;if(["OverconstrainedError","NotFoundError"].includes(e))return!0}return!!("message"in t&&typeof t.message=="string"&&t.message.startsWith("OverconstrainedError"))}const jo=async(t,e)=>{const s={audio:{...Lo.audio,...t}};try{return await dt().prompt({throwOnNotAllowed:!0,forcePrompt:!0}),await Fo(s,e)}catch(n){if(Mo(n)&&(t!=null&&t.deviceId)){const{deviceId:i,...r}=t;return j(["devices"])("warn","Failed to get audio stream, will try again with relaxed constraints",{error:n,constraints:s,relaxedConstraints:r}),jo(r)}throw j(["devices"])("error","Failed to get audio stream",{error:n,constraints:s}),n}},$o=async(t,e)=>{const s={video:{...Uo.video,...t}};try{return await Ln().prompt({throwOnNotAllowed:!0,forcePrompt:!0}),await Fo(s,e)}catch(n){if(Mo(n)&&(t!=null&&t.deviceId)){const{deviceId:i,...r}=t;return j(["devices"])("warn","Failed to get video stream, will try again with relaxed constraints",{error:n,constraints:s,relaxedConstraints:r}),$o(r)}throw j(["devices"])("error","Failed to get video stream",{error:n,constraints:s}),n}};let fg=0;const pg=async(t,e)=>{const s=`navigator.mediaDevices.getDisplayMedia.${fg++}.`;try{e==null||e.trace(s,t);const n=await navigator.mediaDevices.getDisplayMedia({video:!0,audio:{channelCount:{ideal:2},echoCancellation:!1,autoGainControl:!1,noiseSuppression:!1},systemAudio:"include",...t});return e==null||e.trace(`${s}OnSuccess`,Io(n)),n}catch(n){throw e==null||e.trace(`${s}OnFailure`,n.name),j(["devices"])("error","Failed to get screen share stream",n),n}},Un=typeof navigator<"u"&&typeof navigator.mediaDevices<"u"?es().pipe(Jn(void 0),Wn(()=>navigator.mediaDevices.enumerateDevices()),te(1)):void 0,mg=t=>{t.active&&(t.getTracks().forEach(e=>{e.stop()}),typeof t.release=="function"&&t.release())},Bo=()=>/Mobi/i.test(navigator.userAgent);class wi{constructor(e,s,n){this.stopOnLeave=!0,this.subscriptions=[],this.isTrackStoppedDueToTrackEnd=!1,this.filters=[],this.statusChangeConcurrencyTag=Symbol("statusChangeConcurrencyTag"),this.filterRegistrationConcurrencyTag=Symbol("filterRegistrationConcurrencyTag"),this.dispose=()=>{this.subscriptions.forEach(i=>i())},this.call=e,this.state=s,this.trackType=n,this.logger=j([`${C[n].toLowerCase()} manager`]),Un&&!Z()&&(this.trackType===C.AUDIO||this.trackType===C.VIDEO)&&this.handleDisconnectedOrReplacedDevices()}listDevices(){return this.getDevices()}get enabled(){return this.state.status==="enabled"}async enable(){this.state.prevStatus=this.state.optimisticStatus,this.state.optimisticStatus!=="enabled"&&(this.state.setPendingStatus("enabled"),await vs(this.statusChangeConcurrencyTag,async e=>{try{await this.unmuteStream(),this.state.setStatus("enabled")}finally{e.aborted||this.state.setPendingStatus(this.state.status)}}))}async disable(e=!1){this.state.prevStatus=this.state.optimisticStatus,!(!e&&this.state.optimisticStatus==="disabled")&&(this.state.setPendingStatus("disabled"),await vs(this.statusChangeConcurrencyTag,async s=>{try{const n=e||this.state.disableMode==="stop-tracks";await this.muteStream(n),this.state.setStatus("disabled")}finally{s.aborted||this.state.setPendingStatus(this.state.status)}}))}async statusChangeSettled(){await Ts(this.statusChangeConcurrencyTag)}async resume(){this.state.prevStatus==="enabled"&&this.state.status!=="enabled"&&await this.enable()}async toggle(){return this.state.optimisticStatus==="enabled"?await this.disable():await this.enable()}registerFilter(e){const s={start:e,stop:void 0};return this.call.tracer.trace(`registerFilter.${C[this.trackType]}`,null),{registered:de(this.filterRegistrationConcurrencyTag,async()=>{await Ts(this.statusChangeConcurrencyTag),this.filters.push(s),await this.applySettingsToStream()}),unregister:()=>de(this.filterRegistrationConcurrencyTag,async()=>{var i;await Ts(this.statusChangeConcurrencyTag),(i=s.stop)==null||i.call(s),this.filters=this.filters.filter(r=>r!==s),await this.applySettingsToStream()})}}setDefaultConstraints(e){this.state.setDefaultConstraints(e)}async select(e){if(Z())throw new Error("This method is not supported in React Native. Please visit https://getstream.io/video/docs/reactnative/core/camera-and-microphone/#speaker-management for reference.");const s=this.state.selectedDevice;if(e!==s)try{this.state.setDevice(e),await this.applySettingsToStream()}catch(n){throw this.state.setDevice(s),n}}async applySettingsToStream(){await vs(this.statusChangeConcurrencyTag,async e=>{if(this.enabled)try{if(await this.muteStream(),this.state.setStatus("disabled"),e.aborted)return;await this.unmuteStream(),this.state.setStatus("enabled")}finally{e.aborted||this.state.setPendingStatus(this.state.status)}})}publishStream(e){return this.call.publish(e,this.trackType)}stopPublishStream(){return this.call.stopPublish(this.trackType)}getTracks(){var e;return((e=this.state.mediaStream)==null?void 0:e.getTracks())??[]}async muteStream(e=!0){const s=this.state.mediaStream;if(!s)return;this.logger("debug",`${e?"Stopping":"Disabling"} stream`),this.call.state.callingState===R.JOINED&&await this.stopPublishStream(),this.muteLocalStream(e),this.getTracks().every(i=>i.readyState==="ended")&&(typeof s.release=="function"&&s.release(),this.state.setMediaStream(void 0,void 0),this.filters.forEach(i=>{var r;return(r=i.stop)==null?void 0:r.call(i)}))}disableTracks(){this.getTracks().forEach(e=>{e.enabled&&(e.enabled=!1)})}enableTracks(){this.getTracks().forEach(e=>{e.enabled||(e.enabled=!0)})}stopTracks(){this.getTracks().forEach(e=>{e.readyState==="live"&&e.stop()})}muteLocalStream(e){this.state.mediaStream&&(e?this.stopTracks():this.disableTracks())}async unmuteStream(){this.logger("debug","Starting stream");let e,s;if(this.state.mediaStream&&this.getTracks().every(n=>n.readyState==="live"))e=this.state.mediaStream,this.enableTracks();else{const i={...this.state.defaultConstraints,deviceId:this.state.selectedDevice?{exact:this.state.selectedDevice}:void 0},r=a=>async o=>{if(!a)return o;const l=await a;return o.getTracks().forEach(u=>{const d=u.stop;u.stop=function(){d.call(u),l.getTracks().forEach(c=>{c.kind===u.kind&&c.stop()})}}),l.getTracks().forEach(u=>{const d=()=>{o.getTracks().forEach(m=>{u.kind===m.kind&&(m.stop(),m.dispatchEvent(new Event("ended")))})};u.addEventListener("ended",d),this.subscriptions.push(()=>{u.removeEventListener("ended",d)})}),o};s=this.getStream(i),e=await this.filters.reduce((a,o)=>a.then(l=>{const{stop:u,output:d}=o.start(l);return o.stop=u,d}).then(r(a),l=>(this.logger("warn","Filter failed to start and will be ignored",l),a)),s)}if(this.call.state.callingState===R.JOINED&&await this.publishStream(e),this.state.mediaStream!==e){this.state.setMediaStream(e,await s);const n=async()=>{await this.statusChangeSettled(),this.enabled&&(this.isTrackStoppedDueToTrackEnd=!0,setTimeout(()=>{this.isTrackStoppedDueToTrackEnd=!1},2e3),await this.disable())},i=r=>()=>{!Bo()||this.trackType!==C.VIDEO||this.call.notifyTrackMuteState(r,this.trackType).catch(a=>{this.logger("warn","Error while notifying track mute state",a)})};e.getTracks().forEach(r=>{const a=i(!0),o=i(!1);r.addEventListener("mute",a),r.addEventListener("unmute",o),r.addEventListener("ended",n),this.subscriptions.push(()=>{r.removeEventListener("mute",a),r.removeEventListener("unmute",o),r.removeEventListener("ended",n)})})}}get mediaDeviceKind(){return this.trackType===C.AUDIO?"audioinput":this.trackType===C.VIDEO?"videoinput":""}handleDisconnectedOrReplacedDevices(){this.subscriptions.push(ye(ze([Un.pipe(Nd()),this.state.selectedDevice$]),async([[e,s],n])=>{try{if(!n)return;await this.statusChangeSettled();let i=!1,r=!1;const a=this.findDevice(s,n),o=this.findDevice(e,n);!a&&o?i=!0:a&&o&&a.deviceId===o.deviceId&&a.groupId!==o.groupId&&(r=!0),i&&(await this.disable(),await this.select(void 0)),r&&(this.isTrackStoppedDueToTrackEnd&&this.state.status==="disabled"?(await this.enable(),this.isTrackStoppedDueToTrackEnd=!1):await this.applySettingsToStream())}catch(i){this.logger("warn","Unexpected error while handling disconnected or replaced device",i)}}))}findDevice(e,s){const n=this.mediaDeviceKind;return e.find(i=>i.deviceId===s&&i.kind===n)}}class Ei{constructor(e="stop-tracks",s){this.disableMode=e,this.statusSubject=new A(void 0),this.optimisticStatusSubject=new A(void 0),this.mediaStreamSubject=new A(void 0),this.selectedDeviceSubject=new A(void 0),this.defaultConstraintsSubject=new A(void 0),this.mediaStream$=this.mediaStreamSubject.asObservable(),this.selectedDevice$=this.selectedDeviceSubject.asObservable().pipe(ae()),this.status$=this.statusSubject.asObservable().pipe(ae()),this.optimisticStatus$=this.optimisticStatusSubject.asObservable().pipe(ae()),this.defaultConstraints$=this.defaultConstraintsSubject.asObservable(),this.hasBrowserPermission$=s?s.asObservable().pipe(te(1)):bn(!0),this.browserPermissionState$=s?s.asStateObservable().pipe(te(1)):bn("prompt"),this.isPromptingPermission$=s?s.getIsPromptingObservable().pipe(te(1)):bn(!1)}get status(){return X(this.status$)}get optimisticStatus(){return X(this.optimisticStatus$)}get selectedDevice(){return X(this.selectedDevice$)}get mediaStream(){return X(this.mediaStream$)}setStatus(e){Q(this.statusSubject,e)}setPendingStatus(e){Q(this.optimisticStatusSubject,e)}setMediaStream(e,s){Q(this.mediaStreamSubject,e),s&&this.setDevice(this.getDeviceIdFromStream(s))}setDevice(e){Q(this.selectedDeviceSubject,e)}get defaultConstraints(){return X(this.defaultConstraints$)}setDefaultConstraints(e){Q(this.defaultConstraintsSubject,e)}}class gg extends Ei{constructor(){super("stop-tracks",Ln()),this.directionSubject=new A(void 0),this.direction$=this.directionSubject.asObservable().pipe(ae())}get direction(){return X(this.direction$)}setDirection(e){Q(this.directionSubject,e)}setMediaStream(e,s){var n;if(super.setMediaStream(e,s),e){const i=Z()?this.direction:((n=e.getVideoTracks()[0])==null?void 0:n.getSettings().facingMode)==="environment"?"back":"front";this.setDirection(i)}}getDeviceIdFromStream(e){const[s]=e.getVideoTracks();return s==null?void 0:s.getSettings().deviceId}}class bg extends wi{constructor(e){super(e,new gg,C.VIDEO),this.targetResolution={width:1280,height:720}}isDirectionSupportedByDevice(){return Z()||Bo()}async selectDirection(e){if(!this.isDirectionSupportedByDevice()){this.logger("warn","Setting direction is not supported on this device");return}if(this.state.setDirection(e),this.state.setDevice(void 0),Z()){const s=this.getTracks()[0];await(s==null?void 0:s.applyConstraints({facingMode:e==="front"?"user":"environment"}));return}this.getTracks().forEach(s=>s.stop());try{await this.unmuteStream()}catch(s){throw s instanceof Error&&s.name==="NotReadableError"&&(await this.muteStream(),await this.unmuteStream()),s}}async flip(){const e=this.state.direction==="front"?"back":"front";await this.selectDirection(e)}async selectTargetResolution(e){if(this.targetResolution.height=e.height,this.targetResolution.width=e.width,this.state.optimisticStatus==="enabled")try{await this.statusChangeSettled()}catch(s){this.logger("warn","could not apply target resolution",s)}if(this.enabled&&this.state.mediaStream){const[s]=this.state.mediaStream.getVideoTracks();if(!s)return;const{width:n,height:i}=s.getSettings();(n!==this.targetResolution.width||i!==this.targetResolution.height)&&(await this.applySettingsToStream(),this.logger("debug",`${n}x${i} target resolution applied to media stream`))}}async apply(e,s){var m;const n=!!((m=this.call.state.localParticipant)!=null&&m.videoStream),i=this.call.permissionsContext.hasPermission(Y.SEND_AUDIO);if(n||!i)return;await this.statusChangeSettled();const{target_resolution:r,camera_facing:a,camera_default_on:o}=e;let{width:l,height:u}=r;if(l<u&&([l,u]=[u,l]),await this.selectTargetResolution({width:l,height:u}),!this.state.direction&&!this.state.selectedDevice&&this.state.setDirection(a==="front"?"front":"back"),!s)return;const{mediaStream:d}=this.state;this.enabled&&d?await this.publishStream(d):this.state.status===void 0&&o&&await this.enable()}getDevices(){return lg()}getStream(e){return e.width=this.targetResolution.width,e.height=this.targetResolution.height,!e.deviceId&&this.state.direction&&this.isDirectionSupportedByDevice()&&(e.facingMode=this.state.direction==="front"?"user":"environment"),$o(e,this.call.tracer)}}class yg extends Ei{constructor(e){super(e,dt()),this.speakingWhileMutedSubject=new A(!1),this.speakingWhileMuted$=this.speakingWhileMutedSubject.asObservable().pipe(ae())}get speakingWhileMuted(){return X(this.speakingWhileMuted$)}setSpeakingWhileMuted(e){Q(this.speakingWhileMutedSubject,e)}getDeviceIdFromStream(e){const[s]=e.getAudioTracks();return s==null?void 0:s.getSettings().deviceId}}const Sg=500,vg=150,Tg=128,Cg=(t,e,s={})=>{const{detectionFrequencyInMs:n=Sg,audioLevelThreshold:i=vg,fftSize:r=Tg,destroyStreamOnStop:a=!0}=s,o=new AudioContext,l=o.createAnalyser();l.fftSize=r;const u=o.createMediaStreamSource(t);u.connect(l);const d=setInterval(()=>{var p;const m=new Uint8Array(l.frequencyBinCount);l.getByteFrequencyData(m);const c=m.some(g=>g>=i),h=m.reduce((g,b)=>g+b,0)/m.length,f=h>i?100:Math.round(h/i*100);(p=t.getAudioTracks()[0])!=null&&p.enabled?e({isSoundDetected:c,audioLevel:f}):e({isSoundDetected:!1,audioLevel:0})},n);return async function(){clearInterval(d),u.disconnect(),l.disconnect(),o.state!=="closed"&&await o.close(),a&&t.getTracks().forEach(c=>{c.stop(),t.removeTrack(c)})}};class kg{constructor(){this.pc1=new RTCPeerConnection({}),this.pc2=new RTCPeerConnection({})}async start(e){try{this.cleanupAudioStream();const s=await navigator.mediaDevices.getUserMedia({audio:!0});this.audioStream=s,this.pc1.addEventListener("icecandidate",async a=>{await this.pc2.addIceCandidate(a.candidate)}),this.pc2.addEventListener("icecandidate",async a=>{await this.pc1.addIceCandidate(a.candidate)}),this.pc2.addEventListener("track",a=>{a.streams[0].getTracks().forEach(o=>{o._setVolume(0)})}),s.getTracks().forEach(a=>this.pc1.addTrack(a,s));const n=await this.pc1.createOffer({});await this.pc2.setRemoteDescription(n),await this.pc1.setLocalDescription(n);const i=await this.pc2.createAnswer();await this.pc1.setRemoteDescription(i),await this.pc2.setLocalDescription(i);const r=this.onSpeakingDetectedStateChange(e);return()=>{r(),this.stop()}}catch(s){return j(["RNSpeechDetector"])("error","error handling permissions: ",s),()=>{}}}stop(){this.pc1.close(),this.pc2.close(),this.cleanupAudioStream()}onSpeakingDetectedStateChange(e){let n=.13,i=!1,r,a;const o=[],l=10,u=1.1,d=.9,m=500,c=5e3,f=setInterval(async()=>{try{const p=await this.pc1.getStats(),b=Dn(p).find(T=>T.type==="media-source"&&T.kind==="audio");if(b){const{audioLevel:T}=b;if(T){o.push(T),o.length>l&&o.shift();const S=o.reduce((w,E)=>w+E,0)/o.length;S<n*u?a||(a=setTimeout(()=>{n=Math.min(S*d,.13)},c)):(clearTimeout(a),a=void 0),S>n*1.5&&(i||(i=!0,e({isSoundDetected:!0,audioLevel:T})),clearTimeout(r),r=setTimeout(()=>{i=!1,e({isSoundDetected:!1,audioLevel:0})},m))}}}catch(p){j(["RNSpeechDetector"])("error","error checking audio level from stats",p)}},100);return()=>{clearInterval(f),clearTimeout(r),clearTimeout(a)}}cleanupAudioStream(){this.audioStream&&(this.audioStream.getTracks().forEach(e=>e.stop()),typeof this.audioStream.release=="function"&&this.audioStream.release())}}class wg extends wi{constructor(e,s="stop-tracks"){super(e,new yg(s),C.AUDIO),this.speakingWhileMutedNotificationEnabled=!0,this.soundDetectorConcurrencyTag=Symbol("soundDetectorConcurrencyTag"),this.subscriptions.push(mi(ze([this.call.state.callingState$,this.call.state.ownCapabilities$,this.state.selectedDevice$,this.state.status$]),async([n,i,r,a])=>{try{if(n===R.LEFT&&await this.stopSpeakingWhileMutedDetection(),n!==R.JOINED||!this.speakingWhileMutedNotificationEnabled)return;i.includes(Y.SEND_AUDIO)?a==="disabled"?await this.startSpeakingWhileMutedDetection(r):await this.stopSpeakingWhileMutedDetection():await this.stopSpeakingWhileMutedDetection()}catch(o){this.logger("warn","Could not enable speaking while muted",o)}})),this.subscriptions.push(ye(this.call.state.callingState$,n=>{var r,a;if(!this.noiseCancellationRegistration||!this.noiseCancellation)return;((a=(r=this.call.state.settings)==null?void 0:r.audio.noise_cancellation)==null?void 0:a.mode)===ys.AUTO_ON&&n===R.JOINED?this.noiseCancellationRegistration.then(()=>{var o;return(o=this.noiseCancellation)!=null&&o.canAutoEnable?this.noiseCancellation.canAutoEnable():!0}).then(o=>{var l;o&&((l=this.noiseCancellation)==null||l.enable())}).catch(o=>(this.logger("warn","Failed to enable noise cancellation",o),this.call.notifyNoiseCancellationStopped())):n===R.LEFT&&this.noiseCancellationRegistration.then(()=>{var o;return(o=this.noiseCancellation)==null?void 0:o.disable()}).catch(o=>{this.logger("warn","Failed to disable noise cancellation",o)})}))}async enableNoiseCancellation(e){const{ownCapabilities:s,settings:n}=this.call.state;if(!s.includes(Y.ENABLE_NOISE_CANCELLATION))throw new Error("Noise cancellation is not available.");const r=n==null?void 0:n.audio.noise_cancellation;if(!r||r.mode===ys.DISABLED)throw new Error("Noise cancellation is disabled for this call type.");try{if(this.noiseCancellation=e,this.noiseCancellationChangeUnsubscribe=this.noiseCancellation.on("change",a=>{this.call.tracer.trace("noiseCancellation.enabled",a),a?this.call.notifyNoiseCancellationStarting().catch(o=>{this.logger("warn","notifyNoiseCancellationStart failed",o)}):this.call.notifyNoiseCancellationStopped().catch(o=>{this.logger("warn","notifyNoiseCancellationStop failed",o)})}),Z())this.noiseCancellationRegistration=Promise.resolve();else{const a=this.registerFilter(e.toFilter());this.noiseCancellationRegistration=a.registered,this.unregisterNoiseCancellation=a.unregister,await this.noiseCancellationRegistration}if(r.mode===ys.AUTO_ON&&this.call.state.callingState===R.JOINED){let a=!0;e.canAutoEnable&&(a=await e.canAutoEnable()),a&&e.enable()}}catch(a){throw this.logger("warn","Failed to enable noise cancellation",a),await this.disableNoiseCancellation().catch(o=>{this.logger("warn","Failed to disable noise cancellation",o)}),a}}async disableNoiseCancellation(){var e;await(((e=this.unregisterNoiseCancellation)==null?void 0:e.call(this))??Promise.resolve()).then(()=>{var s;return(s=this.noiseCancellation)==null?void 0:s.disable()}).then(()=>{var s;return(s=this.noiseCancellationChangeUnsubscribe)==null?void 0:s.call(this)}).catch(s=>{this.logger("warn","Failed to unregister noise cancellation",s)}),await this.call.notifyNoiseCancellationStopped()}async enableSpeakingWhileMutedNotification(){this.speakingWhileMutedNotificationEnabled=!0,this.state.status==="disabled"&&await this.startSpeakingWhileMutedDetection(this.state.selectedDevice)}async disableSpeakingWhileMutedNotification(){this.speakingWhileMutedNotificationEnabled=!1,await this.stopSpeakingWhileMutedDetection()}async apply(e,s){var a;if(!s)return;const n=!!((a=this.call.state.localParticipant)!=null&&a.audioStream),i=this.call.permissionsContext.hasPermission(Y.SEND_AUDIO);if(n||!i)return;await this.statusChangeSettled();const{mediaStream:r}=this.state;this.enabled&&r?await this.publishStream(r):this.state.status===void 0&&e.mic_default_on&&await this.enable()}getDevices(){return ug()}getStream(e){return jo(e,this.call.tracer)}async startSpeakingWhileMutedDetection(e){await de(this.soundDetectorConcurrencyTag,async()=>{if(await this.stopSpeakingWhileMutedDetection(),Z()){this.rnSpeechDetector=new kg;const s=await this.rnSpeechDetector.start(n=>{this.state.setSpeakingWhileMuted(n.isSoundDetected)});this.soundDetectorCleanup=()=>{s(),this.rnSpeechDetector=void 0}}else{const s=await this.getStream({deviceId:{exact:e}});this.soundDetectorCleanup=Cg(s,n=>{this.state.setSpeakingWhileMuted(n.isSoundDetected)})}})}async stopSpeakingWhileMutedDetection(){await de(this.soundDetectorConcurrencyTag,async()=>{if(!this.soundDetectorCleanup)return;const e=this.soundDetectorCleanup;this.soundDetectorCleanup=void 0,this.state.setSpeakingWhileMuted(!1),await e()})}}class Eg extends Ei{constructor(){super(...arguments),this.audioEnabledSubject=new A(!0),this.settingsSubject=new A(void 0),this.audioEnabled$=this.audioEnabledSubject.asObservable().pipe(ae()),this.settings$=this.settingsSubject.asObservable(),this.getDeviceIdFromStream=e=>{const[s]=e.getTracks();return s==null?void 0:s.getSettings().deviceId}}get audioEnabled(){return X(this.audioEnabled$)}setAudioEnabled(e){Q(this.audioEnabledSubject,e)}get settings(){return X(this.settings$)}setSettings(e){Q(this.settingsSubject,e)}}class Rg extends wi{constructor(e){super(e,new Eg,C.SCREEN_SHARE),this.subscriptions.push(ye(e.state.settings$,s=>{const n=s==null?void 0:s.screensharing.target_resolution;n&&this.setDefaultConstraints({video:{width:n.width,height:n.height}})}))}enableScreenShareAudio(){this.state.setAudioEnabled(!0)}async disableScreenShareAudio(){var e;this.state.setAudioEnabled(!1),(e=this.call.publisher)!=null&&e.isPublishing(C.SCREEN_SHARE_AUDIO)&&await this.call.stopPublish(C.SCREEN_SHARE_AUDIO)}getSettings(){return this.state.settings}setSettings(e){this.state.setSettings(e)}getDevices(){return bn([])}getStream(e){return this.state.audioEnabled||(e.audio=!1),pg(e,this.call.tracer)}async stopPublishStream(){return this.call.stopPublish(C.SCREEN_SHARE,C.SCREEN_SHARE_AUDIO)}async select(){throw new Error("Not supported")}}class _g{constructor(e){this.selectedDeviceSubject=new A(""),this.volumeSubject=new A(1),this.isDeviceSelectionSupported=cg(),this.tracer=e,this.selectedDevice$=this.selectedDeviceSubject.asObservable().pipe(ae()),this.volume$=this.volumeSubject.asObservable().pipe(ae())}get selectedDevice(){return X(this.selectedDevice$)}get volume(){return X(this.volume$)}setDevice(e){Q(this.selectedDeviceSubject,e),this.tracer.trace("navigator.mediaDevices.setSinkId",e)}setVolume(e){Q(this.volumeSubject,e)}}class Ig{constructor(e){this.subscriptions=[],this.dispose=()=>{this.subscriptions.forEach(s=>s.unsubscribe())},this.call=e,this.state=new _g(e.tracer),Un&&!Z()&&this.subscriptions.push(ze([Un,this.state.selectedDevice$]).subscribe(([s,n])=>{if(!n)return;s.find(r=>r.deviceId===n&&r.kind==="audiooutput")||this.select("")}))}listDevices(){if(Z())throw new Error("This feature is not supported in React Native. Please visit https://getstream.io/video/docs/reactnative/core/camera-and-microphone/#speaker-management for more details");return dg()}select(e){if(Z())throw new Error("This feature is not supported in React Native. Please visit https://getstream.io/video/docs/reactnative/core/camera-and-microphone/#speaker-management for more details");this.state.setDevice(e)}setVolume(e){if(Z())throw new Error("This feature is not supported in React Native. Please visit https://getstream.io/video/docs/reactnative/core/camera-and-microphone/#speaker-management for more details");if(e&&(e<0||e>1))throw new Error("Volume must be between 0 and 1");this.state.setVolume(e)}setParticipantVolume(e,s){if(Z())throw new Error("This feature is not supported in React Native. Please visit https://getstream.io/video/docs/reactnative/core/camera-and-microphone/#speaker-management for more details");if(s&&(s<0||s>1))throw new Error("Volume must be between 0 and 1, or undefined");this.call.state.updateParticipant(e,{audioVolume:s})}}class wt{constructor({type:e,id:s,streamClient:n,members:i,ownCapabilities:r,sortParticipantsBy:a,clientStore:o,ringing:l=!1,watching:u=!1}){this.state=new em,this.permissionsContext=new ig,this.tracer=new Si(null),this.dispatcher=new Bp,this.sfuClientTag=0,this.reconnectConcurrencyTag=Symbol("reconnectConcurrencyTag"),this.reconnectAttempts=0,this.reconnectStrategy=N.UNSPECIFIED,this.reconnectReason="",this.fastReconnectDeadlineSeconds=0,this.disconnectionTimeoutSeconds=0,this.lastOfflineTimestamp=0,this.trackPublishOrder=[],this.hasJoinedOnce=!1,this.deviceSettingsAppliedOnce=!1,this.initialized=!1,this.joinLeaveConcurrencyTag=Symbol("joinLeaveConcurrencyTag"),this.leaveCallHooks=new Set,this.streamClientEventHandlers=new Map,this.setup=async()=>{await de(this.joinLeaveConcurrencyTag,async()=>{this.initialized||(this.leaveCallHooks.add(this.on("all",c=>{this.state.updateFromEvent(c)})),this.leaveCallHooks.add(this.on("changePublishOptions",c=>{this.currentPublishOptions=c.publishOptions})),this.leaveCallHooks.add(eg(this,this.dispatcher)),this.registerEffects(),this.registerReconnectHandlers(),this.state.callingState===R.LEFT&&this.state.setCallingState(R.IDLE),this.initialized=!0)})},this.registerEffects=()=>{this.leaveCallHooks.add(ye(this.state.settings$,c=>{c&&this.permissionsContext.setCallSettings(c)})),this.leaveCallHooks.add(mi(this.state.ownCapabilities$,this.handleOwnCapabilitiesUpdated)),this.leaveCallHooks.add(ye(this.state.blockedUserIds$,async c=>{if(!c||c.length===0)return;const h=this.currentUserId;h&&c.includes(h)&&(this.logger("info","Leaving call because of being blocked"),await this.leave({message:"user blocked"}).catch(f=>{this.logger("error","Error leaving call after being blocked",f)}))})),this.leaveCallHooks.add(ye(this.state.session$,c=>{var b;if(!this.ringing)return;const h=(b=this.clientStore.connectedUser)==null?void 0:b.id;if(!h)return;const f=!!(c!=null&&c.accepted_by[h]),p=!!(c!=null&&c.rejected_by[h]);(f||p)&&this.cancelAutoDrop(),(f&&this.state.callingState===R.RINGING||p)&&!qp(this.joinLeaveConcurrencyTag)&&this.leave().catch(()=>{this.logger("error","Could not leave a call that was accepted or rejected elsewhere")})})),this.leaveCallHooks.add(ye(this.ringingSubject,c=>{var w,E;if(!c)return;const h=this.state.session,f=(w=this.clientStore.connectedUser)==null?void 0:w.id,p=h==null?void 0:h.ended_at,g=(E=this.state.createdBy)==null?void 0:E.id,b=h==null?void 0:h.rejected_by,T=h==null?void 0:h.accepted_by;let S=!1;p?S=!0:g&&b?b[g]&&(S=!0):f&&b?b[f]&&(S=!0):f&&T&&T[f]&&(S=!0),S?this.state.callingState!==R.IDLE&&this.state.setCallingState(R.IDLE):(this.state.callingState===R.IDLE&&this.state.setCallingState(R.RINGING),this.scheduleAutoDrop(),this.leaveCallHooks.add(Do(this)))}))},this.handleOwnCapabilitiesUpdated=async c=>{if(this.permissionsContext.setPermissions(c),!this.publisher)return;const h={[Y.SEND_AUDIO]:C.AUDIO,[Y.SEND_VIDEO]:C.VIDEO,[Y.SCREENSHARE]:C.SCREEN_SHARE};for(const[f,p]of Object.entries(h))if(!this.permissionsContext.hasPermission(f))try{switch(p){case C.AUDIO:this.microphone.enabled&&await this.microphone.disable();break;case C.VIDEO:this.camera.enabled&&await this.camera.disable();break;case C.SCREEN_SHARE:this.screenShare.enabled&&await this.screenShare.disable();break}}catch(b){this.logger("error","Can't disable mic/camera/screenshare after revoked permissions",b)}},this.on=(c,h)=>{if(Or(c))return this.dispatcher.on(c,h);const f=this.streamClient.on(c,p=>{const g=p;g.call_cid&&g.call_cid===this.cid&&h(g)});return this.streamClientEventHandlers.set(h,f),()=>{this.off(c,h)}},this.off=(c,h)=>{if(Or(c))return this.dispatcher.off(c,h);const f=this.streamClientEventHandlers.get(h);f&&f()},this.leave=async({reject:c,reason:h,message:f}={})=>{await de(this.joinLeaveConcurrencyTag,async()=>{var b,T,S,w,E,_;const p=this.state.callingState;if(p===R.LEFT)throw new Error("Cannot leave call that has already been left.");if(p===R.JOINING&&await new Promise(F=>{this.state.callingState$.pipe(Bs(G=>G!==R.JOINED,!0)).subscribe(()=>F())}),p===R.RINGING&&c!==!1)if(c)await this.reject(h??"decline");else{const D=this.state.remoteParticipants.length>0;this.isCreatedByMe&&!D&&await this.reject("cancel")}(b=this.statsReporter)==null||b.stop(),this.statsReporter=void 0,(T=this.sfuStatsReporter)==null||T.flush(),(S=this.sfuStatsReporter)==null||S.stop(),this.sfuStatsReporter=void 0,(w=this.subscriber)==null||w.dispose(),this.subscriber=void 0,(E=this.publisher)==null||E.dispose(),this.publisher=void 0,await((_=this.sfuClient)==null?void 0:_.leaveAndClose(f??h??"user is leaving the call")),this.sfuClient=void 0,this.dynascaleManager.setSfuClient(void 0),await this.dynascaleManager.dispose(),this.state.setCallingState(R.LEFT),this.state.setParticipants([]),this.state.dispose(),this.leaveCallHooks.forEach(D=>D()),this.initialized=!1,this.hasJoinedOnce=!1,this.unifiedSessionId=void 0,this.ringingSubject.next(!1),this.cancelAutoDrop(),this.clientStore.unregisterCall(this),this.camera.dispose(),this.microphone.dispose(),this.screenShare.dispose(),this.speaker.dispose();const g=[];this.camera.stopOnLeave&&g.push(this.camera.disable(!0)),this.microphone.stopOnLeave&&g.push(this.microphone.disable(!0)),this.screenShare.stopOnLeave&&g.push(this.screenShare.disable(!0)),await Promise.all(g)})},this.updateFromRingingEvent=async c=>{await this.setup();const{created_by:h,settings:f}=c.call,p=this.state.members.find(b=>b.user.id===h.id);p?this.state.setMembers([p,...c.members]):this.state.setMembers(c.members),this.state.updateFromCallResponse(c.call),this.watching=!0,this.ringingSubject.next(!0);const g=this.clientStore.calls.filter(b=>b.cid!==this.cid);this.clientStore.setCalls([this,...g]),await this.applyDeviceConfig(f,!1)},this.get=async c=>{await this.setup();const h=await this.streamClient.get(this.streamClientBasePath,c);return this.state.updateFromCallResponse(h.call),this.state.setMembers(h.members),this.state.setOwnCapabilities(h.own_capabilities),c!=null&&c.ring&&this.ringingSubject.next(!0),this.streamClient._hasConnectionID()&&(this.watching=!0,this.clientStore.registerCall(this)),await this.applyDeviceConfig(h.call.settings,!1),h},this.getOrCreate=async c=>{await this.setup();const h=await this.streamClient.post(this.streamClientBasePath,c);return this.state.updateFromCallResponse(h.call),this.state.setMembers(h.members),this.state.setOwnCapabilities(h.own_capabilities),c!=null&&c.ring&&this.ringingSubject.next(!0),this.streamClient._hasConnectionID()&&(this.watching=!0,this.clientStore.registerCall(this)),await this.applyDeviceConfig(h.call.settings,!1),h},this.create=async c=>this.getOrCreate(c),this.delete=async(c={})=>this.streamClient.post(`${this.streamClientBasePath}/delete`,c),this.ring=async()=>await this.get({ring:!0}),this.notify=async()=>await this.get({notify:!0}),this.accept=async()=>this.streamClient.post(`${this.streamClientBasePath}/accept`),this.reject=async(c="decline")=>this.streamClient.post(`${this.streamClientBasePath}/reject`,{reason:c}),this.join=async({maxJoinRetries:c=3,...h}={})=>{await this.setup();const f=this.state.callingState;if([R.JOINED,R.JOINING].includes(f))throw new Error("Illegal State: call.join() shall be called only once");this.state.setCallingState(R.JOINING),c=Math.max(c,1);for(let p=0;p<c;p++){try{return this.logger("trace",`Joining call (${p})`,this.cid),await this.doJoin(h)}catch(g){if(this.logger("warn",`Failed to join call (${p})`,this.cid),p===c-1)throw this.state.setCallingState(f),g}await Me(qt(p))}},this.doJoin=async c=>{var F,G,re,fe;const h=Date.now(),f=this.state.callingState;this.joinCallData=c,this.logger("debug","Starting join flow"),this.state.setCallingState(R.JOINING);const p=this.reconnectStrategy===N.MIGRATE,g=this.reconnectStrategy===N.REJOIN,b=this.reconnectStrategy===N.FAST;let T=(F=this.sfuStatsReporter)==null?void 0:F.options;if(!this.credentials||!T||g||p)try{const W=await this.doJoinRequest(c);this.credentials=W.credentials,T=W.stats_options}catch(W){throw this.state.callingState===R.OFFLINE||this.state.setCallingState(f),W}const S=this.sfuClient,w=S==null?void 0:S.sessionId,E=!!(S!=null&&S.isHealthy),_=g||p||!E?new he({logTag:String(++this.sfuClientTag),dispatcher:this.dispatcher,credentials:this.credentials,streamClient:this.streamClient,enableTracing:T.enable_rtc_stats,sessionId:g?void 0:w,onSignalClose:W=>this.handleSfuSignalClose(_,W)}):S;this.sfuClient=_,this.dynascaleManager.setSfuClient(_);const D=await xr();if(S!==_){const[W,$e]=await Promise.all([Ir("recvonly"),Ir("sendonly")]),xe=this.reconnectStrategy!==N.UNSPECIFIED,Gt=xe?this.getReconnectDetails(c==null?void 0:c.migrating_from,w):void 0,et=xe?this.currentPublishOptions||[]:this.getPreferredPublishOptions(),Be=xe?[]:this.getPreferredSubscribeOptions();try{const{callState:Le,fastReconnectDeadlineSeconds:gt,publishOptions:bt}=await _.join({subscriberSdp:W,publisherSdp:$e,clientDetails:D,fastReconnect:b,reconnectDetails:Gt,preferredPublishOptions:et,preferredSubscribeOptions:Be});this.currentPublishOptions=bt,this.fastReconnectDeadlineSeconds=gt,Le&&this.state.updateFromSfuCallState(Le,_.sessionId,Gt)}catch(Le){throw this.logger("warn","Join SFU request failed",Le),_.close(he.ERROR_CONNECTION_UNHEALTHY,"Join request failed, connection considered unhealthy"),this.state.setCallingState(f),Le}}if(p||this.state.setCallingState(R.JOINED),this.hasJoinedOnce=!0,b)await this.restoreICE(_,{includeSubscriber:!1});else{const W=Nm(this.credentials.ice_servers);this.initPublisherAndSubscriber({sfuClient:_,connectionConfig:W,clientDetails:D,statsOptions:T,publishOptions:this.currentPublishOptions||[],closePreviousInstances:!p})}if(!g&&!b&&!p&&((G=this.sfuStatsReporter)==null||G.sendConnectionTime((Date.now()-h)/1e3)),g&&E){const W=N[this.reconnectStrategy];await(S==null?void 0:S.leaveAndClose(`Closing previous WS after reconnect with strategy: ${W}`))}else E||S==null||S.close(he.DISPOSE_OLD_SOCKET,"Closing unhealthy WS after reconnect");!this.deviceSettingsAppliedOnce&&this.state.settings&&(await this.applyDeviceConfig(this.state.settings,!0),this.deviceSettingsAppliedOnce=!0),(re=this.joinCallData)==null||delete re.ring,(fe=this.joinCallData)==null||delete fe.notify,this.reconnectStrategy=N.UNSPECIFIED,this.reconnectReason="",this.logger("info",`Joined call ${this.cid}`)},this.getReconnectDetails=(c,h)=>{var b;const f=this.reconnectStrategy,p=f===N.REJOIN,g=((b=this.publisher)==null?void 0:b.getAnnouncedTracksForReconnect())||[];return{strategy:f,announcedTracks:g,subscriptions:this.dynascaleManager.trackSubscriptions,reconnectAttempt:this.reconnectAttempts,fromSfuId:c||"",previousSessionId:p&&h||"",reason:this.reconnectReason}},this.getPreferredPublishOptions=()=>{const{preferredCodec:c,fmtpLine:h,preferredBitrate:f,maxSimulcastLayers:p}=this.clientPublishOptions||{};if(!c&&!f&&!p)return[];const g=c?De.create({name:c.split("/").pop(),fmtp:h}):void 0,b=[Ut.create({trackType:C.VIDEO,codec:g,bitrate:f,maxSpatialLayers:p})],T=this.screenShare.getSettings();return T&&b.push(Ut.create({trackType:C.SCREEN_SHARE,fps:T.maxFramerate,bitrate:T.maxBitrate})),b},this.getPreferredSubscribeOptions=()=>{const{subscriberCodec:c,subscriberFmtpLine:h}=this.clientPublishOptions||{};return!c||!h?[]:[So.create({trackType:C.VIDEO,codecs:[{name:c.split("/").pop(),fmtp:h}]})]},this.restoreICE=async(c,h={})=>{const{includeSubscriber:f=!0,includePublisher:p=!0}=h;this.subscriber&&(this.subscriber.setSfuClient(c),f&&await this.subscriber.restartIce()),this.publisher&&(this.publisher.setSfuClient(c),p&&await this.publisher.restartIce())},this.initPublisherAndSubscriber=c=>{var E,_,D;const{sfuClient:h,connectionConfig:f,clientDetails:p,statsOptions:g,publishOptions:b,closePreviousInstances:T}=c,{enable_rtc_stats:S}=g;T&&this.subscriber&&this.subscriber.dispose(),this.subscriber=new Pm({sfuClient:h,dispatcher:this.dispatcher,state:this.state,connectionConfig:f,logTag:String(this.sfuClientTag),enableTracing:S,onUnrecoverableError:F=>{this.reconnect(N.REJOIN,F).catch(G=>{this.logger("warn",`[Reconnect] Error reconnecting after a subscriber error: ${F}`,G)})}}),((E=this.streamClient.user)==null?void 0:E.type)==="anonymous"||(T&&this.publisher&&this.publisher.dispose(),this.publisher=new Om({sfuClient:h,dispatcher:this.dispatcher,state:this.state,connectionConfig:f,publishOptions:b,logTag:String(this.sfuClientTag),enableTracing:S,onUnrecoverableError:F=>{this.reconnect(N.REJOIN,F).catch(G=>{this.logger("warn",`[Reconnect] Error reconnecting after a publisher error: ${F}`,G)})}})),(_=this.statsReporter)==null||_.stop(),this.statsReporter=nm({subscriber:this.subscriber,publisher:this.publisher,state:this.state,datacenter:h.edgeName}),this.tracer.setEnabled(S),(D=this.sfuStatsReporter)==null||D.stop(),(g==null?void 0:g.reporting_interval_ms)>0&&(this.unifiedSessionId??(this.unifiedSessionId=h.sessionId),this.sfuStatsReporter=new fm(h,{clientDetails:p,options:g,subscriber:this.subscriber,publisher:this.publisher,microphone:this.microphone,camera:this.camera,state:this.state,tracer:this.tracer,unifiedSessionId:this.unifiedSessionId}),this.sfuStatsReporter.start())},this.doJoinRequest=async c=>{const h=await this.streamClient.getLocationHint(),f={...c,location:h},p=await this.streamClient.post(`${this.streamClientBasePath}/join`,f);return this.state.updateFromCallResponse(p.call),this.state.setMembers(p.members),this.state.setOwnCapabilities(p.own_capabilities),c!=null&&c.ring&&this.ringingSubject.next(!0),!(this.reconnectStrategy!==N.UNSPECIFIED)&&this.ringing&&!this.isCreatedByMe&&await this.accept(),this.streamClient._hasConnectionID()&&(this.watching=!0,this.clientStore.registerCall(this)),p},this.handleSfuSignalClose=(c,h)=>{this.logger("debug","[Reconnect] SFU signal connection closed");const{callingState:f}=this.state;f===R.JOINING||f===R.RECONNECTING||f===R.IDLE||f===R.LEFT||c.isLeaving||c.isClosing||this.reconnect(N.REJOIN,h).catch(p=>{this.logger("warn","[Reconnect] Error reconnecting",p)})},this.reconnect=async(c,h)=>{if(!(this.state.callingState===R.RECONNECTING||this.state.callingState===R.MIGRATING||this.state.callingState===R.RECONNECTING_FAILED))return de(this.reconnectConcurrencyTag,async()=>{var p;const f=Date.now();this.reconnectStrategy=c,this.reconnectReason=h;do{if(this.disconnectionTimeoutSeconds>0&&(Date.now()-f)/1e3>this.disconnectionTimeoutSeconds){this.logger("warn","[Reconnect] Stopping reconnection attempts after reaching disconnection timeout"),this.state.setCallingState(R.RECONNECTING_FAILED);return}this.reconnectStrategy!==N.FAST&&this.reconnectAttempts++;const g=N[this.reconnectStrategy];try{switch(await((p=this.networkAvailableTask)==null?void 0:p.promise),this.logger("info",`[Reconnect] Reconnecting with strategy ${N[this.reconnectStrategy]}`),this.reconnectStrategy){case N.UNSPECIFIED:case N.DISCONNECT:this.logger("debug",`[Reconnect] No-op strategy ${g}`);break;case N.FAST:await this.reconnectFast();break;case N.REJOIN:await this.reconnectRejoin();break;case N.MIGRATE:await this.reconnectMigrate();break;default:Ft(this.reconnectStrategy,"Unknown reconnection strategy");break}break}catch(b){if(this.state.callingState===R.OFFLINE){this.logger("trace","[Reconnect] Can't reconnect while offline, stopping reconnection attempts");break}if(b instanceof go&&b.unrecoverable){this.logger("warn","[Reconnect] Can't reconnect due to coordinator unrecoverable error",b),this.state.setCallingState(R.RECONNECTING_FAILED);return}this.logger("warn",`[Reconnect] ${g} (${this.reconnectAttempts}) failed. Attempting with REJOIN`,b),await Me(500),this.reconnectStrategy=N.REJOIN}}while(this.state.callingState!==R.JOINED&&this.state.callingState!==R.RECONNECTING_FAILED&&this.state.callingState!==R.LEFT);this.logger("info","[Reconnect] Reconnection flow finished")})},this.reconnectFast=async()=>{var h;const c=Date.now();this.reconnectStrategy=N.FAST,this.state.setCallingState(R.RECONNECTING),await this.doJoin(this.joinCallData),(h=this.sfuStatsReporter)==null||h.sendReconnectionTime(N.FAST,(Date.now()-c)/1e3)},this.reconnectRejoin=async()=>{var h;const c=Date.now();this.reconnectStrategy=N.REJOIN,this.state.setCallingState(R.RECONNECTING),await this.doJoin(this.joinCallData),await this.restorePublishedTracks(),this.restoreSubscribedTracks(),(h=this.sfuStatsReporter)==null||h.sendReconnectionTime(N.REJOIN,(Date.now()-c)/1e3)},this.reconnectMigrate=async()=>{var b,T;const c=Date.now(),h=this.sfuClient;if(!h)throw new Error("Cannot migrate without an active SFU client");this.reconnectStrategy=N.MIGRATE,this.state.setCallingState(R.MIGRATING);const f=this.subscriber,p=this.publisher;f==null||f.detachEventHandlers(),p==null||p.detachEventHandlers();const g=Mt(h.enterMigration());try{const S=h.edgeName;await this.doJoin({...this.joinCallData,migrating_from:S})}finally{(b=this.joinCallData)==null||delete b.migrating_from}await this.restorePublishedTracks(),this.restoreSubscribedTracks();try{await g(),this.state.setCallingState(R.JOINED)}finally{f==null||f.dispose(),p==null||p.dispose(),h.close(he.NORMAL_CLOSURE,"Migrating away")}(T=this.sfuStatsReporter)==null||T.sendReconnectionTime(N.MIGRATE,(Date.now()-c)/1e3)},this.registerReconnectHandlers=()=>{const c=this.on("goAway",()=>{this.reconnect(N.MIGRATE,"goAway").catch(p=>this.logger("warn","[Reconnect] Error reconnecting",p))}),h=this.on("error",p=>{const{reconnectStrategy:g,error:b}=p;g!==N.UNSPECIFIED&&(g===N.DISCONNECT?this.leave({message:"SFU instructed to disconnect"}).catch(T=>{this.logger("warn","Can't leave call after disconnect request",T)}):this.reconnect(g,(b==null?void 0:b.message)||"SFU Error").catch(T=>{this.logger("warn","[Reconnect] Error reconnecting",T)}))}),f=this.streamClient.on("network.changed",p=>{var g,b,T,S;if(this.tracer.trace("network.changed",p),p.online)this.logger("debug","[Reconnect] Going online"),(b=this.sfuClient)==null||b.close(he.DISPOSE_OLD_SOCKET,"Closing WS to reconnect after going online"),(T=this.networkAvailableTask)==null||T.resolve(),this.networkAvailableTask=void 0,(S=this.sfuStatsReporter)==null||S.start();else{if(this.logger("debug","[Reconnect] Going offline"),!this.hasJoinedOnce)return;this.lastOfflineTimestamp=Date.now();const w=_t();w.promise.then(()=>{let E=N.FAST;this.lastOfflineTimestamp&&(Date.now()-this.lastOfflineTimestamp)/1e3>this.fastReconnectDeadlineSeconds&&(E=N.REJOIN),this.reconnect(E,"Going online").catch(_=>{this.logger("warn","[Reconnect] Error reconnecting after going online",_)})}),this.networkAvailableTask=w,(g=this.sfuStatsReporter)==null||g.stop(),this.state.setCallingState(R.OFFLINE)}});this.leaveCallHooks.add(c).add(h).add(f)},this.restorePublishedTracks=async()=>{for(const c of this.trackPublishOrder){let h;switch(c){case C.AUDIO:h=this.microphone.state.mediaStream;break;case C.VIDEO:h=this.camera.state.mediaStream;break;case C.SCREEN_SHARE:h=this.screenShare.state.mediaStream;break;case C.SCREEN_SHARE_AUDIO:case C.UNSPECIFIED:break;default:Ft(c,"Unknown track type");break}h&&await this.publish(h,c)}},this.restoreSubscribedTracks=()=>{const{remoteParticipants:c}=this.state;c.length<=0||this.dynascaleManager.applyTrackSubscriptions(void 0)},this.publishVideoStream=async c=>{await this.publish(c,C.VIDEO)},this.publishAudioStream=async c=>{await this.publish(c,C.AUDIO)},this.publishScreenShareStream=async c=>{await this.publish(c,C.SCREEN_SHARE)},this.publish=async(c,h)=>{var g;if(!this.sfuClient)throw new Error("Call not joined yet.");if(await this.sfuClient.joinTask,!this.permissionsContext.canPublish(h))throw new Error(`No permission to publish ${C[h]}`);if(!this.publisher)throw new Error("Publisher is not initialized");const[f]=Ti(h)?c.getAudioTracks():c.getVideoTracks();if(!f)throw new Error(`There is no ${C[h]} track in the stream`);if(f.readyState==="ended")throw new Error("Can't publish ended tracks.");yn(this.trackPublishOrder,h),await this.publisher.publish(f,h);const p=[h];if(h===C.SCREEN_SHARE){const[b]=c.getAudioTracks();b&&(yn(this.trackPublishOrder,C.SCREEN_SHARE_AUDIO),await this.publisher.publish(b,C.SCREEN_SHARE_AUDIO),p.push(C.SCREEN_SHARE_AUDIO))}f.kind==="video"&&((g=this.sfuStatsReporter)==null||g.scheduleOne(3e3)),await this.updateLocalStreamState(c,...p)},this.stopPublish=async(...c)=>{!this.sfuClient||!this.publisher||(this.publisher.stopTracks(...c),await this.updateLocalStreamState(void 0,...c))},this.updateLocalStreamState=async(c,...h)=>{if(!this.sfuClient||!this.sfuClient.sessionId)return;await this.notifyTrackMuteState(!c,...h);const{sessionId:f}=this.sfuClient;for(const p of h){const g=vi(p);g&&this.state.updateParticipant(f,b=>({publishedTracks:c?yn([...b.publishedTracks],p):b.publishedTracks.filter(T=>T!==p),[g]:c}))}},this.updatePublishOptions=c=>{this.logger("warn","[call.updatePublishOptions]: You are manually overriding the publish options for this call. This is not recommended, and it can cause call stability/compatibility issues. Use with caution."),this.state.callingState===R.JOINED&&this.logger("warn","Updating publish options after joining the call does not have an effect"),this.clientPublishOptions={...this.clientPublishOptions,...c}},this.notifyNoiseCancellationStarting=async()=>{var c;return(c=this.sfuClient)==null?void 0:c.startNoiseCancellation().catch(h=>{this.logger("warn","Failed to notify start of noise cancellation",h)})},this.notifyNoiseCancellationStopped=async()=>{var c;return(c=this.sfuClient)==null?void 0:c.stopNoiseCancellation().catch(h=>{this.logger("warn","Failed to notify stop of noise cancellation",h)})},this.notifyTrackMuteState=async(c,...h)=>{this.sfuClient&&await this.sfuClient.updateMuteStates(h.map(f=>({trackType:f,muted:c})))},this.startReportingStatsFor=c=>{var h;return(h=this.statsReporter)==null?void 0:h.startReportingStatsFor(c)},this.stopReportingStatsFor=c=>{var h;return(h=this.statsReporter)==null?void 0:h.stopReportingStatsFor(c)},this.resetReaction=c=>{this.state.updateParticipant(c,{reaction:void 0})},this.setSortParticipantsBy=c=>this.state.setSortParticipantsBy(c),this.sendReaction=async c=>this.streamClient.post(`${this.streamClientBasePath}/reaction`,c),this.blockUser=async c=>this.streamClient.post(`${this.streamClientBasePath}/block`,{user_id:c}),this.unblockUser=async c=>this.streamClient.post(`${this.streamClientBasePath}/unblock`,{user_id:c}),this.muteSelf=c=>{const h=this.currentUserId;if(h)return this.muteUser(h,c)},this.muteOthers=c=>{const h=Sm(c);if(!h)return;const f=[];for(const p of this.state.remoteParticipants)p.publishedTracks.includes(h)&&f.push(p.userId);if(f.length>0)return this.muteUser(f,c)},this.muteUser=(c,h)=>this.streamClient.post(`${this.streamClientBasePath}/mute_users`,{user_ids:Array.isArray(c)?c:[c],[h]:!0}),this.muteAllUsers=c=>this.streamClient.post(`${this.streamClientBasePath}/mute_users`,{mute_all_users:!0,[c]:!0}),this.startRecording=async c=>this.streamClient.post(`${this.streamClientBasePath}/start_recording`,c||{}),this.stopRecording=async()=>this.streamClient.post(`${this.streamClientBasePath}/stop_recording`,{}),this.startTranscription=async c=>this.streamClient.post(`${this.streamClientBasePath}/start_transcription`,c),this.stopTranscription=async()=>this.streamClient.post(`${this.streamClientBasePath}/stop_transcription`),this.startClosedCaptions=async c=>{const h=this.state.setCaptioning(!0);try{return await this.streamClient.post(`${this.streamClientBasePath}/start_closed_captions`,c)}catch(f){throw h.rollback(),f}},this.stopClosedCaptions=async c=>{const h=this.state.setCaptioning(!1);try{return await this.streamClient.post(`${this.streamClientBasePath}/stop_closed_captions`,c)}catch(f){throw h.rollback(),f}},this.updateClosedCaptionSettings=c=>{this.state.updateClosedCaptionSettings(c)},this.requestPermissions=async c=>{const{permissions:h}=c;if(!h.every(p=>this.permissionsContext.canRequest(p)))throw new Error(`You are not allowed to request permissions: ${h.join(", ")}`);return this.streamClient.post(`${this.streamClientBasePath}/request_permission`,c)},this.grantPermissions=async(c,h)=>this.updateUserPermissions({user_id:c,grant_permissions:h}),this.revokePermissions=async(c,h)=>this.updateUserPermissions({user_id:c,revoke_permissions:h}),this.updateUserPermissions=async c=>this.streamClient.post(`${this.streamClientBasePath}/user_permissions`,c),this.goLive=async(c={},h)=>this.streamClient.post(`${this.streamClientBasePath}/go_live`,c,h),this.stopLive=async(c={})=>this.streamClient.post(`${this.streamClientBasePath}/stop_live`,c),this.startHLS=async()=>this.streamClient.post(`${this.streamClientBasePath}/start_broadcasting`,{}),this.stopHLS=async()=>this.streamClient.post(`${this.streamClientBasePath}/stop_broadcasting`,{}),this.startRTMPBroadcasts=async c=>this.streamClient.post(`${this.streamClientBasePath}/rtmp_broadcasts`,c),this.stopAllRTMPBroadcasts=async()=>this.streamClient.post(`${this.streamClientBasePath}/rtmp_broadcasts/stop`),this.stopRTMPBroadcast=async c=>this.streamClient.post(`${this.streamClientBasePath}/rtmp_broadcasts/${c}/stop`),this.startFrameRecording=async c=>this.streamClient.post(`${this.streamClientBasePath}/start_frame_recording`,c),this.stopFrameRecording=async()=>this.streamClient.post(`${this.streamClientBasePath}/stop_frame_recording`),this.update=async c=>{const h=await this.streamClient.patch(`${this.streamClientBasePath}`,c),{call:f,members:p,own_capabilities:g}=h;return this.state.updateFromCallResponse(f),this.state.setMembers(p),this.state.setOwnCapabilities(g),h},this.endCall=async()=>this.streamClient.post(`${this.streamClientBasePath}/mark_ended`),this.pin=c=>{this.state.updateParticipant(c,{pin:{isLocalPin:!0,pinnedAt:Date.now()}})},this.unpin=c=>{this.state.updateParticipant(c,{pin:void 0})},this.pinForEveryone=async c=>this.streamClient.post(`${this.streamClientBasePath}/pin`,c),this.unpinForEveryone=async c=>this.streamClient.post(`${this.streamClientBasePath}/unpin`,c),this.queryMembers=c=>this.streamClient.post("/call/members",{...c||{},id:this.id,type:this.type}),this.updateCallMembers=async c=>this.streamClient.post(`${this.streamClientBasePath}/members`,c),this.scheduleAutoDrop=()=>{this.cancelAutoDrop();const c=this.state.settings;if(!c||this.state.callingState!==R.RINGING)return;const h=this.isCreatedByMe?c.ring.auto_cancel_timeout_ms:c.ring.incoming_call_timeout_ms;h<=0||(this.dropTimeout=setTimeout(()=>{this.state.callingState===R.RINGING&&this.leave({reject:!0,reason:"timeout",message:`ringing timeout - ${this.isCreatedByMe?"no one accepted":"user didn't interact with incoming call screen"}`}).catch(f=>{this.logger("error","Failed to drop call",f)})},h))},this.cancelAutoDrop=()=>{clearTimeout(this.dropTimeout),this.dropTimeout=void 0},this.queryRecordings=async c=>{let h=this.streamClientBasePath;return c&&(h=`${h}/${c}`),this.streamClient.get(`${h}/recordings`)},this.queryTranscriptions=async()=>this.streamClient.get(`${this.streamClientBasePath}/transcriptions`),this.getCallStats=async c=>{const h=`${this.streamClientBasePath}/stats/${c}`;return this.streamClient.get(h)},this.getCallReport=async(c="")=>{const h=`${this.streamClientBasePath}/report`,f=c!==""?{session_id:c}:{};return this.streamClient.get(h,f)},this.submitFeedback=async(c,{reason:h,custom:f}={})=>{var T;const{sdkName:p,sdkVersion:g,...b}=tm(await xr());return this.streamClient.post(`${this.streamClientBasePath}/feedback`,{rating:c,reason:h,user_session_id:(T=this.sfuClient)==null?void 0:T.sessionId,sdk:p,sdk_version:g,custom:{...f,"x-stream-platform-data":b}})},this.sendCustomEvent=async c=>this.streamClient.post(`${this.streamClientBasePath}/event`,{custom:c}),this.applyDeviceConfig=async(c,h)=>{await this.camera.apply(c.video,h).catch(f=>{this.logger("warn","Camera init failed",f)}),await this.microphone.apply(c.audio,h).catch(f=>{this.logger("warn","Mic init failed",f)})},this.trackElementVisibility=(c,h,f)=>this.dynascaleManager.trackElementVisibility(c,h,f),this.setViewport=c=>this.dynascaleManager.setViewport(c),this.bindVideoElement=(c,h,f)=>{const p=this.dynascaleManager.bindVideoElement(c,h,f);if(p)return this.leaveCallHooks.add(p),()=>{this.leaveCallHooks.delete(p),p()}},this.bindAudioElement=(c,h,f="audioTrack")=>{const p=this.dynascaleManager.bindAudioElement(c,h,f);if(p)return this.leaveCallHooks.add(p),()=>{this.leaveCallHooks.delete(p),p()}},this.bindCallThumbnailElement=(c,h={})=>{const f=()=>{c.src=h.fallbackImageSource||"https://getstream.io/random_svg/?name=x&id=x"},p=ye(this.state.thumbnails$,g=>{if(!g)return;c.addEventListener("error",f);const b=new URL(g.image_url);b.searchParams.set("w",String(c.clientWidth)),b.searchParams.set("h",String(c.clientHeight)),c.src=b.toString()});return()=>{p(),c.removeEventListener("error",f)}},this.setPreferredIncomingVideoResolution=(c,h)=>{this.dynascaleManager.setVideoTrackSubscriptionOverrides(c?{enabled:!0,dimension:c}:void 0,h),this.dynascaleManager.applyTrackSubscriptions()},this.setIncomingVideoEnabled=c=>{this.dynascaleManager.setVideoTrackSubscriptionOverrides(c?void 0:{enabled:!1}),this.dynascaleManager.applyTrackSubscriptions()},this.setDisconnectionTimeout=c=>{this.disconnectionTimeoutSeconds=c},this.type=e,this.id=s,this.cid=`${e}:${s}`,this.ringingSubject=new A(l),this.watching=u,this.streamClient=n,this.clientStore=o,this.streamClientBasePath=`/call/${this.type}/${this.id}`,this.logger=j(["Call"]);const d=ag.get(e),m=a||d.options.sortParticipantsBy;m&&this.state.setSortParticipantsBy(m),this.state.setMembers(i||[]),this.state.setOwnCapabilities(r||[]),this.state.setCallingState(l?R.RINGING:R.IDLE),this.camera=new bg(this),this.microphone=new wg(this),this.speaker=new Ig(this),this.screenShare=new Rg(this),this.dynascaleManager=new sg(this.state,this.speaker)}get ringing(){return X(this.ringingSubject)}get currentUserId(){var e;return(e=this.clientStore.connectedUser)==null?void 0:e.id}get isCreatedByMe(){var e;return((e=this.state.createdBy)==null?void 0:e.id)===this.currentUserId}}var Og=null;class Pg{constructor(e){this._log=(s,n={},i="info")=>{this.client.logger(i,`connection:${s}`,n)},this.setClient=s=>{this.client=s},this._buildUrl=()=>{const s=new URLSearchParams;return s.set("api_key",this.client.key),s.set("stream-auth-type",this.client.getAuthType()),s.set("X-Stream-Client",this.client.getUserAgent()),`${this.client.wsBaseURL}/connect?${s.toString()}`},this.onlineStatusChanged=s=>{s.type==="offline"?(this._log("onlineStatusChanged() - Status changing to offline"),this._setHealth(!1,!0)):s.type==="online"&&(this._log(`onlineStatusChanged() - Status changing to online. isHealthy: ${this.isHealthy}`),this.isHealthy||this._reconnect({interval:10}))},this.onopen=s=>{var a;if(this.wsID!==s)return;const n=this.client.user;if(!n){this.client.logger("error","User not set, can't connect to WS");return}const i=this.client._getToken();if(!i){this.client.logger("error","Token not set, can't connect authenticate");return}const r=JSON.stringify({token:i,user_details:{id:n.id,name:n.name,image:n.image,custom:n.custom}});this._log(`onopen() - Sending auth message ${r}`,{},"trace"),(a=this.ws)==null||a.send(r),this._log("onopen() - onopen callback",{wsID:s})},this.onmessage=(s,n)=>{var r,a;if(this.wsID!==s)return;this._log("onmessage() - onmessage callback",{event:n,wsID:s});const i=typeof n.data=="string"?JSON.parse(n.data):null;if(!this.isConnectionOpenResolved&&i&&i.type==="connection.error"&&(this.isConnectionOpenResolved=!0,i.error)){(r=this.rejectConnectionOpen)==null||r.call(this,this._errorFromWSEvent(i,!1));return}if(this.lastEvent=new Date,i&&(i.type==="health.check"||i.type==="connection.ok")&&this.scheduleNextPing(),i&&i.type==="connection.ok"&&((a=this.resolveConnectionOpen)==null||a.call(this,i),this._setHealth(!0)),i&&i.type==="connection.error"&&i.error){const{code:o}=i.error;this.isHealthy=!1,this.isConnecting=!1,this.consecutiveFailures+=1,o===st.TOKEN_EXPIRED&&!this.client.tokenManager.isStatic()&&(clearTimeout(this.connectionCheckTimeoutRef),this._log("connect() - WS failure due to expired token, so going to try to reload token and reconnect"),this._reconnect({refreshToken:!0}))}i&&(i.received_at=new Date,this.client.dispatchEvent(i)),this.scheduleConnectionCheck()},this.onclose=(s,n)=>{var i,r;if(this.wsID===s)if(this._log("onclose() - onclose callback - "+n.code,{event:n,wsID:s}),n.code===st.WS_CLOSED_SUCCESS){const a=new Error(`WS connection reject with error ${n.reason}`);a.reason=n.reason,a.code=n.code,a.wasClean=n.wasClean,a.target=n.target,(i=this.rejectConnectionOpen)==null||i.call(this,a),this._log(`onclose() - WS connection reject with error ${n.reason}`,{event:n})}else this.consecutiveFailures+=1,this.totalFailures+=1,this._setHealth(!1),this.isConnecting=!1,(r=this.rejectConnectionOpen)==null||r.call(this,this._errorFromWSEvent(n)),this._log("onclose() - WS connection closed. Calling reconnect ...",{event:n}),this._reconnect()},this.onerror=(s,n)=>{var i;this.wsID===s&&(this.consecutiveFailures+=1,this.totalFailures+=1,this._setHealth(!1),this.isConnecting=!1,(i=this.rejectConnectionOpen)==null||i.call(this,new Error(`WebSocket error: ${n}`)),this._log("onerror() - WS connection resulted into error",{event:n}),this._reconnect())},this._setHealth=(s,n=!1)=>{if(s!==this.isHealthy){if(this.isHealthy=s,this.isHealthy||n){this.client.dispatchEvent({type:"connection.changed",online:this.isHealthy});return}setTimeout(()=>{this.isHealthy||this.client.dispatchEvent({type:"connection.changed",online:this.isHealthy})},5e3)}},this._errorFromWSEvent=(s,n=!0)=>{let i,r,a;if(Fp(s))i=s.code,a=s.reason,r=0;else{const{error:u}=s;i=u.code,a=u.message,r=u.StatusCode}const o=`WS failed with code: ${i} and reason: ${a}`;this._log(o,{event:s},"warn");const l=new Error(o);return l.code=i,l.StatusCode=r,l.isWSFailure=n,l},this._setupConnectionPromise=()=>{this.isConnectionOpenResolved=!1,this.connectionOpenSafe=Mt(new Promise((s,n)=>{this.resolveConnectionOpen=s,this.rejectConnectionOpen=n}))},this.scheduleNextPing=()=>{const s=xn();this.healthCheckTimeoutRef&&s.clearTimeout(this.healthCheckTimeoutRef),this.healthCheckTimeoutRef=s.setTimeout(()=>{var i;const n=[{type:"health.check",client_id:this.client.clientID}];try{(i=this.ws)==null||i.send(JSON.stringify(n))}catch{}},this.pingInterval)},this.scheduleConnectionCheck=()=>{clearTimeout(this.connectionCheckTimeoutRef),this.connectionCheckTimeoutRef=setTimeout(()=>{const s=new Date;this.lastEvent&&s.getTime()-this.lastEvent.getTime()>this.connectionCheckTimeout&&(this._log("scheduleConnectionCheck - going to reconnect"),this._setHealth(!1),this._reconnect())},this.connectionCheckTimeout)},this.client=e,this.consecutiveFailures=0,this.totalFailures=0,this.isConnecting=!1,this.isDisconnected=!1,this.isConnectionOpenResolved=!1,this.isHealthy=!1,this.wsID=1,this.lastEvent=null,this.pingInterval=25*1e3,this.connectionCheckTimeout=this.pingInterval+10*1e3,Ks(this.onlineStatusChanged)}async connect(e=15e3){if(this.isConnecting)throw Error("You've called connect twice, can only attempt 1 connection at the time");this.isDisconnected=!1;try{const s=await this._connect();this.consecutiveFailures=0,this._log(`connect() - Established ws connection with healthcheck: ${s}`)}catch(s){if(this.isHealthy=!1,this.consecutiveFailures+=1,s.code===st.TOKEN_EXPIRED&&!this.client.tokenManager.isStatic())this._log("connect() - WS failure due to expired token, so going to try to reload token and reconnect"),this._reconnect({refreshToken:!0});else if(!s.isWSFailure)throw new Error(JSON.stringify({code:s.code,StatusCode:s.StatusCode,message:s.message,isWSFailure:s.isWSFailure}))}return await this._waitForHealthy(e)}async _waitForHealthy(e=15e3){return Promise.race([(async()=>{for(let n=0;n<=e;n+=50)try{return await this.connectionOpen}catch(i){if(n===e)throw new Error(JSON.stringify({code:i.code,StatusCode:i.StatusCode,message:i.message,isWSFailure:i.isWSFailure}));await Me(50)}})(),(async()=>{throw await Me(e),this.isConnecting=!1,new Error(JSON.stringify({code:"",StatusCode:"",message:"initial WS connection could not be established",isWSFailure:!0}))})()])}disconnect(e){this._log(`disconnect() - Closing the websocket connection for wsID ${this.wsID}`),this.wsID+=1,this.isConnecting=!1,this.isDisconnected=!0,this.healthCheckTimeoutRef&&xn().clearInterval(this.healthCheckTimeoutRef),this.connectionCheckTimeoutRef&&clearInterval(this.connectionCheckTimeoutRef),Co(this.onlineStatusChanged),this.isHealthy=!1;let s;const{ws:n}=this;return n&&n.close&&n.readyState===n.OPEN?(s=new Promise(i=>{const r=a=>{this._log(`disconnect() - resolving isClosedPromise ${a?"with":"without"} close frame`,{event:a}),i()};n.onclose=r,setTimeout(r,e??1e3)}),this._log("disconnect() - Manually closed connection by calling client.disconnect()"),n.close(st.WS_CLOSED_SUCCESS,"Manually closed connection by calling client.disconnect()")):(this._log("disconnect() - ws connection doesn't exist or it is already closed."),s=Promise.resolve()),delete this.ws,s}async _connect(){var s,n,i,r;if(this.isConnecting)return;this.isConnecting=!0;let e=!1;try{this._log("_connect() - waiting for token"),await this.client.tokenManager.tokenReady(),e=!0}catch{}try{e||(this._log("_connect() - tokenProvider failed before, so going to retry"),await this.client.tokenManager.loadToken()),this.client.isConnectionIsPromisePending||this.client._setupConnectionIdPromise(),this._setupConnectionPromise();const a=this._buildUrl();this._log(`_connect() - Connecting to ${a}`);const o=this.client.options.WebSocketImpl??WebSocket;this.ws=new o(a),this.ws.onopen=this.onopen.bind(this,this.wsID),this.ws.onclose=this.onclose.bind(this,this.wsID),this.ws.onerror=this.onerror.bind(this,this.wsID),this.ws.onmessage=this.onmessage.bind(this,this.wsID);const l=await this.connectionOpen;if(this.isConnecting=!1,l)return this.connectionID=l.connection_id,(n=(s=this.client).resolveConnectionId)==null||n.call(s,this.connectionID),l}catch(a){throw this.client._setupConnectionIdPromise(),this.isConnecting=!1,this._log("_connect() - Error - ",a),(r=(i=this.client).rejectConnectionId)==null||r.call(i,a),a}}async _reconnect(e={}){if(this._log("_reconnect() - Initiating the reconnect"),this.isConnecting||this.isHealthy){this._log("_reconnect() - Abort (1) since already connecting or healthy");return}let s=e.interval;if(s||(s=qt(this.consecutiveFailures)),await Me(s),this.isConnecting||this.isHealthy){this._log("_reconnect() - Abort (2) since already connecting or healthy");return}if(this.isDisconnected){this._log("_reconnect() - Abort (3) since disconnect() is called");return}this._log("_reconnect() - Destroying current WS connection"),this._destroyCurrentWSConnection(),e.refreshToken&&await this.client.tokenManager.loadToken();try{await this._connect(),this._log("_reconnect() - Waiting for recoverCallBack"),this._log("_reconnect() - Finished recoverCallBack"),this.consecutiveFailures=0}catch(n){if(this.isHealthy=!1,this.consecutiveFailures+=1,n.code===st.TOKEN_EXPIRED&&!this.client.tokenManager.isStatic())return this._log("_reconnect() - WS failure due to expired token, so going to try to reload token and reconnect"),this._reconnect({refreshToken:!0});n.isWSFailure&&(this._log("_reconnect() - WS failure, so going to try to reconnect"),this._reconnect())}this._log("_reconnect() - == END ==")}_destroyCurrentWSConnection(){var e;this.wsID+=1;try{(e=this==null?void 0:this.ws)==null||e.close()}catch{}}get connectionOpen(){var e;return(e=this.connectionOpenSafe)==null?void 0:e.call(this)}}function Ag(t){const e=t.split(".");if(e.length!==3)return"";const s=e[1],n=Ng(s);return JSON.parse(n).user_id}const Ng=t=>{const e={},s=String.fromCharCode,n=t.length;let i,r=0,a,o,l=0,u,d="";const m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(i=0;i<64;i++)e[m.charAt(i)]=i;for(o=0;o<n;o++)for(a=e[t.charAt(o)],r=(r<<6)+a,l+=6;l>=8;)((u=r>>>(l-=8)&255)||o<n-2)&&(d+=s(u));return d};class Dg{constructor(e){this.loadTokenPromise=null,this.type="static",this.setTokenOrProvider=async(s,n,i)=>{this.user=n,this.isAnonymous=i,this.validateToken(s),Ws(s)&&(this.tokenProvider=s,this.type="provider"),typeof s=="string"&&(this.token=s,this.type="static"),await this.loadToken()},this.reset=()=>{this.token=void 0,this.tokenProvider=void 0,this.type="static",this.user=void 0,this.loadTokenPromise=null},this.validateToken=s=>{if(!(this.user&&this.isAnonymous&&!s)){if(!this.secret&&!s)throw new Error("User token can not be empty");if(typeof s!="string"&&!Ws(s))throw new Error("User token should either be a string or a function");if(typeof s=="string"){if(this.isAnonymous&&s==="")return;const n=Ag(s);if(s!=null&&(n==null||n===""||!this.isAnonymous&&n!==this.user.id))throw new Error("userToken does not have a user_id or is not matching with user.id")}}},this.tokenReady=()=>this.loadTokenPromise,this.loadToken=()=>(this.loadTokenPromise=new Promise(async(s,n)=>{if(this.type==="static")return s(this.token);if(this.tokenProvider&&typeof this.tokenProvider!="string"){try{const i=await this.tokenProvider();this.validateToken(i),this.token=i}catch(i){return n(new Error(`Call to tokenProvider failed with message: ${i}`,{cause:i}))}s(this.token)}}),this.loadTokenPromise),this.getToken=()=>{if(this.token)return this.token;if(this.user&&!this.token)return this.token;throw new Error("User token is not set. Either client.connectUser wasn't called or client.disconnect was called")},this.isStatic=()=>this.type==="static",this.secret=e}}const jr=async(t="https://hint.stream-io-video.com/",e=2e3,s=3)=>{const n=j(["location-hint"]);let i=0,r="ERR";do{const a=new AbortController,o=setTimeout(()=>a.abort(),e);try{const u=(await fetch(t,{method:"HEAD",signal:a.signal})).headers.get("x-amz-cf-pop")||"ERR";n("debug",`Location header: ${u}`),r=u.substring(0,3)}catch(l){n("warn",`Failed to get location hint from ${t}`,l),r="ERR"}finally{clearTimeout(o)}}while(r==="ERR"&&++i<s);return r};class xg{constructor(e,s){var i;this.listeners={},this.getAuthType=()=>this.anonymous?"anonymous":"jwt",this.setBaseURL=r=>{this.baseURL=r,this.wsBaseURL=this.baseURL.replace("http","ws").replace(":3030",":8800")},this.getLocationHint=async(r,a)=>{const o=await this.locationHint;return!o||o==="ERR"?(this.locationHint=jr(r??this.options.locationHintUrl,a??this.options.locationHintTimeout),this.locationHint):o},this._getConnectionID=()=>{var r;return(r=this.wsConnection)==null?void 0:r.connectionID},this._hasConnectionID=()=>!!this._getConnectionID(),this.connectUser=async(r,a)=>{if(!r.id)throw new Error('The "id" field on the user is missing');if(this.userID===r.id&&this.connectUserTask)return this.logger("warn","Consecutive calls to connectUser is detected, ideally you should only call this function once in your app."),this.connectUserTask;if(this.userID)throw new Error("Use client.disconnect() before trying to connect as a different user. connectUser was called twice.");(this.secret||this.node)&&!this.options.allowServerSideConnect&&this.logger("warn","Please do not use connectUser server side. Use our @stream-io/node-sdk instead: https://getstream.io/video/docs/api/"),this.userID=r.id,this.anonymous=!1,await this.tokenManager.setTokenOrProvider(a,r,!1),this._setUser(r),this.connectUserTask=this.openConnection();try{return Ks(this.updateNetworkConnectionStatus),await this.connectUserTask}catch(o){throw this.persistUserOnConnectionFailure?await this.closeConnection():await this.disconnectUser(),o}},this._setUser=r=>{this.user=r,this.userID=r.id,this._user={...r}},this.closeConnection=async r=>{var a;await((a=this.wsConnection)==null?void 0:a.disconnect(r))},this.openConnection=async()=>{var o,l,u;if(!this.userID)throw Error("UserWithId is not set on client, use client.connectUser or client.connectAnonymousUser instead");const r=(o=this.wsPromiseSafe)==null?void 0:o.call(this);if((l=this.wsConnection)!=null&&l.isConnecting&&r)return this.logger("info","client:openConnection() - connection already in progress"),await r;if((u=this.wsConnection)!=null&&u.isHealthy&&this._hasConnectionID()){this.logger("info","client:openConnection() - openConnection called twice, healthy connection already exists");return}this._setupConnectionIdPromise(),this.clientID=`${this.userID}--${Js()}`;const a=this.connect();return this.wsPromiseSafe=Mt(a),await a},this.disconnectUser=async r=>{this.logger("info","client:disconnect() - Disconnecting the client"),delete this.user,delete this._user,delete this.userID,this.anonymous=!1,await this.closeConnection(r),Co(this.updateNetworkConnectionStatus),this.tokenManager.reset(),this.connectionIdPromiseSafe=void 0,this.rejectConnectionId=void 0,this.resolveConnectionId=void 0},this.connectGuestUser=async r=>{this.guestUserCreatePromise=this.doAxiosRequest("post","/guest",{user:r},{publicEndpoint:!0});const a=await this.guestUserCreatePromise;return this.guestUserCreatePromise.finally(()=>this.guestUserCreatePromise=void 0),this.connectUser(a.user,a.access_token)},this.connectAnonymousUser=async(r,a)=>{var o;Ks(this.updateNetworkConnectionStatus),this._setupConnectionIdPromise(),this.anonymous=!0,await this.tokenManager.setTokenOrProvider(a,r,!0),this._setUser(r),(o=this.resolveConnectionId)==null||o.call(this)},this.on=(r,a)=>{var o;return this.listeners[r]||(this.listeners[r]=[]),this.logger("debug",`Adding listener for ${r} event`),(o=this.listeners[r])==null||o.push(a),()=>{this.off(r,a)}},this.off=(r,a)=>{var o;this.listeners[r]||(this.listeners[r]=[]),this.logger("debug",`Removing listener for ${r} event`),this.listeners[r]=(o=this.listeners[r])==null?void 0:o.filter(l=>l!==a)},this._setupConnectionIdPromise=()=>{this.connectionIdPromiseSafe=Mt(new Promise((r,a)=>{this.resolveConnectionId=r,this.rejectConnectionId=a}))},this._logApiRequest=(r,a,o,l)=>{zs()==="trace"&&this.logger("trace",`client: ${r} - Request - ${a}`,{payload:o,config:l})},this._logApiResponse=(r,a,o)=>{zs()==="trace"&&this.logger("trace",`client:${r} - Response - url: ${a} > status ${o.status}`,{response:o})},this._logApiError=(r,a,o)=>{this.logger("error",`client:${r} - Error - url: ${a}`,{url:a,error:o})},this.doAxiosRequest=async(r,a,o,l={})=>{var d,m;if(!l.publicEndpoint){await Promise.all([this.tokenManager.tokenReady(),this.guestUserCreatePromise]);try{await this.connectionIdPromise}catch{await((d=this.wsConnection)==null?void 0:d._waitForHealthy()),await this.connectionIdPromise}}const u=this._enrichAxiosOptions(l);try{let c;switch(this._logApiRequest(r,a,o,u),r){case"get":c=await this.axiosInstance.get(a,u);break;case"delete":c=await this.axiosInstance.delete(a,u);break;case"post":c=await this.axiosInstance.post(a,o,u);break;case"put":c=await this.axiosInstance.put(a,o,u);break;case"patch":c=await this.axiosInstance.patch(a,o,u);break;case"options":c=await this.axiosInstance.options(a,u);break;default:throw new Error("Invalid request type")}return this._logApiResponse(r,a,c),this.consecutiveFailures=0,this.handleResponse(c)}catch(c){if(c.client_request_id=(m=u.headers)==null?void 0:m["x-client-request-id"],this.consecutiveFailures+=1,c.response)return this._logApiError(r,a,c.response),c.response.data.code===st.TOKEN_EXPIRED&&!this.tokenManager.isStatic()?(this.consecutiveFailures>1&&await Me(qt(this.consecutiveFailures)),await this.tokenManager.loadToken(),await this.doAxiosRequest(r,a,o,l)):this.handleResponse(c.response);throw this._logApiError(r,a,c),c}},this.get=(r,a)=>this.doAxiosRequest("get",r,null,{params:a}),this.put=(r,a,o)=>this.doAxiosRequest("put",r,a,{params:o}),this.post=(r,a,o)=>this.doAxiosRequest("post",r,a,{params:o}),this.patch=(r,a,o)=>this.doAxiosRequest("patch",r,a,{params:o}),this.delete=(r,a)=>this.doAxiosRequest("delete",r,null,{params:a}),this.errorFromResponse=r=>{const{data:a,status:o}=r;return new go({message:`Stream error code ${a.code}: ${a.message}`,code:a.code??null,unrecoverable:a.unrecoverable??null,response:r,status:o})},this.handleResponse=r=>{const a=r.data;if(Up(r))throw this.errorFromResponse(r);return a},this.dispatchEvent=r=>{if(this.logger("debug",`Dispatching event: ${r.type}`,r),!!this.listeners){for(const a of this.listeners.all||[])a(r);for(const a of this.listeners[r.type]||[])a(r)}},this.connect=async()=>{if(!this.userID||!this._user)throw Error("Call connectUser or connectAnonymousUser before starting the connection");if(!this.wsBaseURL)throw Error("Websocket base url not set");if(!this.clientID)throw Error("clientID is not set");return this.wsConnection=new Pg(this),this.logger("info","StreamClient.connect: this.wsConnection.connect()"),await this.wsConnection.connect(this.defaultWSTimeout)},this.getUserAgent=()=>{if(!this.cachedUserAgent){const{clientAppIdentifier:r={}}=this.options,{sdkName:a="js",sdkVersion:o="1.23.5",...l}=r;this.cachedUserAgent=[`stream-video-${a}-v${o}`,...Object.entries(l).map(([u,d])=>`${u}=${d}`),"client_bundle=browser-esm"].join("|")}return this.cachedUserAgent},this._enrichAxiosOptions=(r={params:{},headers:{},config:{}})=>{var l;const a=r.publicEndpoint&&!this.user?void 0:this._getToken(),o=a?{Authorization:a}:void 0;return(l=r.headers)!=null&&l["x-client-request-id"]||(r.headers={...r.headers,"x-client-request-id":Js()}),{params:{user_id:this.userID,connection_id:this._getConnectionID(),api_key:this.key,...r.params},headers:{...o,"stream-auth-type":r.publicEndpoint&&!this.user?"anonymous":this.getAuthType(),"X-Stream-Client":this.getUserAgent(),...r.headers},...r.config,...this.options.axiosRequestConfig}},this._getToken=()=>this.tokenManager?this.tokenManager.getToken():null,this.updateNetworkConnectionStatus=r=>{r.type==="offline"?(this.logger("debug","device went offline"),this.dispatchEvent({type:"network.changed",online:!1})):r.type==="online"&&(this.logger("debug","device went online"),this.dispatchEvent({type:"network.changed",online:!0}))},this.key=e,this.secret=s==null?void 0:s.secret;const n=s||{browser:typeof window<"u"};this.browser=n.browser||typeof window<"u",this.node=!this.browser,this.browser&&(this.locationHint=jr(s==null?void 0:s.locationHintUrl,s==null?void 0:s.locationHintTimeout,s==null?void 0:s.locationHintMaxAttempts)),this.options={timeout:5e3,withCredentials:!1,...n},this.node&&!this.options.httpsAgent&&(this.options.httpsAgent=new Og.Agent({keepAlive:!0,keepAliveMsecs:3e3})),this.setBaseURL(this.options.baseURL||"https://video.stream-io-api.com/video"),this.axiosInstance=z.create({...this.options,baseURL:this.baseURL}),this.wsConnection=null,this.wsPromiseSafe=null,this.connectUserTask=null,this.anonymous=!1,this.persistUserOnConnectionFailure=(i=this.options)==null?void 0:i.persistUserOnConnectionFailure,this.tokenManager=new Dg(this.secret),this.consecutiveFailures=0,this.defaultWSTimeout=this.options.defaultWsTimeout??15e3,this.logger=Ws(n.logger)?n.logger:()=>null}get connectionIdPromise(){var e;return(e=this.connectionIdPromiseSafe)==null?void 0:e.call(this)}get isConnectionIsPromisePending(){var e;return((e=this.connectionIdPromiseSafe)==null?void 0:e.checkPending())??!1}get wsPromise(){var e;return(e=this.wsPromiseSafe)==null?void 0:e.call(this)}}const Cs=(t,e)=>`${t}/${e.id}`,Lg=t=>{const e=(t==null?void 0:t.clientAppIdentifier)||{},s=dm();return s&&(e.sdkName=Ye[s.type].toLowerCase(),e.sdkVersion=`${s.major}.${s.minor}.${s.patch}`),e},Ug=(t,e)=>{const s=Lg(e),n=j(["coordinator"]);return new xg(t,{persistUserOnConnectionFailure:!0,...e,clientAppIdentifier:s,logger:n})},Fg=t=>{const{token:e,tokenProvider:s}=t;if(e&&s){let n=!1;return async function(){return n?s():(n=!0,e)}}return e||s};class Ve{constructor(e,s){this.effectsRegistered=!1,this.eventHandlersToUnregister=[],this.connectionConcurrencyTag=Symbol("connectionConcurrencyTag"),this.registerClientInstance=(a,o)=>{const l=Cs(a,o);Ve._instances.has(l)&&this.logger("warn",`A StreamVideoClient already exists for ${o.id}; Prefer using getOrCreateInstance method`),Ve._instances.set(l,this)},this.registerEffects=()=>{this.effectsRegistered||(this.eventHandlersToUnregister.push(this.on("connection.changed",a=>{if(!a.online)return;const o=this.writeableStateStore.calls.filter(l=>l.watching).map(l=>l.cid);o.length<=0||(this.logger("info",`Rewatching calls ${o.join(", ")}`),this.queryCalls({watch:!0,filter_conditions:{cid:{$in:o}},sort:[{field:"cid",direction:1}]}).catch(l=>{this.logger("error","Failed to re-watch calls",l)}))})),this.eventHandlersToUnregister.push(this.on("call.created",a=>{var d;const{call:o,members:l}=a;if(((d=this.state.connectedUser)==null?void 0:d.id)===o.created_by.id){this.logger("warn","Received `call.created` sent by the current user");return}this.logger("info",`New call created and registered: ${o.cid}`);const u=new wt({streamClient:this.streamClient,type:o.type,id:o.id,members:l,clientStore:this.writeableStateStore});u.state.updateFromCallResponse(o),this.writeableStateStore.registerCall(u)})),this.eventHandlersToUnregister.push(this.on("call.ring",async a=>{var d;const{call:o,members:l}=a;if(((d=this.state.connectedUser)==null?void 0:d.id)===o.created_by.id){this.logger("debug","Received `call.ring` sent by the current user so ignoring the event");return}const u=this.writeableStateStore.findCall(o.type,o.id);u?await u.updateFromRingingEvent(a):await new wt({streamClient:this.streamClient,type:o.type,id:o.id,members:l,clientStore:this.writeableStateStore,ringing:!0}).get()})),this.effectsRegistered=!0)},this.connectUser=async(a,o)=>{if(a.type==="anonymous")return a.id="!anon",this.connectAnonymousUser(a,o);const l=await de(this.connectionConcurrencyTag,async()=>{const u=this.streamClient,{onConnectUserError:d,persistUserOnConnectionFailure:m}=u.options;let{maxConnectUserRetries:c=5}=u.options;c=Math.max(c,1);const h=[];for(let f=0;f<c;f++)try{return this.logger("trace",`Connecting user (${f})`,a),a.type==="guest"?await u.connectGuestUser(a):await u.connectUser(a,o)}catch(p){if(this.logger("warn",`Failed to connect a user (${f})`,p),h.push(p),f===c-1)throw d==null||d(p,h),p;m&&await u.disconnectUser(),await Me(qt(f))}});return l!=null&&l.me&&this.writeableStateStore.setConnectedUser(l.me),this.registerEffects(),l},this.disconnectUser=async a=>{await de(this.connectionConcurrencyTag,async()=>{const{user:o,key:l}=this.streamClient;o&&(await this.streamClient.disconnectUser(a),o.id&&Ve._instances.delete(Cs(l,o)),this.eventHandlersToUnregister.forEach(u=>u()),this.eventHandlersToUnregister=[],this.effectsRegistered=!1,this.writeableStateStore.setConnectedUser(void 0))})},this.on=(a,o)=>this.streamClient.on(a,o),this.off=(a,o)=>this.streamClient.off(a,o),this.call=(a,o)=>new wt({streamClient:this.streamClient,id:o,type:a,clientStore:this.writeableStateStore}),this.createGuestUser=async a=>this.streamClient.doAxiosRequest("post","/guest",a,{publicEndpoint:!0}),this.queryCalls=async(a={})=>{const o=await this.streamClient.post("/calls",a),l=[];for(const u of o.calls){const d=new wt({streamClient:this.streamClient,id:u.call.id,type:u.call.type,members:u.members,ownCapabilities:u.own_capabilities,watching:a.watch,clientStore:this.writeableStateStore});d.state.updateFromCallResponse(u.call),await d.applyDeviceConfig(u.call.settings,!1),a.watch&&(await d.setup(),this.writeableStateStore.registerCall(d)),l.push(d)}return{...o,calls:l}},this.queryCallStats=async(a={})=>this.streamClient.post("/call/stats",a),this.queryAggregateCallStats=async(a={})=>this.streamClient.post("/stats",a),this.edges=async()=>this.streamClient.get("/edges"),this.addDevice=async(a,o,l,u,d)=>await this.streamClient.post("/devices",{id:a,push_provider:o,voip_token:d,...u!=null?{user_id:u}:{},...l!=null?{push_provider_name:l}:{}}),this.addVoipDevice=async(a,o,l,u)=>await this.addDevice(a,o,l,u,!0),this.getDevices=async a=>await this.streamClient.get("/devices",a?{user_id:a}:{}),this.removeDevice=async(a,o)=>await this.streamClient.delete("/devices",{id:a,...o?{user_id:o}:{}}),this.onRingingCall=async a=>{let o=this.state.calls.find(l=>l.cid===a&&l.ringing);if(!o){const[l,u]=a.split(":");o=new wt({streamClient:this.streamClient,type:l,id:u,clientStore:this.writeableStateStore,ringing:!0}),await o.get()}return o},this.connectAnonymousUser=async(a,o)=>de(this.connectionConcurrencyTag,()=>this.streamClient.connectAnonymousUser(a,o));const n=typeof e=="string"?e:e.apiKey,i=typeof e=="string"?s:e.options;i!=null&&i.enableTimerWorker&&Lm();const r=(i==null?void 0:i.logger)||wo;if(Mp(r,(i==null?void 0:i.logLevel)||"warn"),this.logger=j(["client"]),this.streamClient=Ug(n,i),this.writeableStateStore=new zp,this.readOnlyStateStore=new Yp(this.writeableStateStore),typeof e!="string"&&e.user){const a=e.user;a.type==="anonymous"&&(a.id="!anon"),a.id&&this.registerClientInstance(n,a);const o=Fg(e);this.connectUser(a,o).catch(l=>{this.logger("error","Failed to connect",l)})}}static getOrCreateInstance(e){const{apiKey:s,user:n,token:i,tokenProvider:r}=e;if(!n.id&&n.type!=="anonymous")throw new Error("user.id is required for a non-anonymous user");if(!i&&!r&&n.type!=="anonymous"&&n.type!=="guest")throw new Error("tokenProvider or token is required for a authenticated users");return Ve._instances.get(Cs(s,n))||new Ve(e)}get state(){return this.readOnlyStateStore}}Ve._instances=new Map;export{Ve as S,Jg as T};
