import{a5 as F,a6 as U,a7 as O,i as E,K as Y,L as N,a8 as j,a9 as k,aa as x,ab as G,e as T,ac as z,ad as H,a1 as p,ae as K,Y as M,af as V,ag as W,S as Z,ah as J,k as B,ai as Q,M as C,aj as X}from"./runtime.BoVB3PJd.js";import{p as ee}from"./proxy.Dxp3JHm7.js";import{a as re}from"./entry.DaoNXzB7.js";let g=!1;function fe(e,r,a){const n=a[r]??(a[r]={store:null,source:U(void 0),unsubscribe:O});if(n.store!==e)if(n.unsubscribe(),n.store=e,e==null)n.source.v=void 0,n.unsubscribe=O;else{var c=!0;n.unsubscribe=re(e,l=>{c?n.source.v=l:Y(n.source,l)}),c=!1}return E(n.source)}function le(){const e={};return F(()=>{for(var r in e)e[r].unsubscribe()}),e}function ne(e){var r=g;try{return g=!1,[e(),g]}finally{g=r}}function q(e){for(var r=C,a=C;r!==null&&!(r.f&(z|H));)r=r.parent;try{return p(r),e()}finally{p(a)}}function oe(e,r,a,n){var I;var c=(a&K)!==0,l=!M||(a&V)!==0,f=(a&W)!==0,i=(a&X)!==0,P=!1,s;f?[s,P]=ne(()=>e[r]):s=e[r];var d=Z in e||J in e,v=f&&(((I=N(e,r))==null?void 0:I.set)??(d&&r in e&&(t=>e[r]=t)))||void 0,_=n,u=!0,o=!1,b=()=>(o=!0,u&&(u=!1,i?_=T(n):_=n),_);s===void 0&&n!==void 0&&(v&&l&&j(),s=b(),v&&v(s));var h;if(l)h=()=>{var t=e[r];return t===void 0?b():(u=!0,o=!1,t)};else{var L=q(()=>(c?B:Q)(()=>e[r]));L.f|=k,h=()=>{var t=E(L);return t!==void 0&&(_=void 0),t===void 0?_:t}}if(!(a&x))return h;if(v){var $=e.$$legacy;return function(t,m){return arguments.length>0?((!l||!m||$||P)&&v(m?h():t),t):h()}}var R=!1,A=!1,w=U(s),S=q(()=>B(()=>{var t=h(),m=E(w);return R?(R=!1,A=!0,m):(A=!1,w.v=t)}));return c||(S.equals=G),function(t,m){if(arguments.length>0){const y=m?E(S):l&&f?ee(t):t;return S.equals(y)||(R=!0,Y(w,y),o&&_!==void 0&&(_=y),T(()=>E(S))),t}return E(S)}}const te="modulepreload",ae=function(e,r){return new URL(e,r).href},D={},ce=function(r,a,n){let c=Promise.resolve();if(a&&a.length>0){const f=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),P=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));c=Promise.allSettled(a.map(s=>{if(s=ae(s,n),s in D)return;D[s]=!0;const d=s.endsWith(".css"),v=d?'[rel="stylesheet"]':"";if(!!n)for(let o=f.length-1;o>=0;o--){const b=f[o];if(b.href===s&&(!d||b.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${s}"]${v}`))return;const u=document.createElement("link");if(u.rel=d?"stylesheet":te,d||(u.as="script"),u.crossOrigin="",u.href=s,P&&u.setAttribute("nonce",P),document.head.appendChild(u),d)return new Promise((o,b)=>{u.addEventListener("load",o),u.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${s}`)))})}))}function l(f){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=f,window.dispatchEvent(i),!i.defaultPrevented)throw f}return c.then(f=>{for(const i of f||[])i.status==="rejected"&&l(i.reason);return r().catch(l)})};export{ce as _,fe as a,oe as p,le as s};
