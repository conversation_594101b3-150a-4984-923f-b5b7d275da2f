import"../chunks/CWj6FrbW.js";import"../chunks/BUgeVMx1.js";import{p as b,a as h,c as L,r as w,f as u,g as l,a0 as k}from"../chunks/DvS_9Yw_.js";import{f as r,a as t,c as x}from"../chunks/Ckyppc5t.js";import{a as y}from"../chunks/DZ8rvC1m.js";import{i as P}from"../chunks/DrBokXpg.js";import{s as T}from"../chunks/PoYD5o0_.js";import{i as q}from"../chunks/BTdX64hs.js";import{L as C}from"../chunks/DafOIsaB.js";import{g as D}from"../chunks/7czifHHs.js";import{T as F}from"../chunks/BwY1cqmU.js";import{b as N}from"../chunks/CQqdknCc.js";var $=r("<div>No listings yet!</div>"),j=r("<div>Failed to load listings</div>"),z=r("<div>Loading listings...</div>"),A=r("<browse-listings><!></browse-listings>",2);function V(g,v){b(v,!1),q(),F(g,{heading:"browse",children:(d,B)=>{var a=A();T(a,1,"svelte-1seeax");var p=L(a);y(p,()=>N({}),s=>{var o=z();t(s,o)},(s,o)=>{var n=x();const m=k(()=>l(o).listings);var f=u(n);{var _=i=>{C(i,{get listings(){return l(m)},onClickListing:e=>D(`/listing?id=${e.id}`)})},c=i=>{var e=$();t(i,e)};P(f,i=>{l(m).length>0?i(_):i(c,!1)})}t(s,n)},s=>{var o=j();t(s,o)}),w(a),t(d,a)}}),h()}export{V as component};
