import"../chunks/CWj6FrbW.js";import{a as ft}from"../chunks/B8_8MPyK.js";import{h as _t,M as ht,S as yt,c as g,aB as K,r as p,t as L,p as N,f as O,n as j,g as t,d as T,s as _,a as Y,e as U,b as bt,K as kt}from"../chunks/DvS_9Yw_.js";import{s as V}from"../chunks/BGB6NhFz.js";import{a as xt}from"../chunks/DZ8rvC1m.js";import{i as b}from"../chunks/DrBokXpg.js";import{e as Lt}from"../chunks/hasoi-S8.js";import{f as d,a as n,t as G,c as Ut}from"../chunks/Ckyppc5t.js";import{s as Pt,a as z,r as Dt}from"../chunks/D9ToATF9.js";import{s as m}from"../chunks/PoYD5o0_.js";import{l as qt,d as I}from"../chunks/CTtUbKlS.js";import{b as Ct}from"../chunks/CDhH9bvI.js";import{a as H,s as Rt}from"../chunks/CZYSyPBs.js";import{p as wt}from"../chunks/Ctt2IVZk.js";import{R as A}from"../chunks/BHOEOwOo.js";import{T as Tt}from"../chunks/BwY1cqmU.js";import{m as St,o as Bt,p as Ot}from"../chunks/CQqdknCc.js";function jt(o,e,r=e){qt(o,"change",s=>{var c=s?o.defaultChecked:o.checked;r(c)}),(_t&&o.defaultChecked!==o.checked||ht(e)==null)&&r(o.checked),yt(()=>{var s=e();o.checked=!!s})}const zt=function*({title:o,description:e}){o.length===0&&(yield"Title is empty"),e.length===0&&(yield"Description is empty")};class It{constructor(e){this.errors=e}get ok(){return this.errors.length===0}}const $t=o=>e=>new It([...o(e)]),Et={listing:$t(zt)};var Ft=d("<listing-thumbnail><!></listing-thumbnail>",2);function J(o,e){const r=wt(e,"isCreatePlaceholder",3,!1);var s=Ft();s.__click=function(...a){var u;(u=e.onClick)==null||u.apply(this,a)},Pt(s,"tabindex","-1");let c;var h=g(s);H(h,()=>e.children??K),p(s),L(a=>c=m(s,1,"svelte-1d865zk",null,c,a),[()=>({"is-create-placeholder":r()})]),n(o,s)}I(["click"]);var Mt=(o,e,r)=>{t(e)===null||t(e).files===null||r.onSelectFiles(t(e).files)},Vt=d('<!> <input type="file" class="svelte-mzcj0m"/>',1);function At(o,e){N(e,!0);let r=T(null);var s=Vt(),c=O(s);J(c,{isCreatePlaceholder:!0,onClick:()=>{var a;(a=t(r))==null||a.click()},children:(a,u)=>{j();var P=G("+");n(a,P)},$$slots:{default:!0}});var h=_(c,2);h.__change=[Mt,r,e],Ct(h,a=>U(r,a),()=>t(r)),n(o,s),Y()}I(["change"]);var Kt=d('<img alt="Listing thumbnail" class="svelte-r3zp2w"/>');function Nt(o,e){J(o,{get onClick(){return e.onClick},children:(r,s)=>{var c=Kt();L(()=>z(c,"src",e.imageUrl)),n(r,c)},$$slots:{default:!0}})}var Yt=d("<subtle-exclamation><!></subtle-exclamation>",2);function Gt(o,e){var r=Yt();m(r,1,"svelte-d07673");var s=g(r);H(s,()=>e.children??K),p(r),n(o,r)}const Ht=async(o,e,r,s)=>{t(e)===null||!Et.listing({title:t(e).title,description:t(e).description}).ok||(r!==null?await Bt({listingId:r,listingTitle:t(e).title,listingDescription:t(e).description,listingOnDisplay:t(e).onDisplay}):await Ot({listingTitle:t(e).title,listingDescription:t(e).description,listingOnDisplay:t(e).onDisplay}),U(s,!1))};var Jt=d('<div class="svelte-1ey440q"><!> <div><button>Save</button></div></div>'),Qt=d('<img class="svelte-1ey440q"/>'),Wt=d("<div>No images added yet!</div>"),Xt=d("<div> </div>"),Zt=d("<div> </div>"),te=d('<listing-display-toggle><input id="listing-display-toggle" type="checkbox"/> <label for="listing-display-toggle">On display</label></listing-display-toggle>',2),ee=d("<!> <listing-photos><main-photo><!></main-photo> <photos-carousel><!> <!></photos-carousel></listing-photos> <listing-title><!></listing-title> <listing-description><!></listing-description> <!>",3),ie=d('<div class="svelte-1ey440q">Listing failed to load!</div>'),ae=d('<div class="svelte-1ey440q">Loading listing details...</div>'),le=d("<listing-display><!></listing-display>",2);function Ue(o,e){var P;N(e,!0);const r=new URLSearchParams(location.search);let s=T(bt((r.has("new")||r.has("edit"))&&(((P=Rt.user)==null?void 0:P.canSell)??!1)));const c=r.get("id")??null,h=c===null?Promise.resolve({title:"",description:"",imageUrls:[],onDisplay:!1}):St({listingId:c});let a=T(null);(async()=>{const f=await h;U(a,{title:f.title,description:f.description,imageUrls:f.imageUrls,onDisplay:f.onDisplay},!0)})();let u=T(0);kt(()=>{t(a)!==null&&U(u,Math.min(t(u),t(a).imageUrls.length-1),!0)}),ft(()=>{if(t(a)!==null)for(const f of t(a).imageUrls)URL.revokeObjectURL(f)}),Tt(o,{heading:"listing",hasBackButton:!0,children:(f,se)=>{var D=le();let $;var Q=g(D);xt(Q,()=>h,y=>{var k=ae();n(y,k)},y=>{var k=Ut(),W=O(k);{var X=S=>{var E=ee(),F=O(E);{var Z=i=>{var l=Jt(),v=g(l);Gt(v,{children:(mt,re)=>{j();var ut=G("You're editing this listing!");n(mt,ut)}});var x=_(v,2),pt=g(x);pt.__click=[Ht,a,c,s],p(x),p(l),n(i,l)};b(F,i=>{t(s)&&i(Z)})}var q=_(F,2);m(q,1,"svelte-1ey440q");var C=g(q);m(C,1,"svelte-1ey440q");var tt=g(C);{var et=i=>{var l=Qt();L(()=>{z(l,"src",t(a).imageUrls[t(u)]),z(l,"alt",`"${t(a).title}" main image`)}),n(i,l)},it=i=>{var l=Wt();n(i,l)};b(tt,i=>{t(a).imageUrls.length>=1?i(et):i(it,!1)})}p(C);var B=_(C,2);m(B,1,"svelte-1ey440q");var M=g(B);Lt(M,18,()=>t(a).imageUrls,i=>i,(i,l,v)=>{Nt(i,{get imageUrl(){return l},onClick:()=>{U(u,t(v),!0)}})});var at=_(M,2);{var lt=i=>{At(i,{onSelectFiles:l=>{if(t(a)!==null)for(const v of l){const x=URL.createObjectURL(v);t(a).imageUrls.push(x)}}})};b(at,i=>{t(s)&&i(lt)})}p(B),p(q);var R=_(q,2);m(R,1,"svelte-1ey440q");var st=g(R);{var rt=i=>{A(i,{get initialText(){return t(a).title},onInput:l=>t(a)!==null&&(t(a).title=l),placeholder:"an eyecatching title"})},ot=i=>{var l=Xt(),v=g(l,!0);p(l),L(()=>V(v,t(a).title)),n(i,l)};b(st,i=>{t(s)?i(rt):i(ot,!1)})}p(R);var w=_(R,2);m(w,1,"svelte-1ey440q");var nt=g(w);{var ct=i=>{A(i,{get initialText(){return t(a).description},onInput:l=>t(a)!==null&&(t(a).description=l),placeholder:"a detailed description"})},dt=i=>{var l=Zt(),v=g(l,!0);p(l),L(()=>V(v,t(a).description)),n(i,l)};b(nt,i=>{t(s)?i(ct):i(dt,!1)})}p(w);var vt=_(w,2);{var gt=i=>{var l=te();m(l,1,"svelte-1ey440q");var v=g(l);Dt(v),j(2),p(l),jt(v,()=>t(a).onDisplay,x=>t(a).onDisplay=x),n(i,l)};b(vt,i=>{t(s)&&i(gt)})}n(S,E)};b(W,S=>{t(a)!==null&&S(X)})}n(y,k)},y=>{var k=ie();n(y,k)}),p(D),L(y=>$=m(D,1,"svelte-1ey440q",null,$,y),[()=>({editing:t(s)})]),n(f,D)}}),Y()}I(["click"]);export{Ue as component};
