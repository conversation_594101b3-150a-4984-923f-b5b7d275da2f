import"../chunks/CWj6FrbW.js";import{p as E,b as G,f as m,a as H,n as S,g as e,d as J,s as b,u as y,e as L}from"../chunks/DvS_9Yw_.js";import{c as k,a,f,t as $}from"../chunks/Ckyppc5t.js";import{a as K}from"../chunks/DZ8rvC1m.js";import{i as x}from"../chunks/DrBokXpg.js";import{B as I}from"../chunks/DF_v5tP2.js";import{s as w}from"../chunks/CZYSyPBs.js";import{L as M}from"../chunks/Be2B7OK6.js";import{L as O}from"../chunks/DafOIsaB.js";import{g as Q,e as R}from"../chunks/CQqdknCc.js";import{S as T}from"../chunks/ESsD0lEJ.js";import{s as C,r as V}from"../chunks/DdbRmKj_.js";var W=f("<div>No listings yet!</div>"),X=f("<!> <!> <!>",1),Y=f("<div>Failed to load listings</div>");function ft(B,D){E(D,!0);let n=J(!1);const r=G(new T(C().data.listings[Symbol.iterator]().map(i=>i.id))),v=y(()=>C().id),P=async()=>{e(v)!==null&&(L(n,!0),await R({streamId:e(v),listingIds:[...r]}),L(n,!1),V(),history.back())},F=async()=>{history.back()};var c=k(),N=m(c);{var U=i=>{var g=k(),j=m(g);K(j,()=>Q({sellerUserId:w.user.id}),o=>{M(o)},(o,l)=>{var p=X();const u=y(()=>e(l).listings);var _=m(p);I(_,{onClick:P,get disabled(){return e(n)},strong:!0,children:(t,s)=>{S();var d=$("Save");a(t,d)},$$slots:{default:!0}});var h=b(_,2);I(h,{onClick:F,get disabled(){return e(n)},children:(t,s)=>{S();var d=$("Cancel");a(t,d)},$$slots:{default:!0}});var q=b(h,2);{var z=t=>{O(t,{get listings(){return e(u)},onClickListing:s=>{r.has(s.id)?r.delete(s.id):r.add(s.id)},get selectedIds(){return r}})},A=t=>{var s=W();a(t,s)};x(q,t=>{e(u).length>0?t(z):t(A,!1)})}a(o,p)},o=>{var l=Y();a(o,l)}),a(i,g)};x(N,i=>{w.user!==null&&i(U)})}a(B,c),H()}export{ft as component};
