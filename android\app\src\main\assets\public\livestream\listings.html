<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		
		<link rel="modulepreload" href="../_app/immutable/entry/start.DPVxqpiO.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/7czifHHs.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/DvS_9Yw_.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/B8_8MPyK.js">
		<link rel="modulepreload" href="../_app/immutable/entry/app.DzEywp-V.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/Dp1pzeXC.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/BGB6NhFz.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/CTtUbKlS.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/DiITAvcd.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/Ckyppc5t.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/CWj6FrbW.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/DrBokXpg.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/CDhH9bvI.js">
		<link rel="modulepreload" href="../_app/immutable/chunks/Ctt2IVZk.js">
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">
			<script>
				{
					__sveltekit_1jcxyfx = {
						base: new URL("..", location).pathname.slice(0, -1)
					};

					const element = document.currentScript.parentElement;

					Promise.all([
						import("../_app/immutable/entry/start.DPVxqpiO.js"),
						import("../_app/immutable/entry/app.DzEywp-V.js")
					]).then(([kit, app]) => {
						kit.start(app, element);
					});
				}
			</script>
		</div>
	</body>
</html>
