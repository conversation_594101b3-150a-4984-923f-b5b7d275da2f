import"./CWj6FrbW.js";import{o as S,a as j}from"./B8_8MPyK.js";import{p,g as i,d as y,e as _,t as T,a as w,b as B,c as h,r as d,s as f,n as V,K as q,f as D}from"./DvS_9Yw_.js";import{f as x,a as b,t as H}from"./Ckyppc5t.js";import{s as k}from"./PoYD5o0_.js";import{b as L}from"./CDhH9bvI.js";import{p as A}from"./Ctt2IVZk.js";import{s as I}from"./BGB6NhFz.js";import{e as F,i as U}from"./hasoi-S8.js";import{R as K}from"./BHOEOwOo.js";import{d as z}from"./CTtUbKlS.js";import{L as R}from"./BCBegGzd.js";import{B as M}from"./DF_v5tP2.js";import{S as W}from"./ESsD0lEJ.js";var G=x("<video></video>",2);function kt(s,t){p(t,!0);const e=A(t,"isBackdrop",3,!1),c=A(t,"hasShadow",3,!1);let r=y(null),m=y(null);S(()=>{i(m)!==null&&_(r,t.call.bindVideoElement(i(m),t.sessionId,"videoTrack")??null,!0)}),j(()=>{var n;(n=i(r))==null||n()});var a=G();let l;L(a,n=>_(m,n),()=>i(m)),T(n=>l=k(a,1,"svelte-1hybe72",null,l,n),[()=>({backdrop:e(),shadow:c()})]),b(s,a),w()}z(["click"]);var J=x("<chat-message><b> </b> </chat-message>",2),O=x("<livestream-chat><chat-history></chat-history> <chat-entry><!> <!></chat-entry></livestream-chat>",2);function Q(s,t){p(t,!0);let e=y(""),c=B([]);const r=v=>{if(v.custom.type!==R.Chat)return;const o=v.custom;c.push(o.data)};S(()=>{t.call.on("custom",r)}),j(()=>{t.call.off("custom",r)});const m=async()=>{await t.call.sendCustomEvent({type:R.Chat,data:{user:{id:t.userId,name:t.userName},text:i(e)}}),_(e,"")};var a=O();k(a,1,"svelte-1n3b2xr");var l=h(a);k(l,1,"svelte-1n3b2xr"),F(l,21,()=>c,U,(v,o)=>{var u=J();k(u,1,"svelte-1n3b2xr");var E=h(u),N=h(E,!0);d(E);var P=f(E);d(u),T(()=>{I(N,i(o).user.name),I(P,` ${i(o).text??""}`)}),b(v,u)}),d(l);var n=f(l,2),g=h(n);K(g,{get initialText(){return i(e)},onInput:v=>{_(e,v,!0)},placeholder:"Write something…"});var C=f(g,2);M(C,{onClick:m,children:(v,o)=>{V();var u=H("Send");b(v,u)},$$slots:{default:!0}}),d(n),d(a),b(s,a),w()}var X=x("<reaction-emoji> </reaction-emoji>",2);function Y(s,t){p(t,!0);let e=y(0),c=y(0),r=y(0);const m=n=>{i(e)===0&&_(e,n,!0),_(c,n-i(e)),_(r,requestAnimationFrame(m),!0)};S(()=>{_(r,requestAnimationFrame(m),!0)}),j(()=>{cancelAnimationFrame(i(r))}),q(()=>{i(c)<3e3||t.onAnimationEnd()});var a=X();k(a,1,"svelte-1o7tmj7");var l=h(a,!0);d(a),T(()=>I(l,t.emoji)),b(s,a),w()}var Z=(s,t)=>t("🤣"),$=(s,t)=>t("🤩"),tt=(s,t)=>t("😍"),et=(s,t)=>t("❤️"),at=x("<livestream-reactions-visualizer></livestream-reactions-visualizer> <livestream-reactions-rack><button>🤣</button> <button>🤩</button> <button>😍</button> <button>❤️</button></livestream-reactions-rack>",3);function rt(s,t){p(t,!0);let e=B(new W);const c=o=>{if(o.custom.type!==R.React)return;const u=o.custom;e.add({reaction:u.data,uuid:crypto.randomUUID()})};S(()=>{t.call.on("custom",c)}),j(()=>{t.call.off("custom",c)});const r=async o=>{await t.call.sendCustomEvent({type:R.React,data:{emoji:o}})};var m=at(),a=D(m);F(a,21,()=>e,o=>o.uuid,(o,u)=>{Y(o,{get emoji(){return i(u).reaction.emoji},onAnimationEnd:()=>e.delete(i(u))})}),d(a);var l=f(a,2);k(l,1,"svelte-wv28rr");var n=h(l);n.__click=[Z,r];var g=f(n,2);g.__click=[$,r];var C=f(g,2);C.__click=[tt,r];var v=f(C,2);v.__click=[et,r],d(l),b(s,m),w()}z(["click"]);var nt=x("<stream-interaction><!> <!></stream-interaction>",2);function xt(s,t){var e=nt(),c=h(e);Q(c,{get userId(){return t.userId},get userName(){return t.userName},get call(){return t.call}});var r=f(c,2);rt(r,{get call(){return t.call}}),d(e),b(s,e)}export{kt as P,xt as S};
