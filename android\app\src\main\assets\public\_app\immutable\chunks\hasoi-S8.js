import{k as ee,h as w,i as ae,g as B,a0 as re,a1 as ne,H as ie,m as F,o as b,q as k,A as H,a2 as fe,F as J,y as K,G as le,a3 as y,a4 as P,W as G,v as U,x as se,w as W,a5 as D,a6 as ue,a7 as te,a8 as ve,a9 as de,aa as M,ab as _e,ac as oe,ad as ce,ae as he,af as Ee,z as pe,ag as Q,ah as Ae}from"./DvS_9Yw_.js";function we(l,e){return e}function Te(l,e,a,u){for(var v=[],_=e.length,t=0;t<_;t++)ue(e[t].e,v,!0);var o=_>0&&v.length===0&&a!==null;if(o){var A=a.parentNode;te(A),A.append(a),u.clear(),m(l,e[0].prev,e[_-1].next)}ve(v,()=>{for(var h=0;h<_;h++){var d=e[h];o||(u.delete(d.k),m(l,d.prev,d.next)),de(d.e,!o)}})}function Ce(l,e,a,u,v,_=null){var t=l,o={flags:e,items:new Map,first:null},A=(e&Q)!==0;if(A){var h=l;t=w?b(ce(h)):h.appendChild(he())}w&&ae();var d=null,C=!1,i=re(()=>{var s=a();return Ee(s)?s:s==null?[]:P(s)});ee(()=>{var s=B(i),r=s.length;if(C&&r===0)return;C=r===0;let x=!1;if(w){var E=ne(t)===ie;E!==(r===0)&&(t=F(),b(t),k(!1),x=!0)}if(w){for(var p=null,T,c=0;c<r;c++){if(H.nodeType===8&&H.data===fe){t=H,x=!0,k(!1);break}var n=s[c],f=u(n,c);T=Z(H,o,p,null,n,f,c,v,e,a),o.items.set(f,T),p=T}r>0&&b(F())}w||xe(s,o,t,v,e,u,a),_!==null&&(r===0?d?J(d):d=K(()=>_(t)):d!==null&&le(d,()=>{d=null})),x&&k(!0),B(i)}),w&&(t=H)}function xe(l,e,a,u,v,_,t){var O,q,V,Y;var o=(v&Ae)!==0,A=(v&(M|D))!==0,h=l.length,d=e.items,C=e.first,i=C,s,r=null,x,E=[],p=[],T,c,n,f;if(o)for(f=0;f<h;f+=1)T=l[f],c=_(T,f),n=d.get(c),n!==void 0&&((O=n.a)==null||O.measure(),(x??(x=new Set)).add(n));for(f=0;f<h;f+=1){if(T=l[f],c=_(T,f),n=d.get(c),n===void 0){var $=i?i.e.nodes_start:a;r=Z($,e,r,r===null?e.first:r.next,T,c,f,u,v,t),d.set(c,r),E=[],p=[],i=r.next;continue}if(A&&Ie(n,T,f,v),(n.e.f&y)!==0&&(J(n.e),o&&((q=n.a)==null||q.unfix(),(x??(x=new Set)).delete(n))),n!==i){if(s!==void 0&&s.has(n)){if(E.length<p.length){var N=p[0],I;r=N.prev;var L=E[0],R=E[E.length-1];for(I=0;I<E.length;I+=1)X(E[I],N,a);for(I=0;I<p.length;I+=1)s.delete(p[I]);m(e,L.prev,R.next),m(e,r,L),m(e,R,N),i=N,r=R,f-=1,E=[],p=[]}else s.delete(n),X(n,i,a),m(e,n.prev,n.next),m(e,n,r===null?e.first:r.next),m(e,r,n),r=n;continue}for(E=[],p=[];i!==null&&i.k!==c;)(i.e.f&y)===0&&(s??(s=new Set)).add(i),p.push(i),i=i.next;if(i===null)continue;n=i}E.push(n),r=n,i=n.next}if(i!==null||s!==void 0){for(var g=s===void 0?[]:P(s);i!==null;)(i.e.f&y)===0&&g.push(i),i=i.next;var S=g.length;if(S>0){var j=(v&Q)!==0&&h===0?a:null;if(o){for(f=0;f<S;f+=1)(V=g[f].a)==null||V.measure();for(f=0;f<S;f+=1)(Y=g[f].a)==null||Y.fix()}Te(e,g,j,d)}}o&&pe(()=>{var z;if(x!==void 0)for(n of x)(z=n.a)==null||z.apply()}),G.first=e.first&&e.first.e,G.last=r&&r.e}function Ie(l,e,a,u){(u&M)!==0&&U(l.v,e),(u&D)!==0?U(l.i,a):l.i=a}function Z(l,e,a,u,v,_,t,o,A,h){var d=(A&M)!==0,C=(A&_e)===0,i=d?C?se(v):W(v):v,s=(A&D)===0?t:W(t),r={i:s,v:i,k:_,a:null,e:null,prev:a,next:u};try{return r.e=K(()=>o(l,i,s,h),w),r.e.prev=a&&a.e,r.e.next=u&&u.e,a===null?e.first=r:(a.next=r,a.e.next=r.e),u!==null&&(u.prev=r,u.e.prev=r.e),r}finally{}}function X(l,e,a){for(var u=l.next?l.next.e.nodes_start:a,v=e?e.e.nodes_start:a,_=l.e.nodes_start;_!==u;){var t=oe(_);v.before(_),_=t}}function m(l,e,a){e===null?l.first=a:(e.next=a,e.e.next=a&&a.e),a!==null&&(a.prev=e,a.e.prev=e&&e.e)}export{Ce as e,we as i};
