import"../chunks/CWj6FrbW.js";import"../chunks/BUgeVMx1.js";import{p as q,f as v,a as z,c as _,n as D,s as F,g as n,a0 as I,r as c}from"../chunks/DvS_9Yw_.js";import{c as u,a as t,f as m,t as N}from"../chunks/Ckyppc5t.js";import{a as S}from"../chunks/DZ8rvC1m.js";import{i as h}from"../chunks/DrBokXpg.js";import{s as U}from"../chunks/PoYD5o0_.js";import{i as j}from"../chunks/BTdX64hs.js";import{B as A}from"../chunks/DF_v5tP2.js";import{g as L}from"../chunks/7czifHHs.js";import{s as $}from"../chunks/CZYSyPBs.js";import{T as E}from"../chunks/BwY1cqmU.js";import{L as G}from"../chunks/Be2B7OK6.js";import{L as H}from"../chunks/DafOIsaB.js";import{g as J}from"../chunks/CQqdknCc.js";var K=m("<div>No listings yet!</div>"),M=m("<div>Failed to load listings</div>"),O=m("<seller-listings><!> <listings-list><!></listings-list></seller-listings>",2);function ms(k,B){q(B,!1),j();var g=u(),b=v(g);{var x=e=>{E(e,{heading:"my listings",hasBackButton:!0,children:(y,Q)=>{var r=O();U(r,1,"svelte-1t1mbz6");var d=_(r);A(d,{onClick:()=>L("/listing?new"),strong:!0,children:(s,a)=>{D();var o=N("Create new");t(s,o)},$$slots:{default:!0}});var f=F(d,2),w=_(f);S(w,()=>J({sellerUserId:$.user.id}),s=>{G(s)},(s,a)=>{var o=u();const p=I(()=>n(a).listings);var C=v(o);{var P=i=>{H(i,{get listings(){return n(p)},onClickListing:l=>L(`/listing?edit&id=${l.id}`)})},T=i=>{var l=K();t(i,l)};h(C,i=>{n(p).length>0?i(P):i(T,!1)})}t(s,o)},s=>{var a=M();t(s,a)}),c(f),c(r),t(y,r)}})};h(b,e=>{$.user&&e(x)})}t(k,g),z()}export{ms as component};
