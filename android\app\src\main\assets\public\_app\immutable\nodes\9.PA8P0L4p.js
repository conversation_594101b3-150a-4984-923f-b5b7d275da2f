import"../chunks/CWj6FrbW.js";import{p as z,d as A,b as F,g as t,u as g,K as G,a as H,e as J,c as l,r as n,f as M,s as u,n as x}from"../chunks/DvS_9Yw_.js";import{f as T,a as m,t as D}from"../chunks/Ckyppc5t.js";import{i as N}from"../chunks/DrBokXpg.js";import{s as d}from"../chunks/PoYD5o0_.js";import{R as b}from"../chunks/BHOEOwOo.js";import{s as L,a as O}from"../chunks/DdbRmKj_.js";import{L as Q}from"../chunks/Be2B7OK6.js";import{B as I}from"../chunks/DF_v5tP2.js";import{c as U,n as V}from"../chunks/CQqdknCc.js";var W=T("<stream-title><!></stream-title> <livestream-description><!></livestream-description> <save-discard-buttons><!> <!></save-discard-buttons>",3),X=T("<stream-details><!></stream-details>",2);function nt($,S){z(S,!0);const f=g(()=>L().id),r=g(()=>L().data);let e=A(F(t(r)));G(()=>{J(e,{...t(r)},!0)});const w=s=>{t(e)!==null&&(t(e).title=s)},k=s=>{t(e)!==null&&(t(e).description=s)},y=async()=>{if(t(e)!==null){if(t(f)!==null)await U({livestreamId:t(f),livestreamTitle:t(e).title,livestreamDescription:t(e).description});else{const s=await V({livestreamTitle:t(e).title,livestreamDescription:t(e).description});O(s.livestreamId)}t(r).title=t(e).title,t(r).description=t(e).description}},B=()=>{t(e).title=t(r).title,t(e).description=t(r).description};var a=X();d(a,1,"svelte-9pirmh");var C=l(a);{var P=s=>{Q(s)},R=s=>{var _=W(),i=M(_);d(i,1,"svelte-9pirmh");var E=l(i);b(E,{label:"stream title",get initialText(){return t(r).title},onInput:w,placeholder:"stream title",classes:"heading heading-1"}),n(i);var o=u(i,2);d(o,1,"svelte-9pirmh");var K=l(o);b(K,{label:"stream description",get initialText(){return t(r).description},onInput:k,placeholder:"stream description"}),n(o);var p=u(o,2);d(p,1,"svelte-9pirmh");var h=l(p);I(h,{onClick:()=>y(),strong:!0,children:(c,q)=>{x();var v=D("Save");m(c,v)},$$slots:{default:!0}});var j=u(h,2);I(j,{onClick:()=>B(),children:(c,q)=>{x();var v=D("Discard");m(c,v)},$$slots:{default:!0}}),n(p),m(s,_)};N(C,s=>{t(r)===null?s(P):s(R,!1)})}n(a),m($,a),H()}export{nt as component};
