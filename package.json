{"name": "slaylive", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "cross-env BUILD_TYPE=node vite dev", "build": "npm run build:static && npm run build:node", "build:static": "cross-env BUILD_TYPE=static vite build", "build:node": "cross-env BUILD_TYPE=node vite build", "build:vercel": "cross-env BUILD_TYPE=vercel vite build", "build:android": "cross-env CAP_HOST=static npm run build:static && npm run android", "dev:android": "cross-env CAP_HOST=server concurrently \"npm run dev\" \"npm run android\"", "preview": "vite preview", "android": "cap run android", "android:open": "cap open android", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test:unit": "vitest", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@capacitor/cli": "^7.3.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/adapter-vercel": "^5.7.2", "@sveltejs/kit": "^2.21.4", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@types/node": "^24.0.0", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "sass": "^1.89.2", "svelte": "^5.33.19", "svelte-check": "^4.2.1", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-node": "^3.2.3", "vitest": "^3.2.3"}, "dependencies": {"@capacitor-community/stripe": "^7.2.0", "@capacitor/android": "^7.4.0", "@capacitor/core": "^7.4.0", "@capacitor/ios": "^7.4.0", "@capgo/capacitor-social-login": "^7.6.8", "@stream-io/node-sdk": "^0.4.24", "@stream-io/video-client": "^1.23.5", "@stripe/stripe-js": "^7.4.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.50.0", "drizzle-orm": "^0.44.2", "postgres": "^3.4.7", "stripe": "^18.2.1"}, "pnpm": {"patchedDependencies": {"native-run": "patches/native-run.patch"}}}