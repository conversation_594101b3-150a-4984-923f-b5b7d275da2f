import{h as l}from"./DvS_9Yw_.js";function h(f){var n,i,r="";if(typeof f=="string"||typeof f=="number")r+=f;else if(typeof f=="object")if(Array.isArray(f)){var t=f.length;for(n=0;n<t;n++)f[n]&&(i=h(f[n]))&&(r&&(r+=" "),r+=i)}else for(i in f)f[i]&&(r&&(r+=" "),r+=i);return r}function A(){for(var f,n,i=0,r="",t=arguments.length;i<t;i++)(f=arguments[i])&&(n=h(f))&&(r&&(r+=" "),r+=n);return r}function j(f){return typeof f=="object"?A(f):f??""}const v=[...` 	
\r\f \v\uFEFF`];function b(f,n,i){var r=f==null?"":""+f;if(n&&(r=r?r+" "+n:n),i){for(var t in i)if(i[t])r=r?r+" "+t:t;else if(r.length)for(var u=t.length,o=0;(o=r.indexOf(t,o))>=0;){var a=o+u;(o===0||v.includes(r[o-1]))&&(a===r.length||v.includes(r[a]))?r=(o===0?"":r.substring(0,o))+r.substring(a+1):o=a}}return r===""?null:r}function p(f,n=!1){var i=n?" !important;":";",r="";for(var t in f){var u=f[t];u!=null&&u!==""&&(r+=" "+t+": "+u+i)}return r}function m(f,n){if(n){var i="",r,t;return Array.isArray(n)?(r=n[0],t=n[1]):r=n,r&&(i+=p(r)),t&&(i+=p(t,!0)),i=i.trim(),i===""?null:i}return String(f)}function L(f,n,i,r,t,u){var o=f.__className;if(l||o!==i||o===void 0){var a=b(i,r,u);(!l||a!==f.getAttribute("class"))&&(a==null?f.removeAttribute("class"):f.className=a),f.__className=i}else if(u&&t!==u)for(var c in u){var g=!!u[c];(t==null||g!==!!t[c])&&f.classList.toggle(c,g)}return u}export{j as c,L as s,m as t};
