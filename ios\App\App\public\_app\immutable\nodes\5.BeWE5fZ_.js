import{a as i,t as o}from"../chunks/disclose-version.DD3_NYGK.js";import"../chunks/legacy.BXZnuAlm.js";import{p,t as n,a as _}from"../chunks/runtime.BoVB3PJd.js";import{s as l}from"../chunks/attributes.ctXOk-4d.js";import{i as m}from"../chunks/lifecycle.B3mXKHkP.js";import{g as f}from"../chunks/index.BmCJLe3p.js";var u=o("<a>paraglide</a>");function F(a,e){p(e,!1);const r=f(),[s,d]=r;m();var t=u();n(()=>l(t,"href",s("/demo/paraglide",void 0))),i(a,t),_()}export{F as component};
