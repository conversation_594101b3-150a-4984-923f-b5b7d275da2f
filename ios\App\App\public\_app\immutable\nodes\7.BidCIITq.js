import{a as P,t as T,c as y}from"../chunks/disclose-version.DD3_NYGK.js";import{p as k,ak as l,K as o,i as t,c as p,r as h,s as g,t as R,a as U,f as A}from"../chunks/runtime.BoVB3PJd.js";import{i as b}from"../chunks/if.DZd1GKLv.js";import{p as s}from"../chunks/proxy.Dxp3JHm7.js";import{o as K}from"../chunks/index-client.CMvqoH0Y.js";import{s as w}from"../chunks/render.Dmit0_6o.js";import{a as j}from"../chunks/attributes.ctXOk-4d.js";import{S as B,P as E}from"../chunks/ParticipantVideo.fvosWrRQ.js";import{a as M,P as V}from"../chunks/public.DrZ1jm20.js";import{s as i}from"../chunks/store.svelte.BneWDtP5.js";var O=T("<watch-container><div> </div> <div> </div> <!></watch-container>",2);function W(f,e){k(e,!0);const c={id:e.userId,name:e.userName,image:`https://getstream.io/random_svg/?id=${e.userId}&name=${e.userName}`};let m=l(0),a=l(null),v=l(null),r=l(null),C=l(s([]));(async()=>{const u=B.getOrCreateInstance({apiKey:M,token:e.userToken,user:c});o(a,s(u.call("livestream",e.callId))),await t(a).join(),await(async n=>(o(v,s(n.hostUserId)),o(r,s(n.hostSessionId))))(await(await fetch(new URL(`/api/livestream/get-host?call_id=${e.callId}`,V).href)).json()),t(a).state.participantCount$.subscribe(n=>{o(m,s(n||0))}),t(a).state.participants$.subscribe(n=>{o(C,s(n))})})();var d=O();j(d,"class","svelte-auv5vf");var I=p(d),L=p(I);h(I);var _=g(I,2),S=p(_);h(_);var x=g(_,2);{var N=u=>{E(u,{get call(){return t(a)},get sessionId(){return t(r)}})};b(x,u=>{t(a)!==null&&t(r)!==null&&u(N)})}h(d),R(()=>{w(L,`Call id: ${e.callId??""}`),w(S,`Live: ${t(m)??""}`)}),P(f,d),U()}function Z(f,e){k(e,!0);let c=l(null);K(()=>{o(c,s(new URLSearchParams(location.search).get("call_id")??null))});var m=y(),a=A(m);{var v=r=>{W(r,{get callId(){return t(c)},get userToken(){return i.userToken},get userId(){return i.userId},get userName(){return i.userName}})};b(a,r=>{t(c)!==null&&i.userId!==null&&i.userToken!==null&&i.userName!==null&&r(v)})}P(f,m),U()}export{Z as component};
