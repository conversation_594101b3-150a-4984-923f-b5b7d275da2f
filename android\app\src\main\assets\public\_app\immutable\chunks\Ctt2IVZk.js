import{aq as y,M as L,ar as B,as as M,P as x,a0 as Y,g as _,at as N,au as U,b as w,e as z,x as C,av as G,T as $,aw as V,ax as Z,ay as j,az as F,aA as H}from"./DvS_9Yw_.js";let o=!1;function J(a){var r=o;try{return o=!1,[a(),o]}finally{o=r}}function T(a){var r;return((r=a.ctx)==null?void 0:r.d)??!1}function Q(a,r,f,d){var O;var m=(f&H)!==0,l=!j||(f&F)!==0,c=(f&G)!==0,h=(f&Z)!==0,A=!1,t;c?[t,A]=J(()=>a[r]):t=a[r];var q=$ in a||V in a,v=c&&(((O=y(a,r))==null?void 0:O.set)??(q&&r in a&&(e=>a[r]=e)))||void 0,i=d,P=!0,S=!1,E=()=>(S=!0,P&&(P=!1,h?i=L(d):i=d),i);t===void 0&&d!==void 0&&(v&&l&&B(),t=E(),v&&v(t));var u;if(l)u=()=>{var e=a[r];return e===void 0?E():(P=!0,S=!1,e)};else{var R=(m?x:Y)(()=>a[r]);R.f|=M,u=()=>{var e=_(R);return e!==void 0&&(i=void 0),e===void 0?i:e}}if((f&N)===0&&l)return u;if(v){var D=a.$$legacy;return function(e,s){return arguments.length>0?((!l||!s||D||A)&&v(s?u():e),e):u()}}var I=!1,b=C(t),n=x(()=>{var e=u(),s=_(b);return I?(I=!1,s):b.v=e});return c&&_(n),m||(n.equals=U),function(e,s){if(arguments.length>0){const g=s?_(n):l&&c?w(e):e;if(!n.equals(g)){if(I=!0,z(b,g),S&&i!==void 0&&(i=g),T(n))return e;L(()=>_(n))}return e}return T(n)?n.v:_(n)}}export{Q as p};
