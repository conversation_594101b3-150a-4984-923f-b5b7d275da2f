import{k as h,h as u,i as p,ai as g,a1 as S,aj as k,H as D,m as F,o as H,q as I,F as A,y as _,G as b,U as q,A as L}from"./DvS_9Yw_.js";function U(m,v,[t,s]=[0,0]){u&&t===0&&p();var a=m,f=null,e=null,i=q,E=t>0?g:0,n=!1;const N=(c,l=!0)=>{n=!0,o(l,c)},o=(c,l)=>{if(i===(i=c))return;let T=!1;if(u&&s!==-1){if(t===0){const r=S(a);r===k?s=0:r===D?s=1/0:(s=parseInt(r.substring(1)),s!==s&&(s=i?1/0:-1))}const R=s>t;!!i===R&&(a=F(),H(a),I(!1),T=!0,s=-1)}i?(f?A(f):l&&(f=_(()=>l(a))),e&&b(e,()=>{e=null})):(e?A(e):l&&(e=_(()=>l(a,[t+1,s]))),f&&b(f,()=>{f=null})),T&&I(!0)};h(()=>{n=!1,v(N),n||o(null,null)},E),u&&(a=L)}export{U as i};
