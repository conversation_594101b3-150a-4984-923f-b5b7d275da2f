import{a as P,t as U,c as O}from"../chunks/disclose-version.DD3_NYGK.js";import"../chunks/legacy.BXZnuAlm.js";import{p as j,K as d,i as e,c as k,s as m,r as y,t as T,a as R,ak as u,f as A}from"../chunks/runtime.BoVB3PJd.js";import{i as w}from"../chunks/if.DZd1GKLv.js";import{i as z}from"../chunks/lifecycle.B3mXKHkP.js";import{d as G,s as x}from"../chunks/render.Dmit0_6o.js";import{a as M}from"../chunks/attributes.ctXOk-4d.js";import{p as _}from"../chunks/proxy.Dxp3JHm7.js";import{S as Y,P as q}from"../chunks/ParticipantVideo.fvosWrRQ.js";import{P as S,a as D}from"../chunks/public.DrZ1jm20.js";import{s as i}from"../chunks/store.svelte.BneWDtP5.js";var F=(n,t)=>{var r;return(r=e(t))==null?void 0:r.goLive()},H=(n,t)=>{var r;return(r=e(t))==null?void 0:r.stopLive()},Q=U("<!> <button>Go live</button> <button>Stop live</button>",1),W=U("<backstage-container> <div> </div> <div> </div> <!></backstage-container>",2);function X(n,t){j(t,!0);const r={id:t.userId,name:t.userName,image:`https://getstream.io/random_svg/?id=${t.userId}&name=${t.userName}`};let v=u(0),o=u(!1),a=u(null),l=u(null),p=u(null);(async()=>{if(await(async s=>d(l,_(s.callId)))(await(await fetch(new URL("/api/livestream/start",S).href,{method:"post",body:JSON.stringify({userId:t.userId}),headers:{"Content-Type":"application/json"}})).json()),!e(l))return;const c=new Y({apiKey:D,token:t.userToken,user:r});d(a,_(c.call("livestream",e(l)))),await e(a).join();try{await Promise.all([e(a).camera.enable(),e(a).microphone.enable()])}catch(s){alert(`Camera is inaccessible or permission was denied: ${s}`);return}e(a).state.localParticipant$.subscribe(s=>{s&&(d(p,_(s)),fetch(new URL("/api/livestream/set-host-session",S).href,{method:"put",body:JSON.stringify({callId:e(l),hostId:t.userId,sessionId:s.sessionId}),headers:{"Content-Type":"application/json"}}))}),e(a).state.participantCount$.subscribe(s=>{d(v,_(s||0))}),e(a).state.backstage$.subscribe(s=>{d(o,!s)})})();var f=W();M(f,"class","svelte-9am1dz");var C=k(f);C.nodeValue=`${location.href??""} `;var b=m(C),B=k(b);y(b);var g=m(b,2),K=k(g);y(g);var V=m(g,2);{var E=c=>{var s=Q(),L=A(s);{var J=h=>{q(h,{get call(){return e(a)},get sessionId(){return e(p).sessionId}})};w(L,h=>{e(p)!==null&&h(J)})}var I=m(L,2);I.__click=[F,a];var N=m(I,2);N.__click=[H,a],T(()=>{I.disabled=e(o),N.disabled=!e(o)}),P(c,s)};w(V,c=>{e(a)!==null&&c(E)})}y(f),T(()=>{x(B,`Call id: ${e(l)??""}`),x(K,`Live: ${e(v)??""}`)}),P(n,f),R()}G(["click"]);function ce(n,t){j(t,!1),z();var r=O(),v=A(r);{var o=a=>{X(a,{get userToken(){return i.userToken},get userId(){return i.userId},get userName(){return i.userName}})};w(v,a=>{i.userId!==null&&i.userToken!==null&&i.userName!==null&&a(o)})}P(n,r),R()}export{ce as component};
