import"../chunks/CWj6FrbW.js";import{p as Y,f as y,a as Z,g as t,u as l,c as _,n as g,s as c,r as b,e as $,d as tt,t as at}from"../chunks/DvS_9Yw_.js";import{s as et}from"../chunks/BGB6NhFz.js";import{c as rt,a as o,f as B,t as d}from"../chunks/Ckyppc5t.js";import{i as x}from"../chunks/DrBokXpg.js";import{s as h}from"../chunks/PoYD5o0_.js";import{B as w}from"../chunks/DF_v5tP2.js";import{s as k}from"../chunks/DdbRmKj_.js";import{s as I}from"../chunks/CZYSyPBs.js";import{S as st,P as ot}from"../chunks/Ddyo76Dh.js";import{s as nt,d as it}from"../chunks/CQqdknCc.js";var lt=B("<backstage-container><div><!></div> <!> <!></backstage-container>",2),ct=B("<button-row><!> <!></button-row> <button-row><!> <!></button-row> <!>",3);function wt(V,A){Y(A,!0);let n=tt(!1);const u=l(()=>k().data),v=l(()=>k().id),a=l(()=>k().callData),N=async()=>{t(v)!==null&&($(n,!0),await nt({livestreamId:t(v)}),$(n,!1),t(u).active=!0)},O=async()=>{t(v)!==null&&($(n,!0),await it({livestreamId:t(v)}),$(n,!1),t(u).active=!1)};var S=rt(),j=y(S);{var q=P=>{var C=ct(),m=y(C);h(m,1,"svelte-zd93sd");var L=_(m);const E=l(()=>t(u).active||t(n));w(L,{onClick:()=>N(),get disabled(){return t(E)},strong:!0,children:(e,i)=>{g();var r=d("Open room");o(e,r)},$$slots:{default:!0}});var F=c(L,2);const G=l(()=>!t(u).active||t(n));w(F,{onClick:()=>O(),get disabled(){return t(G)},children:(e,i)=>{g();var r=d("Close room");o(e,r)},$$slots:{default:!0}}),b(m);var p=c(m,2);h(p,1,"svelte-zd93sd");var z=_(p);const H=l(()=>t(n)||t(a).started);w(z,{onClick:()=>t(a).call.goLive(),get disabled(){return t(H)},strong:!0,children:(e,i)=>{g();var r=d("Start broadcast");o(e,r)},$$slots:{default:!0}});var J=c(z,2);const K=l(()=>t(n)||!t(a).started);w(J,{onClick:()=>t(a).call.stopLive(),get disabled(){return t(K)},children:(e,i)=>{g();var r=d("Stop broadcast");o(e,r)},$$slots:{default:!0}}),b(p);var M=c(p,2);{var Q=e=>{var i=lt();h(i,1,"svelte-zd93sd");var r=_(i),R=_(r);{var T=s=>{var f=d("Loading call");o(s,f)},U=s=>{var f=d();at(()=>et(f,`${t(a).nParticipants-1} viewers`)),o(s,f)};x(R,s=>{t(a).nParticipants===0?s(T):s(U,!1)})}b(r);var D=c(r,2);{var W=s=>{ot(s,{get call(){return t(a).call},get sessionId(){return t(a).localParticipant.sessionId}})};x(D,s=>{t(a).localParticipant!==null&&s(W)})}var X=c(D,2);st(X,{get userId(){return I.user.streamioAuth.id},get userName(){return I.user.name},get call(){return t(a).call}}),b(i),o(e,i)};x(M,e=>{t(v)!==null&&e(Q)})}o(P,C)};x(j,P=>{t(u)!==null&&t(a)!==null&&I.user!==null&&P(q)})}o(V,S),Z()}export{wt as component};
