const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["../chunks/browser.BaUz-1h7.js","../chunks/_commonjsHelpers.BosuxZz1.js"])))=>i.map(i=>d[i]);
import{a as Pn,c as C}from"../chunks/_commonjsHelpers.BosuxZz1.js";import{s as Nt,p as pe,a as Bt,_ as le}from"../chunks/preload-helper.Kar63Wts.js";import{c as ut,d as ht}from"../chunks/public.DrZ1jm20.js";import{c as ne,a as V,t as $n}from"../chunks/disclose-version.DD3_NYGK.js";import{al as Cn,az as jn,aA as Rn,o as tt,v as L,w as nt,C as Ft,B as je,U as xn,D as H,P as In,at as Mt,H as Ln,x as dt,y as ft,z as Be,ap as Un,aB as Oe,A as qt,M as gt,aC as Dn,aD as Nn,as as Bn,aE as Fn,aF as zt,a3 as Mn,a6 as qn,I as _t,aG as zn,aH as Hn,aI as Jn,V as Kn,E as Gn,a7 as Vn,aJ as Wn,p as rt,aK as j,aL as Ht,f as re,a as st,K as se,i as k,j as K,t as Qn,aM as ie}from"../chunks/runtime.BoVB3PJd.js";import{n as Jt,p as Je,a as Ke,s as Ge,i as Xn}from"../chunks/i18n.BJ2ZmtzR.js";import"../chunks/legacy.BXZnuAlm.js";import{h as Yn}from"../chunks/svelte-head.D6YfliFN.js";import{i as Kt}from"../chunks/if.DZd1GKLv.js";import{i as Gt}from"../chunks/lifecycle.B3mXKHkP.js";import{L as Zn,s as er,a as pt}from"../chunks/index.BmCJLe3p.js";import{b as Vt,c as tr,i as nr}from"../chunks/entry.DaoNXzB7.js";import{s as mt}from"../chunks/attributes.ctXOk-4d.js";function rr(r,e,t){L&&nt();var n=r,s=xn,i,a=Cn()?jn:Rn;tt(()=>{a(s,s=e())&&(i&&Ft(i),i=je(()=>t(n)))}),L&&(n=H)}function sr(r,e){return e}function ir(r,e,t,n){for(var s=[],i=e.length,a=0;a<i;a++)Nn(e[a].e,s,!0);var o=i>0&&s.length===0&&t!==null;if(o){var l=t.parentNode;Bn(l),l.append(t),n.clear(),B(r,e[0].prev,e[i-1].next)}Fn(s,()=>{for(var u=0;u<i;u++){var c=e[u];o||(n.delete(c.k),B(r,c.prev,c.next)),zt(c.e,!o)}})}function ar(r,e,t,n,s,i=null){var a=r,o={flags:e,items:new Map,first:null};L&&nt();var l=null,u=!1;tt(()=>{var c=t(),h=In(c)?c:c==null?[]:Mt(c),d=h.length;if(u&&d===0)return;u=d===0;let f=!1;if(L){var g=a.data===Ln;g!==(d===0)&&(a=dt(),ft(a),Be(!1),f=!0)}if(L){for(var _=null,p,w=0;w<d;w++){if(H.nodeType===8&&H.data===Un){a=H,f=!0,Be(!1);break}var m=h[w],b=n(m,w);p=Wt(H,o,_,null,m,b,w,s,e),o.items.set(b,p),_=p}d>0&&ft(dt())}if(!L){var S=Mn;or(h,o,a,s,e,(S.f&Oe)!==0,n)}i!==null&&(d===0?l?qt(l):l=je(()=>i(a)):l!==null&&Ft(l,()=>{l=null})),f&&Be(!0),t()}),L&&(a=H)}function or(r,e,t,n,s,i,a,o){var l=r.length,u=e.items,c=e.first,h=c,d,f=null,g=[],_=[],p,w,m,b;for(b=0;b<l;b+=1){if(p=r[b],w=a(p,b),m=u.get(w),m===void 0){var S=h?h.e.nodes_start:t;f=Wt(S,e,f,f===null?e.first:f.next,p,w,b,n,s),u.set(w,f),g=[],_=[],h=f.next;continue}if(lr(m,p,b),m.e.f&Oe&&qt(m.e),m!==h){if(d!==void 0&&d.has(m)){if(g.length<_.length){var T=_[0],x;f=T.prev;var ke=g[0],de=g[g.length-1];for(x=0;x<g.length;x+=1)wt(g[x],T,t);for(x=0;x<_.length;x+=1)d.delete(_[x]);B(e,ke.prev,de.next),B(e,f,ke),B(e,de,T),h=T,f=de,b-=1,g=[],_=[]}else d.delete(m),wt(m,h,t),B(e,m.prev,m.next),B(e,m,f===null?e.first:f.next),B(e,f,m),f=m;continue}for(g=[],_=[];h!==null&&h.k!==w;)(i||!(h.e.f&Oe))&&(d??(d=new Set)).add(h),_.push(h),h=h.next;if(h===null)continue;m=h}g.push(m),f=m,h=m.next}if(h!==null||d!==void 0){for(var X=d===void 0?[]:Mt(d);h!==null;)(i||!(h.e.f&Oe))&&X.push(h),h=h.next;var An=X.length;if(An>0){var On=null;ir(e,X,On,u)}}gt.first=e.first&&e.first.e,gt.last=f&&f.e}function lr(r,e,t,n){Dn(r.v,e),r.i=t}function Wt(r,e,t,n,s,i,a,o,l,u){var c=(l&Hn)!==0,h=(l&Jn)===0,d=c?h?qn(s):_t(s):s,f=l&zn?_t(a):a,g={i:f,v:d,k:i,a:null,e:null,prev:t,next:n};try{return g.e=je(()=>o(r,d,f),L),g.e.prev=t&&t.e,g.e.next=n&&n.e,t===null?e.first=g:(t.next=g,t.e.next=g.e),n!==null&&(n.prev=g,n.e.prev=g.e),g}finally{}}function wt(r,e,t){for(var n=r.next?r.next.e.nodes_start:t,s=e?e.e.nodes_start:t,i=r.e.nodes_start;i!==n;){var a=Kn(i);s.before(i),i=a}}function B(r,e,t){e===null?r.first=t:(e.next=t,e.e.next=t&&t.e),t!==null&&(t.prev=e,t.e.prev=e&&e.e)}function cr(r,e,t,n,s){var o;L&&nt();var i=(o=e.$$slots)==null?void 0:o[t],a=!1;i===!0&&(i=e.children,a=!0),i===void 0||i(r,a?()=>n:n)}function ur(r,e,...t){var n=r,s=Vn,i;tt(()=>{s!==(s=e())&&(i&&(zt(i),i=null),i=je(()=>s(n,...t)))},Gn),L&&(n=H)}const hr=Wn;function dr(r,e){return r.protocol!==e.protocol?e.href:e.password||e.username?"//"+[e.username,e.password].filter(Boolean).join(":")+"@"+e.host+e.pathname+e.search+e.hash:r.host!==e.host?"//"+e.host+e.pathname+e.search+e.hash:e.pathname+e.search+e.hash}function fr(r,e,t){const n=new URL(t,e).pathname;return r.origin!==e.origin||!r.pathname.startsWith(n)}var gr=$n('<link rel="alternate">');function _r(r,e){rt(e,!1);const t=Nt(),n=()=>Bt(Je,"$page",t),s=ie(),i=ie(),a=ie(),o=Jt(Vt,new URL(n().url))||"/";let l=pe(e,"availableLanguageTags",8),u=pe(e,"strategy",8),c=pe(e,"currentLang",8);const h=(_,p)=>{const w=[];for(const m of l()){const b=p.getLocalisedPath(_,m),S=Ge(b,o,void 0),T=new URL(S,new URL(n().url)).href;w.push(T)}return w};j(()=>n(),()=>{se(s,Ke(n().url.pathname,o)[0])}),j(()=>(K(u()),k(s),K(c())),()=>{se(i,u().getCanonicalPath(k(s),c()))}),j(()=>(k(i),K(u())),()=>{se(a,h(k(i),u()))}),Ht(),Gt();var d=ne(),f=re(d);{var g=_=>{var p=ne(),w=re(p);ar(w,1,()=>k(a),sr,(m,b,S)=>{var T=gr();Qn(()=>{mt(T,"hreflang",l()[S]),mt(T,"href",k(b))}),V(m,T)}),V(_,p)};Kt(f,_=>{l().length>=1&&_(g)})}V(r,d),st()}const pr=(r,e)=>`${Zn}=${r};Path=${e};SameSite=lax;Max-Age=31557600`;function mr(r,e){rt(e,!1);const t=Nt(),n=()=>Bt(Je,"$page",t),s=ie(),i=ie(),a=Jt(Vt,new URL(n().url))||"/";let o=pe(e,"languageTag",24,()=>{}),l=pe(e,"i18n",8),u=ie(0);function c(f,g){try{const _=new URL(tr(Je).url),[p,w]=Ke(_.pathname,a),m=l().strategy.getCanonicalPath(p,k(s)),b=new URL(_);b.pathname=Ge(m,a,w);const S=new URL(f,new URL(b));if(fr(S,_,a)||l().config.exclude(S.pathname))return f;const T=g??k(s),[x,ke]=Ke(S.pathname,a),de=l().strategy.getLocalisedPath(x,T),X=new URL(S);return X.pathname=Ge(de,a,ke),dr(_,X)}catch{return f}}er({translateHref:c}),j(()=>(K(o()),K(l()),n()),()=>{se(s,o()??l().getLanguageFromUrl(n().url))}),j(()=>(K(l()),k(s)),()=>{l().config.runtime.setLanguageTag(k(s))}),j(()=>k(s),()=>{document.documentElement.lang=k(s)}),j(()=>(K(l()),k(s)),()=>{document.documentElement.dir=l().config.textDirection[k(s)]??"ltr"}),j(()=>(k(s),k(u)),()=>{k(s)&&se(u,k(u)+1)}),j(()=>(k(s),k(u),pt),()=>{k(s)&&(k(u)>1||hr)&&nr(pt)}),j(()=>k(s),()=>{se(i,k(s))}),j(()=>k(s),()=>{document.cookie=pr(k(s),a)}),Ht(),Gt();var h=ne();Yn(f=>{var g=ne(),_=re(g);{var p=w=>{_r(w,{get availableLanguageTags(){return l().config.runtime.availableLanguageTags},get strategy(){return l().strategy},get currentLang(){return k(s)}})};Kt(_,w=>{l().config.seo.noAlternateLinks!==!0&&!l().config.exclude(n().url.pathname)&&w(p)})}V(f,g)});var d=re(h);rr(d,()=>k(i),f=>{var g=ne(),_=re(g);cr(_,e,"default",{}),V(f,g)}),V(r,h),st()}const wr=r=>{let e;return r?e=r:typeof fetch>"u"?e=(...t)=>le(async()=>{const{default:n}=await Promise.resolve().then(()=>ue);return{default:n}},void 0,import.meta.url).then(({default:n})=>n(...t)):e=fetch,(...t)=>e(...t)};class it extends Error{constructor(e,t="FunctionsError",n){super(e),this.name=t,this.context=n}}class yr extends it{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class vr extends it{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class br extends it{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var Ve;(function(r){r.Any="any",r.ApNortheast1="ap-northeast-1",r.ApNortheast2="ap-northeast-2",r.ApSouth1="ap-south-1",r.ApSoutheast1="ap-southeast-1",r.ApSoutheast2="ap-southeast-2",r.CaCentral1="ca-central-1",r.EuCentral1="eu-central-1",r.EuWest1="eu-west-1",r.EuWest2="eu-west-2",r.EuWest3="eu-west-3",r.SaEast1="sa-east-1",r.UsEast1="us-east-1",r.UsWest1="us-west-1",r.UsWest2="us-west-2"})(Ve||(Ve={}));var kr=function(r,e,t,n){function s(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(c){try{u(n.next(c))}catch(h){a(h)}}function l(c){try{u(n.throw(c))}catch(h){a(h)}}function u(c){c.done?i(c.value):s(c.value).then(o,l)}u((n=n.apply(r,e||[])).next())})};class Er{constructor(e,{headers:t={},customFetch:n,region:s=Ve.Any}={}){this.url=e,this.headers=t,this.region=s,this.fetch=wr(n)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var n;return kr(this,void 0,void 0,function*(){try{const{headers:s,method:i,body:a}=t;let o={},{region:l}=t;l||(l=this.region),l&&l!=="any"&&(o["x-region"]=l);let u;a&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&(typeof Blob<"u"&&a instanceof Blob||a instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",u=a):typeof a=="string"?(o["Content-Type"]="text/plain",u=a):typeof FormData<"u"&&a instanceof FormData?u=a:(o["Content-Type"]="application/json",u=JSON.stringify(a)));const c=yield this.fetch(`${this.url}/${e}`,{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),s),body:u}).catch(g=>{throw new yr(g)}),h=c.headers.get("x-relay-error");if(h&&h==="true")throw new vr(c);if(!c.ok)throw new br(c);let d=((n=c.headers.get("Content-Type"))!==null&&n!==void 0?n:"text/plain").split(";")[0].trim(),f;return d==="application/json"?f=yield c.json():d==="application/octet-stream"?f=yield c.blob():d==="text/event-stream"?f=c:d==="multipart/form-data"?f=yield c.formData():f=yield c.text(),{data:f,error:null}}catch(s){return{data:null,error:s}}})}}var P={},at={},Re={},ve={},xe={},Ie={},Sr=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},ce=Sr();const Tr=ce.fetch,Qt=ce.fetch.bind(ce),Xt=ce.Headers,Ar=ce.Request,Or=ce.Response,ue=Object.freeze(Object.defineProperty({__proto__:null,Headers:Xt,Request:Ar,Response:Or,default:Qt,fetch:Tr},Symbol.toStringTag,{value:"Module"})),Pr=Pn(ue);var Le={};Object.defineProperty(Le,"__esModule",{value:!0});let $r=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};Le.default=$r;var Yt=C&&C.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ie,"__esModule",{value:!0});const Cr=Yt(Pr),jr=Yt(Le);let Rr=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:typeof fetch>"u"?this.fetch=Cr.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const n=this.fetch;let s=n(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async i=>{var a,o,l;let u=null,c=null,h=null,d=i.status,f=i.statusText;if(i.ok){if(this.method!=="HEAD"){const w=await i.text();w===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?c=w:c=JSON.parse(w))}const _=(a=this.headers.Prefer)===null||a===void 0?void 0:a.match(/count=(exact|planned|estimated)/),p=(o=i.headers.get("content-range"))===null||o===void 0?void 0:o.split("/");_&&p&&p.length>1&&(h=parseInt(p[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(c)&&(c.length>1?(u={code:"PGRST116",details:`Results contain ${c.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},c=null,h=null,d=406,f="Not Acceptable"):c.length===1?c=c[0]:c=null)}else{const _=await i.text();try{u=JSON.parse(_),Array.isArray(u)&&i.status===404&&(c=[],u=null,d=200,f="OK")}catch{i.status===404&&_===""?(d=204,f="No Content"):u={message:_}}if(u&&this.isMaybeSingle&&(!((l=u==null?void 0:u.details)===null||l===void 0)&&l.includes("0 rows"))&&(u=null,d=200,f="OK"),u&&this.shouldThrowOnError)throw new jr.default(u)}return{error:u,data:c,count:h,status:d,statusText:f}});return this.shouldThrowOnError||(s=s.catch(i=>{var a,o,l;return{error:{message:`${(a=i==null?void 0:i.name)!==null&&a!==void 0?a:"FetchError"}: ${i==null?void 0:i.message}`,details:`${(o=i==null?void 0:i.stack)!==null&&o!==void 0?o:""}`,hint:"",code:`${(l=i==null?void 0:i.code)!==null&&l!==void 0?l:""}`},data:null,count:null,status:0,statusText:""}})),s.then(e,t)}};Ie.default=Rr;var xr=C&&C.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(xe,"__esModule",{value:!0});const Ir=xr(Ie);let Lr=class extends Ir.default{select(e){let t=!1;const n=(e??"*").split("").map(s=>/\s/.test(s)&&!t?"":(s==='"'&&(t=!t),s)).join("");return this.url.searchParams.set("select",n),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:n,foreignTable:s,referencedTable:i=s}={}){const a=i?`${i}.order`:"order",o=this.url.searchParams.get(a);return this.url.searchParams.set(a,`${o?`${o},`:""}${e}.${t?"asc":"desc"}${n===void 0?"":n?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:n=t}={}){const s=typeof n>"u"?"limit":`${n}.limit`;return this.url.searchParams.set(s,`${e}`),this}range(e,t,{foreignTable:n,referencedTable:s=n}={}){const i=typeof s>"u"?"offset":`${s}.offset`,a=typeof s>"u"?"limit":`${s}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(a,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:n=!1,buffers:s=!1,wal:i=!1,format:a="text"}={}){var o;const l=[e?"analyze":null,t?"verbose":null,n?"settings":null,s?"buffers":null,i?"wal":null].filter(Boolean).join("|"),u=(o=this.headers.Accept)!==null&&o!==void 0?o:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${a}; for="${u}"; options=${l};`,a==="json"?this:this}rollback(){var e;return((e=this.headers.Prefer)!==null&&e!==void 0?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};xe.default=Lr;var Ur=C&&C.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ve,"__esModule",{value:!0});const Dr=Ur(xe);let Nr=class extends Dr.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const n=Array.from(new Set(t)).map(s=>typeof s=="string"&&new RegExp("[,()]").test(s)?`"${s}"`:`${s}`).join(",");return this.url.searchParams.append(e,`in.(${n})`),this}contains(e,t){return typeof t=="string"?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return typeof t=="string"?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return typeof t=="string"?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:n,type:s}={}){let i="";s==="plain"?i="pl":s==="phrase"?i="ph":s==="websearch"&&(i="w");const a=n===void 0?"":`(${n})`;return this.url.searchParams.append(e,`${i}fts${a}.${t}`),this}match(e){return Object.entries(e).forEach(([t,n])=>{this.url.searchParams.append(t,`eq.${n}`)}),this}not(e,t,n){return this.url.searchParams.append(e,`not.${t}.${n}`),this}or(e,{foreignTable:t,referencedTable:n=t}={}){const s=n?`${n}.or`:"or";return this.url.searchParams.append(s,`(${e})`),this}filter(e,t,n){return this.url.searchParams.append(e,`${t}.${n}`),this}};ve.default=Nr;var Br=C&&C.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Re,"__esModule",{value:!0});const fe=Br(ve);let Fr=class{constructor(e,{headers:t={},schema:n,fetch:s}){this.url=e,this.headers=t,this.schema=n,this.fetch=s}select(e,{head:t=!1,count:n}={}){const s=t?"HEAD":"GET";let i=!1;const a=(e??"*").split("").map(o=>/\s/.test(o)&&!i?"":(o==='"'&&(i=!i),o)).join("");return this.url.searchParams.set("select",a),n&&(this.headers.Prefer=`count=${n}`),new fe.default({method:s,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:n=!0}={}){const s="POST",i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),t&&i.push(`count=${t}`),n||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){const a=e.reduce((o,l)=>o.concat(Object.keys(l)),[]);if(a.length>0){const o=[...new Set(a)].map(l=>`"${l}"`);this.url.searchParams.set("columns",o.join(","))}}return new fe.default({method:s,url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:n=!1,count:s,defaultToNull:i=!0}={}){const a="POST",o=[`resolution=${n?"ignore":"merge"}-duplicates`];if(t!==void 0&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&o.push(this.headers.Prefer),s&&o.push(`count=${s}`),i||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(e)){const l=e.reduce((u,c)=>u.concat(Object.keys(c)),[]);if(l.length>0){const u=[...new Set(l)].map(c=>`"${c}"`);this.url.searchParams.set("columns",u.join(","))}}return new fe.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const n="PATCH",s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),this.headers.Prefer=s.join(","),new fe.default({method:n,url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t="DELETE",n=[];return e&&n.push(`count=${e}`),this.headers.Prefer&&n.unshift(this.headers.Prefer),this.headers.Prefer=n.join(","),new fe.default({method:t,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};Re.default=Fr;var Ue={},De={};Object.defineProperty(De,"__esModule",{value:!0});De.version=void 0;De.version="0.0.0-automated";Object.defineProperty(Ue,"__esModule",{value:!0});Ue.DEFAULT_HEADERS=void 0;const Mr=De;Ue.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${Mr.version}`};var Zt=C&&C.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(at,"__esModule",{value:!0});const qr=Zt(Re),zr=Zt(ve),Hr=Ue;let Jr=class en{constructor(e,{headers:t={},schema:n,fetch:s}={}){this.url=e,this.headers=Object.assign(Object.assign({},Hr.DEFAULT_HEADERS),t),this.schemaName=n,this.fetch=s}from(e){const t=new URL(`${this.url}/${e}`);return new qr.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new en(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:n=!1,get:s=!1,count:i}={}){let a;const o=new URL(`${this.url}/rpc/${e}`);let l;n||s?(a=n?"HEAD":"GET",Object.entries(t).filter(([c,h])=>h!==void 0).map(([c,h])=>[c,Array.isArray(h)?`{${h.join(",")}}`:`${h}`]).forEach(([c,h])=>{o.searchParams.append(c,h)})):(a="POST",l=t);const u=Object.assign({},this.headers);return i&&(u.Prefer=`count=${i}`),new zr.default({method:a,url:o,headers:u,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}};at.default=Jr;var he=C&&C.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(P,"__esModule",{value:!0});P.PostgrestError=P.PostgrestBuilder=P.PostgrestTransformBuilder=P.PostgrestFilterBuilder=P.PostgrestQueryBuilder=P.PostgrestClient=void 0;const tn=he(at);P.PostgrestClient=tn.default;const nn=he(Re);P.PostgrestQueryBuilder=nn.default;const rn=he(ve);P.PostgrestFilterBuilder=rn.default;const sn=he(xe);P.PostgrestTransformBuilder=sn.default;const an=he(Ie);P.PostgrestBuilder=an.default;const on=he(Le);P.PostgrestError=on.default;var Kr=P.default={PostgrestClient:tn.default,PostgrestQueryBuilder:nn.default,PostgrestFilterBuilder:rn.default,PostgrestTransformBuilder:sn.default,PostgrestBuilder:an.default,PostgrestError:on.default};const{PostgrestClient:Gr,PostgrestQueryBuilder:ca,PostgrestFilterBuilder:ua,PostgrestTransformBuilder:ha,PostgrestBuilder:da,PostgrestError:fa}=Kr,Vr="2.11.2",Wr={"X-Client-Info":`realtime-js/${Vr}`},Qr="1.0.0",ln=1e4,Xr=1e3;var ae;(function(r){r[r.connecting=0]="connecting",r[r.open=1]="open",r[r.closing=2]="closing",r[r.closed=3]="closed"})(ae||(ae={}));var $;(function(r){r.closed="closed",r.errored="errored",r.joined="joined",r.joining="joining",r.leaving="leaving"})($||($={}));var R;(function(r){r.close="phx_close",r.error="phx_error",r.join="phx_join",r.reply="phx_reply",r.leave="phx_leave",r.access_token="access_token"})(R||(R={}));var We;(function(r){r.websocket="websocket"})(We||(We={}));var J;(function(r){r.Connecting="connecting",r.Open="open",r.Closing="closing",r.Closed="closed"})(J||(J={}));class Yr{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t(typeof e=="string"?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),n=new TextDecoder;return this._decodeBroadcast(e,t,n)}_decodeBroadcast(e,t,n){const s=t.getUint8(1),i=t.getUint8(2);let a=this.HEADER_LENGTH+2;const o=n.decode(e.slice(a,a+s));a=a+s;const l=n.decode(e.slice(a,a+i));a=a+i;const u=JSON.parse(n.decode(e.slice(a,e.byteLength)));return{ref:null,topic:o,event:l,payload:u}}}class cn{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var E;(function(r){r.abstime="abstime",r.bool="bool",r.date="date",r.daterange="daterange",r.float4="float4",r.float8="float8",r.int2="int2",r.int4="int4",r.int4range="int4range",r.int8="int8",r.int8range="int8range",r.json="json",r.jsonb="jsonb",r.money="money",r.numeric="numeric",r.oid="oid",r.reltime="reltime",r.text="text",r.time="time",r.timestamp="timestamp",r.timestamptz="timestamptz",r.timetz="timetz",r.tsrange="tsrange",r.tstzrange="tstzrange"})(E||(E={}));const yt=(r,e,t={})=>{var n;const s=(n=t.skipTypes)!==null&&n!==void 0?n:[];return Object.keys(e).reduce((i,a)=>(i[a]=Zr(a,r,e,s),i),{})},Zr=(r,e,t,n)=>{const s=e.find(o=>o.name===r),i=s==null?void 0:s.type,a=t[r];return i&&!n.includes(i)?un(i,a):Qe(a)},un=(r,e)=>{if(r.charAt(0)==="_"){const t=r.slice(1,r.length);return rs(e,t)}switch(r){case E.bool:return es(e);case E.float4:case E.float8:case E.int2:case E.int4:case E.int8:case E.numeric:case E.oid:return ts(e);case E.json:case E.jsonb:return ns(e);case E.timestamp:return ss(e);case E.abstime:case E.date:case E.daterange:case E.int4range:case E.int8range:case E.money:case E.reltime:case E.text:case E.time:case E.timestamptz:case E.timetz:case E.tsrange:case E.tstzrange:return Qe(e);default:return Qe(e)}},Qe=r=>r,es=r=>{switch(r){case"t":return!0;case"f":return!1;default:return r}},ts=r=>{if(typeof r=="string"){const e=parseFloat(r);if(!Number.isNaN(e))return e}return r},ns=r=>{if(typeof r=="string")try{return JSON.parse(r)}catch(e){return console.log(`JSON parse error: ${e}`),r}return r},rs=(r,e)=>{if(typeof r!="string")return r;const t=r.length-1,n=r[t];if(r[0]==="{"&&n==="}"){let i;const a=r.slice(1,t);try{i=JSON.parse("["+a+"]")}catch{i=a?a.split(","):[]}return i.map(o=>un(e,o))}return r},ss=r=>typeof r=="string"?r.replace(" ","T"):r,hn=r=>{let e=r;return e=e.replace(/^ws/i,"http"),e=e.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),e.replace(/\/+$/,"")};class Fe{constructor(e,t,n={},s=ln){this.channel=e,this.event=t,this.payload=n,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var n;return this._hasReceived(e)&&t((n=this.receivedResp)===null||n===void 0?void 0:n.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const e=t=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=t,this._matchReceive(t)};this.channel._on(this.refEvent,{},e),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(n=>n.status===e).forEach(n=>n.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var vt;(function(r){r.SYNC="sync",r.JOIN="join",r.LEAVE="leave"})(vt||(vt={}));class me{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const n=(t==null?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(n.state,{},s=>{const{onJoin:i,onLeave:a,onSync:o}=this.caller;this.joinRef=this.channel._joinRef(),this.state=me.syncState(this.state,s,i,a),this.pendingDiffs.forEach(l=>{this.state=me.syncDiff(this.state,l,i,a)}),this.pendingDiffs=[],o()}),this.channel._on(n.diff,{},s=>{const{onJoin:i,onLeave:a,onSync:o}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(s):(this.state=me.syncDiff(this.state,s,i,a),o())}),this.onJoin((s,i,a)=>{this.channel._trigger("presence",{event:"join",key:s,currentPresences:i,newPresences:a})}),this.onLeave((s,i,a)=>{this.channel._trigger("presence",{event:"leave",key:s,currentPresences:i,leftPresences:a})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,n,s){const i=this.cloneDeep(e),a=this.transformState(t),o={},l={};return this.map(i,(u,c)=>{a[u]||(l[u]=c)}),this.map(a,(u,c)=>{const h=i[u];if(h){const d=c.map(p=>p.presence_ref),f=h.map(p=>p.presence_ref),g=c.filter(p=>f.indexOf(p.presence_ref)<0),_=h.filter(p=>d.indexOf(p.presence_ref)<0);g.length>0&&(o[u]=g),_.length>0&&(l[u]=_)}else o[u]=c}),this.syncDiff(i,{joins:o,leaves:l},n,s)}static syncDiff(e,t,n,s){const{joins:i,leaves:a}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return n||(n=()=>{}),s||(s=()=>{}),this.map(i,(o,l)=>{var u;const c=(u=e[o])!==null&&u!==void 0?u:[];if(e[o]=this.cloneDeep(l),c.length>0){const h=e[o].map(f=>f.presence_ref),d=c.filter(f=>h.indexOf(f.presence_ref)<0);e[o].unshift(...d)}n(o,c,l)}),this.map(a,(o,l)=>{let u=e[o];if(!u)return;const c=l.map(h=>h.presence_ref);u=u.filter(h=>c.indexOf(h.presence_ref)<0),e[o]=u,s(o,u,l),u.length===0&&delete e[o]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(n=>t(n,e[n]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,n)=>{const s=e[n];return"metas"in s?t[n]=s.metas.map(i=>(i.presence_ref=i.phx_ref,delete i.phx_ref,delete i.phx_ref_prev,i)):t[n]=s,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var bt;(function(r){r.ALL="*",r.INSERT="INSERT",r.UPDATE="UPDATE",r.DELETE="DELETE"})(bt||(bt={}));var kt;(function(r){r.BROADCAST="broadcast",r.PRESENCE="presence",r.POSTGRES_CHANGES="postgres_changes",r.SYSTEM="system"})(kt||(kt={}));var U;(function(r){r.SUBSCRIBED="SUBSCRIBED",r.TIMED_OUT="TIMED_OUT",r.CLOSED="CLOSED",r.CHANNEL_ERROR="CHANNEL_ERROR"})(U||(U={}));class ot{constructor(e,t={config:{}},n){this.topic=e,this.params=t,this.socket=n,this.bindings={},this.state=$.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new Fe(this,R.join,this.params,this.timeout),this.rejoinTimer=new cn(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=$.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(s=>s.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=$.closed,this.socket._remove(this)}),this._onError(s=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,s),this.state=$.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=$.errored,this.rejoinTimer.scheduleTimeout())}),this._on(R.reply,{},(s,i)=>{this._trigger(this._replyEventName(i),s)}),this.presence=new me(this),this.broadcastEndpointURL=hn(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var n,s;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:i,presence:a,private:o}}=this.params;this._onError(c=>e==null?void 0:e(U.CHANNEL_ERROR,c)),this._onClose(()=>e==null?void 0:e(U.CLOSED));const l={},u={broadcast:i,presence:a,postgres_changes:(s=(n=this.bindings.postgres_changes)===null||n===void 0?void 0:n.map(c=>c.filter))!==null&&s!==void 0?s:[],private:o};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:c})=>{var h;if(this.socket.setAuth(),c===void 0){e==null||e(U.SUBSCRIBED);return}else{const d=this.bindings.postgres_changes,f=(h=d==null?void 0:d.length)!==null&&h!==void 0?h:0,g=[];for(let _=0;_<f;_++){const p=d[_],{filter:{event:w,schema:m,table:b,filter:S}}=p,T=c&&c[_];if(T&&T.event===w&&T.schema===m&&T.table===b&&T.filter===S)g.push(Object.assign(Object.assign({},p),{id:T.id}));else{this.unsubscribe(),e==null||e(U.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=g,e&&e(U.SUBSCRIBED);return}}).receive("error",c=>{e==null||e(U.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{e==null||e(U.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,n){return this._on(e,t,n)}async send(e,t={}){var n,s;if(!this._canPush()&&e.type==="broadcast"){const{event:i,payload:a}=e,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:a,private:this.private}]})};try{const u=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(n=t.timeout)!==null&&n!==void 0?n:this.timeout);return await((s=u.body)===null||s===void 0?void 0:s.cancel()),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(i=>{var a,o,l;const u=this._push(e.type,e,t.timeout||this.timeout);e.type==="broadcast"&&!(!((l=(o=(a=this.params)===null||a===void 0?void 0:a.config)===null||o===void 0?void 0:o.broadcast)===null||l===void 0)&&l.ack)&&i("ok"),u.receive("ok",()=>i("ok")),u.receive("error",()=>i("error")),u.receive("timeout",()=>i("timed out"))})}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=$.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(R.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(n=>{const s=new Fe(this,R.leave,{},e);s.receive("ok",()=>{t(),n("ok")}).receive("timeout",()=>{t(),n("timed out")}).receive("error",()=>{n("error")}),s.send(),this._canPush()||s.trigger("ok",{})})}async _fetchWithTimeout(e,t,n){const s=new AbortController,i=setTimeout(()=>s.abort(),n),a=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:s.signal}));return clearTimeout(i),a}_push(e,t,n=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new Fe(this,e,t,n);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(e,t,n){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,n){var s,i;const a=e.toLocaleLowerCase(),{close:o,error:l,leave:u,join:c}=R;if(n&&[o,l,u,c].indexOf(a)>=0&&n!==this._joinRef())return;let d=this._onMessage(a,t,n);if(t&&!d)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(a)?(s=this.bindings.postgres_changes)===null||s===void 0||s.filter(f=>{var g,_,p;return((g=f.filter)===null||g===void 0?void 0:g.event)==="*"||((p=(_=f.filter)===null||_===void 0?void 0:_.event)===null||p===void 0?void 0:p.toLocaleLowerCase())===a}).map(f=>f.callback(d,n)):(i=this.bindings[a])===null||i===void 0||i.filter(f=>{var g,_,p,w,m,b;if(["broadcast","presence","postgres_changes"].includes(a))if("id"in f){const S=f.id,T=(g=f.filter)===null||g===void 0?void 0:g.event;return S&&((_=t.ids)===null||_===void 0?void 0:_.includes(S))&&(T==="*"||(T==null?void 0:T.toLocaleLowerCase())===((p=t.data)===null||p===void 0?void 0:p.type.toLocaleLowerCase()))}else{const S=(m=(w=f==null?void 0:f.filter)===null||w===void 0?void 0:w.event)===null||m===void 0?void 0:m.toLocaleLowerCase();return S==="*"||S===((b=t==null?void 0:t.event)===null||b===void 0?void 0:b.toLocaleLowerCase())}else return f.type.toLocaleLowerCase()===a}).map(f=>{if(typeof d=="object"&&"ids"in d){const g=d.data,{schema:_,table:p,commit_timestamp:w,type:m,errors:b}=g;d=Object.assign(Object.assign({},{schema:_,table:p,commit_timestamp:w,eventType:m,new:{},old:{},errors:b}),this._getPayloadRecords(g))}f.callback(d,n)})}_isClosed(){return this.state===$.closed}_isJoined(){return this.state===$.joined}_isJoining(){return this.state===$.joining}_isLeaving(){return this.state===$.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,n){const s=e.toLocaleLowerCase(),i={type:s,filter:t,callback:n};return this.bindings[s]?this.bindings[s].push(i):this.bindings[s]=[i],this}_off(e,t){const n=e.toLocaleLowerCase();return this.bindings[n]=this.bindings[n].filter(s=>{var i;return!(((i=s.type)===null||i===void 0?void 0:i.toLocaleLowerCase())===n&&ot.isEqual(s.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(R.close,{},e)}_onError(e){this._on(R.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=$.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return(e.type==="INSERT"||e.type==="UPDATE")&&(t.new=yt(e.columns,e.record)),(e.type==="UPDATE"||e.type==="DELETE")&&(t.old=yt(e.columns,e.old_record)),t}}const is=()=>{},as=typeof WebSocket<"u",os=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class ls{constructor(e,t){var n;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=Wr,this.params={},this.timeout=ln,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=is,this.conn=null,this.sendBuffer=[],this.serializer=new Yr,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=i=>{let a;return i?a=i:typeof fetch>"u"?a=(...o)=>le(async()=>{const{default:l}=await Promise.resolve().then(()=>ue);return{default:l}},void 0,import.meta.url).then(({default:l})=>l(...o)):a=fetch,(...o)=>a(...o)},this.endPoint=`${e}/${We.websocket}`,this.httpEndpoint=hn(e),t!=null&&t.transport?this.transport=t.transport:this.transport=null,t!=null&&t.params&&(this.params=t.params),t!=null&&t.headers&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),t!=null&&t.timeout&&(this.timeout=t.timeout),t!=null&&t.logger&&(this.logger=t.logger),t!=null&&t.heartbeatIntervalMs&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const s=(n=t==null?void 0:t.params)===null||n===void 0?void 0:n.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=t!=null&&t.reconnectAfterMs?t.reconnectAfterMs:i=>[1e3,2e3,5e3,1e4][i-1]||1e4,this.encode=t!=null&&t.encode?t.encode:(i,a)=>a(JSON.stringify(i)),this.decode=t!=null&&t.decode?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new cn(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(t==null?void 0:t.fetch),t!=null&&t.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(t==null?void 0:t.worker)||!1,this.workerUrl=t==null?void 0:t.workerUrl}this.accessToken=(t==null?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(as){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new cs(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),le(async()=>{const{default:e}=await import("../chunks/browser.BaUz-1h7.js").then(t=>t.b);return{default:e}},__vite__mapDeps([0,1]),import.meta.url).then(({default:e})=>{this.conn=new e(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:Qr}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,t??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return this.channels.length===0&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map(t=>t.unsubscribe()));return this.disconnect(),e}log(e,t,n){this.logger(e,t,n)}connectionState(){switch(this.conn&&this.conn.readyState){case ae.connecting:return J.Connecting;case ae.open:return J.Open;case ae.closing:return J.Closing;default:return J.Closed}}isConnected(){return this.connectionState()===J.Open}channel(e,t={config:{}}){const n=new ot(`realtime:${e}`,t,this);return this.channels.push(n),n}push(e){const{topic:t,event:n,payload:s,ref:i}=e,a=()=>{this.encode(e,o=>{var l;(l=this.conn)===null||l===void 0||l.send(o)})};this.log("push",`${t} ${n} (${i})`,s),this.isConnected()?a():this.sendBuffer.push(a)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(t){let n=null;try{n=JSON.parse(atob(t.split(".")[1]))}catch{}if(n&&n.exp&&!(Math.floor(Date.now()/1e3)-n.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${n.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${n.exp}`);this.accessTokenValue=t,this.channels.forEach(s=>{t&&s.updateJoinPayload({access_token:t}),s.joinedOnce&&s._isJoined()&&s._push(R.access_token,{access_token:t})})}}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),(e=this.conn)===null||e===void 0||e.close(Xr,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(n=>n.topic===e&&(n._isJoined()||n._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t._joinRef()!==e._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,t=>{let{topic:n,event:s,payload:i,ref:a}=t;a&&a===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${n} ${s} ${a&&"("+a+")"||""}`,i),this.channels.filter(o=>o._isMember(n)).forEach(o=>o._trigger(s,i,a)),this.stateChangeCallbacks.message.forEach(o=>o(t))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),!this.worker)this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);else{this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=t=>{this.log("worker","worker error",t.message),this.workerRef.terminate()},this.workerRef.onmessage=t=>{t.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(R.error))}_appendParams(e,t){if(Object.keys(t).length===0)return e;const n=e.match(/\?/)?"&":"?",s=new URLSearchParams(t);return`${e}${n}${s}`}_workerObjectUrl(e){let t;if(e)t=e;else{const n=new Blob([os],{type:"application/javascript"});t=URL.createObjectURL(n)}return t}}class cs{constructor(e,t,n){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=ae.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=n.close}}class lt extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function A(r){return typeof r=="object"&&r!==null&&"__isStorageError"in r}class us extends lt{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Xe extends lt{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var hs=function(r,e,t,n){function s(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(c){try{u(n.next(c))}catch(h){a(h)}}function l(c){try{u(n.throw(c))}catch(h){a(h)}}function u(c){c.done?i(c.value):s(c.value).then(o,l)}u((n=n.apply(r,e||[])).next())})};const dn=r=>{let e;return r?e=r:typeof fetch>"u"?e=(...t)=>le(async()=>{const{default:n}=await Promise.resolve().then(()=>ue);return{default:n}},void 0,import.meta.url).then(({default:n})=>n(...t)):e=fetch,(...t)=>e(...t)},ds=()=>hs(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield le(()=>Promise.resolve().then(()=>ue),void 0,import.meta.url)).Response:Response}),Ye=r=>{if(Array.isArray(r))return r.map(t=>Ye(t));if(typeof r=="function"||r!==Object(r))return r;const e={};return Object.entries(r).forEach(([t,n])=>{const s=t.replace(/([-_][a-z])/gi,i=>i.toUpperCase().replace(/[-_]/g,""));e[s]=Ye(n)}),e};var W=function(r,e,t,n){function s(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(c){try{u(n.next(c))}catch(h){a(h)}}function l(c){try{u(n.throw(c))}catch(h){a(h)}}function u(c){c.done?i(c.value):s(c.value).then(o,l)}u((n=n.apply(r,e||[])).next())})};const Me=r=>r.msg||r.message||r.error_description||r.error||JSON.stringify(r),fs=(r,e,t)=>W(void 0,void 0,void 0,function*(){const n=yield ds();r instanceof n&&!(t!=null&&t.noResolveJson)?r.json().then(s=>{e(new us(Me(s),r.status||500))}).catch(s=>{e(new Xe(Me(s),s))}):e(new Xe(Me(r),r))}),gs=(r,e,t,n)=>{const s={method:r,headers:(e==null?void 0:e.headers)||{}};return r==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json"},e==null?void 0:e.headers),n&&(s.body=JSON.stringify(n)),Object.assign(Object.assign({},s),t))};function be(r,e,t,n,s,i){return W(this,void 0,void 0,function*(){return new Promise((a,o)=>{r(t,gs(e,n,s,i)).then(l=>{if(!l.ok)throw l;return n!=null&&n.noResolveJson?l:l.json()}).then(l=>a(l)).catch(l=>fs(l,o,n))})})}function Pe(r,e,t,n){return W(this,void 0,void 0,function*(){return be(r,"GET",e,t,n)})}function F(r,e,t,n,s){return W(this,void 0,void 0,function*(){return be(r,"POST",e,n,s,t)})}function _s(r,e,t,n,s){return W(this,void 0,void 0,function*(){return be(r,"PUT",e,n,s,t)})}function ps(r,e,t,n){return W(this,void 0,void 0,function*(){return be(r,"HEAD",e,Object.assign(Object.assign({},t),{noResolveJson:!0}),n)})}function fn(r,e,t,n,s){return W(this,void 0,void 0,function*(){return be(r,"DELETE",e,n,s,t)})}var O=function(r,e,t,n){function s(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(c){try{u(n.next(c))}catch(h){a(h)}}function l(c){try{u(n.throw(c))}catch(h){a(h)}}function u(c){c.done?i(c.value):s(c.value).then(o,l)}u((n=n.apply(r,e||[])).next())})};const ms={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Et={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class ws{constructor(e,t={},n,s){this.url=e,this.headers=t,this.bucketId=n,this.fetch=dn(s)}uploadOrUpdate(e,t,n,s){return O(this,void 0,void 0,function*(){try{let i;const a=Object.assign(Object.assign({},Et),s);let o=Object.assign(Object.assign({},this.headers),e==="POST"&&{"x-upsert":String(a.upsert)});const l=a.metadata;typeof Blob<"u"&&n instanceof Blob?(i=new FormData,i.append("cacheControl",a.cacheControl),l&&i.append("metadata",this.encodeMetadata(l)),i.append("",n)):typeof FormData<"u"&&n instanceof FormData?(i=n,i.append("cacheControl",a.cacheControl),l&&i.append("metadata",this.encodeMetadata(l))):(i=n,o["cache-control"]=`max-age=${a.cacheControl}`,o["content-type"]=a.contentType,l&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),s!=null&&s.headers&&(o=Object.assign(Object.assign({},o),s.headers));const u=this._removeEmptyFolders(t),c=this._getFinalPath(u),h=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:i,headers:o},a!=null&&a.duplex?{duplex:a.duplex}:{})),d=yield h.json();return h.ok?{data:{path:u,id:d.Id,fullPath:d.Key},error:null}:{data:null,error:d}}catch(i){if(A(i))return{data:null,error:i};throw i}})}upload(e,t,n){return O(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,n)})}uploadToSignedUrl(e,t,n,s){return O(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(e),a=this._getFinalPath(i),o=new URL(this.url+`/object/upload/sign/${a}`);o.searchParams.set("token",t);try{let l;const u=Object.assign({upsert:Et.upsert},s),c=Object.assign(Object.assign({},this.headers),{"x-upsert":String(u.upsert)});typeof Blob<"u"&&n instanceof Blob?(l=new FormData,l.append("cacheControl",u.cacheControl),l.append("",n)):typeof FormData<"u"&&n instanceof FormData?(l=n,l.append("cacheControl",u.cacheControl)):(l=n,c["cache-control"]=`max-age=${u.cacheControl}`,c["content-type"]=u.contentType);const h=yield this.fetch(o.toString(),{method:"PUT",body:l,headers:c}),d=yield h.json();return h.ok?{data:{path:i,fullPath:d.Key},error:null}:{data:null,error:d}}catch(l){if(A(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(e,t){return O(this,void 0,void 0,function*(){try{let n=this._getFinalPath(e);const s=Object.assign({},this.headers);t!=null&&t.upsert&&(s["x-upsert"]="true");const i=yield F(this.fetch,`${this.url}/object/upload/sign/${n}`,{},{headers:s}),a=new URL(this.url+i.url),o=a.searchParams.get("token");if(!o)throw new lt("No token returned by API");return{data:{signedUrl:a.toString(),path:e,token:o},error:null}}catch(n){if(A(n))return{data:null,error:n};throw n}})}update(e,t,n){return O(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,n)})}move(e,t,n){return O(this,void 0,void 0,function*(){try{return{data:yield F(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:n==null?void 0:n.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(A(s))return{data:null,error:s};throw s}})}copy(e,t,n){return O(this,void 0,void 0,function*(){try{return{data:{path:(yield F(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:n==null?void 0:n.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(A(s))return{data:null,error:s};throw s}})}createSignedUrl(e,t,n){return O(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),i=yield F(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:t},n!=null&&n.transform?{transform:n.transform}:{}),{headers:this.headers});const a=n!=null&&n.download?`&download=${n.download===!0?"":n.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${a}`)},{data:i,error:null}}catch(s){if(A(s))return{data:null,error:s};throw s}})}createSignedUrls(e,t,n){return O(this,void 0,void 0,function*(){try{const s=yield F(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=n!=null&&n.download?`&download=${n.download===!0?"":n.download}`:"";return{data:s.map(a=>Object.assign(Object.assign({},a),{signedUrl:a.signedURL?encodeURI(`${this.url}${a.signedURL}${i}`):null})),error:null}}catch(s){if(A(s))return{data:null,error:s};throw s}})}download(e,t){return O(this,void 0,void 0,function*(){const s=typeof(t==null?void 0:t.transform)<"u"?"render/image/authenticated":"object",i=this.transformOptsToQueryString((t==null?void 0:t.transform)||{}),a=i?`?${i}`:"";try{const o=this._getFinalPath(e);return{data:yield(yield Pe(this.fetch,`${this.url}/${s}/${o}${a}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(o){if(A(o))return{data:null,error:o};throw o}})}info(e){return O(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const n=yield Pe(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:Ye(n),error:null}}catch(n){if(A(n))return{data:null,error:n};throw n}})}exists(e){return O(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield ps(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(n){if(A(n)&&n instanceof Xe){const s=n.originalError;if([400,404].includes(s==null?void 0:s.status))return{data:!1,error:n}}throw n}})}getPublicUrl(e,t){const n=this._getFinalPath(e),s=[],i=t!=null&&t.download?`download=${t.download===!0?"":t.download}`:"";i!==""&&s.push(i);const o=typeof(t==null?void 0:t.transform)<"u"?"render/image":"object",l=this.transformOptsToQueryString((t==null?void 0:t.transform)||{});l!==""&&s.push(l);let u=s.join("&");return u!==""&&(u=`?${u}`),{data:{publicUrl:encodeURI(`${this.url}/${o}/public/${n}${u}`)}}}remove(e){return O(this,void 0,void 0,function*(){try{return{data:yield fn(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(A(t))return{data:null,error:t};throw t}})}list(e,t,n){return O(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},ms),t),{prefix:e||""});return{data:yield F(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},n),error:null}}catch(s){if(A(s))return{data:null,error:s};throw s}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return typeof Buffer<"u"?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const ys="2.7.1",vs={"X-Client-Info":`storage-js/${ys}`};var Y=function(r,e,t,n){function s(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(c){try{u(n.next(c))}catch(h){a(h)}}function l(c){try{u(n.throw(c))}catch(h){a(h)}}function u(c){c.done?i(c.value):s(c.value).then(o,l)}u((n=n.apply(r,e||[])).next())})};class bs{constructor(e,t={},n){this.url=e,this.headers=Object.assign(Object.assign({},vs),t),this.fetch=dn(n)}listBuckets(){return Y(this,void 0,void 0,function*(){try{return{data:yield Pe(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(A(e))return{data:null,error:e};throw e}})}getBucket(e){return Y(this,void 0,void 0,function*(){try{return{data:yield Pe(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(A(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return Y(this,void 0,void 0,function*(){try{return{data:yield F(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(n){if(A(n))return{data:null,error:n};throw n}})}updateBucket(e,t){return Y(this,void 0,void 0,function*(){try{return{data:yield _s(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(n){if(A(n))return{data:null,error:n};throw n}})}emptyBucket(e){return Y(this,void 0,void 0,function*(){try{return{data:yield F(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(A(t))return{data:null,error:t};throw t}})}deleteBucket(e){return Y(this,void 0,void 0,function*(){try{return{data:yield fn(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(A(t))return{data:null,error:t};throw t}})}}class ks extends bs{constructor(e,t={},n){super(e,t,n)}from(e){return new ws(this.url,this.headers,e,this.fetch)}}const Es="2.47.16";let _e="";typeof Deno<"u"?_e="deno":typeof document<"u"?_e="web":typeof navigator<"u"&&navigator.product==="ReactNative"?_e="react-native":_e="node";const Ss={"X-Client-Info":`supabase-js-${_e}/${Es}`},Ts={headers:Ss},As={schema:"public"},Os={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},Ps={};var $s=function(r,e,t,n){function s(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(c){try{u(n.next(c))}catch(h){a(h)}}function l(c){try{u(n.throw(c))}catch(h){a(h)}}function u(c){c.done?i(c.value):s(c.value).then(o,l)}u((n=n.apply(r,e||[])).next())})};const Cs=r=>{let e;return r?e=r:typeof fetch>"u"?e=Qt:e=fetch,(...t)=>e(...t)},js=()=>typeof Headers>"u"?Xt:Headers,Rs=(r,e,t)=>{const n=Cs(t),s=js();return(i,a)=>$s(void 0,void 0,void 0,function*(){var o;const l=(o=yield e())!==null&&o!==void 0?o:r;let u=new s(a==null?void 0:a.headers);return u.has("apikey")||u.set("apikey",r),u.has("Authorization")||u.set("Authorization",`Bearer ${l}`),n(i,Object.assign(Object.assign({},a),{headers:u}))})};var xs=function(r,e,t,n){function s(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(c){try{u(n.next(c))}catch(h){a(h)}}function l(c){try{u(n.throw(c))}catch(h){a(h)}}function u(c){c.done?i(c.value):s(c.value).then(o,l)}u((n=n.apply(r,e||[])).next())})};function Is(r){return r.replace(/\/$/,"")}function Ls(r,e){const{db:t,auth:n,realtime:s,global:i}=r,{db:a,auth:o,realtime:l,global:u}=e,c={db:Object.assign(Object.assign({},a),t),auth:Object.assign(Object.assign({},o),n),realtime:Object.assign(Object.assign({},l),s),global:Object.assign(Object.assign({},u),i),accessToken:()=>xs(this,void 0,void 0,function*(){return""})};return r.accessToken?c.accessToken=r.accessToken:delete c.accessToken,c}const gn="2.67.3",Us="http://localhost:9999",Ds="supabase.auth.token",Ns={"X-Client-Info":`gotrue-js/${gn}`},St=10,Ze="X-Supabase-Api-Version",_n={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}};function Bs(r){return Math.round(Date.now()/1e3)+r}function Fs(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(r){const e=Math.random()*16|0;return(r=="x"?e:e&3|8).toString(16)})}const I=()=>typeof window<"u"&&typeof document<"u",q={tested:!1,writable:!1},we=()=>{if(!I())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(q.tested)return q.writable;const r=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(r,r),globalThis.localStorage.removeItem(r),q.tested=!0,q.writable=!0}catch{q.tested=!0,q.writable=!1}return q.writable};function Ms(r){const e={},t=new URL(r);if(t.hash&&t.hash[0]==="#")try{new URLSearchParams(t.hash.substring(1)).forEach((s,i)=>{e[i]=s})}catch{}return t.searchParams.forEach((n,s)=>{e[s]=n}),e}const pn=r=>{let e;return r?e=r:typeof fetch>"u"?e=(...t)=>le(async()=>{const{default:n}=await Promise.resolve().then(()=>ue);return{default:n}},void 0,import.meta.url).then(({default:n})=>n(...t)):e=fetch,(...t)=>e(...t)},qs=r=>typeof r=="object"&&r!==null&&"status"in r&&"ok"in r&&"json"in r&&typeof r.json=="function",mn=async(r,e,t)=>{await r.setItem(e,JSON.stringify(t))},Ee=async(r,e)=>{const t=await r.getItem(e);if(!t)return null;try{return JSON.parse(t)}catch{return t}},Se=async(r,e)=>{await r.removeItem(e)};function zs(r){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let t="",n,s,i,a,o,l,u,c=0;for(r=r.replace("-","+").replace("_","/");c<r.length;)a=e.indexOf(r.charAt(c++)),o=e.indexOf(r.charAt(c++)),l=e.indexOf(r.charAt(c++)),u=e.indexOf(r.charAt(c++)),n=a<<2|o>>4,s=(o&15)<<4|l>>2,i=(l&3)<<6|u,t=t+String.fromCharCode(n),l!=64&&s!=0&&(t=t+String.fromCharCode(s)),u!=64&&i!=0&&(t=t+String.fromCharCode(i));return t}class Ne{constructor(){this.promise=new Ne.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}Ne.promiseConstructor=Promise;function Tt(r){const e=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}=?$|[a-z0-9_-]{2}(==)?$)$/i,t=r.split(".");if(t.length!==3)throw new Error("JWT is not valid: not a JWT structure");if(!e.test(t[1]))throw new Error("JWT is not valid: payload is not in base64url format");const n=t[1];return JSON.parse(zs(n))}async function Hs(r){return await new Promise(e=>{setTimeout(()=>e(null),r)})}function Js(r,e){return new Promise((n,s)=>{(async()=>{for(let i=0;i<1/0;i++)try{const a=await r(i);if(!e(i,null,a)){n(a);return}}catch(a){if(!e(i,a)){s(a);return}}})()})}function Ks(r){return("0"+r.toString(16)).substr(-2)}function Gs(){const e=new Uint32Array(56);if(typeof crypto>"u"){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",n=t.length;let s="";for(let i=0;i<56;i++)s+=t.charAt(Math.floor(Math.random()*n));return s}return crypto.getRandomValues(e),Array.from(e,Ks).join("")}async function Vs(r){const t=new TextEncoder().encode(r),n=await crypto.subtle.digest("SHA-256",t),s=new Uint8Array(n);return Array.from(s).map(i=>String.fromCharCode(i)).join("")}function Ws(r){return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function Qs(r){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),r;const t=await Vs(r);return Ws(t)}async function Z(r,e,t=!1){const n=Gs();let s=n;t&&(s+="/PASSWORD_RECOVERY"),await mn(r,`${e}-code-verifier`,s);const i=await Qs(n);return[i,n===i?"plain":"s256"]}const Xs=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function Ys(r){const e=r.headers.get(Ze);if(!e||!e.match(Xs))return null;try{return new Date(`${e}T00:00:00.0Z`)}catch{return null}}class ct extends Error{constructor(e,t,n){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=n}}function y(r){return typeof r=="object"&&r!==null&&"__isAuthError"in r}class Zs extends ct{constructor(e,t,n){super(e,t,n),this.name="AuthApiError",this.status=t,this.code=n}}function ei(r){return y(r)&&r.name==="AuthApiError"}class wn extends ct{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class Q extends ct{constructor(e,t,n,s){super(e,n,s),this.name=t,this.status=n}}class D extends Q{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function ti(r){return y(r)&&r.name==="AuthSessionMissingError"}class qe extends Q{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Te extends Q{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class Ae extends Q{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function ni(r){return y(r)&&r.name==="AuthImplicitGrantRedirectError"}class At extends Q{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class et extends Q{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function ze(r){return y(r)&&r.name==="AuthRetryableFetchError"}class Ot extends Q{constructor(e,t,n){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=n}}var ri=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const z=r=>r.msg||r.message||r.error_description||r.error||JSON.stringify(r),si=[502,503,504];async function Pt(r){var e;if(!qs(r))throw new et(z(r),0);if(si.includes(r.status))throw new et(z(r),r.status);let t;try{t=await r.json()}catch(i){throw new wn(z(i),i)}let n;const s=Ys(r);if(s&&s.getTime()>=_n["2024-01-01"].timestamp&&typeof t=="object"&&t&&typeof t.code=="string"?n=t.code:typeof t=="object"&&t&&typeof t.error_code=="string"&&(n=t.error_code),n){if(n==="weak_password")throw new Ot(z(t),r.status,((e=t.weak_password)===null||e===void 0?void 0:e.reasons)||[]);if(n==="session_not_found")throw new D}else if(typeof t=="object"&&t&&typeof t.weak_password=="object"&&t.weak_password&&Array.isArray(t.weak_password.reasons)&&t.weak_password.reasons.length&&t.weak_password.reasons.reduce((i,a)=>i&&typeof a=="string",!0))throw new Ot(z(t),r.status,t.weak_password.reasons);throw new Zs(z(t),r.status||500,n)}const ii=(r,e,t,n)=>{const s={method:r,headers:(e==null?void 0:e.headers)||{}};return r==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},e==null?void 0:e.headers),s.body=JSON.stringify(n),Object.assign(Object.assign({},s),t))};async function v(r,e,t,n){var s;const i=Object.assign({},n==null?void 0:n.headers);i[Ze]||(i[Ze]=_n["2024-01-01"].name),n!=null&&n.jwt&&(i.Authorization=`Bearer ${n.jwt}`);const a=(s=n==null?void 0:n.query)!==null&&s!==void 0?s:{};n!=null&&n.redirectTo&&(a.redirect_to=n.redirectTo);const o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await ai(r,e,t+o,{headers:i,noResolveJson:n==null?void 0:n.noResolveJson},{},n==null?void 0:n.body);return n!=null&&n.xform?n==null?void 0:n.xform(l):{data:Object.assign({},l),error:null}}async function ai(r,e,t,n,s,i){const a=ii(e,n,s,i);let o;try{o=await r(t,Object.assign({},a))}catch(l){throw console.error(l),new et(z(l),0)}if(o.ok||await Pt(o),n!=null&&n.noResolveJson)return o;try{return await o.json()}catch(l){await Pt(l)}}function N(r){var e;let t=null;ui(r)&&(t=Object.assign({},r),r.expires_at||(t.expires_at=Bs(r.expires_in)));const n=(e=r.user)!==null&&e!==void 0?e:r;return{data:{session:t,user:n},error:null}}function $t(r){const e=N(r);return!e.error&&r.weak_password&&typeof r.weak_password=="object"&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.message&&typeof r.weak_password.message=="string"&&r.weak_password.reasons.reduce((t,n)=>t&&typeof n=="string",!0)&&(e.data.weak_password=r.weak_password),e}function M(r){var e;return{data:{user:(e=r.user)!==null&&e!==void 0?e:r},error:null}}function oi(r){return{data:r,error:null}}function li(r){const{action_link:e,email_otp:t,hashed_token:n,redirect_to:s,verification_type:i}=r,a=ri(r,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),o={action_link:e,email_otp:t,hashed_token:n,redirect_to:s,verification_type:i},l=Object.assign({},a);return{data:{properties:o,user:l},error:null}}function ci(r){return r}function ui(r){return r.access_token&&r.refresh_token&&r.expires_in}var hi=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};class di{constructor({url:e="",headers:t={},fetch:n}){this.url=e,this.headers=t,this.fetch=pn(n),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t="global"){try{return await v(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(n){if(y(n))return{data:null,error:n};throw n}}async inviteUserByEmail(e,t={}){try{return await v(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:M})}catch(n){if(y(n))return{data:{user:null},error:n};throw n}}async generateLink(e){try{const{options:t}=e,n=hi(e,["options"]),s=Object.assign(Object.assign({},n),t);return"newEmail"in n&&(s.new_email=n==null?void 0:n.newEmail,delete s.newEmail),await v(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:li,redirectTo:t==null?void 0:t.redirectTo})}catch(t){if(y(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await v(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:M})}catch(t){if(y(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,n,s,i,a,o,l;try{const u={nextPage:null,lastPage:0,total:0},c=await v(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(n=(t=e==null?void 0:e.page)===null||t===void 0?void 0:t.toString())!==null&&n!==void 0?n:"",per_page:(i=(s=e==null?void 0:e.perPage)===null||s===void 0?void 0:s.toString())!==null&&i!==void 0?i:""},xform:ci});if(c.error)throw c.error;const h=await c.json(),d=(a=c.headers.get("x-total-count"))!==null&&a!==void 0?a:0,f=(l=(o=c.headers.get("link"))===null||o===void 0?void 0:o.split(","))!==null&&l!==void 0?l:[];return f.length>0&&(f.forEach(g=>{const _=parseInt(g.split(";")[0].split("=")[1].substring(0,1)),p=JSON.parse(g.split(";")[1].split("=")[1]);u[`${p}Page`]=_}),u.total=parseInt(d)),{data:Object.assign(Object.assign({},h),u),error:null}}catch(u){if(y(u))return{data:{users:[]},error:u};throw u}}async getUserById(e){try{return await v(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:M})}catch(t){if(y(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){try{return await v(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:M})}catch(n){if(y(n))return{data:{user:null},error:n};throw n}}async deleteUser(e,t=!1){try{return await v(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:M})}catch(n){if(y(n))return{data:{user:null},error:n};throw n}}async _listFactors(e){try{const{data:t,error:n}=await v(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:s=>({data:{factors:s},error:null})});return{data:t,error:n}}catch(t){if(y(t))return{data:null,error:t};throw t}}async _deleteFactor(e){try{return{data:await v(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(y(t))return{data:null,error:t};throw t}}}const fi={getItem:r=>we()?globalThis.localStorage.getItem(r):null,setItem:(r,e)=>{we()&&globalThis.localStorage.setItem(r,e)},removeItem:r=>{we()&&globalThis.localStorage.removeItem(r)}};function Ct(r={}){return{getItem:e=>r[e]||null,setItem:(e,t)=>{r[e]=t},removeItem:e=>{delete r[e]}}}function gi(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const ee={debug:!!(globalThis&&we()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class yn extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class _i extends yn{}async function pi(r,e,t){ee.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",r,e);const n=new globalThis.AbortController;return e>0&&setTimeout(()=>{n.abort(),ee.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",r)},e),await Promise.resolve().then(()=>globalThis.navigator.locks.request(r,e===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:n.signal},async s=>{if(s){ee.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",r,s.name);try{return await t()}finally{ee.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",r,s.name)}}else{if(e===0)throw ee.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",r),new _i(`Acquiring an exclusive Navigator LockManager lock "${r}" immediately failed`);if(ee.debug)try{const i=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(i,null,"  "))}catch(i){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",i)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await t()}}))}gi();const mi={url:Us,storageKey:Ds,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Ns,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1},ge=30*1e3,jt=3;async function Rt(r,e,t){return await t()}class ye{constructor(e){var t,n;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=ye.nextInstanceID,ye.nextInstanceID+=1,this.instanceID>0&&I()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const s=Object.assign(Object.assign({},mi),e);if(this.logDebugMessages=!!s.debug,typeof s.debug=="function"&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new di({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=pn(s.fetch),this.lock=s.lock||Rt,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:I()&&(!((t=globalThis==null?void 0:globalThis.navigator)===null||t===void 0)&&t.locks)?this.lock=pi:this.lock=Rt,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?s.storage?this.storage=s.storage:we()?this.storage=fi:(this.memoryStorage={},this.storage=Ct(this.memoryStorage)):(this.memoryStorage={},this.storage=Ct(this.memoryStorage)),I()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",i)}(n=this.broadcastChannel)===null||n===void 0||n.addEventListener("message",async i=>{this._debug("received broadcast notification from other tab or client",i),await this._notifyAllSubscribers(i.data.event,i.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${gn}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var e;try{const t=Ms(window.location.href);let n="none";if(this._isImplicitGrantCallback(t)?n="implicit":await this._isPKCECallback(t)&&(n="pkce"),I()&&this.detectSessionInUrl&&n!=="none"){const{data:s,error:i}=await this._getSessionFromURL(t,n);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),ni(i)){const l=(e=i.details)===null||e===void 0?void 0:e.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:i}}return await this._removeSession(),{error:i}}const{session:a,redirectType:o}=s;return this._debug("#_initialize()","detected session in URL",a,"redirect type",o),await this._saveSession(a),setTimeout(async()=>{o==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",a):await this._notifyAllSubscribers("SIGNED_IN",a)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return y(t)?{error:t}:{error:new wn("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,n,s;try{const i=await v(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(n=(t=e==null?void 0:e.options)===null||t===void 0?void 0:t.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:(s=e==null?void 0:e.options)===null||s===void 0?void 0:s.captchaToken}},xform:N}),{data:a,error:o}=i;if(o||!a)return{data:{user:null,session:null},error:o};const l=a.session,u=a.user;return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(y(i))return{data:{user:null,session:null},error:i};throw i}}async signUp(e){var t,n,s;try{let i;if("email"in e){const{email:c,password:h,options:d}=e;let f=null,g=null;this.flowType==="pkce"&&([f,g]=await Z(this.storage,this.storageKey)),i=await v(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:d==null?void 0:d.emailRedirectTo,body:{email:c,password:h,data:(t=d==null?void 0:d.data)!==null&&t!==void 0?t:{},gotrue_meta_security:{captcha_token:d==null?void 0:d.captchaToken},code_challenge:f,code_challenge_method:g},xform:N})}else if("phone"in e){const{phone:c,password:h,options:d}=e;i=await v(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:c,password:h,data:(n=d==null?void 0:d.data)!==null&&n!==void 0?n:{},channel:(s=d==null?void 0:d.channel)!==null&&s!==void 0?s:"sms",gotrue_meta_security:{captcha_token:d==null?void 0:d.captchaToken}},xform:N})}else throw new Te("You must provide either an email or phone number and a password");const{data:a,error:o}=i;if(o||!a)return{data:{user:null,session:null},error:o};const l=a.session,u=a.user;return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(y(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithPassword(e){try{let t;if("email"in e){const{email:i,password:a,options:o}=e;t=await v(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:i,password:a,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}},xform:$t})}else if("phone"in e){const{phone:i,password:a,options:o}=e;t=await v(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:i,password:a,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}},xform:$t})}else throw new Te("You must provide either an email or phone number and a password");const{data:n,error:s}=t;return s?{data:{user:null,session:null},error:s}:!n||!n.session||!n.user?{data:{user:null,session:null},error:new qe}:(n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:Object.assign({user:n.user,session:n.session},n.weak_password?{weakPassword:n.weak_password}:null),error:s})}catch(t){if(y(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,n,s,i;return await this._handleProviderSignIn(e.provider,{redirectTo:(t=e.options)===null||t===void 0?void 0:t.redirectTo,scopes:(n=e.options)===null||n===void 0?void 0:n.scopes,queryParams:(s=e.options)===null||s===void 0?void 0:s.queryParams,skipBrowserRedirect:(i=e.options)===null||i===void 0?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async _exchangeCodeForSession(e){const t=await Ee(this.storage,`${this.storageKey}-code-verifier`),[n,s]=(t??"").split("/");try{const{data:i,error:a}=await v(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:n},xform:N});if(await Se(this.storage,`${this.storageKey}-code-verifier`),a)throw a;return!i||!i.session||!i.user?{data:{user:null,session:null,redirectType:null},error:new qe}:(i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:Object.assign(Object.assign({},i),{redirectType:s??null}),error:a})}catch(i){if(y(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}}async signInWithIdToken(e){try{const{options:t,provider:n,token:s,access_token:i,nonce:a}=e,o=await v(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:n,id_token:s,access_token:i,nonce:a,gotrue_meta_security:{captcha_token:t==null?void 0:t.captchaToken}},xform:N}),{data:l,error:u}=o;return u?{data:{user:null,session:null},error:u}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new qe}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:u})}catch(t){if(y(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,n,s,i,a;try{if("email"in e){const{email:o,options:l}=e;let u=null,c=null;this.flowType==="pkce"&&([u,c]=await Z(this.storage,this.storageKey));const{error:h}=await v(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:o,data:(t=l==null?void 0:l.data)!==null&&t!==void 0?t:{},create_user:(n=l==null?void 0:l.shouldCreateUser)!==null&&n!==void 0?n:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:u,code_challenge_method:c},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:h}}if("phone"in e){const{phone:o,options:l}=e,{data:u,error:c}=await v(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:o,data:(s=l==null?void 0:l.data)!==null&&s!==void 0?s:{},create_user:(i=l==null?void 0:l.shouldCreateUser)!==null&&i!==void 0?i:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(a=l==null?void 0:l.channel)!==null&&a!==void 0?a:"sms"}});return{data:{user:null,session:null,messageId:u==null?void 0:u.message_id},error:c}}throw new Te("You must provide either an email or phone number.")}catch(o){if(y(o))return{data:{user:null,session:null},error:o};throw o}}async verifyOtp(e){var t,n;try{let s,i;"options"in e&&(s=(t=e.options)===null||t===void 0?void 0:t.redirectTo,i=(n=e.options)===null||n===void 0?void 0:n.captchaToken);const{data:a,error:o}=await v(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:s,xform:N});if(o)throw o;if(!a)throw new Error("An error occurred on token verification.");const l=a.session,u=a.user;return l!=null&&l.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(e.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(s){if(y(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithSSO(e){var t,n,s;try{let i=null,a=null;return this.flowType==="pkce"&&([i,a]=await Z(this.storage,this.storageKey)),await v(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:(n=(t=e.options)===null||t===void 0?void 0:t.redirectTo)!==null&&n!==void 0?n:void 0}),!((s=e==null?void 0:e.options)===null||s===void 0)&&s.captchaToken?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:a}),headers:this.headers,xform:oi})}catch(i){if(y(i))return{data:null,error:i};throw i}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{const{data:{session:t},error:n}=e;if(n)throw n;if(!t)throw new D;const{error:s}=await v(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:s}})}catch(e){if(y(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:n,type:s,options:i}=e,{error:a}=await v(this.fetch,"POST",t,{headers:this.headers,body:{email:n,type:s,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},redirectTo:i==null?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:a}}else if("phone"in e){const{phone:n,type:s,options:i}=e,{data:a,error:o}=await v(this.fetch,"POST",t,{headers:this.headers,body:{phone:n,type:s,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:a==null?void 0:a.message_id},error:o}}throw new Te("You must provide either an email or phone number and a type")}catch(t){if(y(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async t=>t))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const n=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await n,await t()))();return this.pendingInLock.push((async()=>{try{await s}catch{}})()),s}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const n=t();for(this.pendingInLock.push((async()=>{try{await n}catch{}})()),await n;this.pendingInLock.length;){const s=[...this.pendingInLock];await Promise.all(s),this.pendingInLock.splice(0,s.length)}return await n}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let e=null;const t=await Ee(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),t!==null&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const n=e.expires_at?e.expires_at<=Date.now()/1e3:!1;if(this._debug("#__loadSession()",`session has${n?"":" not"} expired`,"expires_at",e.expires_at),!n){if(this.storage.isServer){let a=this.suppressGetSessionWarning;e=new Proxy(e,{get:(l,u,c)=>(!a&&u==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),a=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,u,c))})}return{data:{session:e},error:null}}const{session:s,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{session:null},error:i}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{return e?await v(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:M}):await this._useSession(async t=>{var n,s,i;const{data:a,error:o}=t;if(o)throw o;return!(!((n=a.session)===null||n===void 0)&&n.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new D}:await v(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(i=(s=a.session)===null||s===void 0?void 0:s.access_token)!==null&&i!==void 0?i:void 0,xform:M})})}catch(t){if(y(t))return ti(t)&&(await this._removeSession(),await Se(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async n=>{const{data:s,error:i}=n;if(i)throw i;if(!s.session)throw new D;const a=s.session;let o=null,l=null;this.flowType==="pkce"&&e.email!=null&&([o,l]=await Z(this.storage,this.storageKey));const{data:u,error:c}=await v(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:t==null?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:o,code_challenge_method:l}),jwt:a.access_token,xform:M});if(c)throw c;return a.user=u.user,await this._saveSession(a),await this._notifyAllSubscribers("USER_UPDATED",a),{data:{user:a.user},error:null}})}catch(n){if(y(n))return{data:{user:null},error:n};throw n}}_decodeJWT(e){return Tt(e)}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new D;const t=Date.now()/1e3;let n=t,s=!0,i=null;const a=Tt(e.access_token);if(a.exp&&(n=a.exp,s=n<=t),s){const{session:o,error:l}=await this._callRefreshToken(e.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!o)return{data:{user:null,session:null},error:null};i=o}else{const{data:o,error:l}=await this._getUser(e.access_token);if(l)throw l;i={access_token:e.access_token,refresh_token:e.refresh_token,user:o.user,token_type:"bearer",expires_in:n-t,expires_at:n},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(t){if(y(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var n;if(!e){const{data:a,error:o}=t;if(o)throw o;e=(n=a.session)!==null&&n!==void 0?n:void 0}if(!(e!=null&&e.refresh_token))throw new D;const{session:s,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(t){if(y(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!I())throw new Ae("No browser detected.");if(e.error||e.error_description||e.error_code)throw new Ae(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if(this.flowType==="pkce")throw new At("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new Ae("Not a valid implicit grant flow url.");break;default:}if(t==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new At("No code detected.");const{data:m,error:b}=await this._exchangeCodeForSession(e.code);if(b)throw b;const S=new URL(window.location.href);return S.searchParams.delete("code"),window.history.replaceState(window.history.state,"",S.toString()),{data:{session:m.session,redirectType:null},error:null}}const{provider_token:n,provider_refresh_token:s,access_token:i,refresh_token:a,expires_in:o,expires_at:l,token_type:u}=e;if(!i||!o||!a||!u)throw new Ae("No session defined in URL");const c=Math.round(Date.now()/1e3),h=parseInt(o);let d=c+h;l&&(d=parseInt(l));const f=d-c;f*1e3<=ge&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${f}s, should have been closer to ${h}s`);const g=d-h;c-g>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",g,d,c):c-g<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",g,d,c);const{data:_,error:p}=await this._getUser(i);if(p)throw p;const w={provider_token:n,provider_refresh_token:s,access_token:i,expires_in:h,expires_at:d,refresh_token:a,token_type:u,user:_.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:w,redirectType:e.type},error:null}}catch(n){if(y(n))return{data:{session:null,redirectType:null},error:n};throw n}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await Ee(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var n;const{data:s,error:i}=t;if(i)return{error:i};const a=(n=s.session)===null||n===void 0?void 0:n.access_token;if(a){const{error:o}=await this.admin.signOut(a,e);if(o&&!(ei(o)&&(o.status===404||o.status===401||o.status===403)))return{error:o}}return e!=="others"&&(await this._removeSession(),await Se(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){const t=Fs(),n={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,n),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})))(),{data:{subscription:n}}}async _emitInitialSession(e){return await this._useSession(async t=>{var n,s;try{const{data:{session:i},error:a}=t;if(a)throw a;await((n=this.stateChangeEmitters.get(e))===null||n===void 0?void 0:n.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(i){await((s=this.stateChangeEmitters.get(e))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",i),console.error(i)}})}async resetPasswordForEmail(e,t={}){let n=null,s=null;this.flowType==="pkce"&&([n,s]=await Z(this.storage,this.storageKey,!0));try{return await v(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:n,code_challenge_method:s,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(i){if(y(i))return{data:null,error:i};throw i}}async getUserIdentities(){var e;try{const{data:t,error:n}=await this.getUser();if(n)throw n;return{data:{identities:(e=t.user.identities)!==null&&e!==void 0?e:[]},error:null}}catch(t){if(y(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:n,error:s}=await this._useSession(async i=>{var a,o,l,u,c;const{data:h,error:d}=i;if(d)throw d;const f=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:(a=e.options)===null||a===void 0?void 0:a.redirectTo,scopes:(o=e.options)===null||o===void 0?void 0:o.scopes,queryParams:(l=e.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await v(this.fetch,"GET",f,{headers:this.headers,jwt:(c=(u=h.session)===null||u===void 0?void 0:u.access_token)!==null&&c!==void 0?c:void 0})});if(s)throw s;return I()&&!(!((t=e.options)===null||t===void 0)&&t.skipBrowserRedirect)&&window.location.assign(n==null?void 0:n.url),{data:{provider:e.provider,url:n==null?void 0:n.url},error:null}}catch(n){if(y(n))return{data:{provider:e.provider,url:null},error:n};throw n}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var n,s;const{data:i,error:a}=t;if(a)throw a;return await v(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:(s=(n=i.session)===null||n===void 0?void 0:n.access_token)!==null&&s!==void 0?s:void 0})})}catch(t){if(y(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const n=Date.now();return await Js(async s=>(s>0&&await Hs(200*Math.pow(2,s-1)),this._debug(t,"refreshing attempt",s),await v(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:N})),(s,i)=>{const a=200*Math.pow(2,s);return i&&ze(i)&&Date.now()+a-n<ge})}catch(n){if(this._debug(t,"error",n),y(n))return{data:{session:null,user:null},error:n};throw n}finally{this._debug(t,"end")}}_isValidSession(e){return typeof e=="object"&&e!==null&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const n=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",n),I()&&!t.skipBrowserRedirect&&window.location.assign(n),{data:{provider:e,url:n},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const n=await Ee(this.storage,this.storageKey);if(this._debug(t,"session from storage",n),!this._isValidSession(n)){this._debug(t,"session is not valid"),n!==null&&await this._removeSession();return}const s=Math.round(Date.now()/1e3),i=((e=n.expires_at)!==null&&e!==void 0?e:1/0)<s+St;if(this._debug(t,`session has${i?"":" not"} expired with margin of ${St}s`),i){if(this.autoRefreshToken&&n.refresh_token){const{error:a}=await this._callRefreshToken(n.refresh_token);a&&(console.error(a),ze(a)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",a),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",n)}catch(n){this._debug(t,"error",n),console.error(n);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,n;if(!e)throw new D;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new Ne;const{data:i,error:a}=await this._refreshAccessToken(e);if(a)throw a;if(!i.session)throw new D;await this._saveSession(i.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const o={session:i.session,error:null};return this.refreshingDeferred.resolve(o),o}catch(i){if(this._debug(s,"error",i),y(i)){const a={session:null,error:i};return ze(i)||await this._removeSession(),(t=this.refreshingDeferred)===null||t===void 0||t.resolve(a),a}throw(n=this.refreshingDeferred)===null||n===void 0||n.reject(i),i}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(e,t,n=!0){const s=`#_notifyAllSubscribers(${e})`;this._debug(s,"begin",t,`broadcast = ${n}`);try{this.broadcastChannel&&n&&this.broadcastChannel.postMessage({event:e,session:t});const i=[],a=Array.from(this.stateChangeEmitters.values()).map(async o=>{try{await o.callback(e,t)}catch(l){i.push(l)}});if(await Promise.all(a),i.length>0){for(let o=0;o<i.length;o+=1)console.error(i[o]);throw i[0]}}finally{this._debug(s,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await mn(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await Se(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&I()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),ge);this.autoRefreshTicker=e,e&&typeof e=="object"&&typeof e.unref=="function"?e.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const e=Date.now();try{return await this._useSession(async t=>{const{data:{session:n}}=t;if(!n||!n.refresh_token||!n.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const s=Math.floor((n.expires_at*1e3-e)/ge);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts ${ge}ms, refresh threshold is ${jt} ticks`),s<=jt&&await this._callRefreshToken(n.refresh_token)})}catch(t){console.error("Auto refresh tick failed with error. This is likely a transient error.",t)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof yn)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!I()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,n){const s=[`provider=${encodeURIComponent(t)}`];if(n!=null&&n.redirectTo&&s.push(`redirect_to=${encodeURIComponent(n.redirectTo)}`),n!=null&&n.scopes&&s.push(`scopes=${encodeURIComponent(n.scopes)}`),this.flowType==="pkce"){const[i,a]=await Z(this.storage,this.storageKey),o=new URLSearchParams({code_challenge:`${encodeURIComponent(i)}`,code_challenge_method:`${encodeURIComponent(a)}`});s.push(o.toString())}if(n!=null&&n.queryParams){const i=new URLSearchParams(n.queryParams);s.push(i.toString())}return n!=null&&n.skipBrowserRedirect&&s.push(`skip_http_redirect=${n.skipBrowserRedirect}`),`${e}?${s.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var n;const{data:s,error:i}=t;return i?{data:null,error:i}:await v(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:(n=s==null?void 0:s.session)===null||n===void 0?void 0:n.access_token})})}catch(t){if(y(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession(async t=>{var n,s;const{data:i,error:a}=t;if(a)return{data:null,error:a};const o=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},e.factorType==="phone"?{phone:e.phone}:{issuer:e.issuer}),{data:l,error:u}=await v(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:(n=i==null?void 0:i.session)===null||n===void 0?void 0:n.access_token});return u?{data:null,error:u}:(e.factorType==="totp"&&(!((s=l==null?void 0:l.totp)===null||s===void 0)&&s.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(t){if(y(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var n;const{data:s,error:i}=t;if(i)return{data:null,error:i};const{data:a,error:o}=await v(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:(n=s==null?void 0:s.session)===null||n===void 0?void 0:n.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+a.expires_in},a)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",a),{data:a,error:o})})}catch(t){if(y(t))return{data:null,error:t};throw t}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var n;const{data:s,error:i}=t;return i?{data:null,error:i}:await v(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:(n=s==null?void 0:s.session)===null||n===void 0?void 0:n.access_token})})}catch(t){if(y(t))return{data:null,error:t};throw t}})}async _challengeAndVerify(e){const{data:t,error:n}=await this._challenge({factorId:e.factorId});return n?{data:null,error:n}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const n=(e==null?void 0:e.factors)||[],s=n.filter(a=>a.factor_type==="totp"&&a.status==="verified"),i=n.filter(a=>a.factor_type==="phone"&&a.status==="verified");return{data:{all:n,totp:s,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,n;const{data:{session:s},error:i}=e;if(i)return{data:null,error:i};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const a=this._decodeJWT(s.access_token);let o=null;a.aal&&(o=a.aal);let l=o;((n=(t=s.user.factors)===null||t===void 0?void 0:t.filter(h=>h.status==="verified"))!==null&&n!==void 0?n:[]).length>0&&(l="aal2");const c=a.amr||[];return{data:{currentLevel:o,nextLevel:l,currentAuthenticationMethods:c},error:null}}))}}ye.nextInstanceID=0;const wi=ye;class yi extends wi{constructor(e){super(e)}}var vi=function(r,e,t,n){function s(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(c){try{u(n.next(c))}catch(h){a(h)}}function l(c){try{u(n.throw(c))}catch(h){a(h)}}function u(c){c.done?i(c.value):s(c.value).then(o,l)}u((n=n.apply(r,e||[])).next())})};class bi{constructor(e,t,n){var s,i,a;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const o=Is(e);this.realtimeUrl=`${o}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${o}/auth/v1`,this.storageUrl=`${o}/storage/v1`,this.functionsUrl=`${o}/functions/v1`;const l=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,u={db:As,realtime:Ps,auth:Object.assign(Object.assign({},Os),{storageKey:l}),global:Ts},c=Ls(n??{},u);this.storageKey=(s=c.auth.storageKey)!==null&&s!==void 0?s:"",this.headers=(i=c.global.headers)!==null&&i!==void 0?i:{},c.accessToken?(this.accessToken=c.accessToken,this.auth=new Proxy({},{get:(h,d)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(d)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((a=c.auth)!==null&&a!==void 0?a:{},this.headers,c.global.fetch),this.fetch=Rs(t,this._getAccessToken.bind(this),c.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},c.realtime)),this.rest=new Gr(`${o}/rest/v1`,{headers:this.headers,schema:c.db.schema,fetch:this.fetch}),c.accessToken||this._listenForAuthEvents()}get functions(){return new Er(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new ks(this.storageUrl,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},n={}){return this.rest.rpc(e,t,n)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return vi(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:n}=yield this.auth.getSession();return(t=(e=n.session)===null||e===void 0?void 0:e.access_token)!==null&&t!==void 0?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:n,storage:s,storageKey:i,flowType:a,lock:o,debug:l},u,c){const h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new yi({url:this.authUrl,headers:Object.assign(Object.assign({},h),u),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:n,storage:s,flowType:a,lock:o,debug:l,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new ls(this.realtimeUrl,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},e==null?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((t,n)=>{this._handleTokenChanged(t,"CLIENT",n==null?void 0:n.access_token)})}_handleTokenChanged(e,t,n){(e==="TOKEN_REFRESHED"||e==="SIGNED_IN")&&this.changedAccessToken!==n?this.changedAccessToken=n:e==="SIGNED_OUT"&&(this.realtime.setAuth(),t=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const vn=(r,e,t)=>new bi(r,e,t),bn="0.5.2";/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var ki=Ci,Ei=ji,Si=Object.prototype.toString,Ti=Object.prototype.hasOwnProperty,Ai=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,Oi=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,Pi=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,$i=/^[\u0020-\u003A\u003D-\u007E]*$/;function Ci(r,e){if(typeof r!="string")throw new TypeError("argument str must be a string");var t={},n=r.length;if(n<2)return t;var s=e&&e.decode||Ri,i=0,a=0,o=0;do{if(a=r.indexOf("=",i),a===-1)break;if(o=r.indexOf(";",i),o===-1)o=n;else if(a>o){i=r.lastIndexOf(";",a-1)+1;continue}var l=xt(r,i,a),u=It(r,a,l),c=r.slice(l,u);if(!Ti.call(t,c)){var h=xt(r,a+1,o),d=It(r,o,h);r.charCodeAt(h)===34&&r.charCodeAt(d-1)===34&&(h++,d--);var f=r.slice(h,d);t[c]=Ii(f,s)}i=o+1}while(i<n);return t}function xt(r,e,t){do{var n=r.charCodeAt(e);if(n!==32&&n!==9)return e}while(++e<t);return t}function It(r,e,t){for(;e>t;){var n=r.charCodeAt(--e);if(n!==32&&n!==9)return e+1}return t}function ji(r,e,t){var n=t&&t.encode||encodeURIComponent;if(typeof n!="function")throw new TypeError("option encode is invalid");if(!Ai.test(r))throw new TypeError("argument name is invalid");var s=n(e);if(!Oi.test(s))throw new TypeError("argument val is invalid");var i=r+"="+s;if(!t)return i;if(t.maxAge!=null){var a=Math.floor(t.maxAge);if(!isFinite(a))throw new TypeError("option maxAge is invalid");i+="; Max-Age="+a}if(t.domain){if(!Pi.test(t.domain))throw new TypeError("option domain is invalid");i+="; Domain="+t.domain}if(t.path){if(!$i.test(t.path))throw new TypeError("option path is invalid");i+="; Path="+t.path}if(t.expires){var o=t.expires;if(!xi(o)||isNaN(o.valueOf()))throw new TypeError("option expires is invalid");i+="; Expires="+o.toUTCString()}if(t.httpOnly&&(i+="; HttpOnly"),t.secure&&(i+="; Secure"),t.partitioned&&(i+="; Partitioned"),t.priority){var l=typeof t.priority=="string"?t.priority.toLowerCase():t.priority;switch(l){case"low":i+="; Priority=Low";break;case"medium":i+="; Priority=Medium";break;case"high":i+="; Priority=High";break;default:throw new TypeError("option priority is invalid")}}if(t.sameSite){var u=typeof t.sameSite=="string"?t.sameSite.toLowerCase():t.sameSite;switch(u){case!0:i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"strict":i+="; SameSite=Strict";break;case"none":i+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return i}function Ri(r){return r.indexOf("%")!==-1?decodeURIComponent(r):r}function xi(r){return Si.call(r)==="[object Date]"}function Ii(r,e){try{return e(r)}catch{return r}}function oe(){return typeof window<"u"&&typeof window.document<"u"}const G={path:"/",sameSite:"lax",httpOnly:!1,maxAge:400*24*60*60},Li=3180,Ui=/^(.*)[.](0|[1-9][0-9]*)$/;function $e(r,e){if(r===e)return!0;const t=r.match(Ui);return!!(t&&t[1]===e)}function kn(r,e,t){const n=Li;let s=encodeURIComponent(e);if(s.length<=n)return[{name:r,value:e}];const i=[];for(;s.length>0;){let a=s.slice(0,n);const o=a.lastIndexOf("%");o>n-3&&(a=a.slice(0,o));let l="";for(;a.length>0;)try{l=decodeURIComponent(a);break}catch(u){if(u instanceof URIError&&a.at(-3)==="%"&&a.length>3)a=a.slice(0,a.length-3);else throw u}i.push(l),s=s.slice(a.length)}return i.map((a,o)=>({name:`${r}.${o}`,value:a}))}async function Lt(r,e){const t=await e(r);if(t)return t;let n=[];for(let s=0;;s++){const i=`${r}.${s}`,a=await e(i);if(!a)break;n.push(a)}return n.length>0?n.join(""):null}const Ce="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Ut=` 	
\r=`.split(""),Di=(()=>{const r=new Array(128);for(let e=0;e<r.length;e+=1)r[e]=-1;for(let e=0;e<Ut.length;e+=1)r[Ut[e].charCodeAt(0)]=-2;for(let e=0;e<Ce.length;e+=1)r[Ce[e].charCodeAt(0)]=e;return r})();function En(r){const e=[];let t=0,n=0;if(Bi(r,i=>{for(t=t<<8|i,n+=8;n>=6;){const a=t>>n-6&63;e.push(Ce[a]),n-=6}}),n>0)for(t=t<<6-n,n=6;n>=6;){const i=t>>n-6&63;e.push(Ce[i]),n-=6}return e.join("")}function Dt(r){const e=[],t=a=>{e.push(String.fromCodePoint(a))},n={utf8seq:0,codepoint:0};let s=0,i=0;for(let a=0;a<r.length;a+=1){const o=r.charCodeAt(a),l=Di[o];if(l>-1)for(s=s<<6|l,i+=6;i>=8;)Fi(s>>i-8&255,n,t),i-=8;else{if(l===-2)continue;throw new Error(`Invalid Base64-URL character "${r.at(a)}" at position ${a}`)}}return e.join("")}function Ni(r,e){if(r<=127){e(r);return}else if(r<=2047){e(192|r>>6),e(128|r&63);return}else if(r<=65535){e(224|r>>12),e(128|r>>6&63),e(128|r&63);return}else if(r<=1114111){e(240|r>>18),e(128|r>>12&63),e(128|r>>6&63),e(128|r&63);return}throw new Error(`Unrecognized Unicode codepoint: ${r.toString(16)}`)}function Bi(r,e){for(let t=0;t<r.length;t+=1){let n=r.charCodeAt(t);if(n>55295&&n<=56319){const s=(n-55296)*1024&65535;n=(r.charCodeAt(t+1)-56320&65535|s)+65536,t+=1}Ni(n,e)}}function Fi(r,e,t){if(e.utf8seq===0){if(r<=127){t(r);return}for(let n=1;n<6;n+=1)if(!(r>>7-n&1)){e.utf8seq=n;break}if(e.utf8seq===2)e.codepoint=r&31;else if(e.utf8seq===3)e.codepoint=r&15;else if(e.utf8seq===4)e.codepoint=r&7;else throw new Error("Invalid UTF-8 sequence");e.utf8seq-=1}else if(e.utf8seq>0){if(r<=127)throw new Error("Invalid UTF-8 sequence");e.codepoint=e.codepoint<<6|r&63,e.utf8seq-=1,e.utf8seq===0&&t(e.codepoint)}}const te="base64-";function Sn(r,e){const t=r.cookies??null,n=r.cookieEncoding,s={},i={};let a,o;if(t)if("get"in t){const l=async u=>{const c=u.flatMap(d=>[d,...Array.from({length:5}).map((f,g)=>`${d}.${g}`)]),h=[];for(let d=0;d<c.length;d+=1){const f=await t.get(c[d]);!f&&typeof f!="string"||h.push({name:c[d],value:f})}return h};if(a=async u=>await l(u),"set"in t&&"remove"in t)o=async u=>{for(let c=0;c<u.length;c+=1){const{name:h,value:d,options:f}=u[c];d?await t.set(h,d,f):await t.remove(h,f)}};else if(e)o=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw new Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in t)if(a=async()=>await t.getAll(),"setAll"in t)o=t.setAll;else if(e)o=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw new Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw new Error(`@supabase/ssr: ${e?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${oe()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!e&&oe()){const l=()=>{const u=ki(document.cookie);return Object.keys(u).map(c=>({name:c,value:u[c]}))};a=()=>l(),o=u=>{u.forEach(({name:c,value:h,options:d})=>{document.cookie=Ei(c,h,d)})}}else{if(e)throw new Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");a=()=>[],o=()=>{throw new Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")}}return e?{getAll:a,setAll:o,setItems:s,removedItems:i,storage:{isServer:!0,getItem:async l=>{if(typeof s[l]=="string")return s[l];if(i[l])return null;const u=await a([l]),c=await Lt(l,async d=>{const f=(u==null?void 0:u.find(({name:g})=>g===d))||null;return f?f.value:null});if(!c)return null;let h=c;return typeof c=="string"&&c.startsWith(te)&&(h=Dt(c.substring(te.length))),h},setItem:async(l,u)=>{l.endsWith("-code-verifier")&&await Tn({getAll:a,setAll:o,setItems:{[l]:u},removedItems:{}},{cookieOptions:(r==null?void 0:r.cookieOptions)??null,cookieEncoding:n}),s[l]=u,delete i[l]},removeItem:async l=>{delete s[l],i[l]=!0}}}:{getAll:a,setAll:o,setItems:s,removedItems:i,storage:{isServer:!1,getItem:async l=>{const u=await a([l]),c=await Lt(l,async d=>{const f=(u==null?void 0:u.find(({name:g})=>g===d))||null;return f?f.value:null});if(!c)return null;let h=c;return c.startsWith(te)&&(h=Dt(c.substring(te.length))),h},setItem:async(l,u)=>{const c=await a([l]),h=(c==null?void 0:c.map(({name:m})=>m))||[],d=new Set(h.filter(m=>$e(m,l)));let f=u;n==="base64url"&&(f=te+En(u));const g=kn(l,f);g.forEach(({name:m})=>{d.delete(m)});const _={...G,...r==null?void 0:r.cookieOptions,maxAge:0},p={...G,...r==null?void 0:r.cookieOptions,maxAge:G.maxAge};delete _.name,delete p.name;const w=[...[...d].map(m=>({name:m,value:"",options:_})),...g.map(({name:m,value:b})=>({name:m,value:b,options:p}))];w.length>0&&await o(w)},removeItem:async l=>{const u=await a([l]),h=((u==null?void 0:u.map(({name:f})=>f))||[]).filter(f=>$e(f,l)),d={...G,...r==null?void 0:r.cookieOptions,maxAge:0};delete d.name,h.length>0&&await o(h.map(f=>({name:f,value:"",options:d})))}}}}async function Tn({getAll:r,setAll:e,setItems:t,removedItems:n},s){const i=s.cookieEncoding,a=s.cookieOptions??null,o=await r([...t?Object.keys(t):[],...n?Object.keys(n):[]]),l=(o==null?void 0:o.map(({name:f})=>f))||[],u=Object.keys(n).flatMap(f=>l.filter(g=>$e(g,f))),c=Object.keys(t).flatMap(f=>{const g=new Set(l.filter(w=>$e(w,f)));let _=t[f];i==="base64url"&&(_=te+En(_));const p=kn(f,_);return p.forEach(w=>{g.delete(w.name)}),u.push(...g),p}),h={...G,...a,maxAge:0},d={...G,...a,maxAge:G.maxAge};delete h.name,delete d.name,await e([...u.map(f=>({name:f,value:"",options:h})),...c.map(({name:f,value:g})=>({name:f,value:g,options:d}))])}let He;function Mi(r,e,t){var a,o;const n=(t==null?void 0:t.isSingleton)===!0||(!t||!("isSingleton"in t))&&oe();if(n&&He)return He;const{storage:s}=Sn({...t,cookieEncoding:(t==null?void 0:t.cookieEncoding)??"base64url"},!1),i=vn(r,e,{...t,global:{...t==null?void 0:t.global,headers:{...(a=t==null?void 0:t.global)==null?void 0:a.headers,"X-Client-Info":`supabase-ssr/${bn}`}},auth:{...t==null?void 0:t.auth,...(o=t==null?void 0:t.cookieOptions)!=null&&o.name?{storageKey:t.cookieOptions.name}:null,flowType:"pkce",autoRefreshToken:oe(),detectSessionInUrl:oe(),persistSession:!0,storage:s}});return n&&(He=i),i}function qi(r,e,t){var u,c;const{storage:n,getAll:s,setAll:i,setItems:a,removedItems:o}=Sn({...t,cookieEncoding:(t==null?void 0:t.cookieEncoding)??"base64url"},!0),l=vn(r,e,{...t,global:{...t==null?void 0:t.global,headers:{...(u=t==null?void 0:t.global)==null?void 0:u.headers,"X-Client-Info":`supabase-ssr/${bn}`}},auth:{...(c=t==null?void 0:t.cookieOptions)!=null&&c.name?{storageKey:t.cookieOptions.name}:null,...t==null?void 0:t.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:n}});return l.auth.onAuthStateChange(async h=>{(Object.keys(a).length>0||Object.keys(o).length>0)&&(h==="SIGNED_IN"||h==="TOKEN_REFRESHED"||h==="USER_UPDATED"||h==="PASSWORD_RECOVERY"||h==="SIGNED_OUT"||h==="MFA_CHALLENGE_VERIFIED")&&await Tn({getAll:s,setAll:i,setItems:a,removedItems:o},{cookieOptions:(t==null?void 0:t.cookieOptions)??null,cookieEncoding:(t==null?void 0:t.cookieEncoding)??"base64url"})}),l}const zi=!0,Hi=async({data:r,depends:e,fetch:t})=>{e("supabase:auth");const n=oe()?Mi(ht,ut,{global:{fetch:t}}):qi(ht,ut,{global:{fetch:t},cookies:{getAll(){return r.cookies}}}),{data:{session:s}}=await n.auth.getSession(),{data:{user:i}}=await n.auth.getUser();return{session:s,supabase:n,user:i}},ga=Object.freeze(Object.defineProperty({__proto__:null,load:Hi,prerender:zi},Symbol.toStringTag,{value:"Module"}));function _a(r,e){rt(e,!0),mr(r,{i18n:Xn,children:(t,n)=>{var s=ne(),i=re(s);ur(i,()=>e.children),V(t,s)},$$slots:{default:!0}}),st()}export{_a as component,ga as universal};
