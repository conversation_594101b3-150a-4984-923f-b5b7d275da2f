import{a as u,t as h}from"../chunks/disclose-version.DD3_NYGK.js";import"../chunks/legacy.BXZnuAlm.js";import{p as v,f as d,t as _,a as b,c as s,r as n,s as k}from"../chunks/runtime.BoVB3PJd.js";import{d as w,s as x}from"../chunks/render.Dmit0_6o.js";import{i as T}from"../chunks/lifecycle.B3mXKHkP.js";import{l as P,i}from"../chunks/i18n.BJ2ZmtzR.js";import{p as $}from"../chunks/index.D8JMZMVP.js";import{g as H}from"../chunks/entry.DaoNXzB7.js";const K=a=>`Hello, ${a.name} from en!`,L=(a,t={})=>({en:K})[t.languageTag??P()](a);var R=(a,t)=>t("en"),S=h("<h1> </h1> <div><button>en</button></div>",1);function D(a,t){v(t,!1);function l(c){const f=i.route($.url.pathname),g=i.resolveRoute(f,c);H(g)}T();var e=S(),o=d(e),m=s(o,!0);_(()=>x(m,L({name:"SvelteKit User"}))),n(o);var r=k(o,2),p=s(r);p.__click=[R,l],n(r),u(a,e),b()}w(["click"]);export{D as component};
