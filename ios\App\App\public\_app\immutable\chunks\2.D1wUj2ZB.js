import{a as K,t as z}from"./disclose-version.DD3_NYGK.js";import"./legacy.BXZnuAlm.js";import{v as W,K as ue,i as T,I as fe,p as V,t as ge,a as J,c as Q,r as X,k as me}from"./runtime.BoVB3PJd.js";import{i as pe}from"./lifecycle.B3mXKHkP.js";import{o as he}from"./index-client.CMvqoH0Y.js";import{d as be,s as ye}from"./render.Dmit0_6o.js";import{p as M,_ as we}from"./preload-helper.Kar63Wts.js";import{g as Pe}from"./entry.DaoNXzB7.js";import{b as ve}from"./public.DrZ1jm20.js";import{s as Le}from"./store.svelte.BneWDtP5.js";function Y(t){var e,n,s="";if(typeof t=="string"||typeof t=="number")s+=t;else if(typeof t=="object")if(Array.isArray(t)){var r=t.length;for(e=0;e<r;e++)t[e]&&(n=Y(t[e]))&&(s&&(s+=" "),s+=n)}else for(n in t)t[n]&&(s&&(s+=" "),s+=n);return s}function Ce(){for(var t,e,n=0,s="",r=arguments.length;n<r;n++)(t=arguments[n])&&(e=Y(t))&&(s&&(s+=" "),s+=e);return s}function _e(t){return typeof t=="object"?Ce(t):t??""}function Ee(t,e,n){var s=t.__className,r=ke(e);W&&t.className===r?t.__className=r:(s!==r||W&&t.className!==r)&&(e==null?t.removeAttribute("class"):t.className=r,t.__className=r)}function ke(t,e){return(t??"")+""}function D(t,e,n){if(n){if(t.classList.contains(e))return;t.classList.add(e)}else{if(!t.classList.contains(e))return;t.classList.remove(e)}}function xe(t,e,n,s){var r=t.__styles??(t.__styles={});r[e]!==n&&(r[e]=n,n==null?t.style.removeProperty(e):t.style.setProperty(e,n,""))}function Oe(t){var e=fe(0);return function(){return arguments.length===1?(ue(e,T(e)+1),arguments[0]):(T(e),t())}}var Ae=z('<button type="button"> </button>');function je(t,e){V(e,!0);const n=M(e,"primary",3,!1),s=M(e,"size",3,"medium");var r=Ae();const i=me(()=>_e(["storybook-button",`storybook-button--${s()}`].join(" ")));r.__click=function(...o){var u;(u=e.onClick)==null||u.apply(this,o)};var a=Q(r,!0);X(r),ge(()=>{Ee(r,T(i)),D(r,"storybook-button--primary",n()),D(r,"storybook-button--secondary",!n()),xe(r,"background-color",e.backgroundColor),ye(a,e.label)}),K(t,r),J()}be(["click"]);/*! Capacitor: https://capacitorjs.com/ - MIT License */const $e=t=>{const e=new Map;e.set("web",{name:"web"});const n=t.CapacitorPlatforms||{currentPlatform:{name:"web"},platforms:e},s=(i,a)=>{n.platforms.set(i,a)},r=i=>{n.platforms.has(i)&&(n.currentPlatform=n.platforms.get(i))};return n.addPlatform=s,n.setPlatform=r,n},Ue=t=>t.CapacitorPlatforms=$e(t),Z=Ue(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});Z.addPlatform;Z.setPlatform;var C;(function(t){t.Unimplemented="UNIMPLEMENTED",t.Unavailable="UNAVAILABLE"})(C||(C={}));class U extends Error{constructor(e,n,s){super(e),this.message=e,this.code=n,this.data=s}}const Te=t=>{var e,n;return t!=null&&t.androidBridge?"android":!((n=(e=t==null?void 0:t.webkit)===null||e===void 0?void 0:e.messageHandlers)===null||n===void 0)&&n.bridge?"ios":"web"},Se=t=>{var e,n,s,r,i;const a=t.CapacitorCustomPlatform||null,o=t.Capacitor||{},u=o.Plugins=o.Plugins||{},l=t.CapacitorPlatforms,_=()=>a!==null?a.name:Te(t),P=((e=l==null?void 0:l.currentPlatform)===null||e===void 0?void 0:e.getPlatform)||_,O=()=>P()!=="web",ee=((n=l==null?void 0:l.currentPlatform)===null||n===void 0?void 0:n.isNativePlatform)||O,te=c=>{const d=A.get(c);return!!(d!=null&&d.platforms.has(P())||I(c))},ne=((s=l==null?void 0:l.currentPlatform)===null||s===void 0?void 0:s.isPluginAvailable)||te,re=c=>{var d;return(d=o.PluginHeaders)===null||d===void 0?void 0:d.find(v=>v.name===c)},I=((r=l==null?void 0:l.currentPlatform)===null||r===void 0?void 0:r.getPluginHeader)||re,se=c=>t.console.error(c),ie=(c,d,v)=>Promise.reject(`${v} does not have an implementation of "${d}".`),A=new Map,oe=(c,d={})=>{const v=A.get(c);if(v)return console.warn(`Capacitor plugin "${c}" already registered. Cannot register plugins twice.`),v.proxy;const w=P(),L=I(c);let h;const le=async()=>(!h&&w in d?h=typeof d[w]=="function"?h=await d[w]():h=d[w]:a!==null&&!h&&"web"in d&&(h=typeof d.web=="function"?h=await d.web():h=d.web),h),ce=(f,g)=>{var p,b;if(L){const y=L==null?void 0:L.methods.find(m=>g===m.name);if(y)return y.rtype==="promise"?m=>o.nativePromise(c,g.toString(),m):(m,E)=>o.nativeCallback(c,g.toString(),m,E);if(f)return(p=f[g])===null||p===void 0?void 0:p.bind(f)}else{if(f)return(b=f[g])===null||b===void 0?void 0:b.bind(f);throw new U(`"${c}" plugin is not implemented on ${w}`,C.Unimplemented)}},j=f=>{let g;const p=(...b)=>{const y=le().then(m=>{const E=ce(m,f);if(E){const k=E(...b);return g=k==null?void 0:k.remove,k}else throw new U(`"${c}.${f}()" is not implemented on ${w}`,C.Unimplemented)});return f==="addListener"&&(y.remove=async()=>g()),y};return p.toString=()=>`${f.toString()}() { [capacitor code] }`,Object.defineProperty(p,"name",{value:f,writable:!1,configurable:!1}),p},H=j("addListener"),R=j("removeListener"),de=(f,g)=>{const p=H({eventName:f},g),b=async()=>{const m=await p;R({eventName:f,callbackId:m},g)},y=new Promise(m=>p.then(()=>m({remove:b})));return y.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await b()},y},$=new Proxy({},{get(f,g){switch(g){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return L?de:H;case"removeListener":return R;default:return j(g)}}});return u[c]=$,A.set(c,{name:c,proxy:$,platforms:new Set([...Object.keys(d),...L?[w]:[]])}),$},ae=((i=l==null?void 0:l.currentPlatform)===null||i===void 0?void 0:i.registerPlugin)||oe;return o.convertFileSrc||(o.convertFileSrc=c=>c),o.getPlatform=P,o.handleError=se,o.isNativePlatform=ee,o.isPluginAvailable=ne,o.pluginMethodNoop=ie,o.registerPlugin=ae,o.Exception=U,o.DEBUG=!!o.DEBUG,o.isLoggingEnabled=!!o.isLoggingEnabled,o.platform=o.getPlatform(),o.isNative=o.isNativePlatform(),o},Ie=t=>t.Capacitor=Se(t),x=Ie(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),S=x.registerPlugin;x.Plugins;class N{constructor(e){this.listeners={},this.retainedEventArguments={},this.windowListeners={},e&&(console.warn(`Capacitor WebPlugin "${e.name}" config object was deprecated in v3 and will be removed in v4.`),this.config=e)}addListener(e,n){let s=!1;this.listeners[e]||(this.listeners[e]=[],s=!0),this.listeners[e].push(n);const i=this.windowListeners[e];i&&!i.registered&&this.addWindowListener(i),s&&this.sendRetainedArgumentsForEvent(e);const a=async()=>this.removeListener(e,n);return Promise.resolve({remove:a})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,n,s){const r=this.listeners[e];if(!r){if(s){let i=this.retainedEventArguments[e];i||(i=[]),i.push(n),this.retainedEventArguments[e]=i}return}r.forEach(i=>i(n))}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,n){this.windowListeners[n]={registered:!1,windowEventName:e,pluginEventName:n,handler:s=>{this.notifyListeners(n,s)}}}unimplemented(e="not implemented"){return new x.Exception(e,C.Unimplemented)}unavailable(e="not available"){return new x.Exception(e,C.Unavailable)}async removeListener(e,n){const s=this.listeners[e];if(!s)return;const r=s.indexOf(n);this.listeners[e].splice(r,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const n=this.retainedEventArguments[e];n&&(delete this.retainedEventArguments[e],n.forEach(s=>{this.notifyListeners(e,s)}))}}const G=t=>encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),B=t=>t.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class He extends N{async getCookies(){const e=document.cookie,n={};return e.split(";").forEach(s=>{if(s.length<=0)return;let[r,i]=s.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");r=B(r).trim(),i=B(i).trim(),n[r]=i}),n}async setCookie(e){try{const n=G(e.key),s=G(e.value),r=`; expires=${(e.expires||"").replace("expires=","")}`,i=(e.path||"/").replace("path=",""),a=e.url!=null&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${n}=${s||""}${r}; path=${i}; ${a};`}catch(n){return Promise.reject(n)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(n){return Promise.reject(n)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const n of e)document.cookie=n.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}S("CapacitorCookies",{web:()=>new He});const Re=async t=>new Promise((e,n)=>{const s=new FileReader;s.onload=()=>{const r=s.result;e(r.indexOf(",")>=0?r.split(",")[1]:r)},s.onerror=r=>n(r),s.readAsDataURL(t)}),We=(t={})=>{const e=Object.keys(t);return Object.keys(t).map(r=>r.toLocaleLowerCase()).reduce((r,i,a)=>(r[i]=t[e[a]],r),{})},Me=(t,e=!0)=>t?Object.entries(t).reduce((s,r)=>{const[i,a]=r;let o,u;return Array.isArray(a)?(u="",a.forEach(l=>{o=e?encodeURIComponent(l):l,u+=`${i}=${o}&`}),u.slice(0,-1)):(o=e?encodeURIComponent(a):a,u=`${i}=${o}`),`${s}&${u}`},"").substr(1):null,De=(t,e={})=>{const n=Object.assign({method:t.method||"GET",headers:t.headers},e),r=We(t.headers)["content-type"]||"";if(typeof t.data=="string")n.body=t.data;else if(r.includes("application/x-www-form-urlencoded")){const i=new URLSearchParams;for(const[a,o]of Object.entries(t.data||{}))i.set(a,o);n.body=i.toString()}else if(r.includes("multipart/form-data")||t.data instanceof FormData){const i=new FormData;if(t.data instanceof FormData)t.data.forEach((o,u)=>{i.append(u,o)});else for(const o of Object.keys(t.data))i.append(o,t.data[o]);n.body=i;const a=new Headers(n.headers);a.delete("content-type"),n.headers=a}else(r.includes("application/json")||typeof t.data=="object")&&(n.body=JSON.stringify(t.data));return n};class Ge extends N{async request(e){const n=De(e,e.webFetchExtra),s=Me(e.params,e.shouldEncodeUrlParams),r=s?`${e.url}?${s}`:e.url,i=await fetch(r,n),a=i.headers.get("content-type")||"";let{responseType:o="text"}=i.ok?e:{};a.includes("application/json")&&(o="json");let u,l;switch(o){case"arraybuffer":case"blob":l=await i.blob(),u=await Re(l);break;case"json":u=await i.json();break;case"document":case"text":default:u=await i.text()}const _={};return i.headers.forEach((P,O)=>{_[O]=P}),{data:u,headers:_,status:i.status,url:i.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}S("CapacitorHttp",{web:()=>new Ge});const F=S("SocialLogin",{web:()=>we(()=>import("./web.CBiZ_G5I.js"),[],import.meta.url).then(t=>new t.SocialLoginWeb)});var q=Oe(()=>Le),Be=z("<div><!></div>");function Ne(t,e){V(e,!1),he(()=>{F.initialize({google:{webClientId:ve}})});const n=async()=>{const i=await F.login({provider:"google",options:{}});q(q().accessToken=i.result.accessToken),Pe("/control")};pe();var s=Be(),r=Q(s);je(r,{label:"Login with Google",onClick:()=>n()}),X(s),K(t,s),J()}export{N as W,Ne as _};
