import"./CWj6FrbW.js";import{c as n,r as p,t as d}from"./DvS_9Yw_.js";import{s as u}from"./BGB6NhFz.js";import{f as _,a as x}from"./Ckyppc5t.js";import{d as b}from"./CTtUbKlS.js";import{s as k}from"./PoYD5o0_.js";import{p as l}from"./Ctt2IVZk.js";var v=_('<button tabindex="-1"> </button>');function j(i,e){const r=l(e,"onClick",3,()=>{}),m=l(e,"small",3,!1),c=l(e,"selected",3,!1);var t=v();t.__click=function(...s){var o;(o=r())==null||o.apply(this,s)};let a;var f=n(t,!0);p(t),d(s=>{a=k(t,1,"svelte-g8wsx3",null,a,s),u(f,e.title)},[()=>({small:m(),selected:c()})]),x(i,t)}b(["click"]);export{j as L};
