import{Z as _,_ as g}from"./runtime.BoVB3PJd.js";const u="data-no-translate",l="paraglide:lang",E="paraglide_lang",s={},A=()=>g(s),N=n=>{_(s,n)};function T(){const n=A();function a(t,e){return typeof t!="string"||!n?t:n.translateHref(t,e)}function c(t,e){if(t[u])return t;for(const{attribute_name:r,lang_attribute_name:o}of e)if(r in t){const f=t[r],i=o?t[o]:void 0;t[r]=a(f,typeof i=="string"?i:void 0)}return t}return[a,c]}export{E as L,l as a,T as g,N as s};
