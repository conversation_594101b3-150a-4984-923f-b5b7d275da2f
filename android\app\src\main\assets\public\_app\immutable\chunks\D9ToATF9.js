import{C as f,B as n,V as u,W as v,X as h,Y as g,h as _,q as d,Z as m,_ as y,$ as N}from"./DvS_9Yw_.js";import{a as A}from"./CTtUbKlS.js";const E=Symbol("is custom element"),S=Symbol("is html");function T(s){if(_){var e=!1,r=()=>{if(!e){if(e=!0,s.hasAttribute("value")){var t=s.value;i(s,"value",null),s.value=t}if(s.hasAttribute("checked")){var a=s.checked;i(s,"checked",null),s.checked=a}}};s.__on_r=r,y(r),A()}}function i(s,e,r,t){var a=L(s);_&&(a[e]=s.getAttribute(e),e==="src"||e==="srcset"||e==="href"&&s.nodeName==="LINK")||a[e]!==(a[e]=r)&&(e==="loading"&&(s[N]=r),r==null?s.removeAttribute(e):typeof r!="string"&&l(s).includes(e)?s[e]=r:s.setAttribute(e,r))}function k(s,e,r){var t=u,a=v;let o=_;_&&d(!1),f(null),n(null);try{e!=="style"&&(c.has(s.nodeName)||!customElements||customElements.get(s.tagName.toLowerCase())?l(s).includes(e):r&&typeof r=="object")?s[e]=r:i(s,e,r==null?r:String(r))}finally{f(t),n(a),o&&d(!0)}}function L(s){return s.__attributes??(s.__attributes={[E]:s.nodeName.includes("-"),[S]:s.namespaceURI===g})}var c=new Map;function l(s){var e=c.get(s.nodeName);if(e)return e;c.set(s.nodeName,e=[]);for(var r,t=s,a=Element.prototype;a!==t;){r=m(t);for(var o in r)r[o].set&&e.push(o);t=h(t)}return e}export{i as a,T as r,k as s};
