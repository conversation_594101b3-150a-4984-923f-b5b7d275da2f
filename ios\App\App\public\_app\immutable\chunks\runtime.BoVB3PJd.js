const jn=!1;var Bn=Array.isArray,tn=Array.prototype.indexOf,Un=Array.from,Vn=Object.defineProperty,ht=Object.getOwnPropertyDescriptor,nn=Object.getOwnPropertyDescriptors,Gn=Object.prototype,Kn=Array.prototype,rn=Object.getPrototypeOf;const $n=()=>{};function Zn(t){return t()}function mt(t){for(var n=0;n<t.length;n++)t[n]()}const y=2,Tt=4,j=8,ot=16,T=32,X=64,rt=128,I=256,K=512,d=1024,R=2048,B=4096,C=8192,b=16384,en=32768,gt=65536,zn=1<<17,ln=1<<19,At=1<<20,dt=Symbol("$state"),Jn=Symbol("legacy props"),Wn=Symbol("");function xt(t){return t===this.v}function sn(t,n){return t!=t?n==n:t!==n||t!==null&&typeof t=="object"||typeof t=="function"}function Xn(t,n){return t!==n}function kt(t){return!sn(t,this.v)}function an(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function un(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function on(t){throw new Error("https://svelte.dev/e/effect_orphan")}function fn(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function Qn(){throw new Error("https://svelte.dev/e/hydration_failed")}function tr(t){throw new Error("https://svelte.dev/e/props_invalid_value")}function nr(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function rr(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function _n(){throw new Error("https://svelte.dev/e/state_unsafe_local_read")}function cn(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}let Q=!1;function er(){Q=!0}const lr=1,sr=2,ar=16,ur=1,or=2,ir=4,fr=8,_r=16,cr=1,vr=2,vn="[",pn="[!",hn="]",Rt={},pr=Symbol();function it(t,n){var r={f:0,v:t,reactions:null,equals:xt,rv:0,wv:0};return r}function hr(t){return Dt(it(t))}function dn(t,n=!1){var e;const r=it(t);return n||(r.equals=kt),Q&&o!==null&&o.l!==null&&((e=o.l).s??(e.s=[])).push(r),r}function dr(t,n=!1){return Dt(dn(t,n))}function Dt(t){return i!==null&&i.f&y&&(m===null?On([t]):m.push(t)),t}function En(t,n){return i!==null&&vt()&&i.f&(y|ot)&&(m===null||!m.includes(t))&&cn(),yn(t,n)}function yn(t,n){return t.equals(n)||(t.v,t.v=n,t.wv=$t(),It(t,R),vt()&&u!==null&&u.f&d&&!(u.f&T)&&(h!==null&&h.includes(t)?(w(u,R),nt(u)):A===null?Cn([t]):A.push(t))),n}function It(t,n){var r=t.reactions;if(r!==null)for(var e=vt(),l=r.length,s=0;s<l;s++){var a=r[s],f=a.f;f&R||!e&&a===u||(w(a,n),f&(d|I)&&(f&y?It(a,B):nt(a)))}}function St(t){console.warn("https://svelte.dev/e/hydration_mismatch")}let N=!1;function Er(t){N=t}let g;function M(t){if(t===null)throw St(),Rt;return g=t}function yr(){return M(q(g))}function wr(t){if(N){if(q(g)!==null)throw St(),Rt;g=t}}function mr(){for(var t=0,n=g;;){if(n.nodeType===8){var r=n.data;if(r===hn){if(t===0)return n;t-=1}else(r===vn||r===pn)&&(t+=1)}var e=q(n);n.remove(),n=e}}var Et,Ot,Ct;function Tr(){if(Et===void 0){Et=window;var t=Element.prototype,n=Node.prototype;Ot=ht(n,"firstChild").get,Ct=ht(n,"nextSibling").get,t.__click=void 0,t.__className="",t.__attributes=null,t.__styles=null,t.__e=void 0,Text.prototype.__t=void 0}}function et(t=""){return document.createTextNode(t)}function lt(t){return Ot.call(t)}function q(t){return Ct.call(t)}function gr(t,n){if(!N)return lt(t);var r=lt(g);if(r===null)r=g.appendChild(et());else if(n&&r.nodeType!==3){var e=et();return r==null||r.before(e),M(e),e}return M(r),r}function Ar(t,n){if(!N){var r=lt(t);return r instanceof Comment&&r.data===""?q(r):r}return g}function xr(t,n=1,r=!1){let e=N?g:t;for(var l;n--;)l=e,e=q(e);if(!N)return e;var s=e==null?void 0:e.nodeType;if(r&&s!==3){var a=et();return e===null?l==null||l.after(a):e.before(a),M(a),a}return M(e),e}function kr(t){t.textContent=""}function wn(t){var n=y|R;u===null?n|=I:u.f|=At;var r=i!==null&&i.f&y?i:null;const e={children:null,ctx:o,deps:null,equals:xt,f:n,fn:t,reactions:null,rv:0,v:null,wv:0,parent:r??u};return r!==null&&(r.children??(r.children=[])).push(e),e}function Rr(t){const n=wn(t);return n.equals=kt,n}function Nt(t){var n=t.children;if(n!==null){t.children=null;for(var r=0;r<n.length;r+=1){var e=n[r];e.f&y?ft(e):D(e)}}}function mn(t){for(var n=t.parent;n!==null;){if(!(n.f&y))return n;n=n.parent}return null}function bt(t){var n,r=u;J(mn(t));try{Nt(t),n=Zt(t)}finally{J(r)}return n}function qt(t){var n=bt(t),r=(k||t.f&I)&&t.deps!==null?B:d;w(t,r),t.equals(n)||(t.v=n,t.wv=$t())}function ft(t){Nt(t),H(t,0),w(t,b),t.v=t.children=t.deps=t.ctx=t.reactions=null}function Pt(t){u===null&&i===null&&on(),i!==null&&i.f&I&&un(),ct&&an()}function Tn(t,n){var r=n.last;r===null?n.last=n.first=t:(r.next=t,t.prev=r,n.last=t)}function P(t,n,r,e=!0){var l=(t&X)!==0,s=u,a={ctx:o,deps:null,deriveds:null,nodes_start:null,nodes_end:null,f:t|R,first:null,fn:n,last:null,next:null,parent:l?null:s,prev:null,teardown:null,transitions:null,wv:0};if(r){var f=S;try{yt(!0),U(a),a.f|=en}catch(c){throw D(a),c}finally{yt(f)}}else n!==null&&nt(a);var _=r&&a.deps===null&&a.first===null&&a.nodes_start===null&&a.teardown===null&&(a.f&At)===0;if(!_&&!l&&e&&(s!==null&&Tn(a,s),i!==null&&i.f&y)){var p=i;(p.children??(p.children=[])).push(a)}return a}function Dr(t){const n=P(j,null,!1);return w(n,d),n.teardown=t,n}function Ir(t){Pt();var n=u!==null&&(u.f&T)!==0&&o!==null&&!o.m;if(n){var r=o;(r.e??(r.e=[])).push({fn:t,effect:u,reaction:i})}else{var e=Ft(t);return e}}function Sr(t){return Pt(),_t(t)}function Or(t){const n=P(X,t,!0);return(r={})=>new Promise(e=>{r.outro?xn(n,()=>{D(n),e(void 0)}):(D(n),e(void 0))})}function Ft(t){return P(Tt,t,!1)}function Cr(t,n){var r=o,e={effect:null,ran:!1};r.l.r1.push(e),e.effect=_t(()=>{t(),!e.ran&&(e.ran=!0,En(r.l.r2,!0),Ln(n))})}function Nr(){var t=o;_t(()=>{if(Mn(t.l.r2)){for(var n of t.l.r1){var r=n.effect;r.f&d&&w(r,B),F(r)&&U(r),n.ran=!1}t.l.r2.v=!1}})}function _t(t){return P(j,t,!0)}function br(t){return gn(t)}function gn(t,n=0){return P(j|ot|n,t,!0)}function qr(t,n=!0){return P(j|T,t,!0,n)}function Mt(t){var n=t.teardown;if(n!==null){const r=ct,e=i;wt(!0),z(null);try{n.call(null)}finally{wt(r),z(e)}}}function Lt(t){var n=t.deriveds;if(n!==null){t.deriveds=null;for(var r=0;r<n.length;r+=1)ft(n[r])}}function Yt(t,n=!1){var r=t.first;for(t.first=t.last=null;r!==null;){var e=r.next;D(r,n),r=e}}function An(t){for(var n=t.first;n!==null;){var r=n.next;n.f&T||D(n),n=r}}function D(t,n=!0){var r=!1;if((n||t.f&ln)&&t.nodes_start!==null){for(var e=t.nodes_start,l=t.nodes_end;e!==null;){var s=e===l?null:q(e);e.remove(),e=s}r=!0}Yt(t,n&&!r),Lt(t),H(t,0),w(t,b);var a=t.transitions;if(a!==null)for(const _ of a)_.stop();Mt(t);var f=t.parent;f!==null&&f.first!==null&&Ht(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=null}function Ht(t){var n=t.parent,r=t.prev,e=t.next;r!==null&&(r.next=e),e!==null&&(e.prev=r),n!==null&&(n.first===t&&(n.first=e),n.last===t&&(n.last=r))}function xn(t,n){var r=[];jt(t,r,!0),kn(r,()=>{D(t),n&&n()})}function kn(t,n){var r=t.length;if(r>0){var e=()=>--r||n();for(var l of t)l.out(e)}else n()}function jt(t,n,r){if(!(t.f&C)){if(t.f^=C,t.transitions!==null)for(const a of t.transitions)(a.is_global||r)&&n.push(a);for(var e=t.first;e!==null;){var l=e.next,s=(e.f&gt)!==0||(e.f&T)!==0;jt(e,n,s?r:!1),e=l}}}function Pr(t){Bt(t,!0)}function Bt(t,n){if(t.f&C){F(t)&&U(t),t.f^=C;for(var r=t.first;r!==null;){var e=r.next,l=(r.f&gt)!==0||(r.f&T)!==0;Bt(r,l?n:!1),r=e}if(t.transitions!==null)for(const s of t.transitions)(s.is_global||n)&&s.in()}}const Rn=typeof requestIdleCallback>"u"?t=>setTimeout(t,1):requestIdleCallback;let $=!1,Z=!1,st=[],at=[];function Ut(){$=!1;const t=st.slice();st=[],mt(t)}function Vt(){Z=!1;const t=at.slice();at=[],mt(t)}function Fr(t){$||($=!0,queueMicrotask(Ut)),st.push(t)}function Mr(t){Z||(Z=!0,Rn(Vt)),at.push(t)}function Dn(){$&&Ut(),Z&&Vt()}function In(t){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}const Gt=0,Sn=1;let V=!1,G=Gt,L=!1,Y=null,S=!1,ct=!1;function yt(t){S=t}function wt(t){ct=t}let x=[],O=0;let i=null;function z(t){i=t}let u=null;function J(t){u=t}let m=null;function On(t){m=t}let h=null,E=0,A=null;function Cn(t){A=t}let Kt=1,W=0,k=!1,o=null;function $t(){return++Kt}function vt(){return!Q||o!==null&&o.l===null}function F(t){var p;var n=t.f;if(n&R)return!0;if(n&B){var r=t.deps,e=(n&I)!==0;if(r!==null){var l,s,a=(n&K)!==0,f=e&&u!==null&&!k,_=r.length;if(a||f){for(l=0;l<_;l++)s=r[l],(a||!((p=s==null?void 0:s.reactions)!=null&&p.includes(t)))&&(s.reactions??(s.reactions=[])).push(t);a&&(t.f^=K)}for(l=0;l<_;l++)if(s=r[l],F(s)&&qt(s),s.wv>t.wv)return!0}(!e||u!==null&&!k)&&w(t,d)}return!1}function Nn(t,n){for(var r=n;r!==null;){if(r.f&rt)try{r.fn(t);return}catch{r.f^=rt}r=r.parent}throw V=!1,t}function bn(t){return(t.f&b)===0&&(t.parent===null||(t.parent.f&rt)===0)}function tt(t,n,r,e){if(V){if(r===null&&(V=!1),bn(n))throw t;return}r!==null&&(V=!0);{Nn(t,n);return}}function Zt(t){var pt;var n=h,r=E,e=A,l=i,s=k,a=m,f=o,_=t.f;h=null,E=0,A=null,i=_&(T|X)?null:t,k=!S&&(_&I)!==0,m=null,o=t.ctx,W++;try{var p=(0,t.fn)(),c=t.deps;if(h!==null){var v;if(H(t,E),c!==null&&E>0)for(c.length=E+h.length,v=0;v<h.length;v++)c[E+v]=h[v];else t.deps=c=h;if(!k)for(v=E;v<c.length;v++)((pt=c[v]).reactions??(pt.reactions=[])).push(t)}else c!==null&&E<c.length&&(H(t,E),c.length=E);return l!==null&&W++,p}finally{h=n,E=r,A=e,i=l,k=s,m=a,o=f}}function qn(t,n){let r=n.reactions;if(r!==null){var e=tn.call(r,t);if(e!==-1){var l=r.length-1;l===0?r=n.reactions=null:(r[e]=r[l],r.pop())}}r===null&&n.f&y&&(h===null||!h.includes(n))&&(w(n,B),n.f&(I|K)||(n.f^=K),H(n,0))}function H(t,n){var r=t.deps;if(r!==null)for(var e=n;e<r.length;e++)qn(t,r[e])}function U(t){var n=t.f;if(!(n&b)){w(t,d);var r=u,e=o;u=t;try{n&ot?An(t):Yt(t),Lt(t),Mt(t);var l=Zt(t);t.teardown=typeof l=="function"?l:null,t.wv=Kt;var s=t.deps,a}catch(f){tt(f,t,r,e||t.ctx)}finally{u=r}}}function zt(){if(O>1e3){O=0;try{fn()}catch(t){if(Y!==null)tt(t,Y,null);else throw t}}O++}function Jt(t){var n=t.length;if(n!==0){zt();var r=S;S=!0;try{for(var e=0;e<n;e++){var l=t[e];l.f&d||(l.f^=d);var s=[];Wt(l,s),Pn(s)}}finally{S=r}}}function Pn(t){var n=t.length;if(n!==0)for(var r=0;r<n;r++){var e=t[r];if(!(e.f&(b|C)))try{F(e)&&(U(e),e.deps===null&&e.first===null&&e.nodes_start===null&&(e.teardown===null?Ht(e):e.fn=null))}catch(l){tt(l,e,null,e.ctx)}}}function Fn(){if(L=!1,O>1001)return;const t=x;x=[],Jt(t),L||(O=0,Y=null)}function nt(t){G===Gt&&(L||(L=!0,queueMicrotask(Fn))),Y=t;for(var n=t;n.parent!==null;){n=n.parent;var r=n.f;if(r&(X|T)){if(!(r&d))return;n.f^=d}}x.push(n)}function Wt(t,n){var r=t.first,e=[];t:for(;r!==null;){var l=r.f,s=(l&T)!==0,a=s&&(l&d)!==0,f=r.next;if(!a&&!(l&C))if(l&j){if(s)r.f^=d;else try{F(r)&&U(r)}catch(v){tt(v,r,null,r.ctx)}var _=r.first;if(_!==null){r=_;continue}}else l&Tt&&e.push(r);if(f===null){let v=r.parent;for(;v!==null;){if(t===v)break t;var p=v.next;if(p!==null){r=p;continue t}v=v.parent}}r=f}for(var c=0;c<e.length;c++)_=e[c],n.push(_),Wt(_,n)}function Xt(t){var n=G,r=x;try{zt();const l=[];G=Sn,x=l,L=!1,Jt(r);var e=t==null?void 0:t();return Dn(),(x.length>0||l.length>0)&&Xt(),O=0,Y=null,e}finally{G=n,x=r}}async function Lr(){await Promise.resolve(),Xt()}function Mn(t){var c;var n=t.f,r=(n&y)!==0;if(r&&n&b){var e=bt(t);return ft(t),e}if(i!==null){m!==null&&m.includes(t)&&_n();var l=i.deps;t.rv<W&&(t.rv=W,h===null&&l!==null&&l[E]===t?E++:h===null?h=[t]:h.push(t),A!==null&&u!==null&&u.f&d&&!(u.f&T)&&A.includes(t)&&(w(u,R),nt(u)))}else if(r&&t.deps===null)for(var s=t,a=s.parent,f=s;a!==null;)if(a.f&y){var _=a;f=_,a=_.parent}else{var p=a;(c=p.deriveds)!=null&&c.includes(f)||(p.deriveds??(p.deriveds=[])).push(f);break}return r&&(s=t,F(s)&&qt(s)),t.v}function Ln(t){const n=i;try{return i=null,t()}finally{i=n}}const Yn=-7169;function w(t,n){t.f=t.f&Yn|n}function Yr(t){return Qt().get(t)}function Hr(t,n){return Qt().set(t,n),n}function Qt(t){return o===null&&In(),o.c??(o.c=new Map(Hn(o)||void 0))}function Hn(t){let n=t.p;for(;n!==null;){const r=n.c;if(r!==null)return r;n=n.p}return null}function jr(t,n=!1,r){o={p:o,c:null,e:null,m:!1,s:t,x:null,l:null},Q&&!n&&(o.l={s:null,u:null,r1:[],r2:it(!1)})}function Br(t){const n=o;if(n!==null){const a=n.e;if(a!==null){var r=u,e=i;n.e=null;try{for(var l=0;l<a.length;l++){var s=a[l];J(s.effect),z(s.reaction),Ft(s.fn)}}finally{J(r),z(e)}}o=n.p,n.m=!0}return{}}function Ur(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if(dt in t)ut(t);else if(!Array.isArray(t))for(let n in t){const r=t[n];typeof r=="object"&&r&&dt in r&&ut(r)}}}function ut(t,n=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!n.has(t)){n.add(t),t instanceof Date&&t.getTime();for(let e in t)try{ut(t[e],n)}catch{}const r=rn(t);if(r!==Object.prototype&&r!==Array.prototype&&r!==Map.prototype&&r!==Set.prototype&&r!==Date.prototype){const e=nn(r);for(let l in e){const s=e[l].get;if(s)try{s.call(t)}catch{}}}}}export{Mr as $,Pr as A,qr as B,xn as C,g as D,gt as E,Gn as F,Kn as G,pn as H,it as I,nr as J,En as K,ht as L,u as M,rr as N,rn as O,Bn as P,et as Q,ln as R,dt as S,vn as T,pr as U,q as V,lt as W,In as X,Q as Y,Hr as Z,Yr as _,Br as a,z as a0,J as a1,Wn as a2,i as a3,nn as a4,Dr as a5,dn as a6,$n as a7,tr as a8,zn as a9,sn as aA,C as aB,yn as aC,jt as aD,kn as aE,D as aF,sr as aG,lr as aH,ar as aI,jn as aJ,Cr as aK,Nr as aL,dr as aM,ir as aa,kt as ab,T as ac,X as ad,ur as ae,or as af,fr as ag,Jn as ah,Rr as ai,_r as aj,hr as ak,vt as al,Vn as am,Tr as an,Rt as ao,hn as ap,St as aq,Qn as ar,kr as as,Un as at,Or as au,cr as av,vr as aw,Xt as ax,Lr as ay,Xn as az,Ir as b,gr as c,o as d,Ln as e,Ar as f,Zn as g,mt as h,Mn as i,Ur as j,wn as k,er as l,Ft as m,_t as n,gn as o,jr as p,Fr as q,wr as r,xr as s,br as t,Sr as u,N as v,yr as w,mr as x,M as y,Er as z};
