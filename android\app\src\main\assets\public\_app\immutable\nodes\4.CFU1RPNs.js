import"../chunks/CWj6FrbW.js";import{a as L}from"../chunks/B8_8MPyK.js";import{c as C,r as y,p as j,d as E,b as O,K as _,f as P,a as R,g as r,u as n,e as U,s as z}from"../chunks/DvS_9Yw_.js";import{f as D,a as c,c as S}from"../chunks/Ckyppc5t.js";import{i as B}from"../chunks/DrBokXpg.js";import{a as I,s as t}from"../chunks/CZYSyPBs.js";import{s as T}from"../chunks/PoYD5o0_.js";import{b as H,c as M,d as p,s as k}from"../chunks/DdbRmKj_.js";import{g as b}from"../chunks/7czifHHs.js";import{S as V,T as Y}from"../chunks/Ce5yDgIc.js";import{P as q}from"../chunks/BeWv36fu.js";import{i as F}from"../chunks/CQqdknCc.js";var G=D("<page-scroller><!></page-scroller>",2);function J(m,o){var s=G();T(s,1,"svelte-l5h9uv");var i=C(s);I(i,()=>o.children),y(s),c(m,s)}var N=D("<stream-dashboard><!> <!></stream-dashboard>",2);function ce(m,o){j(o,!0);const s={details:"Details",listings:"Listings",record:"Record"};let i=E(O(s.details));_(()=>{switch(r(i)){case s.details:b("/livestream/details");break;case s.listings:b("/livestream/listings");break;case s.record:b("/livestream/record");break}});const d=n(()=>k().id);let f=n(()=>t.user===null?null:{id:t.user.streamioAuth.id,name:t.user.name,image:`https://getstream.io/random_svg/?id=${t.user.streamioAuth.id}&name=${t.user.name}`});const g=n(()=>k().callData);_(()=>{(async()=>{if(r(d)===null||t.user===null||r(f)===null)return;const e=V.getOrCreateInstance({apiKey:q,token:t.user.streamioAuth.token,user:r(f)}).call("livestream",r(d));M({call:e,nParticipants:0,localParticipant:null,started:!1}),await e.join();try{await Promise.all([e.camera.enable(),e.microphone.enable()])}catch(a){alert(`Camera is inaccessible or permission was denied: ${a}`);return}e.state.localParticipant$.subscribe(a=>{a&&(p({localParticipant:a}),F({streamId:r(d),sessionId:a.sessionId}))}),e.state.participantCount$.subscribe(a=>{p({nParticipants:a})}),e.state.backstage$.subscribe(a=>{p({started:!a})})})()}),L(()=>{r(g)!==null&&r(g).call.leave()}),H();var v=S(),$=P(v);{var A=l=>{var e=N();T(e,1,"svelte-8yzfr2");var a=C(e);const w=n(()=>Object.values(s));Y(a,{get currentLabel(){return r(i)},get labels(){return r(w)},onClickTab:u=>{U(i,u,!0)}});var x=z(a,2);J(x,{children:(u,Q)=>{var h=S(),K=P(h);I(K,()=>o.children),c(u,h)}}),y(e),c(l,e)};B($,l=>{t.isSeller&&t.user!==null&&l(A)})}c(m,v),R()}export{ce as component};
