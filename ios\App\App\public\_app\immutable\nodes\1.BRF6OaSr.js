import{a as h,t as l}from"../chunks/disclose-version.DD3_NYGK.js";import"../chunks/legacy.BXZnuAlm.js";import{p as v,f as u,t as g,a as x,c as e,r as p,s as _}from"../chunks/runtime.BoVB3PJd.js";import{s as o}from"../chunks/render.Dmit0_6o.js";import{i as d}from"../chunks/lifecycle.B3mXKHkP.js";import{p as m}from"../chunks/index.D8JMZMVP.js";var b=l("<h1> </h1> <p> </p>",1);function z(i,f){v(f,!1),d();var r=b(),t=u(r),n=e(t,!0);p(t);var a=_(t,2),c=e(a,!0);p(a),g(()=>{var s;o(n,m.status),o(c,(s=m.error)==null?void 0:s.message)}),h(i,r),x()}export{z as component};
