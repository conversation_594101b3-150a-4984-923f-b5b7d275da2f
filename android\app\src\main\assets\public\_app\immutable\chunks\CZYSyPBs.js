var n=Object.defineProperty;var l=(s,e,r)=>e in s?n(s,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):s[e]=r;var o=(s,e,r)=>l(s,typeof e!="symbol"?e+"":e,r);import{k as c,ai as p,y as h,aB as u,a9 as f,h as v,A as m,b as y}from"./DvS_9Yw_.js";function _(s,e,...r){var t=s,i=u,a;c(()=>{i!==(i=e())&&(a&&(f(a),a=null),a=h(()=>i(t,...r)))},p),v&&(t=m)}class b{constructor(){o(this,"promise");o(this,"resolve");o(this,"reject");const{promise:e,resolve:r,reject:t}=Promise.withResolvers();this.promise=e,this.resolve=r,this.reject=t}async then(e){e(await this.promise)}}const d=()=>new b,j=y({user:null,get isSeller(){return this.user!==null&&this.user.canSell},buildType:d()});export{_ as a,j as s};
