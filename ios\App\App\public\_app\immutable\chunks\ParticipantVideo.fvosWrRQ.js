import{g as xo,c as Lo}from"./_commonjsHelpers.BosuxZz1.js";import{a as Uo,t as Mo}from"./disclose-version.DD3_NYGK.js";import{p as Fo,i as Vt,K as bi,a as jo,ak as yi}from"./runtime.BoVB3PJd.js";import{p as Vo}from"./proxy.Dxp3JHm7.js";import{b as $o}from"./this.BW1W9Pn3.js";import{o as Bo,a as qo}from"./index-client.CMvqoH0Y.js";function Ho(t,e){for(var n=0;n<e.length;n++){const s=e[n];if(typeof s!="string"&&!Array.isArray(s)){for(const i in s)if(i!=="default"&&!(i in t)){const r=Object.getOwnPropertyDescriptor(s,i);r&&Object.defineProperty(t,i,r.get?r:{enumerable:!0,get:()=>s[i]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}let br=!0,yr=!0;function Yt(t,e,n){const s=t.match(e);return s&&s.length>=n&&parseInt(s[n],10)}function He(t,e,n){if(!t.RTCPeerConnection)return;const s=t.RTCPeerConnection.prototype,i=s.addEventListener;s.addEventListener=function(a,o){if(a!==e)return i.apply(this,arguments);const d=c=>{const l=n(c);l&&(o.handleEvent?o.handleEvent(l):o(l))};return this._eventMap=this._eventMap||{},this._eventMap[e]||(this._eventMap[e]=new Map),this._eventMap[e].set(o,d),i.apply(this,[a,d])};const r=s.removeEventListener;s.removeEventListener=function(a,o){if(a!==e||!this._eventMap||!this._eventMap[e])return r.apply(this,arguments);if(!this._eventMap[e].has(o))return r.apply(this,arguments);const d=this._eventMap[e].get(o);return this._eventMap[e].delete(o),this._eventMap[e].size===0&&delete this._eventMap[e],Object.keys(this._eventMap).length===0&&delete this._eventMap,r.apply(this,[a,d])},Object.defineProperty(s,"on"+e,{get(){return this["_on"+e]},set(a){this["_on"+e]&&(this.removeEventListener(e,this["_on"+e]),delete this["_on"+e]),a&&this.addEventListener(e,this["_on"+e]=a)},enumerable:!0,configurable:!0})}function Go(t){return typeof t!="boolean"?new Error("Argument type: "+typeof t+". Please use a boolean."):(br=t,t?"adapter.js logging disabled":"adapter.js logging enabled")}function Wo(t){return typeof t!="boolean"?new Error("Argument type: "+typeof t+". Please use a boolean."):(yr=!t,"adapter.js deprecation warnings "+(t?"disabled":"enabled"))}function Sr(){if(typeof window=="object"){if(br)return;typeof console<"u"&&typeof console.log=="function"&&console.log.apply(console,arguments)}}function Ms(t,e){yr&&console.warn(t+" is deprecated, please use "+e+" instead.")}function Jo(t){const e={browser:null,version:null};if(typeof t>"u"||!t.navigator||!t.navigator.userAgent)return e.browser="Not a browser.",e;const{navigator:n}=t;if(n.mozGetUserMedia)e.browser="firefox",e.version=Yt(n.userAgent,/Firefox\/(\d+)\./,1);else if(n.webkitGetUserMedia||t.isSecureContext===!1&&t.webkitRTCPeerConnection)e.browser="chrome",e.version=Yt(n.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else if(t.RTCPeerConnection&&n.userAgent.match(/AppleWebKit\/(\d+)\./))e.browser="safari",e.version=Yt(n.userAgent,/AppleWebKit\/(\d+)\./,1),e.supportsUnifiedPlan=t.RTCRtpTransceiver&&"currentDirection"in t.RTCRtpTransceiver.prototype;else return e.browser="Not a supported browser.",e;return e}function Si(t){return Object.prototype.toString.call(t)==="[object Object]"}function Cr(t){return Si(t)?Object.keys(t).reduce(function(e,n){const s=Si(t[n]),i=s?Cr(t[n]):t[n],r=s&&!Object.keys(i).length;return i===void 0||r?e:Object.assign(e,{[n]:i})},{}):t}function ds(t,e,n){!e||n.has(e.id)||(n.set(e.id,e),Object.keys(e).forEach(s=>{s.endsWith("Id")?ds(t,t.get(e[s]),n):s.endsWith("Ids")&&e[s].forEach(i=>{ds(t,t.get(i),n)})}))}function Ci(t,e,n){const s=n?"outbound-rtp":"inbound-rtp",i=new Map;if(e===null)return i;const r=[];return t.forEach(a=>{a.type==="track"&&a.trackIdentifier===e.id&&r.push(a)}),r.forEach(a=>{t.forEach(o=>{o.type===s&&o.trackId===a.id&&ds(t,o,i)})}),i}const vi=Sr;function vr(t,e){const n=t&&t.navigator;if(!n.mediaDevices)return;const s=function(o){if(typeof o!="object"||o.mandatory||o.optional)return o;const d={};return Object.keys(o).forEach(c=>{if(c==="require"||c==="advanced"||c==="mediaSource")return;const l=typeof o[c]=="object"?o[c]:{ideal:o[c]};l.exact!==void 0&&typeof l.exact=="number"&&(l.min=l.max=l.exact);const h=function(u,f){return u?u+f.charAt(0).toUpperCase()+f.slice(1):f==="deviceId"?"sourceId":f};if(l.ideal!==void 0){d.optional=d.optional||[];let u={};typeof l.ideal=="number"?(u[h("min",c)]=l.ideal,d.optional.push(u),u={},u[h("max",c)]=l.ideal,d.optional.push(u)):(u[h("",c)]=l.ideal,d.optional.push(u))}l.exact!==void 0&&typeof l.exact!="number"?(d.mandatory=d.mandatory||{},d.mandatory[h("",c)]=l.exact):["min","max"].forEach(u=>{l[u]!==void 0&&(d.mandatory=d.mandatory||{},d.mandatory[h(u,c)]=l[u])})}),o.advanced&&(d.optional=(d.optional||[]).concat(o.advanced)),d},i=function(o,d){if(e.version>=61)return d(o);if(o=JSON.parse(JSON.stringify(o)),o&&typeof o.audio=="object"){const c=function(l,h,u){h in l&&!(u in l)&&(l[u]=l[h],delete l[h])};o=JSON.parse(JSON.stringify(o)),c(o.audio,"autoGainControl","googAutoGainControl"),c(o.audio,"noiseSuppression","googNoiseSuppression"),o.audio=s(o.audio)}if(o&&typeof o.video=="object"){let c=o.video.facingMode;c=c&&(typeof c=="object"?c:{ideal:c});const l=e.version<66;if(c&&(c.exact==="user"||c.exact==="environment"||c.ideal==="user"||c.ideal==="environment")&&!(n.mediaDevices.getSupportedConstraints&&n.mediaDevices.getSupportedConstraints().facingMode&&!l)){delete o.video.facingMode;let h;if(c.exact==="environment"||c.ideal==="environment"?h=["back","rear"]:(c.exact==="user"||c.ideal==="user")&&(h=["front"]),h)return n.mediaDevices.enumerateDevices().then(u=>{u=u.filter(p=>p.kind==="videoinput");let f=u.find(p=>h.some(m=>p.label.toLowerCase().includes(m)));return!f&&u.length&&h.includes("back")&&(f=u[u.length-1]),f&&(o.video.deviceId=c.exact?{exact:f.deviceId}:{ideal:f.deviceId}),o.video=s(o.video),vi("chrome: "+JSON.stringify(o)),d(o)})}o.video=s(o.video)}return vi("chrome: "+JSON.stringify(o)),d(o)},r=function(o){return e.version>=64?o:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[o.name]||o.name,message:o.message,constraint:o.constraint||o.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}},a=function(o,d,c){i(o,l=>{n.webkitGetUserMedia(l,d,h=>{c&&c(r(h))})})};if(n.getUserMedia=a.bind(n),n.mediaDevices.getUserMedia){const o=n.mediaDevices.getUserMedia.bind(n.mediaDevices);n.mediaDevices.getUserMedia=function(d){return i(d,c=>o(c).then(l=>{if(c.audio&&!l.getAudioTracks().length||c.video&&!l.getVideoTracks().length)throw l.getTracks().forEach(h=>{h.stop()}),new DOMException("","NotFoundError");return l},l=>Promise.reject(r(l))))}}}function Ko(t,e){if(!(t.navigator.mediaDevices&&"getDisplayMedia"in t.navigator.mediaDevices)&&t.navigator.mediaDevices){if(typeof e!="function"){console.error("shimGetDisplayMedia: getSourceId argument is not a function");return}t.navigator.mediaDevices.getDisplayMedia=function(s){return e(s).then(i=>{const r=s.video&&s.video.width,a=s.video&&s.video.height,o=s.video&&s.video.frameRate;return s.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:i,maxFrameRate:o||3}},r&&(s.video.mandatory.maxWidth=r),a&&(s.video.mandatory.maxHeight=a),t.navigator.mediaDevices.getUserMedia(s)})}}}function Tr(t){t.MediaStream=t.MediaStream||t.webkitMediaStream}function wr(t){if(typeof t=="object"&&t.RTCPeerConnection&&!("ontrack"in t.RTCPeerConnection.prototype)){Object.defineProperty(t.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(n){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=n)},enumerable:!0,configurable:!0});const e=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=s=>{s.stream.addEventListener("addtrack",i=>{let r;t.RTCPeerConnection.prototype.getReceivers?r=this.getReceivers().find(o=>o.track&&o.track.id===i.track.id):r={track:i.track};const a=new Event("track");a.track=i.track,a.receiver=r,a.transceiver={receiver:r},a.streams=[s.stream],this.dispatchEvent(a)}),s.stream.getTracks().forEach(i=>{let r;t.RTCPeerConnection.prototype.getReceivers?r=this.getReceivers().find(o=>o.track&&o.track.id===i.id):r={track:i};const a=new Event("track");a.track=i,a.receiver=r,a.transceiver={receiver:r},a.streams=[s.stream],this.dispatchEvent(a)})},this.addEventListener("addstream",this._ontrackpoly)),e.apply(this,arguments)}}else He(t,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e))}function Er(t){if(typeof t=="object"&&t.RTCPeerConnection&&!("getSenders"in t.RTCPeerConnection.prototype)&&"createDTMFSender"in t.RTCPeerConnection.prototype){const e=function(i,r){return{track:r,get dtmf(){return this._dtmf===void 0&&(r.kind==="audio"?this._dtmf=i.createDTMFSender(r):this._dtmf=null),this._dtmf},_pc:i}};if(!t.RTCPeerConnection.prototype.getSenders){t.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const i=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addTrack=function(o,d){let c=i.apply(this,arguments);return c||(c=e(this,o),this._senders.push(c)),c};const r=t.RTCPeerConnection.prototype.removeTrack;t.RTCPeerConnection.prototype.removeTrack=function(o){r.apply(this,arguments);const d=this._senders.indexOf(o);d!==-1&&this._senders.splice(d,1)}}const n=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(r){this._senders=this._senders||[],n.apply(this,[r]),r.getTracks().forEach(a=>{this._senders.push(e(this,a))})};const s=t.RTCPeerConnection.prototype.removeStream;t.RTCPeerConnection.prototype.removeStream=function(r){this._senders=this._senders||[],s.apply(this,[r]),r.getTracks().forEach(a=>{const o=this._senders.find(d=>d.track===a);o&&this._senders.splice(this._senders.indexOf(o),1)})}}else if(typeof t=="object"&&t.RTCPeerConnection&&"getSenders"in t.RTCPeerConnection.prototype&&"createDTMFSender"in t.RTCPeerConnection.prototype&&t.RTCRtpSender&&!("dtmf"in t.RTCRtpSender.prototype)){const e=t.RTCPeerConnection.prototype.getSenders;t.RTCPeerConnection.prototype.getSenders=function(){const s=e.apply(this,[]);return s.forEach(i=>i._pc=this),s},Object.defineProperty(t.RTCRtpSender.prototype,"dtmf",{get(){return this._dtmf===void 0&&(this.track.kind==="audio"?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function kr(t){if(!t.RTCPeerConnection)return;const e=t.RTCPeerConnection.prototype.getStats;t.RTCPeerConnection.prototype.getStats=function(){const[s,i,r]=arguments;if(arguments.length>0&&typeof s=="function")return e.apply(this,arguments);if(e.length===0&&(arguments.length===0||typeof s!="function"))return e.apply(this,[]);const a=function(d){const c={};return d.result().forEach(h=>{const u={id:h.id,timestamp:h.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[h.type]||h.type};h.names().forEach(f=>{u[f]=h.stat(f)}),c[u.id]=u}),c},o=function(d){return new Map(Object.keys(d).map(c=>[c,d[c]]))};if(arguments.length>=2){const d=function(c){i(o(a(c)))};return e.apply(this,[d,s])}return new Promise((d,c)=>{e.apply(this,[function(l){d(o(a(l)))},c])}).then(i,r)}}function Rr(t){if(!(typeof t=="object"&&t.RTCPeerConnection&&t.RTCRtpSender&&t.RTCRtpReceiver))return;if(!("getStats"in t.RTCRtpSender.prototype)){const n=t.RTCPeerConnection.prototype.getSenders;n&&(t.RTCPeerConnection.prototype.getSenders=function(){const r=n.apply(this,[]);return r.forEach(a=>a._pc=this),r});const s=t.RTCPeerConnection.prototype.addTrack;s&&(t.RTCPeerConnection.prototype.addTrack=function(){const r=s.apply(this,arguments);return r._pc=this,r}),t.RTCRtpSender.prototype.getStats=function(){const r=this;return this._pc.getStats().then(a=>Ci(a,r.track,!0))}}if(!("getStats"in t.RTCRtpReceiver.prototype)){const n=t.RTCPeerConnection.prototype.getReceivers;n&&(t.RTCPeerConnection.prototype.getReceivers=function(){const i=n.apply(this,[]);return i.forEach(r=>r._pc=this),i}),He(t,"track",s=>(s.receiver._pc=s.srcElement,s)),t.RTCRtpReceiver.prototype.getStats=function(){const i=this;return this._pc.getStats().then(r=>Ci(r,i.track,!1))}}if(!("getStats"in t.RTCRtpSender.prototype&&"getStats"in t.RTCRtpReceiver.prototype))return;const e=t.RTCPeerConnection.prototype.getStats;t.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof t.MediaStreamTrack){const s=arguments[0];let i,r,a;return this.getSenders().forEach(o=>{o.track===s&&(i?a=!0:i=o)}),this.getReceivers().forEach(o=>(o.track===s&&(r?a=!0:r=o),o.track===s)),a||i&&r?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):i?i.getStats():r?r.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return e.apply(this,arguments)}}function Ir(t){t.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(a=>this._shimmedLocalStreams[a][0])};const e=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addTrack=function(a,o){if(!o)return e.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const d=e.apply(this,arguments);return this._shimmedLocalStreams[o.id]?this._shimmedLocalStreams[o.id].indexOf(d)===-1&&this._shimmedLocalStreams[o.id].push(d):this._shimmedLocalStreams[o.id]=[o,d],d};const n=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(a){this._shimmedLocalStreams=this._shimmedLocalStreams||{},a.getTracks().forEach(c=>{if(this.getSenders().find(h=>h.track===c))throw new DOMException("Track already exists.","InvalidAccessError")});const o=this.getSenders();n.apply(this,arguments);const d=this.getSenders().filter(c=>o.indexOf(c)===-1);this._shimmedLocalStreams[a.id]=[a].concat(d)};const s=t.RTCPeerConnection.prototype.removeStream;t.RTCPeerConnection.prototype.removeStream=function(a){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[a.id],s.apply(this,arguments)};const i=t.RTCPeerConnection.prototype.removeTrack;t.RTCPeerConnection.prototype.removeTrack=function(a){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},a&&Object.keys(this._shimmedLocalStreams).forEach(o=>{const d=this._shimmedLocalStreams[o].indexOf(a);d!==-1&&this._shimmedLocalStreams[o].splice(d,1),this._shimmedLocalStreams[o].length===1&&delete this._shimmedLocalStreams[o]}),i.apply(this,arguments)}}function _r(t,e){if(!t.RTCPeerConnection)return;if(t.RTCPeerConnection.prototype.addTrack&&e.version>=65)return Ir(t);const n=t.RTCPeerConnection.prototype.getLocalStreams;t.RTCPeerConnection.prototype.getLocalStreams=function(){const l=n.apply(this);return this._reverseStreams=this._reverseStreams||{},l.map(h=>this._reverseStreams[h.id])};const s=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(l){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},l.getTracks().forEach(h=>{if(this.getSenders().find(f=>f.track===h))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[l.id]){const h=new t.MediaStream(l.getTracks());this._streams[l.id]=h,this._reverseStreams[h.id]=l,l=h}s.apply(this,[l])};const i=t.RTCPeerConnection.prototype.removeStream;t.RTCPeerConnection.prototype.removeStream=function(l){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[l.id]||l]),delete this._reverseStreams[this._streams[l.id]?this._streams[l.id].id:l.id],delete this._streams[l.id]},t.RTCPeerConnection.prototype.addTrack=function(l,h){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const u=[].slice.call(arguments,1);if(u.length!==1||!u[0].getTracks().find(m=>m===l))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(m=>m.track===l))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const p=this._streams[h.id];if(p)p.addTrack(l),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const m=new t.MediaStream([l]);this._streams[h.id]=m,this._reverseStreams[m.id]=h,this.addStream(m)}return this.getSenders().find(m=>m.track===l)};function r(c,l){let h=l.sdp;return Object.keys(c._reverseStreams||[]).forEach(u=>{const f=c._reverseStreams[u],p=c._streams[f.id];h=h.replace(new RegExp(p.id,"g"),f.id)}),new RTCSessionDescription({type:l.type,sdp:h})}function a(c,l){let h=l.sdp;return Object.keys(c._reverseStreams||[]).forEach(u=>{const f=c._reverseStreams[u],p=c._streams[f.id];h=h.replace(new RegExp(f.id,"g"),p.id)}),new RTCSessionDescription({type:l.type,sdp:h})}["createOffer","createAnswer"].forEach(function(c){const l=t.RTCPeerConnection.prototype[c],h={[c](){const u=arguments;return arguments.length&&typeof arguments[0]=="function"?l.apply(this,[p=>{const m=r(this,p);u[0].apply(null,[m])},p=>{u[1]&&u[1].apply(null,p)},arguments[2]]):l.apply(this,arguments).then(p=>r(this,p))}};t.RTCPeerConnection.prototype[c]=h[c]});const o=t.RTCPeerConnection.prototype.setLocalDescription;t.RTCPeerConnection.prototype.setLocalDescription=function(){return!arguments.length||!arguments[0].type?o.apply(this,arguments):(arguments[0]=a(this,arguments[0]),o.apply(this,arguments))};const d=Object.getOwnPropertyDescriptor(t.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(t.RTCPeerConnection.prototype,"localDescription",{get(){const c=d.get.apply(this);return c.type===""?c:r(this,c)}}),t.RTCPeerConnection.prototype.removeTrack=function(l){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!l._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(l._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};let u;Object.keys(this._streams).forEach(f=>{this._streams[f].getTracks().find(m=>l.track===m)&&(u=this._streams[f])}),u&&(u.getTracks().length===1?this.removeStream(this._reverseStreams[u.id]):u.removeTrack(l.track),this.dispatchEvent(new Event("negotiationneeded")))}}function hs(t,e){!t.RTCPeerConnection&&t.webkitRTCPeerConnection&&(t.RTCPeerConnection=t.webkitRTCPeerConnection),t.RTCPeerConnection&&e.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(n){const s=t.RTCPeerConnection.prototype[n],i={[n](){return arguments[0]=new(n==="addIceCandidate"?t.RTCIceCandidate:t.RTCSessionDescription)(arguments[0]),s.apply(this,arguments)}};t.RTCPeerConnection.prototype[n]=i[n]})}function Pr(t,e){He(t,"negotiationneeded",n=>{const s=n.target;if(!((e.version<72||s.getConfiguration&&s.getConfiguration().sdpSemantics==="plan-b")&&s.signalingState!=="stable"))return n})}const Ti=Object.freeze(Object.defineProperty({__proto__:null,fixNegotiationNeeded:Pr,shimAddTrackRemoveTrack:_r,shimAddTrackRemoveTrackWithNative:Ir,shimGetDisplayMedia:Ko,shimGetSendersWithDtmf:Er,shimGetStats:kr,shimGetUserMedia:vr,shimMediaStream:Tr,shimOnTrack:wr,shimPeerConnection:hs,shimSenderReceiverGetStats:Rr},Symbol.toStringTag,{value:"Module"}));function Or(t,e){const n=t&&t.navigator,s=t&&t.MediaStreamTrack;if(n.getUserMedia=function(i,r,a){Ms("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),n.mediaDevices.getUserMedia(i).then(r,a)},!(e.version>55&&"autoGainControl"in n.mediaDevices.getSupportedConstraints())){const i=function(a,o,d){o in a&&!(d in a)&&(a[d]=a[o],delete a[o])},r=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(a){return typeof a=="object"&&typeof a.audio=="object"&&(a=JSON.parse(JSON.stringify(a)),i(a.audio,"autoGainControl","mozAutoGainControl"),i(a.audio,"noiseSuppression","mozNoiseSuppression")),r(a)},s&&s.prototype.getSettings){const a=s.prototype.getSettings;s.prototype.getSettings=function(){const o=a.apply(this,arguments);return i(o,"mozAutoGainControl","autoGainControl"),i(o,"mozNoiseSuppression","noiseSuppression"),o}}if(s&&s.prototype.applyConstraints){const a=s.prototype.applyConstraints;s.prototype.applyConstraints=function(o){return this.kind==="audio"&&typeof o=="object"&&(o=JSON.parse(JSON.stringify(o)),i(o,"autoGainControl","mozAutoGainControl"),i(o,"noiseSuppression","mozNoiseSuppression")),a.apply(this,[o])}}}}function zo(t,e){t.navigator.mediaDevices&&"getDisplayMedia"in t.navigator.mediaDevices||t.navigator.mediaDevices&&(t.navigator.mediaDevices.getDisplayMedia=function(s){if(!(s&&s.video)){const i=new DOMException("getDisplayMedia without video constraints is undefined");return i.name="NotFoundError",i.code=8,Promise.reject(i)}return s.video===!0?s.video={mediaSource:e}:s.video.mediaSource=e,t.navigator.mediaDevices.getUserMedia(s)})}function Ar(t){typeof t=="object"&&t.RTCTrackEvent&&"receiver"in t.RTCTrackEvent.prototype&&!("transceiver"in t.RTCTrackEvent.prototype)&&Object.defineProperty(t.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function fs(t,e){if(typeof t!="object"||!(t.RTCPeerConnection||t.mozRTCPeerConnection))return;!t.RTCPeerConnection&&t.mozRTCPeerConnection&&(t.RTCPeerConnection=t.mozRTCPeerConnection),e.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(i){const r=t.RTCPeerConnection.prototype[i],a={[i](){return arguments[0]=new(i==="addIceCandidate"?t.RTCIceCandidate:t.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};t.RTCPeerConnection.prototype[i]=a[i]});const n={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},s=t.RTCPeerConnection.prototype.getStats;t.RTCPeerConnection.prototype.getStats=function(){const[r,a,o]=arguments;return s.apply(this,[r||null]).then(d=>{if(e.version<53&&!a)try{d.forEach(c=>{c.type=n[c.type]||c.type})}catch(c){if(c.name!=="TypeError")throw c;d.forEach((l,h)=>{d.set(h,Object.assign({},l,{type:n[l.type]||l.type}))})}return d}).then(a,o)}}function Dr(t){if(!(typeof t=="object"&&t.RTCPeerConnection&&t.RTCRtpSender)||t.RTCRtpSender&&"getStats"in t.RTCRtpSender.prototype)return;const e=t.RTCPeerConnection.prototype.getSenders;e&&(t.RTCPeerConnection.prototype.getSenders=function(){const i=e.apply(this,[]);return i.forEach(r=>r._pc=this),i});const n=t.RTCPeerConnection.prototype.addTrack;n&&(t.RTCPeerConnection.prototype.addTrack=function(){const i=n.apply(this,arguments);return i._pc=this,i}),t.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function Nr(t){if(!(typeof t=="object"&&t.RTCPeerConnection&&t.RTCRtpSender)||t.RTCRtpSender&&"getStats"in t.RTCRtpReceiver.prototype)return;const e=t.RTCPeerConnection.prototype.getReceivers;e&&(t.RTCPeerConnection.prototype.getReceivers=function(){const s=e.apply(this,[]);return s.forEach(i=>i._pc=this),s}),He(t,"track",n=>(n.receiver._pc=n.srcElement,n)),t.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function xr(t){!t.RTCPeerConnection||"removeStream"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.removeStream=function(n){Ms("removeStream","removeTrack"),this.getSenders().forEach(s=>{s.track&&n.getTracks().includes(s.track)&&this.removeTrack(s)})})}function Lr(t){t.DataChannel&&!t.RTCDataChannel&&(t.RTCDataChannel=t.DataChannel)}function Ur(t){if(!(typeof t=="object"&&t.RTCPeerConnection))return;const e=t.RTCPeerConnection.prototype.addTransceiver;e&&(t.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let s=arguments[1]&&arguments[1].sendEncodings;s===void 0&&(s=[]),s=[...s];const i=s.length>0;i&&s.forEach(a=>{if("rid"in a&&!/^[a-z0-9]{0,16}$/i.test(a.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in a&&!(parseFloat(a.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in a&&!(parseFloat(a.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});const r=e.apply(this,arguments);if(i){const{sender:a}=r,o=a.getParameters();(!("encodings"in o)||o.encodings.length===1&&Object.keys(o.encodings[0]).length===0)&&(o.encodings=s,a.sendEncodings=s,this.setParametersPromises.push(a.setParameters(o).then(()=>{delete a.sendEncodings}).catch(()=>{delete a.sendEncodings})))}return r})}function Mr(t){if(!(typeof t=="object"&&t.RTCRtpSender))return;const e=t.RTCRtpSender.prototype.getParameters;e&&(t.RTCRtpSender.prototype.getParameters=function(){const s=e.apply(this,arguments);return"encodings"in s||(s.encodings=[].concat(this.sendEncodings||[{}])),s})}function Fr(t){if(!(typeof t=="object"&&t.RTCPeerConnection))return;const e=t.RTCPeerConnection.prototype.createOffer;t.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>e.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):e.apply(this,arguments)}}function jr(t){if(!(typeof t=="object"&&t.RTCPeerConnection))return;const e=t.RTCPeerConnection.prototype.createAnswer;t.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>e.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):e.apply(this,arguments)}}const wi=Object.freeze(Object.defineProperty({__proto__:null,shimAddTransceiver:Ur,shimCreateAnswer:jr,shimCreateOffer:Fr,shimGetDisplayMedia:zo,shimGetParameters:Mr,shimGetUserMedia:Or,shimOnTrack:Ar,shimPeerConnection:fs,shimRTCDataChannel:Lr,shimReceiverGetStats:Nr,shimRemoveStream:xr,shimSenderGetStats:Dr},Symbol.toStringTag,{value:"Module"}));function Vr(t){if(!(typeof t!="object"||!t.RTCPeerConnection)){if("getLocalStreams"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in t.RTCPeerConnection.prototype)){const e=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addStream=function(s){this._localStreams||(this._localStreams=[]),this._localStreams.includes(s)||this._localStreams.push(s),s.getAudioTracks().forEach(i=>e.call(this,i,s)),s.getVideoTracks().forEach(i=>e.call(this,i,s))},t.RTCPeerConnection.prototype.addTrack=function(s,...i){return i&&i.forEach(r=>{this._localStreams?this._localStreams.includes(r)||this._localStreams.push(r):this._localStreams=[r]}),e.apply(this,arguments)}}"removeStream"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.removeStream=function(n){this._localStreams||(this._localStreams=[]);const s=this._localStreams.indexOf(n);if(s===-1)return;this._localStreams.splice(s,1);const i=n.getTracks();this.getSenders().forEach(r=>{i.includes(r.track)&&this.removeTrack(r)})})}}function $r(t){if(!(typeof t!="object"||!t.RTCPeerConnection)&&("getRemoteStreams"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in t.RTCPeerConnection.prototype))){Object.defineProperty(t.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(n){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=n),this.addEventListener("track",this._onaddstreampoly=s=>{s.streams.forEach(i=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(i))return;this._remoteStreams.push(i);const r=new Event("addstream");r.stream=i,this.dispatchEvent(r)})})}});const e=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){const s=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(i){i.streams.forEach(r=>{if(s._remoteStreams||(s._remoteStreams=[]),s._remoteStreams.indexOf(r)>=0)return;s._remoteStreams.push(r);const a=new Event("addstream");a.stream=r,s.dispatchEvent(a)})}),e.apply(s,arguments)}}}function Br(t){if(typeof t!="object"||!t.RTCPeerConnection)return;const e=t.RTCPeerConnection.prototype,n=e.createOffer,s=e.createAnswer,i=e.setLocalDescription,r=e.setRemoteDescription,a=e.addIceCandidate;e.createOffer=function(c,l){const h=arguments.length>=2?arguments[2]:arguments[0],u=n.apply(this,[h]);return l?(u.then(c,l),Promise.resolve()):u},e.createAnswer=function(c,l){const h=arguments.length>=2?arguments[2]:arguments[0],u=s.apply(this,[h]);return l?(u.then(c,l),Promise.resolve()):u};let o=function(d,c,l){const h=i.apply(this,[d]);return l?(h.then(c,l),Promise.resolve()):h};e.setLocalDescription=o,o=function(d,c,l){const h=r.apply(this,[d]);return l?(h.then(c,l),Promise.resolve()):h},e.setRemoteDescription=o,o=function(d,c,l){const h=a.apply(this,[d]);return l?(h.then(c,l),Promise.resolve()):h},e.addIceCandidate=o}function qr(t){const e=t&&t.navigator;if(e.mediaDevices&&e.mediaDevices.getUserMedia){const n=e.mediaDevices,s=n.getUserMedia.bind(n);e.mediaDevices.getUserMedia=i=>s(Hr(i))}!e.getUserMedia&&e.mediaDevices&&e.mediaDevices.getUserMedia&&(e.getUserMedia=(function(s,i,r){e.mediaDevices.getUserMedia(s).then(i,r)}).bind(e))}function Hr(t){return t&&t.video!==void 0?Object.assign({},t,{video:Cr(t.video)}):t}function Gr(t){if(!t.RTCPeerConnection)return;const e=t.RTCPeerConnection;t.RTCPeerConnection=function(s,i){if(s&&s.iceServers){const r=[];for(let a=0;a<s.iceServers.length;a++){let o=s.iceServers[a];o.urls===void 0&&o.url?(Ms("RTCIceServer.url","RTCIceServer.urls"),o=JSON.parse(JSON.stringify(o)),o.urls=o.url,delete o.url,r.push(o)):r.push(s.iceServers[a])}s.iceServers=r}return new e(s,i)},t.RTCPeerConnection.prototype=e.prototype,"generateCertificate"in e&&Object.defineProperty(t.RTCPeerConnection,"generateCertificate",{get(){return e.generateCertificate}})}function Wr(t){typeof t=="object"&&t.RTCTrackEvent&&"receiver"in t.RTCTrackEvent.prototype&&!("transceiver"in t.RTCTrackEvent.prototype)&&Object.defineProperty(t.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Jr(t){const e=t.RTCPeerConnection.prototype.createOffer;t.RTCPeerConnection.prototype.createOffer=function(s){if(s){typeof s.offerToReceiveAudio<"u"&&(s.offerToReceiveAudio=!!s.offerToReceiveAudio);const i=this.getTransceivers().find(a=>a.receiver.track.kind==="audio");s.offerToReceiveAudio===!1&&i?i.direction==="sendrecv"?i.setDirection?i.setDirection("sendonly"):i.direction="sendonly":i.direction==="recvonly"&&(i.setDirection?i.setDirection("inactive"):i.direction="inactive"):s.offerToReceiveAudio===!0&&!i&&this.addTransceiver("audio",{direction:"recvonly"}),typeof s.offerToReceiveVideo<"u"&&(s.offerToReceiveVideo=!!s.offerToReceiveVideo);const r=this.getTransceivers().find(a=>a.receiver.track.kind==="video");s.offerToReceiveVideo===!1&&r?r.direction==="sendrecv"?r.setDirection?r.setDirection("sendonly"):r.direction="sendonly":r.direction==="recvonly"&&(r.setDirection?r.setDirection("inactive"):r.direction="inactive"):s.offerToReceiveVideo===!0&&!r&&this.addTransceiver("video",{direction:"recvonly"})}return e.apply(this,arguments)}}function Kr(t){typeof t!="object"||t.AudioContext||(t.AudioContext=t.webkitAudioContext)}const Ei=Object.freeze(Object.defineProperty({__proto__:null,shimAudioContext:Kr,shimCallbacksAPI:Br,shimConstraints:Hr,shimCreateOfferLegacy:Jr,shimGetUserMedia:qr,shimLocalStreamsAPI:Vr,shimRTCIceServerUrls:Gr,shimRemoteStreamsAPI:$r,shimTrackEventTransceiver:Wr},Symbol.toStringTag,{value:"Module"}));var zr={exports:{}};(function(t){const e={};e.generateIdentifier=function(){return Math.random().toString(36).substring(2,12)},e.localCName=e.generateIdentifier(),e.splitLines=function(n){return n.trim().split(`
`).map(s=>s.trim())},e.splitSections=function(n){return n.split(`
m=`).map((i,r)=>(r>0?"m="+i:i).trim()+`\r
`)},e.getDescription=function(n){const s=e.splitSections(n);return s&&s[0]},e.getMediaSections=function(n){const s=e.splitSections(n);return s.shift(),s},e.matchPrefix=function(n,s){return e.splitLines(n).filter(i=>i.indexOf(s)===0)},e.parseCandidate=function(n){let s;n.indexOf("a=candidate:")===0?s=n.substring(12).split(" "):s=n.substring(10).split(" ");const i={foundation:s[0],component:{1:"rtp",2:"rtcp"}[s[1]]||s[1],protocol:s[2].toLowerCase(),priority:parseInt(s[3],10),ip:s[4],address:s[4],port:parseInt(s[5],10),type:s[7]};for(let r=8;r<s.length;r+=2)switch(s[r]){case"raddr":i.relatedAddress=s[r+1];break;case"rport":i.relatedPort=parseInt(s[r+1],10);break;case"tcptype":i.tcpType=s[r+1];break;case"ufrag":i.ufrag=s[r+1],i.usernameFragment=s[r+1];break;default:i[s[r]]===void 0&&(i[s[r]]=s[r+1]);break}return i},e.writeCandidate=function(n){const s=[];s.push(n.foundation);const i=n.component;i==="rtp"?s.push(1):i==="rtcp"?s.push(2):s.push(i),s.push(n.protocol.toUpperCase()),s.push(n.priority),s.push(n.address||n.ip),s.push(n.port);const r=n.type;return s.push("typ"),s.push(r),r!=="host"&&n.relatedAddress&&n.relatedPort&&(s.push("raddr"),s.push(n.relatedAddress),s.push("rport"),s.push(n.relatedPort)),n.tcpType&&n.protocol.toLowerCase()==="tcp"&&(s.push("tcptype"),s.push(n.tcpType)),(n.usernameFragment||n.ufrag)&&(s.push("ufrag"),s.push(n.usernameFragment||n.ufrag)),"candidate:"+s.join(" ")},e.parseIceOptions=function(n){return n.substring(14).split(" ")},e.parseRtpMap=function(n){let s=n.substring(9).split(" ");const i={payloadType:parseInt(s.shift(),10)};return s=s[0].split("/"),i.name=s[0],i.clockRate=parseInt(s[1],10),i.channels=s.length===3?parseInt(s[2],10):1,i.numChannels=i.channels,i},e.writeRtpMap=function(n){let s=n.payloadType;n.preferredPayloadType!==void 0&&(s=n.preferredPayloadType);const i=n.channels||n.numChannels||1;return"a=rtpmap:"+s+" "+n.name+"/"+n.clockRate+(i!==1?"/"+i:"")+`\r
`},e.parseExtmap=function(n){const s=n.substring(9).split(" ");return{id:parseInt(s[0],10),direction:s[0].indexOf("/")>0?s[0].split("/")[1]:"sendrecv",uri:s[1],attributes:s.slice(2).join(" ")}},e.writeExtmap=function(n){return"a=extmap:"+(n.id||n.preferredId)+(n.direction&&n.direction!=="sendrecv"?"/"+n.direction:"")+" "+n.uri+(n.attributes?" "+n.attributes:"")+`\r
`},e.parseFmtp=function(n){const s={};let i;const r=n.substring(n.indexOf(" ")+1).split(";");for(let a=0;a<r.length;a++)i=r[a].trim().split("="),s[i[0].trim()]=i[1];return s},e.writeFmtp=function(n){let s="",i=n.payloadType;if(n.preferredPayloadType!==void 0&&(i=n.preferredPayloadType),n.parameters&&Object.keys(n.parameters).length){const r=[];Object.keys(n.parameters).forEach(a=>{n.parameters[a]!==void 0?r.push(a+"="+n.parameters[a]):r.push(a)}),s+="a=fmtp:"+i+" "+r.join(";")+`\r
`}return s},e.parseRtcpFb=function(n){const s=n.substring(n.indexOf(" ")+1).split(" ");return{type:s.shift(),parameter:s.join(" ")}},e.writeRtcpFb=function(n){let s="",i=n.payloadType;return n.preferredPayloadType!==void 0&&(i=n.preferredPayloadType),n.rtcpFeedback&&n.rtcpFeedback.length&&n.rtcpFeedback.forEach(r=>{s+="a=rtcp-fb:"+i+" "+r.type+(r.parameter&&r.parameter.length?" "+r.parameter:"")+`\r
`}),s},e.parseSsrcMedia=function(n){const s=n.indexOf(" "),i={ssrc:parseInt(n.substring(7,s),10)},r=n.indexOf(":",s);return r>-1?(i.attribute=n.substring(s+1,r),i.value=n.substring(r+1)):i.attribute=n.substring(s+1),i},e.parseSsrcGroup=function(n){const s=n.substring(13).split(" ");return{semantics:s.shift(),ssrcs:s.map(i=>parseInt(i,10))}},e.getMid=function(n){const s=e.matchPrefix(n,"a=mid:")[0];if(s)return s.substring(6)},e.parseFingerprint=function(n){const s=n.substring(14).split(" ");return{algorithm:s[0].toLowerCase(),value:s[1].toUpperCase()}},e.getDtlsParameters=function(n,s){return{role:"auto",fingerprints:e.matchPrefix(n+s,"a=fingerprint:").map(e.parseFingerprint)}},e.writeDtlsParameters=function(n,s){let i="a=setup:"+s+`\r
`;return n.fingerprints.forEach(r=>{i+="a=fingerprint:"+r.algorithm+" "+r.value+`\r
`}),i},e.parseCryptoLine=function(n){const s=n.substring(9).split(" ");return{tag:parseInt(s[0],10),cryptoSuite:s[1],keyParams:s[2],sessionParams:s.slice(3)}},e.writeCryptoLine=function(n){return"a=crypto:"+n.tag+" "+n.cryptoSuite+" "+(typeof n.keyParams=="object"?e.writeCryptoKeyParams(n.keyParams):n.keyParams)+(n.sessionParams?" "+n.sessionParams.join(" "):"")+`\r
`},e.parseCryptoKeyParams=function(n){if(n.indexOf("inline:")!==0)return null;const s=n.substring(7).split("|");return{keyMethod:"inline",keySalt:s[0],lifeTime:s[1],mkiValue:s[2]?s[2].split(":")[0]:void 0,mkiLength:s[2]?s[2].split(":")[1]:void 0}},e.writeCryptoKeyParams=function(n){return n.keyMethod+":"+n.keySalt+(n.lifeTime?"|"+n.lifeTime:"")+(n.mkiValue&&n.mkiLength?"|"+n.mkiValue+":"+n.mkiLength:"")},e.getCryptoParameters=function(n,s){return e.matchPrefix(n+s,"a=crypto:").map(e.parseCryptoLine)},e.getIceParameters=function(n,s){const i=e.matchPrefix(n+s,"a=ice-ufrag:")[0],r=e.matchPrefix(n+s,"a=ice-pwd:")[0];return i&&r?{usernameFragment:i.substring(12),password:r.substring(10)}:null},e.writeIceParameters=function(n){let s="a=ice-ufrag:"+n.usernameFragment+`\r
a=ice-pwd:`+n.password+`\r
`;return n.iceLite&&(s+=`a=ice-lite\r
`),s},e.parseRtpParameters=function(n){const s={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=e.splitLines(n)[0].split(" ");s.profile=r[2];for(let o=3;o<r.length;o++){const d=r[o],c=e.matchPrefix(n,"a=rtpmap:"+d+" ")[0];if(c){const l=e.parseRtpMap(c),h=e.matchPrefix(n,"a=fmtp:"+d+" ");switch(l.parameters=h.length?e.parseFmtp(h[0]):{},l.rtcpFeedback=e.matchPrefix(n,"a=rtcp-fb:"+d+" ").map(e.parseRtcpFb),s.codecs.push(l),l.name.toUpperCase()){case"RED":case"ULPFEC":s.fecMechanisms.push(l.name.toUpperCase());break}}}e.matchPrefix(n,"a=extmap:").forEach(o=>{s.headerExtensions.push(e.parseExtmap(o))});const a=e.matchPrefix(n,"a=rtcp-fb:* ").map(e.parseRtcpFb);return s.codecs.forEach(o=>{a.forEach(d=>{o.rtcpFeedback.find(l=>l.type===d.type&&l.parameter===d.parameter)||o.rtcpFeedback.push(d)})}),s},e.writeRtpDescription=function(n,s){let i="";i+="m="+n+" ",i+=s.codecs.length>0?"9":"0",i+=" "+(s.profile||"UDP/TLS/RTP/SAVPF")+" ",i+=s.codecs.map(a=>a.preferredPayloadType!==void 0?a.preferredPayloadType:a.payloadType).join(" ")+`\r
`,i+=`c=IN IP4 0.0.0.0\r
`,i+=`a=rtcp:9 IN IP4 0.0.0.0\r
`,s.codecs.forEach(a=>{i+=e.writeRtpMap(a),i+=e.writeFmtp(a),i+=e.writeRtcpFb(a)});let r=0;return s.codecs.forEach(a=>{a.maxptime>r&&(r=a.maxptime)}),r>0&&(i+="a=maxptime:"+r+`\r
`),s.headerExtensions&&s.headerExtensions.forEach(a=>{i+=e.writeExtmap(a)}),i},e.parseRtpEncodingParameters=function(n){const s=[],i=e.parseRtpParameters(n),r=i.fecMechanisms.indexOf("RED")!==-1,a=i.fecMechanisms.indexOf("ULPFEC")!==-1,o=e.matchPrefix(n,"a=ssrc:").map(u=>e.parseSsrcMedia(u)).filter(u=>u.attribute==="cname"),d=o.length>0&&o[0].ssrc;let c;const l=e.matchPrefix(n,"a=ssrc-group:FID").map(u=>u.substring(17).split(" ").map(p=>parseInt(p,10)));l.length>0&&l[0].length>1&&l[0][0]===d&&(c=l[0][1]),i.codecs.forEach(u=>{if(u.name.toUpperCase()==="RTX"&&u.parameters.apt){let f={ssrc:d,codecPayloadType:parseInt(u.parameters.apt,10)};d&&c&&(f.rtx={ssrc:c}),s.push(f),r&&(f=JSON.parse(JSON.stringify(f)),f.fec={ssrc:d,mechanism:a?"red+ulpfec":"red"},s.push(f))}}),s.length===0&&d&&s.push({ssrc:d});let h=e.matchPrefix(n,"b=");return h.length&&(h[0].indexOf("b=TIAS:")===0?h=parseInt(h[0].substring(7),10):h[0].indexOf("b=AS:")===0?h=parseInt(h[0].substring(5),10)*1e3*.95-50*40*8:h=void 0,s.forEach(u=>{u.maxBitrate=h})),s},e.parseRtcpParameters=function(n){const s={},i=e.matchPrefix(n,"a=ssrc:").map(o=>e.parseSsrcMedia(o)).filter(o=>o.attribute==="cname")[0];i&&(s.cname=i.value,s.ssrc=i.ssrc);const r=e.matchPrefix(n,"a=rtcp-rsize");s.reducedSize=r.length>0,s.compound=r.length===0;const a=e.matchPrefix(n,"a=rtcp-mux");return s.mux=a.length>0,s},e.writeRtcpParameters=function(n){let s="";return n.reducedSize&&(s+=`a=rtcp-rsize\r
`),n.mux&&(s+=`a=rtcp-mux\r
`),n.ssrc!==void 0&&n.cname&&(s+="a=ssrc:"+n.ssrc+" cname:"+n.cname+`\r
`),s},e.parseMsid=function(n){let s;const i=e.matchPrefix(n,"a=msid:");if(i.length===1)return s=i[0].substring(7).split(" "),{stream:s[0],track:s[1]};const r=e.matchPrefix(n,"a=ssrc:").map(a=>e.parseSsrcMedia(a)).filter(a=>a.attribute==="msid");if(r.length>0)return s=r[0].value.split(" "),{stream:s[0],track:s[1]}},e.parseSctpDescription=function(n){const s=e.parseMLine(n),i=e.matchPrefix(n,"a=max-message-size:");let r;i.length>0&&(r=parseInt(i[0].substring(19),10)),isNaN(r)&&(r=65536);const a=e.matchPrefix(n,"a=sctp-port:");if(a.length>0)return{port:parseInt(a[0].substring(12),10),protocol:s.fmt,maxMessageSize:r};const o=e.matchPrefix(n,"a=sctpmap:");if(o.length>0){const d=o[0].substring(10).split(" ");return{port:parseInt(d[0],10),protocol:d[1],maxMessageSize:r}}},e.writeSctpDescription=function(n,s){let i=[];return n.protocol!=="DTLS/SCTP"?i=["m="+n.kind+" 9 "+n.protocol+" "+s.protocol+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctp-port:"+s.port+`\r
`]:i=["m="+n.kind+" 9 "+n.protocol+" "+s.port+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctpmap:"+s.port+" "+s.protocol+` 65535\r
`],s.maxMessageSize!==void 0&&i.push("a=max-message-size:"+s.maxMessageSize+`\r
`),i.join("")},e.generateSessionId=function(){return Math.random().toString().substr(2,22)},e.writeSessionBoilerplate=function(n,s,i){let r;const a=s!==void 0?s:2;return n?r=n:r=e.generateSessionId(),`v=0\r
o=`+(i||"thisisadapterortc")+" "+r+" "+a+` IN IP4 127.0.0.1\r
s=-\r
t=0 0\r
`},e.getDirection=function(n,s){const i=e.splitLines(n);for(let r=0;r<i.length;r++)switch(i[r]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return i[r].substring(2)}return s?e.getDirection(s):"sendrecv"},e.getKind=function(n){return e.splitLines(n)[0].split(" ")[0].substring(2)},e.isRejected=function(n){return n.split(" ",2)[1]==="0"},e.parseMLine=function(n){const i=e.splitLines(n)[0].substring(2).split(" ");return{kind:i[0],port:parseInt(i[1],10),protocol:i[2],fmt:i.slice(3).join(" ")}},e.parseOLine=function(n){const i=e.matchPrefix(n,"o=")[0].substring(2).split(" ");return{username:i[0],sessionId:i[1],sessionVersion:parseInt(i[2],10),netType:i[3],addressType:i[4],address:i[5]}},e.isValidSDP=function(n){if(typeof n!="string"||n.length===0)return!1;const s=e.splitLines(n);for(let i=0;i<s.length;i++)if(s[i].length<2||s[i].charAt(1)!=="=")return!1;return!0},t.exports=e})(zr);var Yr=zr.exports;const Ze=xo(Yr),Yo=Ho({__proto__:null,default:Ze},[Yr]);function Xt(t){if(!t.RTCIceCandidate||t.RTCIceCandidate&&"foundation"in t.RTCIceCandidate.prototype)return;const e=t.RTCIceCandidate;t.RTCIceCandidate=function(s){if(typeof s=="object"&&s.candidate&&s.candidate.indexOf("a=")===0&&(s=JSON.parse(JSON.stringify(s)),s.candidate=s.candidate.substring(2)),s.candidate&&s.candidate.length){const i=new e(s),r=Ze.parseCandidate(s.candidate);for(const a in r)a in i||Object.defineProperty(i,a,{value:r[a]});return i.toJSON=function(){return{candidate:i.candidate,sdpMid:i.sdpMid,sdpMLineIndex:i.sdpMLineIndex,usernameFragment:i.usernameFragment}},i}return new e(s)},t.RTCIceCandidate.prototype=e.prototype,He(t,"icecandidate",n=>(n.candidate&&Object.defineProperty(n,"candidate",{value:new t.RTCIceCandidate(n.candidate),writable:"false"}),n))}function ps(t){!t.RTCIceCandidate||t.RTCIceCandidate&&"relayProtocol"in t.RTCIceCandidate.prototype||He(t,"icecandidate",e=>{if(e.candidate){const n=Ze.parseCandidate(e.candidate.candidate);n.type==="relay"&&(e.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[n.priority>>24])}return e})}function Zt(t,e){if(!t.RTCPeerConnection)return;"sctp"in t.RTCPeerConnection.prototype||Object.defineProperty(t.RTCPeerConnection.prototype,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp}});const n=function(o){if(!o||!o.sdp)return!1;const d=Ze.splitSections(o.sdp);return d.shift(),d.some(c=>{const l=Ze.parseMLine(c);return l&&l.kind==="application"&&l.protocol.indexOf("SCTP")!==-1})},s=function(o){const d=o.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(d===null||d.length<2)return-1;const c=parseInt(d[1],10);return c!==c?-1:c},i=function(o){let d=65536;return e.browser==="firefox"&&(e.version<57?o===-1?d=16384:d=2147483637:e.version<60?d=e.version===57?65535:65536:d=2147483637),d},r=function(o,d){let c=65536;e.browser==="firefox"&&e.version===57&&(c=65535);const l=Ze.matchPrefix(o.sdp,"a=max-message-size:");return l.length>0?c=parseInt(l[0].substring(19),10):e.browser==="firefox"&&d!==-1&&(c=2147483637),c},a=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,e.browser==="chrome"&&e.version>=76){const{sdpSemantics:d}=this.getConfiguration();d==="plan-b"&&Object.defineProperty(this,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp},enumerable:!0,configurable:!0})}if(n(arguments[0])){const d=s(arguments[0]),c=i(d),l=r(arguments[0],d);let h;c===0&&l===0?h=Number.POSITIVE_INFINITY:c===0||l===0?h=Math.max(c,l):h=Math.min(c,l);const u={};Object.defineProperty(u,"maxMessageSize",{get(){return h}}),this._sctp=u}return a.apply(this,arguments)}}function Qt(t){if(!(t.RTCPeerConnection&&"createDataChannel"in t.RTCPeerConnection.prototype))return;function e(s,i){const r=s.send;s.send=function(){const o=arguments[0],d=o.length||o.size||o.byteLength;if(s.readyState==="open"&&i.sctp&&d>i.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+i.sctp.maxMessageSize+" bytes)");return r.apply(s,arguments)}}const n=t.RTCPeerConnection.prototype.createDataChannel;t.RTCPeerConnection.prototype.createDataChannel=function(){const i=n.apply(this,arguments);return e(i,this),i},He(t,"datachannel",s=>(e(s.channel,s.target),s))}function ms(t){if(!t.RTCPeerConnection||"connectionState"in t.RTCPeerConnection.prototype)return;const e=t.RTCPeerConnection.prototype;Object.defineProperty(e,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(e,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(n){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),n&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=n)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(n=>{const s=e[n];e[n]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=i=>{const r=i.target;if(r._lastConnectionState!==r.connectionState){r._lastConnectionState=r.connectionState;const a=new Event("connectionstatechange",i);r.dispatchEvent(a)}return i},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),s.apply(this,arguments)}})}function gs(t,e){if(!t.RTCPeerConnection||e.browser==="chrome"&&e.version>=71||e.browser==="safari"&&e.version>=605)return;const n=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(i){if(i&&i.sdp&&i.sdp.indexOf(`
a=extmap-allow-mixed`)!==-1){const r=i.sdp.split(`
`).filter(a=>a.trim()!=="a=extmap-allow-mixed").join(`
`);t.RTCSessionDescription&&i instanceof t.RTCSessionDescription?arguments[0]=new t.RTCSessionDescription({type:i.type,sdp:r}):i.sdp=r}return n.apply(this,arguments)}}function en(t,e){if(!(t.RTCPeerConnection&&t.RTCPeerConnection.prototype))return;const n=t.RTCPeerConnection.prototype.addIceCandidate;!n||n.length===0||(t.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?(e.browser==="chrome"&&e.version<78||e.browser==="firefox"&&e.version<68||e.browser==="safari")&&arguments[0]&&arguments[0].candidate===""?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function tn(t,e){if(!(t.RTCPeerConnection&&t.RTCPeerConnection.prototype))return;const n=t.RTCPeerConnection.prototype.setLocalDescription;!n||n.length===0||(t.RTCPeerConnection.prototype.setLocalDescription=function(){let i=arguments[0]||{};if(typeof i!="object"||i.type&&i.sdp)return n.apply(this,arguments);if(i={type:i.type,sdp:i.sdp},!i.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":i.type="offer";break;default:i.type="answer";break}return i.sdp||i.type!=="offer"&&i.type!=="answer"?n.apply(this,[i]):(i.type==="offer"?this.createOffer:this.createAnswer).apply(this).then(a=>n.apply(this,[a]))})}const Xo=Object.freeze(Object.defineProperty({__proto__:null,removeExtmapAllowMixed:gs,shimAddIceCandidateNullOrEmpty:en,shimConnectionState:ms,shimMaxMessageSize:Zt,shimParameterlessSetLocalDescription:tn,shimRTCIceCandidate:Xt,shimRTCIceCandidateRelayProtocol:ps,shimSendThrowTypeError:Qt},Symbol.toStringTag,{value:"Module"}));function Zo({window:t}={},e={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const n=Sr,s=Jo(t),i={browserDetails:s,commonShim:Xo,extractVersion:Yt,disableLog:Go,disableWarnings:Wo,sdp:Yo};switch(s.browser){case"chrome":if(!Ti||!hs||!e.shimChrome)return n("Chrome shim is not included in this adapter release."),i;if(s.version===null)return n("Chrome shim can not determine version, not shimming."),i;n("adapter.js shimming chrome."),i.browserShim=Ti,en(t,s),tn(t),vr(t,s),Tr(t),hs(t,s),wr(t),_r(t,s),Er(t),kr(t),Rr(t),Pr(t,s),Xt(t),ps(t),ms(t),Zt(t,s),Qt(t),gs(t,s);break;case"firefox":if(!wi||!fs||!e.shimFirefox)return n("Firefox shim is not included in this adapter release."),i;n("adapter.js shimming firefox."),i.browserShim=wi,en(t,s),tn(t),Or(t,s),fs(t,s),Ar(t),xr(t),Dr(t),Nr(t),Lr(t),Ur(t),Mr(t),Fr(t),jr(t),Xt(t),ms(t),Zt(t,s),Qt(t);break;case"safari":if(!Ei||!e.shimSafari)return n("Safari shim is not included in this adapter release."),i;n("adapter.js shimming safari."),i.browserShim=Ei,en(t,s),tn(t),Gr(t),Jr(t),Br(t),Vr(t),$r(t),Wr(t),qr(t),Kr(t),Xt(t),ps(t),Zt(t,s),Qt(t),gs(t,s);break;default:n("Unsupported browser!");break}return i}Zo({window:typeof window>"u"?void 0:window});function rt(t){let e=typeof t;if(e=="object"){if(Array.isArray(t))return"array";if(t===null)return"null"}return e}function cn(t){return t!==null&&typeof t=="object"&&!Array.isArray(t)}let _e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),On=[];for(let t=0;t<_e.length;t++)On[_e[t].charCodeAt(0)]=t;On[45]=_e.indexOf("+");On[95]=_e.indexOf("/");function Qo(t){let e=t.length*3/4;t[t.length-2]=="="?e-=2:t[t.length-1]=="="&&(e-=1);let n=new Uint8Array(e),s=0,i=0,r,a=0;for(let o=0;o<t.length;o++){if(r=On[t.charCodeAt(o)],r===void 0)switch(t[o]){case"=":i=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string.")}switch(i){case 0:a=r,i=1;break;case 1:n[s++]=a<<2|(r&48)>>4,a=r,i=2;break;case 2:n[s++]=(a&15)<<4|(r&60)>>2,a=r,i=3;break;case 3:n[s++]=(a&3)<<6|r,i=0;break}}if(i==1)throw Error("invalid base64 string.");return n.subarray(0,s)}function ec(t){let e="",n=0,s,i=0;for(let r=0;r<t.length;r++)switch(s=t[r],n){case 0:e+=_e[s>>2],i=(s&3)<<4,n=1;break;case 1:e+=_e[i|s>>4],i=(s&15)<<2,n=2;break;case 2:e+=_e[i|s>>6],e+=_e[s&63],n=0;break}return n&&(e+=_e[i],e+="=",n==1&&(e+="=")),e}var un;(function(t){t.symbol=Symbol.for("protobuf-ts/unknown"),t.onRead=(n,s,i,r,a)=>{(e(s)?s[t.symbol]:s[t.symbol]=[]).push({no:i,wireType:r,data:a})},t.onWrite=(n,s,i)=>{for(let{no:r,wireType:a,data:o}of t.list(s))i.tag(r,a).raw(o)},t.list=(n,s)=>{if(e(n)){let i=n[t.symbol];return s?i.filter(r=>r.no==s):i}return[]},t.last=(n,s)=>t.list(n,s).slice(-1)[0];const e=n=>n&&Array.isArray(n[t.symbol])})(un||(un={}));function tc(t,e){return Object.assign(Object.assign({},t),e)}var G;(function(t){t[t.Varint=0]="Varint",t[t.Bit64=1]="Bit64",t[t.LengthDelimited=2]="LengthDelimited",t[t.StartGroup=3]="StartGroup",t[t.EndGroup=4]="EndGroup",t[t.Bit32=5]="Bit32"})(G||(G={}));function nc(){let t=0,e=0;for(let s=0;s<28;s+=7){let i=this.buf[this.pos++];if(t|=(i&127)<<s,!(i&128))return this.assertBounds(),[t,e]}let n=this.buf[this.pos++];if(t|=(n&15)<<28,e=(n&112)>>4,!(n&128))return this.assertBounds(),[t,e];for(let s=3;s<=31;s+=7){let i=this.buf[this.pos++];if(e|=(i&127)<<s,!(i&128))return this.assertBounds(),[t,e]}throw new Error("invalid varint")}function zn(t,e,n){for(let r=0;r<28;r=r+7){const a=t>>>r,o=!(!(a>>>7)&&e==0),d=(o?a|128:a)&255;if(n.push(d),!o)return}const s=t>>>28&15|(e&7)<<4,i=!!(e>>3);if(n.push((i?s|128:s)&255),!!i){for(let r=3;r<31;r=r+7){const a=e>>>r,o=!!(a>>>7),d=(o?a|128:a)&255;if(n.push(d),!o)return}n.push(e>>>31&1)}}const nn=65536*65536;function Xr(t){let e=t[0]=="-";e&&(t=t.slice(1));const n=1e6;let s=0,i=0;function r(a,o){const d=Number(t.slice(a,o));i*=n,s=s*n+d,s>=nn&&(i=i+(s/nn|0),s=s%nn)}return r(-24,-18),r(-18,-12),r(-12,-6),r(-6),[e,s,i]}function bs(t,e){if(e>>>0<=2097151)return""+(nn*e+(t>>>0));let n=t&16777215,s=(t>>>24|e<<8)>>>0&16777215,i=e>>16&65535,r=n+s*6777216+i*6710656,a=s+i*8147497,o=i*2,d=1e7;r>=d&&(a+=Math.floor(r/d),r%=d),a>=d&&(o+=Math.floor(a/d),a%=d);function c(l,h){let u=l?String(l):"";return h?"0000000".slice(u.length)+u:u}return c(o,0)+c(a,o)+c(r,1)}function ki(t,e){if(t>=0){for(;t>127;)e.push(t&127|128),t=t>>>7;e.push(t)}else{for(let n=0;n<9;n++)e.push(t&127|128),t=t>>7;e.push(1)}}function sc(){let t=this.buf[this.pos++],e=t&127;if(!(t&128))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(t&127)<<7,!(t&128))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(t&127)<<14,!(t&128))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(t&127)<<21,!(t&128))return this.assertBounds(),e;t=this.buf[this.pos++],e|=(t&15)<<28;for(let n=5;t&128&&n<10;n++)t=this.buf[this.pos++];if(t&128)throw new Error("invalid varint");return this.assertBounds(),e>>>0}let j;function ic(){const t=new DataView(new ArrayBuffer(8));j=globalThis.BigInt!==void 0&&typeof t.getBigInt64=="function"&&typeof t.getBigUint64=="function"&&typeof t.setBigInt64=="function"&&typeof t.setBigUint64=="function"?{MIN:BigInt("-9223372036854775808"),MAX:BigInt("9223372036854775807"),UMIN:BigInt("0"),UMAX:BigInt("18446744073709551615"),C:BigInt,V:t}:void 0}ic();function Zr(t){if(!t)throw new Error("BigInt unavailable, see https://github.com/timostamm/protobuf-ts/blob/v1.0.8/MANUAL.md#bigint-support")}const Qr=/^-?[0-9]+$/,ln=4294967296,$t=2147483648;class ea{constructor(e,n){this.lo=e|0,this.hi=n|0}isZero(){return this.lo==0&&this.hi==0}toNumber(){let e=this.hi*ln+(this.lo>>>0);if(!Number.isSafeInteger(e))throw new Error("cannot convert to safe number");return e}}class X extends ea{static from(e){if(j)switch(typeof e){case"string":if(e=="0")return this.ZERO;if(e=="")throw new Error("string is no integer");e=j.C(e);case"number":if(e===0)return this.ZERO;e=j.C(e);case"bigint":if(!e)return this.ZERO;if(e<j.UMIN)throw new Error("signed value for ulong");if(e>j.UMAX)throw new Error("ulong too large");return j.V.setBigUint64(0,e,!0),new X(j.V.getInt32(0,!0),j.V.getInt32(4,!0))}else switch(typeof e){case"string":if(e=="0")return this.ZERO;if(e=e.trim(),!Qr.test(e))throw new Error("string is no integer");let[n,s,i]=Xr(e);if(n)throw new Error("signed value for ulong");return new X(s,i);case"number":if(e==0)return this.ZERO;if(!Number.isSafeInteger(e))throw new Error("number is no integer");if(e<0)throw new Error("signed value for ulong");return new X(e,e/ln)}throw new Error("unknown value "+typeof e)}toString(){return j?this.toBigInt().toString():bs(this.lo,this.hi)}toBigInt(){return Zr(j),j.V.setInt32(0,this.lo,!0),j.V.setInt32(4,this.hi,!0),j.V.getBigUint64(0,!0)}}X.ZERO=new X(0,0);class B extends ea{static from(e){if(j)switch(typeof e){case"string":if(e=="0")return this.ZERO;if(e=="")throw new Error("string is no integer");e=j.C(e);case"number":if(e===0)return this.ZERO;e=j.C(e);case"bigint":if(!e)return this.ZERO;if(e<j.MIN)throw new Error("signed long too small");if(e>j.MAX)throw new Error("signed long too large");return j.V.setBigInt64(0,e,!0),new B(j.V.getInt32(0,!0),j.V.getInt32(4,!0))}else switch(typeof e){case"string":if(e=="0")return this.ZERO;if(e=e.trim(),!Qr.test(e))throw new Error("string is no integer");let[n,s,i]=Xr(e);if(n){if(i>$t||i==$t&&s!=0)throw new Error("signed long too small")}else if(i>=$t)throw new Error("signed long too large");let r=new B(s,i);return n?r.negate():r;case"number":if(e==0)return this.ZERO;if(!Number.isSafeInteger(e))throw new Error("number is no integer");return e>0?new B(e,e/ln):new B(-e,-e/ln).negate()}throw new Error("unknown value "+typeof e)}isNegative(){return(this.hi&$t)!==0}negate(){let e=~this.hi,n=this.lo;return n?n=~n+1:e+=1,new B(n,e)}toString(){if(j)return this.toBigInt().toString();if(this.isNegative()){let e=this.negate();return"-"+bs(e.lo,e.hi)}return bs(this.lo,this.hi)}toBigInt(){return Zr(j),j.V.setInt32(0,this.lo,!0),j.V.setInt32(4,this.hi,!0),j.V.getBigInt64(0,!0)}}B.ZERO=new B(0,0);const Ri={readUnknownField:!0,readerFactory:t=>new ac(t)};function rc(t){return t?Object.assign(Object.assign({},Ri),t):Ri}class ac{constructor(e,n){this.varint64=nc,this.uint32=sc,this.buf=e,this.len=e.length,this.pos=0,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength),this.textDecoder=n??new TextDecoder("utf-8",{fatal:!0,ignoreBOM:!0})}tag(){let e=this.uint32(),n=e>>>3,s=e&7;if(n<=0||s<0||s>5)throw new Error("illegal tag: field no "+n+" wire type "+s);return[n,s]}skip(e){let n=this.pos;switch(e){case G.Varint:for(;this.buf[this.pos++]&128;);break;case G.Bit64:this.pos+=4;case G.Bit32:this.pos+=4;break;case G.LengthDelimited:let s=this.uint32();this.pos+=s;break;case G.StartGroup:let i;for(;(i=this.tag()[1])!==G.EndGroup;)this.skip(i);break;default:throw new Error("cant skip wire type "+e)}return this.assertBounds(),this.buf.subarray(n,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return this.uint32()|0}sint32(){let e=this.uint32();return e>>>1^-(e&1)}int64(){return new B(...this.varint64())}uint64(){return new X(...this.varint64())}sint64(){let[e,n]=this.varint64(),s=-(e&1);return e=(e>>>1|(n&1)<<31)^s,n=n>>>1^s,new B(e,n)}bool(){let[e,n]=this.varint64();return e!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return new X(this.sfixed32(),this.sfixed32())}sfixed64(){return new B(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let e=this.uint32(),n=this.pos;return this.pos+=e,this.assertBounds(),this.buf.subarray(n,n+e)}string(){return this.textDecoder.decode(this.bytes())}}function U(t,e){if(!t)throw new Error(e)}const oc=34028234663852886e22,cc=-34028234663852886e22,uc=4294967295,lc=2147483647,dc=-2147483648;function vt(t){if(typeof t!="number")throw new Error("invalid int 32: "+typeof t);if(!Number.isInteger(t)||t>lc||t<dc)throw new Error("invalid int 32: "+t)}function dn(t){if(typeof t!="number")throw new Error("invalid uint 32: "+typeof t);if(!Number.isInteger(t)||t>uc||t<0)throw new Error("invalid uint 32: "+t)}function Fs(t){if(typeof t!="number")throw new Error("invalid float 32: "+typeof t);if(Number.isFinite(t)&&(t>oc||t<cc))throw new Error("invalid float 32: "+t)}const Ii={writeUnknownFields:!0,writerFactory:()=>new fc};function hc(t){return t?Object.assign(Object.assign({},Ii),t):Ii}class fc{constructor(e){this.stack=[],this.textEncoder=e??new TextEncoder,this.chunks=[],this.buf=[]}finish(){this.chunks.push(new Uint8Array(this.buf));let e=0;for(let i=0;i<this.chunks.length;i++)e+=this.chunks[i].length;let n=new Uint8Array(e),s=0;for(let i=0;i<this.chunks.length;i++)n.set(this.chunks[i],s),s+=this.chunks[i].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let e=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(e.byteLength),this.raw(e)}tag(e,n){return this.uint32((e<<3|n)>>>0)}raw(e){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(e),this}uint32(e){for(dn(e);e>127;)this.buf.push(e&127|128),e=e>>>7;return this.buf.push(e),this}int32(e){return vt(e),ki(e,this.buf),this}bool(e){return this.buf.push(e?1:0),this}bytes(e){return this.uint32(e.byteLength),this.raw(e)}string(e){let n=this.textEncoder.encode(e);return this.uint32(n.byteLength),this.raw(n)}float(e){Fs(e);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,e,!0),this.raw(n)}double(e){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,e,!0),this.raw(n)}fixed32(e){dn(e);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,e,!0),this.raw(n)}sfixed32(e){vt(e);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,e,!0),this.raw(n)}sint32(e){return vt(e),e=(e<<1^e>>31)>>>0,ki(e,this.buf),this}sfixed64(e){let n=new Uint8Array(8),s=new DataView(n.buffer),i=B.from(e);return s.setInt32(0,i.lo,!0),s.setInt32(4,i.hi,!0),this.raw(n)}fixed64(e){let n=new Uint8Array(8),s=new DataView(n.buffer),i=X.from(e);return s.setInt32(0,i.lo,!0),s.setInt32(4,i.hi,!0),this.raw(n)}int64(e){let n=B.from(e);return zn(n.lo,n.hi,this.buf),this}sint64(e){let n=B.from(e),s=n.hi>>31,i=n.lo<<1^s,r=(n.hi<<1|n.lo>>>31)^s;return zn(i,r,this.buf),this}uint64(e){let n=X.from(e);return zn(n.lo,n.hi,this.buf),this}}const _i={emitDefaultValues:!1,enumAsInteger:!1,useProtoFieldName:!1,prettySpaces:0},Pi={ignoreUnknownFields:!1};function pc(t){return t?Object.assign(Object.assign({},Pi),t):Pi}function mc(t){return t?Object.assign(Object.assign({},_i),t):_i}function gc(t,e){var n,s;let i=Object.assign(Object.assign({},t),e);return i.typeRegistry=[...(n=t==null?void 0:t.typeRegistry)!==null&&n!==void 0?n:[],...(s=e==null?void 0:e.typeRegistry)!==null&&s!==void 0?s:[]],i}const ta=Symbol.for("protobuf-ts/message-type");function hn(t){let e=!1;const n=[];for(let s=0;s<t.length;s++){let i=t.charAt(s);i=="_"?e=!0:/\d/.test(i)?(n.push(i),e=!0):e?(n.push(i.toUpperCase()),e=!1):s==0?n.push(i.toLowerCase()):n.push(i)}return n.join("")}var S;(function(t){t[t.DOUBLE=1]="DOUBLE",t[t.FLOAT=2]="FLOAT",t[t.INT64=3]="INT64",t[t.UINT64=4]="UINT64",t[t.INT32=5]="INT32",t[t.FIXED64=6]="FIXED64",t[t.FIXED32=7]="FIXED32",t[t.BOOL=8]="BOOL",t[t.STRING=9]="STRING",t[t.BYTES=12]="BYTES",t[t.UINT32=13]="UINT32",t[t.SFIXED32=15]="SFIXED32",t[t.SFIXED64=16]="SFIXED64",t[t.SINT32=17]="SINT32",t[t.SINT64=18]="SINT64"})(S||(S={}));var Ce;(function(t){t[t.BIGINT=0]="BIGINT",t[t.STRING=1]="STRING",t[t.NUMBER=2]="NUMBER"})(Ce||(Ce={}));var fn;(function(t){t[t.NO=0]="NO",t[t.PACKED=1]="PACKED",t[t.UNPACKED=2]="UNPACKED"})(fn||(fn={}));function bc(t){var e,n,s,i;return t.localName=(e=t.localName)!==null&&e!==void 0?e:hn(t.name),t.jsonName=(n=t.jsonName)!==null&&n!==void 0?n:hn(t.name),t.repeat=(s=t.repeat)!==null&&s!==void 0?s:fn.NO,t.opt=(i=t.opt)!==null&&i!==void 0?i:t.repeat||t.oneof?!1:t.kind=="message",t}function yc(t){if(typeof t!="object"||t===null||!t.hasOwnProperty("oneofKind"))return!1;switch(typeof t.oneofKind){case"string":return t[t.oneofKind]===void 0?!1:Object.keys(t).length==2;case"undefined":return Object.keys(t).length==1;default:return!1}}class Sc{constructor(e){var n;this.fields=(n=e.fields)!==null&&n!==void 0?n:[]}prepare(){if(this.data)return;const e=[],n=[],s=[];for(let i of this.fields)if(i.oneof)s.includes(i.oneof)||(s.push(i.oneof),e.push(i.oneof),n.push(i.oneof));else switch(n.push(i.localName),i.kind){case"scalar":case"enum":(!i.opt||i.repeat)&&e.push(i.localName);break;case"message":i.repeat&&e.push(i.localName);break;case"map":e.push(i.localName);break}this.data={req:e,known:n,oneofs:Object.values(s)}}is(e,n,s=!1){if(n<0)return!0;if(e==null||typeof e!="object")return!1;this.prepare();let i=Object.keys(e),r=this.data;if(i.length<r.req.length||r.req.some(a=>!i.includes(a))||!s&&i.some(a=>!r.known.includes(a)))return!1;if(n<1)return!0;for(const a of r.oneofs){const o=e[a];if(!yc(o))return!1;if(o.oneofKind===void 0)continue;const d=this.fields.find(c=>c.localName===o.oneofKind);if(!d||!this.field(o[o.oneofKind],d,s,n))return!1}for(const a of this.fields)if(a.oneof===void 0&&!this.field(e[a.localName],a,s,n))return!1;return!0}field(e,n,s,i){let r=n.repeat;switch(n.kind){case"scalar":return e===void 0?n.opt:r?this.scalars(e,n.T,i,n.L):this.scalar(e,n.T,n.L);case"enum":return e===void 0?n.opt:r?this.scalars(e,S.INT32,i):this.scalar(e,S.INT32);case"message":return e===void 0?!0:r?this.messages(e,n.T(),s,i):this.message(e,n.T(),s,i);case"map":if(typeof e!="object"||e===null)return!1;if(i<2)return!0;if(!this.mapKeys(e,n.K,i))return!1;switch(n.V.kind){case"scalar":return this.scalars(Object.values(e),n.V.T,i,n.V.L);case"enum":return this.scalars(Object.values(e),S.INT32,i);case"message":return this.messages(Object.values(e),n.V.T(),s,i)}break}return!0}message(e,n,s,i){return s?n.isAssignable(e,i):n.is(e,i)}messages(e,n,s,i){if(!Array.isArray(e))return!1;if(i<2)return!0;if(s){for(let r=0;r<e.length&&r<i;r++)if(!n.isAssignable(e[r],i-1))return!1}else for(let r=0;r<e.length&&r<i;r++)if(!n.is(e[r],i-1))return!1;return!0}scalar(e,n,s){let i=typeof e;switch(n){case S.UINT64:case S.FIXED64:case S.INT64:case S.SFIXED64:case S.SINT64:switch(s){case Ce.BIGINT:return i=="bigint";case Ce.NUMBER:return i=="number"&&!isNaN(e);default:return i=="string"}case S.BOOL:return i=="boolean";case S.STRING:return i=="string";case S.BYTES:return e instanceof Uint8Array;case S.DOUBLE:case S.FLOAT:return i=="number"&&!isNaN(e);default:return i=="number"&&Number.isInteger(e)}}scalars(e,n,s,i){if(!Array.isArray(e))return!1;if(s<2)return!0;if(Array.isArray(e)){for(let r=0;r<e.length&&r<s;r++)if(!this.scalar(e[r],n,i))return!1}return!0}mapKeys(e,n,s){let i=Object.keys(e);switch(n){case S.INT32:case S.FIXED32:case S.SFIXED32:case S.SINT32:case S.UINT32:return this.scalars(i.slice(0,s).map(r=>parseInt(r)),n,s);case S.BOOL:return this.scalars(i.slice(0,s).map(r=>r=="true"?!0:r=="false"?!1:r),n,s);default:return this.scalars(i,n,s,Ce.STRING)}}}function he(t,e){switch(e){case Ce.BIGINT:return t.toBigInt();case Ce.NUMBER:return t.toNumber();default:return t.toString()}}class Cc{constructor(e){this.info=e}prepare(){var e;if(this.fMap===void 0){this.fMap={};const n=(e=this.info.fields)!==null&&e!==void 0?e:[];for(const s of n)this.fMap[s.name]=s,this.fMap[s.jsonName]=s,this.fMap[s.localName]=s}}assert(e,n,s){if(!e){let i=rt(s);throw(i=="number"||i=="boolean")&&(i=s.toString()),new Error(`Cannot parse JSON ${i} for ${this.info.typeName}#${n}`)}}read(e,n,s){this.prepare();const i=[];for(const[r,a]of Object.entries(e)){const o=this.fMap[r];if(!o){if(!s.ignoreUnknownFields)throw new Error(`Found unknown field while reading ${this.info.typeName} from JSON format. JSON key: ${r}`);continue}const d=o.localName;let c;if(o.oneof){if(a===null&&(o.kind!=="enum"||o.T()[0]!=="google.protobuf.NullValue"))continue;if(i.includes(o.oneof))throw new Error(`Multiple members of the oneof group "${o.oneof}" of ${this.info.typeName} are present in JSON.`);i.push(o.oneof),c=n[o.oneof]={oneofKind:d}}else c=n;if(o.kind=="map"){if(a===null)continue;this.assert(cn(a),o.name,a);const l=c[d];for(const[h,u]of Object.entries(a)){this.assert(u!==null,o.name+" map value",null);let f;switch(o.V.kind){case"message":f=o.V.T().internalJsonRead(u,s);break;case"enum":if(f=this.enum(o.V.T(),u,o.name,s.ignoreUnknownFields),f===!1)continue;break;case"scalar":f=this.scalar(u,o.V.T,o.V.L,o.name);break}this.assert(f!==void 0,o.name+" map value",u);let p=h;o.K==S.BOOL&&(p=p=="true"?!0:p=="false"?!1:p),p=this.scalar(p,o.K,Ce.STRING,o.name).toString(),l[p]=f}}else if(o.repeat){if(a===null)continue;this.assert(Array.isArray(a),o.name,a);const l=c[d];for(const h of a){this.assert(h!==null,o.name,null);let u;switch(o.kind){case"message":u=o.T().internalJsonRead(h,s);break;case"enum":if(u=this.enum(o.T(),h,o.name,s.ignoreUnknownFields),u===!1)continue;break;case"scalar":u=this.scalar(h,o.T,o.L,o.name);break}this.assert(u!==void 0,o.name,a),l.push(u)}}else switch(o.kind){case"message":if(a===null&&o.T().typeName!="google.protobuf.Value"){this.assert(o.oneof===void 0,o.name+" (oneof member)",null);continue}c[d]=o.T().internalJsonRead(a,s,c[d]);break;case"enum":let l=this.enum(o.T(),a,o.name,s.ignoreUnknownFields);if(l===!1)continue;c[d]=l;break;case"scalar":c[d]=this.scalar(a,o.T,o.L,o.name);break}}}enum(e,n,s,i){if(e[0]=="google.protobuf.NullValue"&&U(n===null||n==="NULL_VALUE",`Unable to parse field ${this.info.typeName}#${s}, enum ${e[0]} only accepts null.`),n===null)return 0;switch(typeof n){case"number":return U(Number.isInteger(n),`Unable to parse field ${this.info.typeName}#${s}, enum can only be integral number, got ${n}.`),n;case"string":let r=n;e[2]&&n.substring(0,e[2].length)===e[2]&&(r=n.substring(e[2].length));let a=e[1][r];return typeof a>"u"&&i?!1:(U(typeof a=="number",`Unable to parse field ${this.info.typeName}#${s}, enum ${e[0]} has no value for "${n}".`),a)}U(!1,`Unable to parse field ${this.info.typeName}#${s}, cannot parse enum value from ${typeof n}".`)}scalar(e,n,s,i){let r;try{switch(n){case S.DOUBLE:case S.FLOAT:if(e===null)return 0;if(e==="NaN")return Number.NaN;if(e==="Infinity")return Number.POSITIVE_INFINITY;if(e==="-Infinity")return Number.NEGATIVE_INFINITY;if(e===""){r="empty string";break}if(typeof e=="string"&&e.trim().length!==e.length){r="extra whitespace";break}if(typeof e!="string"&&typeof e!="number")break;let a=Number(e);if(Number.isNaN(a)){r="not a number";break}if(!Number.isFinite(a)){r="too large or small";break}return n==S.FLOAT&&Fs(a),a;case S.INT32:case S.FIXED32:case S.SFIXED32:case S.SINT32:case S.UINT32:if(e===null)return 0;let o;if(typeof e=="number"?o=e:e===""?r="empty string":typeof e=="string"&&(e.trim().length!==e.length?r="extra whitespace":o=Number(e)),o===void 0)break;return n==S.UINT32?dn(o):vt(o),o;case S.INT64:case S.SFIXED64:case S.SINT64:if(e===null)return he(B.ZERO,s);if(typeof e!="number"&&typeof e!="string")break;return he(B.from(e),s);case S.FIXED64:case S.UINT64:if(e===null)return he(X.ZERO,s);if(typeof e!="number"&&typeof e!="string")break;return he(X.from(e),s);case S.BOOL:if(e===null)return!1;if(typeof e!="boolean")break;return e;case S.STRING:if(e===null)return"";if(typeof e!="string"){r="extra whitespace";break}try{encodeURIComponent(e)}catch(d){d="invalid UTF8";break}return e;case S.BYTES:if(e===null||e==="")return new Uint8Array(0);if(typeof e!="string")break;return Qo(e)}}catch(a){r=a.message}this.assert(!1,i+(r?" - "+r:""),e)}}class vc{constructor(e){var n;this.fields=(n=e.fields)!==null&&n!==void 0?n:[]}write(e,n){const s={},i=e;for(const r of this.fields){if(!r.oneof){let c=this.field(r,i[r.localName],n);c!==void 0&&(s[n.useProtoFieldName?r.name:r.jsonName]=c);continue}const a=i[r.oneof];if(a.oneofKind!==r.localName)continue;const o=r.kind=="scalar"||r.kind=="enum"?Object.assign(Object.assign({},n),{emitDefaultValues:!0}):n;let d=this.field(r,a[r.localName],o);U(d!==void 0),s[n.useProtoFieldName?r.name:r.jsonName]=d}return s}field(e,n,s){let i;if(e.kind=="map"){U(typeof n=="object"&&n!==null);const r={};switch(e.V.kind){case"scalar":for(const[d,c]of Object.entries(n)){const l=this.scalar(e.V.T,c,e.name,!1,!0);U(l!==void 0),r[d.toString()]=l}break;case"message":const a=e.V.T();for(const[d,c]of Object.entries(n)){const l=this.message(a,c,e.name,s);U(l!==void 0),r[d.toString()]=l}break;case"enum":const o=e.V.T();for(const[d,c]of Object.entries(n)){U(c===void 0||typeof c=="number");const l=this.enum(o,c,e.name,!1,!0,s.enumAsInteger);U(l!==void 0),r[d.toString()]=l}break}(s.emitDefaultValues||Object.keys(r).length>0)&&(i=r)}else if(e.repeat){U(Array.isArray(n));const r=[];switch(e.kind){case"scalar":for(let d=0;d<n.length;d++){const c=this.scalar(e.T,n[d],e.name,e.opt,!0);U(c!==void 0),r.push(c)}break;case"enum":const a=e.T();for(let d=0;d<n.length;d++){U(n[d]===void 0||typeof n[d]=="number");const c=this.enum(a,n[d],e.name,e.opt,!0,s.enumAsInteger);U(c!==void 0),r.push(c)}break;case"message":const o=e.T();for(let d=0;d<n.length;d++){const c=this.message(o,n[d],e.name,s);U(c!==void 0),r.push(c)}break}(s.emitDefaultValues||r.length>0||s.emitDefaultValues)&&(i=r)}else switch(e.kind){case"scalar":i=this.scalar(e.T,n,e.name,e.opt,s.emitDefaultValues);break;case"enum":i=this.enum(e.T(),n,e.name,e.opt,s.emitDefaultValues,s.enumAsInteger);break;case"message":i=this.message(e.T(),n,e.name,s);break}return i}enum(e,n,s,i,r,a){if(e[0]=="google.protobuf.NullValue")return!r&&!i?void 0:null;if(n===void 0){U(i);return}if(!(n===0&&!r&&!i))return U(typeof n=="number"),U(Number.isInteger(n)),a||!e[1].hasOwnProperty(n)?n:e[2]?e[2]+e[1][n]:e[1][n]}message(e,n,s,i){return n===void 0?i.emitDefaultValues?null:void 0:e.internalJsonWrite(n,i)}scalar(e,n,s,i,r){if(n===void 0){U(i);return}const a=r||i;switch(e){case S.INT32:case S.SFIXED32:case S.SINT32:return n===0?a?0:void 0:(vt(n),n);case S.FIXED32:case S.UINT32:return n===0?a?0:void 0:(dn(n),n);case S.FLOAT:Fs(n);case S.DOUBLE:return n===0?a?0:void 0:(U(typeof n=="number"),Number.isNaN(n)?"NaN":n===Number.POSITIVE_INFINITY?"Infinity":n===Number.NEGATIVE_INFINITY?"-Infinity":n);case S.STRING:return n===""?a?"":void 0:(U(typeof n=="string"),n);case S.BOOL:return n===!1?a?!1:void 0:(U(typeof n=="boolean"),n);case S.UINT64:case S.FIXED64:U(typeof n=="number"||typeof n=="string"||typeof n=="bigint");let o=X.from(n);return o.isZero()&&!a?void 0:o.toString();case S.INT64:case S.SFIXED64:case S.SINT64:U(typeof n=="number"||typeof n=="string"||typeof n=="bigint");let d=B.from(n);return d.isZero()&&!a?void 0:d.toString();case S.BYTES:return U(n instanceof Uint8Array),n.byteLength?ec(n):a?"":void 0}}}function ys(t,e=Ce.STRING){switch(t){case S.BOOL:return!1;case S.UINT64:case S.FIXED64:return he(X.ZERO,e);case S.INT64:case S.SFIXED64:case S.SINT64:return he(B.ZERO,e);case S.DOUBLE:case S.FLOAT:return 0;case S.BYTES:return new Uint8Array(0);case S.STRING:return"";default:return 0}}class Tc{constructor(e){this.info=e}prepare(){var e;if(!this.fieldNoToField){const n=(e=this.info.fields)!==null&&e!==void 0?e:[];this.fieldNoToField=new Map(n.map(s=>[s.no,s]))}}read(e,n,s,i){this.prepare();const r=i===void 0?e.len:e.pos+i;for(;e.pos<r;){const[a,o]=e.tag(),d=this.fieldNoToField.get(a);if(!d){let u=s.readUnknownField;if(u=="throw")throw new Error(`Unknown field ${a} (wire type ${o}) for ${this.info.typeName}`);let f=e.skip(o);u!==!1&&(u===!0?un.onRead:u)(this.info.typeName,n,a,o,f);continue}let c=n,l=d.repeat,h=d.localName;switch(d.oneof&&(c=c[d.oneof],c.oneofKind!==h&&(c=n[d.oneof]={oneofKind:h})),d.kind){case"scalar":case"enum":let u=d.kind=="enum"?S.INT32:d.T,f=d.kind=="scalar"?d.L:void 0;if(l){let g=c[h];if(o==G.LengthDelimited&&u!=S.STRING&&u!=S.BYTES){let b=e.uint32()+e.pos;for(;e.pos<b;)g.push(this.scalar(e,u,f))}else g.push(this.scalar(e,u,f))}else c[h]=this.scalar(e,u,f);break;case"message":if(l){let g=c[h],b=d.T().internalBinaryRead(e,e.uint32(),s);g.push(b)}else c[h]=d.T().internalBinaryRead(e,e.uint32(),s,c[h]);break;case"map":let[p,m]=this.mapEntry(d,e,s);c[h][p]=m;break}}}mapEntry(e,n,s){let i=n.uint32(),r=n.pos+i,a,o;for(;n.pos<r;){let[d,c]=n.tag();switch(d){case 1:e.K==S.BOOL?a=n.bool().toString():a=this.scalar(n,e.K,Ce.STRING);break;case 2:switch(e.V.kind){case"scalar":o=this.scalar(n,e.V.T,e.V.L);break;case"enum":o=n.int32();break;case"message":o=e.V.T().internalBinaryRead(n,n.uint32(),s);break}break;default:throw new Error(`Unknown field ${d} (wire type ${c}) in map entry for ${this.info.typeName}#${e.name}`)}}if(a===void 0){let d=ys(e.K);a=e.K==S.BOOL?d.toString():d}if(o===void 0)switch(e.V.kind){case"scalar":o=ys(e.V.T,e.V.L);break;case"enum":o=0;break;case"message":o=e.V.T().create();break}return[a,o]}scalar(e,n,s){switch(n){case S.INT32:return e.int32();case S.STRING:return e.string();case S.BOOL:return e.bool();case S.DOUBLE:return e.double();case S.FLOAT:return e.float();case S.INT64:return he(e.int64(),s);case S.UINT64:return he(e.uint64(),s);case S.FIXED64:return he(e.fixed64(),s);case S.FIXED32:return e.fixed32();case S.BYTES:return e.bytes();case S.UINT32:return e.uint32();case S.SFIXED32:return e.sfixed32();case S.SFIXED64:return he(e.sfixed64(),s);case S.SINT32:return e.sint32();case S.SINT64:return he(e.sint64(),s)}}}class wc{constructor(e){this.info=e}prepare(){if(!this.fields){const e=this.info.fields?this.info.fields.concat():[];this.fields=e.sort((n,s)=>n.no-s.no)}}write(e,n,s){this.prepare();for(const r of this.fields){let a,o,d=r.repeat,c=r.localName;if(r.oneof){const l=e[r.oneof];if(l.oneofKind!==c)continue;a=l[c],o=!0}else a=e[c],o=!1;switch(r.kind){case"scalar":case"enum":let l=r.kind=="enum"?S.INT32:r.T;if(d)if(U(Array.isArray(a)),d==fn.PACKED)this.packed(n,l,r.no,a);else for(const h of a)this.scalar(n,l,r.no,h,!0);else a===void 0?U(r.opt):this.scalar(n,l,r.no,a,o||r.opt);break;case"message":if(d){U(Array.isArray(a));for(const h of a)this.message(n,s,r.T(),r.no,h)}else this.message(n,s,r.T(),r.no,a);break;case"map":U(typeof a=="object"&&a!==null);for(const[h,u]of Object.entries(a))this.mapEntry(n,s,r,h,u);break}}let i=s.writeUnknownFields;i!==!1&&(i===!0?un.onWrite:i)(this.info.typeName,e,n)}mapEntry(e,n,s,i,r){e.tag(s.no,G.LengthDelimited),e.fork();let a=i;switch(s.K){case S.INT32:case S.FIXED32:case S.UINT32:case S.SFIXED32:case S.SINT32:a=Number.parseInt(i);break;case S.BOOL:U(i=="true"||i=="false"),a=i=="true";break}switch(this.scalar(e,s.K,1,a,!0),s.V.kind){case"scalar":this.scalar(e,s.V.T,2,r,!0);break;case"enum":this.scalar(e,S.INT32,2,r,!0);break;case"message":this.message(e,n,s.V.T(),2,r);break}e.join()}message(e,n,s,i,r){r!==void 0&&(s.internalBinaryWrite(r,e.tag(i,G.LengthDelimited).fork(),n),e.join())}scalar(e,n,s,i,r){let[a,o,d]=this.scalarInfo(n,i);(!d||r)&&(e.tag(s,a),e[o](i))}packed(e,n,s,i){if(!i.length)return;U(n!==S.BYTES&&n!==S.STRING),e.tag(s,G.LengthDelimited),e.fork();let[,r]=this.scalarInfo(n);for(let a=0;a<i.length;a++)e[r](i[a]);e.join()}scalarInfo(e,n){let s=G.Varint,i,r=n===void 0,a=n===0;switch(e){case S.INT32:i="int32";break;case S.STRING:a=r||!n.length,s=G.LengthDelimited,i="string";break;case S.BOOL:a=n===!1,i="bool";break;case S.UINT32:i="uint32";break;case S.DOUBLE:s=G.Bit64,i="double";break;case S.FLOAT:s=G.Bit32,i="float";break;case S.INT64:a=r||B.from(n).isZero(),i="int64";break;case S.UINT64:a=r||X.from(n).isZero(),i="uint64";break;case S.FIXED64:a=r||X.from(n).isZero(),s=G.Bit64,i="fixed64";break;case S.BYTES:a=r||!n.byteLength,s=G.LengthDelimited,i="bytes";break;case S.FIXED32:s=G.Bit32,i="fixed32";break;case S.SFIXED32:s=G.Bit32,i="sfixed32";break;case S.SFIXED64:a=r||B.from(n).isZero(),s=G.Bit64,i="sfixed64";break;case S.SINT32:i="sint32";break;case S.SINT64:a=r||B.from(n).isZero(),i="sint64";break}return[s,i,r||a]}}function Ec(t){const e=t.messagePrototype?Object.create(t.messagePrototype):Object.defineProperty({},ta,{value:t});for(let n of t.fields){let s=n.localName;if(!n.opt)if(n.oneof)e[n.oneof]={oneofKind:void 0};else if(n.repeat)e[s]=[];else switch(n.kind){case"scalar":e[s]=ys(n.T,n.L);break;case"enum":e[s]=0;break;case"map":e[s]={};break}}return e}function Yn(t,e,n){let s,i=n,r;for(let a of t.fields){let o=a.localName;if(a.oneof){const d=i[a.oneof];if((d==null?void 0:d.oneofKind)==null)continue;if(s=d[o],r=e[a.oneof],r.oneofKind=d.oneofKind,s==null){delete r[o];continue}}else if(s=i[o],r=e,s==null)continue;switch(a.repeat&&(r[o].length=s.length),a.kind){case"scalar":case"enum":if(a.repeat)for(let c=0;c<s.length;c++)r[o][c]=s[c];else r[o]=s;break;case"message":let d=a.T();if(a.repeat)for(let c=0;c<s.length;c++)r[o][c]=d.create(s[c]);else r[o]===void 0?r[o]=d.create(s):d.mergePartial(r[o],s);break;case"map":switch(a.V.kind){case"scalar":case"enum":Object.assign(r[o],s);break;case"message":let c=a.V.T();for(let l of Object.keys(s))r[o][l]=c.create(s[l]);break}break}}}function kc(t,e,n){if(e===n)return!0;if(!e||!n)return!1;for(let s of t.fields){let i=s.localName,r=s.oneof?e[s.oneof][i]:e[i],a=s.oneof?n[s.oneof][i]:n[i];switch(s.kind){case"enum":case"scalar":let o=s.kind=="enum"?S.INT32:s.T;if(!(s.repeat?Oi(o,r,a):na(o,r,a)))return!1;break;case"map":if(!(s.V.kind=="message"?Ai(s.V.T(),Bt(r),Bt(a)):Oi(s.V.kind=="enum"?S.INT32:s.V.T,Bt(r),Bt(a))))return!1;break;case"message":let d=s.T();if(!(s.repeat?Ai(d,r,a):d.equals(r,a)))return!1;break}}return!0}const Bt=Object.values;function na(t,e,n){if(e===n)return!0;if(t!==S.BYTES)return!1;let s=e,i=n;if(s.length!==i.length)return!1;for(let r=0;r<s.length;r++)if(s[r]!=i[r])return!1;return!0}function Oi(t,e,n){if(e.length!==n.length)return!1;for(let s=0;s<e.length;s++)if(!na(t,e[s],n[s]))return!1;return!0}function Ai(t,e,n){if(e.length!==n.length)return!1;for(let s=0;s<e.length;s++)if(!t.equals(e[s],n[s]))return!1;return!0}const Rc=Object.getOwnPropertyDescriptors(Object.getPrototypeOf({}));class T{constructor(e,n,s){this.defaultCheckDepth=16,this.typeName=e,this.fields=n.map(bc),this.options=s??{},this.messagePrototype=Object.create(null,Object.assign(Object.assign({},Rc),{[ta]:{value:this}})),this.refTypeCheck=new Sc(this),this.refJsonReader=new Cc(this),this.refJsonWriter=new vc(this),this.refBinReader=new Tc(this),this.refBinWriter=new wc(this)}create(e){let n=Ec(this);return e!==void 0&&Yn(this,n,e),n}clone(e){let n=this.create();return Yn(this,n,e),n}equals(e,n){return kc(this,e,n)}is(e,n=this.defaultCheckDepth){return this.refTypeCheck.is(e,n,!1)}isAssignable(e,n=this.defaultCheckDepth){return this.refTypeCheck.is(e,n,!0)}mergePartial(e,n){Yn(this,e,n)}fromBinary(e,n){let s=rc(n);return this.internalBinaryRead(s.readerFactory(e),e.byteLength,s)}fromJson(e,n){return this.internalJsonRead(e,pc(n))}fromJsonString(e,n){let s=JSON.parse(e);return this.fromJson(s,n)}toJson(e,n){return this.internalJsonWrite(e,mc(n))}toJsonString(e,n){var s;let i=this.toJson(e,n);return JSON.stringify(i,null,(s=n==null?void 0:n.prettySpaces)!==null&&s!==void 0?s:0)}toBinary(e,n){let s=hc(n);return this.internalBinaryWrite(e,s.writerFactory(),s).finish()}internalJsonRead(e,n,s){if(e!==null&&typeof e=="object"&&!Array.isArray(e)){let i=s??this.create();return this.refJsonReader.read(e,i,n),i}throw new Error(`Unable to parse message ${this.typeName} from JSON ${rt(e)}.`)}internalJsonWrite(e,n){return this.refJsonWriter.write(e,n)}internalBinaryWrite(e,n,s){return this.refBinWriter.write(e,n,s),n}internalBinaryRead(e,n,s,i){let r=i??this.create();return this.refBinReader.read(e,r,s,n),r}}function Ic(t,e){var n,s,i;let r=t;return r.service=e,r.localName=(n=r.localName)!==null&&n!==void 0?n:hn(r.name),r.serverStreaming=!!r.serverStreaming,r.clientStreaming=!!r.clientStreaming,r.options=(s=r.options)!==null&&s!==void 0?s:{},r.idempotency=(i=r.idempotency)!==null&&i!==void 0?i:void 0,r}class _c{constructor(e,n,s){this.typeName=e,this.methods=n.map(i=>Ic(i,this)),this.options=s??{}}}class ie extends Error{constructor(e,n="UNKNOWN",s){super(e),this.name="RpcError",Object.setPrototypeOf(this,new.target.prototype),this.code=n,this.meta=s??{}}toString(){const e=[this.name+": "+this.message];this.code&&(e.push(""),e.push("Code: "+this.code)),this.serviceName&&this.methodName&&e.push("Method: "+this.serviceName+"/"+this.methodName);let n=Object.entries(this.meta);if(n.length){e.push(""),e.push("Meta:");for(let[s,i]of n)e.push(`  ${s}: ${i}`)}return e.join(`
`)}}function Pc(t,e){if(!e)return t;let n={};qt(t,n),qt(e,n);for(let s of Object.keys(e)){let i=e[s];switch(s){case"jsonOptions":n.jsonOptions=gc(t.jsonOptions,n.jsonOptions);break;case"binaryOptions":n.binaryOptions=tc(t.binaryOptions,n.binaryOptions);break;case"meta":n.meta={},qt(t.meta,n.meta),qt(e.meta,n.meta);break;case"interceptors":n.interceptors=t.interceptors?t.interceptors.concat(i):i.concat();break}}return n}function qt(t,e){if(!t)return;let n=e;for(let[s,i]of Object.entries(t))i instanceof Date?n[s]=new Date(i.getTime()):Array.isArray(i)?n[s]=i.concat():n[s]=i}var de;(function(t){t[t.PENDING=0]="PENDING",t[t.REJECTED=1]="REJECTED",t[t.RESOLVED=2]="RESOLVED"})(de||(de={}));class Ht{constructor(e=!0){this._state=de.PENDING,this._promise=new Promise((n,s)=>{this._resolve=n,this._reject=s}),e&&this._promise.catch(n=>{})}get state(){return this._state}get promise(){return this._promise}resolve(e){if(this.state!==de.PENDING)throw new Error(`cannot resolve ${de[this.state].toLowerCase()}`);this._resolve(e),this._state=de.RESOLVED}reject(e){if(this.state!==de.PENDING)throw new Error(`cannot reject ${de[this.state].toLowerCase()}`);this._reject(e),this._state=de.REJECTED}resolvePending(e){this._state===de.PENDING&&this.resolve(e)}rejectPending(e){this._state===de.PENDING&&this.reject(e)}}var Oc=function(t,e,n,s){function i(r){return r instanceof n?r:new n(function(a){a(r)})}return new(n||(n=Promise))(function(r,a){function o(l){try{c(s.next(l))}catch(h){a(h)}}function d(l){try{c(s.throw(l))}catch(h){a(h)}}function c(l){l.done?r(l.value):i(l.value).then(o,d)}c((s=s.apply(t,e||[])).next())})};class Ac{constructor(e,n,s,i,r,a,o){this.method=e,this.requestHeaders=n,this.request=s,this.headers=i,this.response=r,this.status=a,this.trailers=o}then(e,n){return this.promiseFinished().then(s=>e?Promise.resolve(e(s)):s,s=>n?Promise.resolve(n(s)):Promise.reject(s))}promiseFinished(){return Oc(this,void 0,void 0,function*(){let[e,n,s,i]=yield Promise.all([this.headers,this.response,this.status,this.trailers]);return{method:this.method,requestHeaders:this.requestHeaders,request:this.request,headers:e,response:n,status:s,trailers:i}})}}function Re(t,e,n,s,i){var r;{let a=(o,d,c)=>e.unary(o,d,c);for(const o of((r=s.interceptors)!==null&&r!==void 0?r:[]).filter(d=>d.interceptUnary).reverse()){const d=a;a=(c,l,h)=>o.interceptUnary(d,c,l,h)}return a(n,i,s)}}function sa(t,e){return function(){return t.apply(e,arguments)}}const{toString:Dc}=Object.prototype,{getPrototypeOf:js}=Object,An=(t=>e=>{const n=Dc.call(e);return t[n]||(t[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),pe=t=>(t=t.toLowerCase(),e=>An(e)===t),Dn=t=>e=>typeof e===t,{isArray:at}=Array,Tt=Dn("undefined");function Nc(t){return t!==null&&!Tt(t)&&t.constructor!==null&&!Tt(t.constructor)&&ce(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const ia=pe("ArrayBuffer");function xc(t){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(t):e=t&&t.buffer&&ia(t.buffer),e}const Lc=Dn("string"),ce=Dn("function"),ra=Dn("number"),Nn=t=>t!==null&&typeof t=="object",Uc=t=>t===!0||t===!1,sn=t=>{if(An(t)!=="object")return!1;const e=js(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in t)&&!(Symbol.iterator in t)},Mc=pe("Date"),Fc=pe("File"),jc=pe("Blob"),Vc=pe("FileList"),$c=t=>Nn(t)&&ce(t.pipe),Bc=t=>{let e;return t&&(typeof FormData=="function"&&t instanceof FormData||ce(t.append)&&((e=An(t))==="formdata"||e==="object"&&ce(t.toString)&&t.toString()==="[object FormData]"))},qc=pe("URLSearchParams"),[Hc,Gc,Wc,Jc]=["ReadableStream","Request","Response","Headers"].map(pe),Kc=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Pt(t,e,{allOwnKeys:n=!1}={}){if(t===null||typeof t>"u")return;let s,i;if(typeof t!="object"&&(t=[t]),at(t))for(s=0,i=t.length;s<i;s++)e.call(null,t[s],s,t);else{const r=n?Object.getOwnPropertyNames(t):Object.keys(t),a=r.length;let o;for(s=0;s<a;s++)o=r[s],e.call(null,t[o],o,t)}}function aa(t,e){e=e.toLowerCase();const n=Object.keys(t);let s=n.length,i;for(;s-- >0;)if(i=n[s],e===i.toLowerCase())return i;return null}const Ue=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,oa=t=>!Tt(t)&&t!==Ue;function Ss(){const{caseless:t}=oa(this)&&this||{},e={},n=(s,i)=>{const r=t&&aa(e,i)||i;sn(e[r])&&sn(s)?e[r]=Ss(e[r],s):sn(s)?e[r]=Ss({},s):at(s)?e[r]=s.slice():e[r]=s};for(let s=0,i=arguments.length;s<i;s++)arguments[s]&&Pt(arguments[s],n);return e}const zc=(t,e,n,{allOwnKeys:s}={})=>(Pt(e,(i,r)=>{n&&ce(i)?t[r]=sa(i,n):t[r]=i},{allOwnKeys:s}),t),Yc=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),Xc=(t,e,n,s)=>{t.prototype=Object.create(e.prototype,s),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),n&&Object.assign(t.prototype,n)},Zc=(t,e,n,s)=>{let i,r,a;const o={};if(e=e||{},t==null)return e;do{for(i=Object.getOwnPropertyNames(t),r=i.length;r-- >0;)a=i[r],(!s||s(a,t,e))&&!o[a]&&(e[a]=t[a],o[a]=!0);t=n!==!1&&js(t)}while(t&&(!n||n(t,e))&&t!==Object.prototype);return e},Qc=(t,e,n)=>{t=String(t),(n===void 0||n>t.length)&&(n=t.length),n-=e.length;const s=t.indexOf(e,n);return s!==-1&&s===n},eu=t=>{if(!t)return null;if(at(t))return t;let e=t.length;if(!ra(e))return null;const n=new Array(e);for(;e-- >0;)n[e]=t[e];return n},tu=(t=>e=>t&&e instanceof t)(typeof Uint8Array<"u"&&js(Uint8Array)),nu=(t,e)=>{const s=(t&&t[Symbol.iterator]).call(t);let i;for(;(i=s.next())&&!i.done;){const r=i.value;e.call(t,r[0],r[1])}},su=(t,e)=>{let n;const s=[];for(;(n=t.exec(e))!==null;)s.push(n);return s},iu=pe("HTMLFormElement"),ru=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,i){return s.toUpperCase()+i}),Di=(({hasOwnProperty:t})=>(e,n)=>t.call(e,n))(Object.prototype),au=pe("RegExp"),ca=(t,e)=>{const n=Object.getOwnPropertyDescriptors(t),s={};Pt(n,(i,r)=>{let a;(a=e(i,r,t))!==!1&&(s[r]=a||i)}),Object.defineProperties(t,s)},ou=t=>{ca(t,(e,n)=>{if(ce(t)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=t[n];if(ce(s)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},cu=(t,e)=>{const n={},s=i=>{i.forEach(r=>{n[r]=!0})};return at(t)?s(t):s(String(t).split(e)),n},uu=()=>{},lu=(t,e)=>t!=null&&Number.isFinite(t=+t)?t:e,Xn="abcdefghijklmnopqrstuvwxyz",Ni="0123456789",ua={DIGIT:Ni,ALPHA:Xn,ALPHA_DIGIT:Xn+Xn.toUpperCase()+Ni},du=(t=16,e=ua.ALPHA_DIGIT)=>{let n="";const{length:s}=e;for(;t--;)n+=e[Math.random()*s|0];return n};function hu(t){return!!(t&&ce(t.append)&&t[Symbol.toStringTag]==="FormData"&&t[Symbol.iterator])}const fu=t=>{const e=new Array(10),n=(s,i)=>{if(Nn(s)){if(e.indexOf(s)>=0)return;if(!("toJSON"in s)){e[i]=s;const r=at(s)?[]:{};return Pt(s,(a,o)=>{const d=n(a,i+1);!Tt(d)&&(r[o]=d)}),e[i]=void 0,r}}return s};return n(t,0)},pu=pe("AsyncFunction"),mu=t=>t&&(Nn(t)||ce(t))&&ce(t.then)&&ce(t.catch),la=((t,e)=>t?setImmediate:e?((n,s)=>(Ue.addEventListener("message",({source:i,data:r})=>{i===Ue&&r===n&&s.length&&s.shift()()},!1),i=>{s.push(i),Ue.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",ce(Ue.postMessage)),gu=typeof queueMicrotask<"u"?queueMicrotask.bind(Ue):typeof process<"u"&&process.nextTick||la,y={isArray:at,isArrayBuffer:ia,isBuffer:Nc,isFormData:Bc,isArrayBufferView:xc,isString:Lc,isNumber:ra,isBoolean:Uc,isObject:Nn,isPlainObject:sn,isReadableStream:Hc,isRequest:Gc,isResponse:Wc,isHeaders:Jc,isUndefined:Tt,isDate:Mc,isFile:Fc,isBlob:jc,isRegExp:au,isFunction:ce,isStream:$c,isURLSearchParams:qc,isTypedArray:tu,isFileList:Vc,forEach:Pt,merge:Ss,extend:zc,trim:Kc,stripBOM:Yc,inherits:Xc,toFlatObject:Zc,kindOf:An,kindOfTest:pe,endsWith:Qc,toArray:eu,forEachEntry:nu,matchAll:su,isHTMLForm:iu,hasOwnProperty:Di,hasOwnProp:Di,reduceDescriptors:ca,freezeMethods:ou,toObjectSet:cu,toCamelCase:ru,noop:uu,toFiniteNumber:lu,findKey:aa,global:Ue,isContextDefined:oa,ALPHABET:ua,generateString:du,isSpecCompliantForm:hu,toJSONObject:fu,isAsyncFn:pu,isThenable:mu,setImmediate:la,asap:gu};function O(t,e,n,s,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),n&&(this.config=n),s&&(this.request=s),i&&(this.response=i,this.status=i.status?i.status:null)}y.inherits(O,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:y.toJSONObject(this.config),code:this.code,status:this.status}}});const da=O.prototype,ha={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{ha[t]={value:t}});Object.defineProperties(O,ha);Object.defineProperty(da,"isAxiosError",{value:!0});O.from=(t,e,n,s,i,r)=>{const a=Object.create(da);return y.toFlatObject(t,a,function(d){return d!==Error.prototype},o=>o!=="isAxiosError"),O.call(a,t.message,e,n,s,i),a.cause=t,a.name=t.name,r&&Object.assign(a,r),a};const bu=null;function Cs(t){return y.isPlainObject(t)||y.isArray(t)}function fa(t){return y.endsWith(t,"[]")?t.slice(0,-2):t}function xi(t,e,n){return t?t.concat(e).map(function(i,r){return i=fa(i),!n&&r?"["+i+"]":i}).join(n?".":""):e}function yu(t){return y.isArray(t)&&!t.some(Cs)}const Su=y.toFlatObject(y,{},null,function(e){return/^is[A-Z]/.test(e)});function xn(t,e,n){if(!y.isObject(t))throw new TypeError("target must be an object");e=e||new FormData,n=y.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,g){return!y.isUndefined(g[m])});const s=n.metaTokens,i=n.visitor||l,r=n.dots,a=n.indexes,d=(n.Blob||typeof Blob<"u"&&Blob)&&y.isSpecCompliantForm(e);if(!y.isFunction(i))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(y.isDate(p))return p.toISOString();if(!d&&y.isBlob(p))throw new O("Blob is not supported. Use a Buffer instead.");return y.isArrayBuffer(p)||y.isTypedArray(p)?d&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function l(p,m,g){let b=p;if(p&&!g&&typeof p=="object"){if(y.endsWith(m,"{}"))m=s?m:m.slice(0,-2),p=JSON.stringify(p);else if(y.isArray(p)&&yu(p)||(y.isFileList(p)||y.endsWith(m,"[]"))&&(b=y.toArray(p)))return m=fa(m),b.forEach(function(C,w){!(y.isUndefined(C)||C===null)&&e.append(a===!0?xi([m],w,r):a===null?m:m+"[]",c(C))}),!1}return Cs(p)?!0:(e.append(xi(g,m,r),c(p)),!1)}const h=[],u=Object.assign(Su,{defaultVisitor:l,convertValue:c,isVisitable:Cs});function f(p,m){if(!y.isUndefined(p)){if(h.indexOf(p)!==-1)throw Error("Circular reference detected in "+m.join("."));h.push(p),y.forEach(p,function(b,E){(!(y.isUndefined(b)||b===null)&&i.call(e,b,y.isString(E)?E.trim():E,m,u))===!0&&f(b,m?m.concat(E):[E])}),h.pop()}}if(!y.isObject(t))throw new TypeError("data must be an object");return f(t),e}function Li(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(s){return e[s]})}function Vs(t,e){this._pairs=[],t&&xn(t,this,e)}const pa=Vs.prototype;pa.append=function(e,n){this._pairs.push([e,n])};pa.toString=function(e){const n=e?function(s){return e.call(this,s,Li)}:Li;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function Cu(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ma(t,e,n){if(!e)return t;const s=n&&n.encode||Cu;y.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let r;if(i?r=i(e,n):r=y.isURLSearchParams(e)?e.toString():new Vs(e,n).toString(s),r){const a=t.indexOf("#");a!==-1&&(t=t.slice(0,a)),t+=(t.indexOf("?")===-1?"?":"&")+r}return t}class Ui{constructor(){this.handlers=[]}use(e,n,s){return this.handlers.push({fulfilled:e,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){y.forEach(this.handlers,function(s){s!==null&&e(s)})}}const ga={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},vu=typeof URLSearchParams<"u"?URLSearchParams:Vs,Tu=typeof FormData<"u"?FormData:null,wu=typeof Blob<"u"?Blob:null,Eu={isBrowser:!0,classes:{URLSearchParams:vu,FormData:Tu,Blob:wu},protocols:["http","https","file","blob","url","data"]},$s=typeof window<"u"&&typeof document<"u",vs=typeof navigator=="object"&&navigator||void 0,ku=$s&&(!vs||["ReactNative","NativeScript","NS"].indexOf(vs.product)<0),Ru=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Iu=$s&&window.location.href||"http://localhost",_u=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:$s,hasStandardBrowserEnv:ku,hasStandardBrowserWebWorkerEnv:Ru,navigator:vs,origin:Iu},Symbol.toStringTag,{value:"Module"})),Y={..._u,...Eu};function Pu(t,e){return xn(t,new Y.classes.URLSearchParams,Object.assign({visitor:function(n,s,i,r){return Y.isNode&&y.isBuffer(n)?(this.append(s,n.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},e))}function Ou(t){return y.matchAll(/\w+|\[(\w*)]/g,t).map(e=>e[0]==="[]"?"":e[1]||e[0])}function Au(t){const e={},n=Object.keys(t);let s;const i=n.length;let r;for(s=0;s<i;s++)r=n[s],e[r]=t[r];return e}function ba(t){function e(n,s,i,r){let a=n[r++];if(a==="__proto__")return!0;const o=Number.isFinite(+a),d=r>=n.length;return a=!a&&y.isArray(i)?i.length:a,d?(y.hasOwnProp(i,a)?i[a]=[i[a],s]:i[a]=s,!o):((!i[a]||!y.isObject(i[a]))&&(i[a]=[]),e(n,s,i[a],r)&&y.isArray(i[a])&&(i[a]=Au(i[a])),!o)}if(y.isFormData(t)&&y.isFunction(t.entries)){const n={};return y.forEachEntry(t,(s,i)=>{e(Ou(s),i,n,0)}),n}return null}function Du(t,e,n){if(y.isString(t))try{return(e||JSON.parse)(t),y.trim(t)}catch(s){if(s.name!=="SyntaxError")throw s}return(0,JSON.stringify)(t)}const Ot={transitional:ga,adapter:["xhr","http","fetch"],transformRequest:[function(e,n){const s=n.getContentType()||"",i=s.indexOf("application/json")>-1,r=y.isObject(e);if(r&&y.isHTMLForm(e)&&(e=new FormData(e)),y.isFormData(e))return i?JSON.stringify(ba(e)):e;if(y.isArrayBuffer(e)||y.isBuffer(e)||y.isStream(e)||y.isFile(e)||y.isBlob(e)||y.isReadableStream(e))return e;if(y.isArrayBufferView(e))return e.buffer;if(y.isURLSearchParams(e))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(r){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Pu(e,this.formSerializer).toString();if((o=y.isFileList(e))||s.indexOf("multipart/form-data")>-1){const d=this.env&&this.env.FormData;return xn(o?{"files[]":e}:e,d&&new d,this.formSerializer)}}return r||i?(n.setContentType("application/json",!1),Du(e)):e}],transformResponse:[function(e){const n=this.transitional||Ot.transitional,s=n&&n.forcedJSONParsing,i=this.responseType==="json";if(y.isResponse(e)||y.isReadableStream(e))return e;if(e&&y.isString(e)&&(s&&!this.responseType||i)){const a=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(e)}catch(o){if(a)throw o.name==="SyntaxError"?O.from(o,O.ERR_BAD_RESPONSE,this,null,this.response):o}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Y.classes.FormData,Blob:Y.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};y.forEach(["delete","get","head","post","put","patch"],t=>{Ot.headers[t]={}});const Nu=y.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),xu=t=>{const e={};let n,s,i;return t&&t.split(`
`).forEach(function(a){i=a.indexOf(":"),n=a.substring(0,i).trim().toLowerCase(),s=a.substring(i+1).trim(),!(!n||e[n]&&Nu[n])&&(n==="set-cookie"?e[n]?e[n].push(s):e[n]=[s]:e[n]=e[n]?e[n]+", "+s:s)}),e},Mi=Symbol("internals");function pt(t){return t&&String(t).trim().toLowerCase()}function rn(t){return t===!1||t==null?t:y.isArray(t)?t.map(rn):String(t)}function Lu(t){const e=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(t);)e[s[1]]=s[2];return e}const Uu=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function Zn(t,e,n,s,i){if(y.isFunction(s))return s.call(this,e,n);if(i&&(e=n),!!y.isString(e)){if(y.isString(s))return e.indexOf(s)!==-1;if(y.isRegExp(s))return s.test(e)}}function Mu(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,n,s)=>n.toUpperCase()+s)}function Fu(t,e){const n=y.toCamelCase(" "+e);["get","set","has"].forEach(s=>{Object.defineProperty(t,s+n,{value:function(i,r,a){return this[s].call(this,e,i,r,a)},configurable:!0})})}class ae{constructor(e){e&&this.set(e)}set(e,n,s){const i=this;function r(o,d,c){const l=pt(d);if(!l)throw new Error("header name must be a non-empty string");const h=y.findKey(i,l);(!h||i[h]===void 0||c===!0||c===void 0&&i[h]!==!1)&&(i[h||d]=rn(o))}const a=(o,d)=>y.forEach(o,(c,l)=>r(c,l,d));if(y.isPlainObject(e)||e instanceof this.constructor)a(e,n);else if(y.isString(e)&&(e=e.trim())&&!Uu(e))a(xu(e),n);else if(y.isHeaders(e))for(const[o,d]of e.entries())r(d,o,s);else e!=null&&r(n,e,s);return this}get(e,n){if(e=pt(e),e){const s=y.findKey(this,e);if(s){const i=this[s];if(!n)return i;if(n===!0)return Lu(i);if(y.isFunction(n))return n.call(this,i,s);if(y.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,n){if(e=pt(e),e){const s=y.findKey(this,e);return!!(s&&this[s]!==void 0&&(!n||Zn(this,this[s],s,n)))}return!1}delete(e,n){const s=this;let i=!1;function r(a){if(a=pt(a),a){const o=y.findKey(s,a);o&&(!n||Zn(s,s[o],o,n))&&(delete s[o],i=!0)}}return y.isArray(e)?e.forEach(r):r(e),i}clear(e){const n=Object.keys(this);let s=n.length,i=!1;for(;s--;){const r=n[s];(!e||Zn(this,this[r],r,e,!0))&&(delete this[r],i=!0)}return i}normalize(e){const n=this,s={};return y.forEach(this,(i,r)=>{const a=y.findKey(s,r);if(a){n[a]=rn(i),delete n[r];return}const o=e?Mu(r):String(r).trim();o!==r&&delete n[r],n[o]=rn(i),s[o]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const n=Object.create(null);return y.forEach(this,(s,i)=>{s!=null&&s!==!1&&(n[i]=e&&y.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,n])=>e+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...n){const s=new this(e);return n.forEach(i=>s.set(i)),s}static accessor(e){const s=(this[Mi]=this[Mi]={accessors:{}}).accessors,i=this.prototype;function r(a){const o=pt(a);s[o]||(Fu(i,a),s[o]=!0)}return y.isArray(e)?e.forEach(r):r(e),this}}ae.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);y.reduceDescriptors(ae.prototype,({value:t},e)=>{let n=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(s){this[n]=s}}});y.freezeMethods(ae);function Qn(t,e){const n=this||Ot,s=e||n,i=ae.from(s.headers);let r=s.data;return y.forEach(t,function(o){r=o.call(n,r,i.normalize(),e?e.status:void 0)}),i.normalize(),r}function ya(t){return!!(t&&t.__CANCEL__)}function ot(t,e,n){O.call(this,t??"canceled",O.ERR_CANCELED,e,n),this.name="CanceledError"}y.inherits(ot,O,{__CANCEL__:!0});function Sa(t,e,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?t(n):e(new O("Request failed with status code "+n.status,[O.ERR_BAD_REQUEST,O.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function ju(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}function Vu(t,e){t=t||10;const n=new Array(t),s=new Array(t);let i=0,r=0,a;return e=e!==void 0?e:1e3,function(d){const c=Date.now(),l=s[r];a||(a=c),n[i]=d,s[i]=c;let h=r,u=0;for(;h!==i;)u+=n[h++],h=h%t;if(i=(i+1)%t,i===r&&(r=(r+1)%t),c-a<e)return;const f=l&&c-l;return f?Math.round(u*1e3/f):void 0}}function $u(t,e){let n=0,s=1e3/e,i,r;const a=(c,l=Date.now())=>{n=l,i=null,r&&(clearTimeout(r),r=null),t.apply(null,c)};return[(...c)=>{const l=Date.now(),h=l-n;h>=s?a(c,l):(i=c,r||(r=setTimeout(()=>{r=null,a(i)},s-h)))},()=>i&&a(i)]}const pn=(t,e,n=3)=>{let s=0;const i=Vu(50,250);return $u(r=>{const a=r.loaded,o=r.lengthComputable?r.total:void 0,d=a-s,c=i(d),l=a<=o;s=a;const h={loaded:a,total:o,progress:o?a/o:void 0,bytes:d,rate:c||void 0,estimated:c&&o&&l?(o-a)/c:void 0,event:r,lengthComputable:o!=null,[e?"download":"upload"]:!0};t(h)},n)},Fi=(t,e)=>{const n=t!=null;return[s=>e[0]({lengthComputable:n,total:t,loaded:s}),e[1]]},ji=t=>(...e)=>y.asap(()=>t(...e)),Bu=Y.hasStandardBrowserEnv?((t,e)=>n=>(n=new URL(n,Y.origin),t.protocol===n.protocol&&t.host===n.host&&(e||t.port===n.port)))(new URL(Y.origin),Y.navigator&&/(msie|trident)/i.test(Y.navigator.userAgent)):()=>!0,qu=Y.hasStandardBrowserEnv?{write(t,e,n,s,i,r){const a=[t+"="+encodeURIComponent(e)];y.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),y.isString(s)&&a.push("path="+s),y.isString(i)&&a.push("domain="+i),r===!0&&a.push("secure"),document.cookie=a.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Hu(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function Gu(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}function Ca(t,e){return t&&!Hu(e)?Gu(t,e):e}const Vi=t=>t instanceof ae?{...t}:t;function Ve(t,e){e=e||{};const n={};function s(c,l,h,u){return y.isPlainObject(c)&&y.isPlainObject(l)?y.merge.call({caseless:u},c,l):y.isPlainObject(l)?y.merge({},l):y.isArray(l)?l.slice():l}function i(c,l,h,u){if(y.isUndefined(l)){if(!y.isUndefined(c))return s(void 0,c,h,u)}else return s(c,l,h,u)}function r(c,l){if(!y.isUndefined(l))return s(void 0,l)}function a(c,l){if(y.isUndefined(l)){if(!y.isUndefined(c))return s(void 0,c)}else return s(void 0,l)}function o(c,l,h){if(h in e)return s(c,l);if(h in t)return s(void 0,c)}const d={url:r,method:r,data:r,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:o,headers:(c,l,h)=>i(Vi(c),Vi(l),h,!0)};return y.forEach(Object.keys(Object.assign({},t,e)),function(l){const h=d[l]||i,u=h(t[l],e[l],l);y.isUndefined(u)&&h!==o||(n[l]=u)}),n}const va=t=>{const e=Ve({},t);let{data:n,withXSRFToken:s,xsrfHeaderName:i,xsrfCookieName:r,headers:a,auth:o}=e;e.headers=a=ae.from(a),e.url=ma(Ca(e.baseURL,e.url),t.params,t.paramsSerializer),o&&a.set("Authorization","Basic "+btoa((o.username||"")+":"+(o.password?unescape(encodeURIComponent(o.password)):"")));let d;if(y.isFormData(n)){if(Y.hasStandardBrowserEnv||Y.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((d=a.getContentType())!==!1){const[c,...l]=d?d.split(";").map(h=>h.trim()).filter(Boolean):[];a.setContentType([c||"multipart/form-data",...l].join("; "))}}if(Y.hasStandardBrowserEnv&&(s&&y.isFunction(s)&&(s=s(e)),s||s!==!1&&Bu(e.url))){const c=i&&r&&qu.read(r);c&&a.set(i,c)}return e},Wu=typeof XMLHttpRequest<"u",Ju=Wu&&function(t){return new Promise(function(n,s){const i=va(t);let r=i.data;const a=ae.from(i.headers).normalize();let{responseType:o,onUploadProgress:d,onDownloadProgress:c}=i,l,h,u,f,p;function m(){f&&f(),p&&p(),i.cancelToken&&i.cancelToken.unsubscribe(l),i.signal&&i.signal.removeEventListener("abort",l)}let g=new XMLHttpRequest;g.open(i.method.toUpperCase(),i.url,!0),g.timeout=i.timeout;function b(){if(!g)return;const C=ae.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders()),k={data:!o||o==="text"||o==="json"?g.responseText:g.response,status:g.status,statusText:g.statusText,headers:C,config:t,request:g};Sa(function(x){n(x),m()},function(x){s(x),m()},k),g=null}"onloadend"in g?g.onloadend=b:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout(b)},g.onabort=function(){g&&(s(new O("Request aborted",O.ECONNABORTED,t,g)),g=null)},g.onerror=function(){s(new O("Network Error",O.ERR_NETWORK,t,g)),g=null},g.ontimeout=function(){let w=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const k=i.transitional||ga;i.timeoutErrorMessage&&(w=i.timeoutErrorMessage),s(new O(w,k.clarifyTimeoutError?O.ETIMEDOUT:O.ECONNABORTED,t,g)),g=null},r===void 0&&a.setContentType(null),"setRequestHeader"in g&&y.forEach(a.toJSON(),function(w,k){g.setRequestHeader(k,w)}),y.isUndefined(i.withCredentials)||(g.withCredentials=!!i.withCredentials),o&&o!=="json"&&(g.responseType=i.responseType),c&&([u,p]=pn(c,!0),g.addEventListener("progress",u)),d&&g.upload&&([h,f]=pn(d),g.upload.addEventListener("progress",h),g.upload.addEventListener("loadend",f)),(i.cancelToken||i.signal)&&(l=C=>{g&&(s(!C||C.type?new ot(null,t,g):C),g.abort(),g=null)},i.cancelToken&&i.cancelToken.subscribe(l),i.signal&&(i.signal.aborted?l():i.signal.addEventListener("abort",l)));const E=ju(i.url);if(E&&Y.protocols.indexOf(E)===-1){s(new O("Unsupported protocol "+E+":",O.ERR_BAD_REQUEST,t));return}g.send(r||null)})},Ku=(t,e)=>{const{length:n}=t=t?t.filter(Boolean):[];if(e||n){let s=new AbortController,i;const r=function(c){if(!i){i=!0,o();const l=c instanceof Error?c:this.reason;s.abort(l instanceof O?l:new ot(l instanceof Error?l.message:l))}};let a=e&&setTimeout(()=>{a=null,r(new O(`timeout ${e} of ms exceeded`,O.ETIMEDOUT))},e);const o=()=>{t&&(a&&clearTimeout(a),a=null,t.forEach(c=>{c.unsubscribe?c.unsubscribe(r):c.removeEventListener("abort",r)}),t=null)};t.forEach(c=>c.addEventListener("abort",r));const{signal:d}=s;return d.unsubscribe=()=>y.asap(o),d}},zu=function*(t,e){let n=t.byteLength;if(n<e){yield t;return}let s=0,i;for(;s<n;)i=s+e,yield t.slice(s,i),s=i},Yu=async function*(t,e){for await(const n of Xu(t))yield*zu(n,e)},Xu=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const e=t.getReader();try{for(;;){const{done:n,value:s}=await e.read();if(n)break;yield s}}finally{await e.cancel()}},$i=(t,e,n,s)=>{const i=Yu(t,e);let r=0,a,o=d=>{a||(a=!0,s&&s(d))};return new ReadableStream({async pull(d){try{const{done:c,value:l}=await i.next();if(c){o(),d.close();return}let h=l.byteLength;if(n){let u=r+=h;n(u)}d.enqueue(new Uint8Array(l))}catch(c){throw o(c),c}},cancel(d){return o(d),i.return()}},{highWaterMark:2})},Ln=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ta=Ln&&typeof ReadableStream=="function",Zu=Ln&&(typeof TextEncoder=="function"?(t=>e=>t.encode(e))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),wa=(t,...e)=>{try{return!!t(...e)}catch{return!1}},Qu=Ta&&wa(()=>{let t=!1;const e=new Request(Y.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),Bi=64*1024,Ts=Ta&&wa(()=>y.isReadableStream(new Response("").body)),mn={stream:Ts&&(t=>t.body)};Ln&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!mn[e]&&(mn[e]=y.isFunction(t[e])?n=>n[e]():(n,s)=>{throw new O(`Response type '${e}' is not supported`,O.ERR_NOT_SUPPORT,s)})})})(new Response);const el=async t=>{if(t==null)return 0;if(y.isBlob(t))return t.size;if(y.isSpecCompliantForm(t))return(await new Request(Y.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(y.isArrayBufferView(t)||y.isArrayBuffer(t))return t.byteLength;if(y.isURLSearchParams(t)&&(t=t+""),y.isString(t))return(await Zu(t)).byteLength},tl=async(t,e)=>{const n=y.toFiniteNumber(t.getContentLength());return n??el(e)},nl=Ln&&(async t=>{let{url:e,method:n,data:s,signal:i,cancelToken:r,timeout:a,onDownloadProgress:o,onUploadProgress:d,responseType:c,headers:l,withCredentials:h="same-origin",fetchOptions:u}=va(t);c=c?(c+"").toLowerCase():"text";let f=Ku([i,r&&r.toAbortSignal()],a),p;const m=f&&f.unsubscribe&&(()=>{f.unsubscribe()});let g;try{if(d&&Qu&&n!=="get"&&n!=="head"&&(g=await tl(l,s))!==0){let k=new Request(e,{method:"POST",body:s,duplex:"half"}),R;if(y.isFormData(s)&&(R=k.headers.get("content-type"))&&l.setContentType(R),k.body){const[x,H]=Fi(g,pn(ji(d)));s=$i(k.body,Bi,x,H)}}y.isString(h)||(h=h?"include":"omit");const b="credentials"in Request.prototype;p=new Request(e,{...u,signal:f,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:s,duplex:"half",credentials:b?h:void 0});let E=await fetch(p);const C=Ts&&(c==="stream"||c==="response");if(Ts&&(o||C&&m)){const k={};["status","statusText","headers"].forEach(se=>{k[se]=E[se]});const R=y.toFiniteNumber(E.headers.get("content-length")),[x,H]=o&&Fi(R,pn(ji(o),!0))||[];E=new Response($i(E.body,Bi,x,()=>{H&&H(),m&&m()}),k)}c=c||"text";let w=await mn[y.findKey(mn,c)||"text"](E,t);return!C&&m&&m(),await new Promise((k,R)=>{Sa(k,R,{data:w,headers:ae.from(E.headers),status:E.status,statusText:E.statusText,config:t,request:p})})}catch(b){throw m&&m(),b&&b.name==="TypeError"&&/fetch/i.test(b.message)?Object.assign(new O("Network Error",O.ERR_NETWORK,t,p),{cause:b.cause||b}):O.from(b,b&&b.code,t,p)}}),ws={http:bu,xhr:Ju,fetch:nl};y.forEach(ws,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch{}Object.defineProperty(t,"adapterName",{value:e})}});const qi=t=>`- ${t}`,sl=t=>y.isFunction(t)||t===null||t===!1,Ea={getAdapter:t=>{t=y.isArray(t)?t:[t];const{length:e}=t;let n,s;const i={};for(let r=0;r<e;r++){n=t[r];let a;if(s=n,!sl(n)&&(s=ws[(a=String(n)).toLowerCase()],s===void 0))throw new O(`Unknown adapter '${a}'`);if(s)break;i[a||"#"+r]=s}if(!s){const r=Object.entries(i).map(([o,d])=>`adapter ${o} `+(d===!1?"is not supported by the environment":"is not available in the build"));let a=e?r.length>1?`since :
`+r.map(qi).join(`
`):" "+qi(r[0]):"as no adapter specified";throw new O("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return s},adapters:ws};function es(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new ot(null,t)}function Hi(t){return es(t),t.headers=ae.from(t.headers),t.data=Qn.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),Ea.getAdapter(t.adapter||Ot.adapter)(t).then(function(s){return es(t),s.data=Qn.call(t,t.transformResponse,s),s.headers=ae.from(s.headers),s},function(s){return ya(s)||(es(t),s&&s.response&&(s.response.data=Qn.call(t,t.transformResponse,s.response),s.response.headers=ae.from(s.response.headers))),Promise.reject(s)})}const ka="1.7.9",Un={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{Un[t]=function(s){return typeof s===t||"a"+(e<1?"n ":" ")+t}});const Gi={};Un.transitional=function(e,n,s){function i(r,a){return"[Axios v"+ka+"] Transitional option '"+r+"'"+a+(s?". "+s:"")}return(r,a,o)=>{if(e===!1)throw new O(i(a," has been removed"+(n?" in "+n:"")),O.ERR_DEPRECATED);return n&&!Gi[a]&&(Gi[a]=!0,console.warn(i(a," has been deprecated since v"+n+" and will be removed in the near future"))),e?e(r,a,o):!0}};Un.spelling=function(e){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${e}`),!0)};function il(t,e,n){if(typeof t!="object")throw new O("options must be an object",O.ERR_BAD_OPTION_VALUE);const s=Object.keys(t);let i=s.length;for(;i-- >0;){const r=s[i],a=e[r];if(a){const o=t[r],d=o===void 0||a(o,r,t);if(d!==!0)throw new O("option "+r+" must be "+d,O.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new O("Unknown option "+r,O.ERR_BAD_OPTION)}}const an={assertOptions:il,validators:Un},ye=an.validators;class Fe{constructor(e){this.defaults=e,this.interceptors={request:new Ui,response:new Ui}}async request(e,n){try{return await this._request(e,n)}catch(s){if(s instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const r=i.stack?i.stack.replace(/^.+\n/,""):"";try{s.stack?r&&!String(s.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+r):s.stack=r}catch{}}throw s}}_request(e,n){typeof e=="string"?(n=n||{},n.url=e):n=e||{},n=Ve(this.defaults,n);const{transitional:s,paramsSerializer:i,headers:r}=n;s!==void 0&&an.assertOptions(s,{silentJSONParsing:ye.transitional(ye.boolean),forcedJSONParsing:ye.transitional(ye.boolean),clarifyTimeoutError:ye.transitional(ye.boolean)},!1),i!=null&&(y.isFunction(i)?n.paramsSerializer={serialize:i}:an.assertOptions(i,{encode:ye.function,serialize:ye.function},!0)),an.assertOptions(n,{baseUrl:ye.spelling("baseURL"),withXsrfToken:ye.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let a=r&&y.merge(r.common,r[n.method]);r&&y.forEach(["delete","get","head","post","put","patch","common"],p=>{delete r[p]}),n.headers=ae.concat(a,r);const o=[];let d=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(n)===!1||(d=d&&m.synchronous,o.unshift(m.fulfilled,m.rejected))});const c=[];this.interceptors.response.forEach(function(m){c.push(m.fulfilled,m.rejected)});let l,h=0,u;if(!d){const p=[Hi.bind(this),void 0];for(p.unshift.apply(p,o),p.push.apply(p,c),u=p.length,l=Promise.resolve(n);h<u;)l=l.then(p[h++],p[h++]);return l}u=o.length;let f=n;for(h=0;h<u;){const p=o[h++],m=o[h++];try{f=p(f)}catch(g){m.call(this,g);break}}try{l=Hi.call(this,f)}catch(p){return Promise.reject(p)}for(h=0,u=c.length;h<u;)l=l.then(c[h++],c[h++]);return l}getUri(e){e=Ve(this.defaults,e);const n=Ca(e.baseURL,e.url);return ma(n,e.params,e.paramsSerializer)}}y.forEach(["delete","get","head","options"],function(e){Fe.prototype[e]=function(n,s){return this.request(Ve(s||{},{method:e,url:n,data:(s||{}).data}))}});y.forEach(["post","put","patch"],function(e){function n(s){return function(r,a,o){return this.request(Ve(o||{},{method:e,headers:s?{"Content-Type":"multipart/form-data"}:{},url:r,data:a}))}}Fe.prototype[e]=n(),Fe.prototype[e+"Form"]=n(!0)});class Bs{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(r){n=r});const s=this;this.promise.then(i=>{if(!s._listeners)return;let r=s._listeners.length;for(;r-- >0;)s._listeners[r](i);s._listeners=null}),this.promise.then=i=>{let r;const a=new Promise(o=>{s.subscribe(o),r=o}).then(i);return a.cancel=function(){s.unsubscribe(r)},a},e(function(r,a,o){s.reason||(s.reason=new ot(r,a,o),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const n=this._listeners.indexOf(e);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const e=new AbortController,n=s=>{e.abort(s)};return this.subscribe(n),e.signal.unsubscribe=()=>this.unsubscribe(n),e.signal}static source(){let e;return{token:new Bs(function(i){e=i}),cancel:e}}}function rl(t){return function(n){return t.apply(null,n)}}function al(t){return y.isObject(t)&&t.isAxiosError===!0}const Es={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Es).forEach(([t,e])=>{Es[e]=t});function Ra(t){const e=new Fe(t),n=sa(Fe.prototype.request,e);return y.extend(n,Fe.prototype,e,{allOwnKeys:!0}),y.extend(n,e,null,{allOwnKeys:!0}),n.create=function(i){return Ra(Ve(t,i))},n}const W=Ra(Ot);W.Axios=Fe;W.CanceledError=ot;W.CancelToken=Bs;W.isCancel=ya;W.VERSION=ka;W.toFormData=xn;W.AxiosError=O;W.Cancel=W.CanceledError;W.all=function(e){return Promise.all(e)};W.spread=rl;W.isAxiosError=al;W.mergeConfig=Ve;W.AxiosHeaders=ae;W.formToJSON=t=>ba(y.isHTMLForm(t)?new FormData(t):t);W.getAdapter=Ea.getAdapter;W.HttpStatusCode=Es;W.default=W;var q;(function(t){t[t.cancelled=0]="cancelled",t[t.unknown=1]="unknown",t[t.invalid_argument=2]="invalid_argument",t[t.malformed=3]="malformed",t[t.deadline_exceeded=4]="deadline_exceeded",t[t.not_found=5]="not_found",t[t.bad_route=6]="bad_route",t[t.already_exists=7]="already_exists",t[t.permission_denied=8]="permission_denied",t[t.unauthenticated=9]="unauthenticated",t[t.resource_exhausted=10]="resource_exhausted",t[t.failed_precondition=11]="failed_precondition",t[t.aborted=12]="aborted",t[t.out_of_range=13]="out_of_range",t[t.unimplemented=14]="unimplemented",t[t.internal=15]="internal",t[t.unavailable=16]="unavailable",t[t.dataloss=17]="dataloss"})(q||(q={}));function ol(t,e,n){if(n)for(let[s,i]of Object.entries(n))if(typeof i=="string")t.append(s,i);else for(let r of i)t.append(s,r);return t.set("Content-Type",e?"application/json":"application/protobuf"),t.set("Accept",e?"application/json":"application/protobuf, application/json"),t}function cl(t){if(!cn(t)||typeof t.code!="string"||typeof t.msg!="string")return new ie("cannot read twirp error response",q[q.internal]);let e={};if(cn(t.meta))for(let[n,s]of Object.entries(t.meta))typeof s=="string"&&(e[n]=s);return new ie(t.msg,t.code,e)}function ul(t){let e={};return t.forEach((n,s)=>{s.toLowerCase()!=="content-type"&&s.toLowerCase()!=="content-length"&&(e.hasOwnProperty(s)?e[s].push(n):e[s]=n)}),e}class ll{constructor(e){this.defaultOptions=e}mergeOptions(e){return Pc(this.defaultOptions,e)}unary(e,n,s){var i,r,a;let o=s,d=this.makeUrl(e,o),c=(i=o.fetchInit)!==null&&i!==void 0?i:{},l=o.sendJson?e.I.toJsonString(n,o.jsonOptions):e.I.toBinary(n,o.binaryOptions),h=new Ht,u=new Ht,f=new Ht,p=new Ht;return globalThis.fetch(d,Object.assign(Object.assign({},c),{method:"POST",headers:ol(new globalThis.Headers,!!o.sendJson,o.meta),body:l,signal:(r=s.abort)!==null&&r!==void 0?r:null})).then(m=>{h.resolve(ul(m.headers));let g;try{g=m.type}catch{}switch(g){case"error":case"opaque":case"opaqueredirect":throw new ie(`fetch response type ${m.type}`,q[q.unknown])}return m.ok?o.sendJson?m.json().then(b=>e.O.fromJson(b,o.jsonOptions),()=>{throw new ie("unable to read response body as json",q[q.dataloss])}):m.arrayBuffer().then(b=>e.O.fromBinary(new Uint8Array(b),o.binaryOptions),()=>{throw new ie("unable to read response body",q[q.dataloss])}):m.json().then(b=>{throw cl(b)},()=>{throw new ie("received HTTP "+m.status+", unable to read response body as json",q[q.internal])})},m=>{throw m instanceof Error&&m.name==="AbortError"?new ie(m.message,q[q.cancelled]):new ie(m instanceof Error?m.message:m)}).then(m=>{u.resolve(m),f.resolve({code:"OK",detail:""}),p.resolve({})}).catch(m=>{let g=m instanceof ie?m:new ie(m instanceof Error?m.message:m,q[q.internal]);g.methodName=e.name,g.serviceName=e.service.typeName,h.rejectPending(g),u.rejectPending(g),f.rejectPending(g),p.rejectPending(g)}),new Ac(e,(a=o.meta)!==null&&a!==void 0?a:{},n,h.promise,u.promise,f.promise,p.promise)}makeUrl(e,n){let s=n.baseUrl;s.endsWith("/")&&(s=s.substring(0,s.length-1));let i=e.name;return n.useProtoMethodName!==!0&&(i=hn(i),i=i.substring(0,1).toUpperCase()+i.substring(1)),`${s}/${e.service.typeName}/${i}`}clientStreaming(e){const n=new ie("Client streaming is not supported by Twirp",q[q.unimplemented]);throw n.methodName=e.name,n.serviceName=e.service.typeName,n}duplex(e){const n=new ie("Duplex streaming is not supported by Twirp",q[q.unimplemented]);throw n.methodName=e.name,n.serviceName=e.service.typeName,n}serverStreaming(e){const n=new ie("Server streaming is not supported by Twirp",q[q.unimplemented]);throw n.methodName=e.name,n.serviceName=e.service.typeName,n}}var ks={exports:{}};(function(t,e){(function(n,s){var i="1.0.40",r="",a="?",o="function",d="undefined",c="object",l="string",h="major",u="model",f="name",p="type",m="vendor",g="version",b="architecture",E="console",C="mobile",w="tablet",k="smarttv",R="wearable",x="embedded",H=500,se="Amazon",me="Apple",ut="ASUS",oe="BlackBerry",Ne="Browser",xe="Chrome",Bn="Edge",Lt="Firefox",lt="Google",ai="Huawei",qn="LG",Hn="Microsoft",oi="Motorola",dt="Opera",ht="Samsung",ci="Sharp",Ut="Sony",Gn="Xiaomi",Wn="Zebra",ui="Facebook",li="Chromium OS",di="Mac OS",hi=" Browser",Ao=function(N,L){var P={};for(var $ in N)L[$]&&L[$].length%2===0?P[$]=L[$].concat(N[$]):P[$]=N[$];return P},Mt=function(N){for(var L={},P=0;P<N.length;P++)L[N[P].toUpperCase()]=N[P];return L},fi=function(N,L){return typeof N===l?Ke(L).indexOf(Ke(N))!==-1:!1},Ke=function(N){return N.toLowerCase()},Do=function(N){return typeof N===l?N.replace(/[^\d\.]/g,r).split(".")[0]:s},Jn=function(N,L){if(typeof N===l)return N=N.replace(/^\s\s*/,r),typeof L===d?N:N.substring(0,H)},ft=function(N,L){for(var P=0,$,ke,ge,M,_,be;P<L.length&&!_;){var Kn=L[P],gi=L[P+1];for($=ke=0;$<Kn.length&&!_&&Kn[$];)if(_=Kn[$++].exec(N),_)for(ge=0;ge<gi.length;ge++)be=_[++ke],M=gi[ge],typeof M===c&&M.length>0?M.length===2?typeof M[1]==o?this[M[0]]=M[1].call(this,be):this[M[0]]=M[1]:M.length===3?typeof M[1]===o&&!(M[1].exec&&M[1].test)?this[M[0]]=be?M[1].call(this,be,M[2]):s:this[M[0]]=be?be.replace(M[1],M[2]):s:M.length===4&&(this[M[0]]=be?M[3].call(this,be.replace(M[1],M[2])):s):this[M]=be||s;P+=2}},Ft=function(N,L){for(var P in L)if(typeof L[P]===c&&L[P].length>0){for(var $=0;$<L[P].length;$++)if(fi(L[P][$],N))return P===a?s:P}else if(fi(L[P],N))return P===a?s:P;return L.hasOwnProperty("*")?L["*"]:N},No={"1.0":"/8","1.2":"/1","1.3":"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},pi={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2","8.1":"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},mi={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[g,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[g,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,g],[/opios[\/ ]+([\w\.]+)/i],[g,[f,dt+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[g,[f,dt+" GX"]],[/\bopr\/([\w\.]+)/i],[g,[f,dt]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[g,[f,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[g,[f,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,/(heytap|ovi|115)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,g],[/quark(?:pc)?\/([-\w\.]+)/i],[g,[f,"Quark"]],[/\bddg\/([\w\.]+)/i],[g,[f,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[g,[f,"UC"+Ne]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[g,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[g,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[g,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[g,[f,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[g,[f,"Smart Lenovo "+Ne]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+Ne],g],[/\bfocus\/([\w\.]+)/i],[g,[f,Lt+" Focus"]],[/\bopt\/([\w\.]+)/i],[g,[f,dt+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[g,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[g,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[g,[f,dt+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[g,[f,"MIUI"+hi]],[/fxios\/([\w\.-]+)/i],[g,[f,Lt]],[/\bqihoobrowser\/?([\w\.]*)/i],[g,[f,"360"]],[/\b(qq)\/([\w\.]+)/i],[[f,/(.+)/,"$1Browser"],g],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1"+hi],g],[/samsungbrowser\/([\w\.]+)/i],[g,[f,ht+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[g,[f,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[f,"Sogou Mobile"],g],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[f,g],[/(lbbrowser|rekonq)/i,/\[(linkedin)app\]/i],[f],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[g,f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,ui],g],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[f,g],[/\bgsa\/([\w\.]+) .*safari\//i],[g,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[g,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[g,[f,xe+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,xe+" WebView"],g],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[g,[f,"Android "+Ne]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,g],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[g,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[g,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[g,Ft,No]],[/(webkit|khtml)\/([\w\.]+)/i],[f,g],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],g],[/(wolvic|librewolf)\/([\w\.]+)/i],[f,g],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[g,[f,Lt+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[f,[g,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[f,[g,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,Ke]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,r,Ke]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,Ke]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[u,[m,ht],[p,w]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[u,[m,ht],[p,C]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[u,[m,me],[p,C]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[u,[m,me],[p,w]],[/(macintosh);/i],[u,[m,me]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[u,[m,ci],[p,C]],[/(?:honor)([-\w ]+)[;\)]/i],[u,[m,"Honor"],[p,C]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[u,[m,ai],[p,w]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[u,[m,ai],[p,C]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i],[[u,/_/g," "],[m,Gn],[p,C]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[u,/_/g," "],[m,Gn],[p,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[u,[m,"OPPO"],[p,C]],[/\b(opd2\d{3}a?) bui/i],[u,[m,"OPPO"],[p,w]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[u,[m,"Vivo"],[p,C]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[u,[m,"Realme"],[p,C]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[u,[m,oi],[p,C]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[u,[m,oi],[p,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[u,[m,qn],[p,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[u,[m,qn],[p,C]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[u,[m,"Lenovo"],[p,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[u,/_/g," "],[m,"Nokia"],[p,C]],[/(pixel c)\b/i],[u,[m,lt],[p,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[u,[m,lt],[p,C]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[u,[m,Ut],[p,C]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[u,"Xperia Tablet"],[m,Ut],[p,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[u,[m,"OnePlus"],[p,C]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[u,[m,se],[p,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[u,/(.+)/g,"Fire Phone $1"],[m,se],[p,C]],[/(playbook);[-\w\),; ]+(rim)/i],[u,m,[p,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[u,[m,oe],[p,C]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[u,[m,ut],[p,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[u,[m,ut],[p,C]],[/(nexus 9)/i],[u,[m,"HTC"],[p,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[u,/_/g," "],[p,C]],[/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\w*(\)| bui)/i],[u,[m,"TCL"],[p,w]],[/(itel) ((\w+))/i],[[m,Ke],u,[p,Ft,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[u,[m,"Acer"],[p,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[u,[m,"Meizu"],[p,C]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[u,[m,"Ulefone"],[p,C]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[u,[m,"Energizer"],[p,C]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[u,[m,"Cat"],[p,C]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[u,[m,"Smartfren"],[p,C]],[/droid.+; (a(?:015|06[35]|142p?))/i],[u,[m,"Nothing"],[p,C]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (imo) ((?!tab)[\w ]+?)(?: bui|\))/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,u,[p,C]],[/(imo) (tab \w+)/i,/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,u,[p,w]],[/(surface duo)/i],[u,[m,Hn],[p,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[u,[m,"Fairphone"],[p,C]],[/(u304aa)/i],[u,[m,"AT&T"],[p,C]],[/\bsie-(\w*)/i],[u,[m,"Siemens"],[p,C]],[/\b(rct\w+) b/i],[u,[m,"RCA"],[p,w]],[/\b(venue[\d ]{2,7}) b/i],[u,[m,"Dell"],[p,w]],[/\b(q(?:mv|ta)\w+) b/i],[u,[m,"Verizon"],[p,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[u,[m,"Barnes & Noble"],[p,w]],[/\b(tm\d{3}\w+) b/i],[u,[m,"NuVision"],[p,w]],[/\b(k88) b/i],[u,[m,"ZTE"],[p,w]],[/\b(nx\d{3}j) b/i],[u,[m,"ZTE"],[p,C]],[/\b(gen\d{3}) b.+49h/i],[u,[m,"Swiss"],[p,C]],[/\b(zur\d{3}) b/i],[u,[m,"Swiss"],[p,w]],[/\b((zeki)?tb.*\b) b/i],[u,[m,"Zeki"],[p,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],u,[p,w]],[/\b(ns-?\w{0,9}) b/i],[u,[m,"Insignia"],[p,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[u,[m,"NextBook"],[p,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],u,[p,C]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],u,[p,C]],[/\b(ph-1) /i],[u,[m,"Essential"],[p,C]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[u,[m,"Envizen"],[p,w]],[/\b(trio[-\w\. ]+) b/i],[u,[m,"MachSpeed"],[p,w]],[/\btu_(1491) b/i],[u,[m,"Rotor"],[p,w]],[/(shield[\w ]+) b/i],[u,[m,"Nvidia"],[p,w]],[/(sprint) (\w+)/i],[m,u,[p,C]],[/(kin\.[onetw]{3})/i],[[u,/\./g," "],[m,Hn],[p,C]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[u,[m,Wn],[p,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[u,[m,Wn],[p,C]],[/smart-tv.+(samsung)/i],[m,[p,k]],[/hbbtv.+maple;(\d+)/i],[[u,/^/,"SmartTV"],[m,ht],[p,k]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,qn],[p,k]],[/(apple) ?tv/i],[m,[u,me+" TV"],[p,k]],[/crkey/i],[[u,xe+"cast"],[m,lt],[p,k]],[/droid.+aft(\w+)( bui|\))/i],[u,[m,se],[p,k]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[u,[m,ci],[p,k]],[/(bravia[\w ]+)( bui|\))/i],[u,[m,Ut],[p,k]],[/(mitv-\w{5}) bui/i],[u,[m,Gn],[p,k]],[/Hbbtv.*(technisat) (.*);/i],[m,u,[p,k]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,Jn],[u,Jn],[p,k]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,k]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,u,[p,E]],[/droid.+; (shield) bui/i],[u,[m,"Nvidia"],[p,E]],[/(playstation [345portablevi]+)/i],[u,[m,Ut],[p,E]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[u,[m,Hn],[p,E]],[/\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i],[u,[m,ht],[p,R]],[/((pebble))app/i],[m,u,[p,R]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[u,[m,me],[p,R]],[/droid.+; (glass) \d/i],[u,[m,lt],[p,R]],[/droid.+; (wt63?0{2,3})\)/i],[u,[m,Wn],[p,R]],[/droid.+; (glass) \d/i],[u,[m,lt],[p,R]],[/(pico) (4|neo3(?: link|pro)?)/i],[m,u,[p,R]],[/; (quest( \d| pro)?)/i],[u,[m,ui],[p,R]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[p,x]],[/(aeobc)\b/i],[u,[m,se],[p,x]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[u,[p,C]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[u,[p,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,C]],[/(android[-\w\. ]{0,9});.+buil/i],[u,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[g,[f,Bn+"HTML"]],[/(arkweb)\/([\w\.]+)/i],[f,g],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[g,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,g],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[g,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,g],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[f,[g,Ft,pi]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[g,Ft,pi],[f,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[g,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,di],[g,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[g,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,g],[/\(bb(10);/i],[g,[f,oe]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[g,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[g,[f,Lt+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[g,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[g,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[g,[f,xe+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,li],g],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,g],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],g],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,g]]},le=function(N,L){if(typeof N===c&&(L=N,N=s),!(this instanceof le))return new le(N,L).getResult();var P=typeof n!==d&&n.navigator?n.navigator:s,$=N||(P&&P.userAgent?P.userAgent:r),ke=P&&P.userAgentData?P.userAgentData:s,ge=L?Ao(mi,L):mi,M=P&&P.userAgent==$;return this.getBrowser=function(){var _={};return _[f]=s,_[g]=s,ft.call(_,$,ge.browser),_[h]=Do(_[g]),M&&P&&P.brave&&typeof P.brave.isBrave==o&&(_[f]="Brave"),_},this.getCPU=function(){var _={};return _[b]=s,ft.call(_,$,ge.cpu),_},this.getDevice=function(){var _={};return _[m]=s,_[u]=s,_[p]=s,ft.call(_,$,ge.device),M&&!_[p]&&ke&&ke.mobile&&(_[p]=C),M&&_[u]=="Macintosh"&&P&&typeof P.standalone!==d&&P.maxTouchPoints&&P.maxTouchPoints>2&&(_[u]="iPad",_[p]=w),_},this.getEngine=function(){var _={};return _[f]=s,_[g]=s,ft.call(_,$,ge.engine),_},this.getOS=function(){var _={};return _[f]=s,_[g]=s,ft.call(_,$,ge.os),M&&!_[f]&&ke&&ke.platform&&ke.platform!="Unknown"&&(_[f]=ke.platform.replace(/chrome os/i,li).replace(/macos/i,di)),_},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return $},this.setUA=function(_){return $=typeof _===l&&_.length>H?Jn(_,H):_,this},this.setUA($),this};le.VERSION=i,le.BROWSER=Mt([f,g,h]),le.CPU=Mt([b]),le.DEVICE=Mt([u,m,p,E,C,k,w,R,x]),le.ENGINE=le.OS=Mt([f,g]),t.exports&&(e=t.exports=le),e.UAParser=le;var ze=typeof n!==d&&(n.jQuery||n.Zepto);if(ze&&!ze.ua){var jt=new le;ze.ua=jt.getResult(),ze.ua.get=function(){return jt.getUA()},ze.ua.set=function(N){jt.setUA(N);var L=jt.getResult();for(var P in L)ze.ua[P]=L[P]}}})(typeof window=="object"?window:Lo)})(ks,ks.exports);var dl=ks.exports,Rs=function(t,e){return Rs=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,s){n.__proto__=s}||function(n,s){for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(n[i]=s[i])},Rs(t,e)};function Te(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");Rs(t,e);function n(){this.constructor=t}t.prototype=e===null?Object.create(e):(n.prototype=e.prototype,new n)}function hl(t,e,n,s){function i(r){return r instanceof n?r:new n(function(a){a(r)})}return new(n||(n=Promise))(function(r,a){function o(l){try{c(s.next(l))}catch(h){a(h)}}function d(l){try{c(s.throw(l))}catch(h){a(h)}}function c(l){l.done?r(l.value):i(l.value).then(o,d)}c((s=s.apply(t,e||[])).next())})}function Ia(t,e){var n={label:0,sent:function(){if(r[0]&1)throw r[1];return r[1]},trys:[],ops:[]},s,i,r,a=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return a.next=o(0),a.throw=o(1),a.return=o(2),typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function o(c){return function(l){return d([c,l])}}function d(c){if(s)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(n=0)),n;)try{if(s=1,i&&(r=c[0]&2?i.return:c[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,c[1])).done)return r;switch(i=0,r&&(c=[c[0]&2,r.value]),c[0]){case 0:case 1:r=c;break;case 4:return n.label++,{value:c[1],done:!1};case 5:n.label++,i=c[1],c=[0];continue;case 7:c=n.ops.pop(),n.trys.pop();continue;default:if(r=n.trys,!(r=r.length>0&&r[r.length-1])&&(c[0]===6||c[0]===2)){n=0;continue}if(c[0]===3&&(!r||c[1]>r[0]&&c[1]<r[3])){n.label=c[1];break}if(c[0]===6&&n.label<r[1]){n.label=r[1],r=c;break}if(r&&n.label<r[2]){n.label=r[2],n.ops.push(c);break}r[2]&&n.ops.pop(),n.trys.pop();continue}c=e.call(t,n)}catch(l){c=[6,l],i=0}finally{s=r=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function nt(t){var e=typeof Symbol=="function"&&Symbol.iterator,n=e&&t[e],s=0;if(n)return n.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&s>=t.length&&(t=void 0),{value:t&&t[s++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function $e(t,e){var n=typeof Symbol=="function"&&t[Symbol.iterator];if(!n)return t;var s=n.call(t),i,r=[],a;try{for(;(e===void 0||e-- >0)&&!(i=s.next()).done;)r.push(i.value)}catch(o){a={error:o}}finally{try{i&&!i.done&&(n=s.return)&&n.call(s)}finally{if(a)throw a.error}}return r}function st(t,e,n){if(n||arguments.length===2)for(var s=0,i=e.length,r;s<i;s++)(r||!(s in e))&&(r||(r=Array.prototype.slice.call(e,0,s)),r[s]=e[s]);return t.concat(r||Array.prototype.slice.call(e))}function Qe(t){return this instanceof Qe?(this.v=t,this):new Qe(t)}function fl(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s=n.apply(t,e||[]),i,r=[];return i=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",a),i[Symbol.asyncIterator]=function(){return this},i;function a(f){return function(p){return Promise.resolve(p).then(f,h)}}function o(f,p){s[f]&&(i[f]=function(m){return new Promise(function(g,b){r.push([f,m,g,b])>1||d(f,m)})},p&&(i[f]=p(i[f])))}function d(f,p){try{c(s[f](p))}catch(m){u(r[0][3],m)}}function c(f){f.value instanceof Qe?Promise.resolve(f.value.v).then(l,h):u(r[0][2],f)}function l(f){d("next",f)}function h(f){d("throw",f)}function u(f,p){f(p),r.shift(),r.length&&d(r[0][0],r[0][1])}}function pl(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],n;return e?e.call(t):(t=typeof nt=="function"?nt(t):t[Symbol.iterator](),n={},s("next"),s("throw"),s("return"),n[Symbol.asyncIterator]=function(){return this},n);function s(r){n[r]=t[r]&&function(a){return new Promise(function(o,d){a=t[r](a),i(o,d,a.done,a.value)})}}function i(r,a,o,d){Promise.resolve(d).then(function(c){r({value:c,done:o})},a)}}function F(t){return typeof t=="function"}function _a(t){var e=function(s){Error.call(s),s.stack=new Error().stack},n=t(e);return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var ts=_a(function(t){return function(n){t(this),this.message=n?n.length+` errors occurred during unsubscription:
`+n.map(function(s,i){return i+1+") "+s.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=n}});function gn(t,e){if(t){var n=t.indexOf(e);0<=n&&t.splice(n,1)}}var At=function(){function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){var e,n,s,i,r;if(!this.closed){this.closed=!0;var a=this._parentage;if(a)if(this._parentage=null,Array.isArray(a))try{for(var o=nt(a),d=o.next();!d.done;d=o.next()){var c=d.value;c.remove(this)}}catch(m){e={error:m}}finally{try{d&&!d.done&&(n=o.return)&&n.call(o)}finally{if(e)throw e.error}}else a.remove(this);var l=this.initialTeardown;if(F(l))try{l()}catch(m){r=m instanceof ts?m.errors:[m]}var h=this._finalizers;if(h){this._finalizers=null;try{for(var u=nt(h),f=u.next();!f.done;f=u.next()){var p=f.value;try{Wi(p)}catch(m){r=r??[],m instanceof ts?r=st(st([],$e(r)),$e(m.errors)):r.push(m)}}}catch(m){s={error:m}}finally{try{f&&!f.done&&(i=u.return)&&i.call(u)}finally{if(s)throw s.error}}}if(r)throw new ts(r)}},t.prototype.add=function(e){var n;if(e&&e!==this)if(this.closed)Wi(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(e)}},t.prototype._hasParent=function(e){var n=this._parentage;return n===e||Array.isArray(n)&&n.includes(e)},t.prototype._addParent=function(e){var n=this._parentage;this._parentage=Array.isArray(n)?(n.push(e),n):n?[n,e]:e},t.prototype._removeParent=function(e){var n=this._parentage;n===e?this._parentage=null:Array.isArray(n)&&gn(n,e)},t.prototype.remove=function(e){var n=this._finalizers;n&&gn(n,e),e instanceof t&&e._removeParent(this)},t.EMPTY=function(){var e=new t;return e.closed=!0,e}(),t}(),Pa=At.EMPTY;function Oa(t){return t instanceof At||t&&"closed"in t&&F(t.remove)&&F(t.add)&&F(t.unsubscribe)}function Wi(t){F(t)?t():t.unsubscribe()}var ml={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},Aa={setTimeout:function(t,e){for(var n=[],s=2;s<arguments.length;s++)n[s-2]=arguments[s];return setTimeout.apply(void 0,st([t,e],$e(n)))},clearTimeout:function(t){var e=Aa.delegate;return((e==null?void 0:e.clearTimeout)||clearTimeout)(t)},delegate:void 0};function Da(t){Aa.setTimeout(function(){throw t})}function Ji(){}function on(t){t()}var qs=function(t){Te(e,t);function e(n){var s=t.call(this)||this;return s.isStopped=!1,n?(s.destination=n,Oa(n)&&n.add(s)):s.destination=yl,s}return e.create=function(n,s,i){return new wt(n,s,i)},e.prototype.next=function(n){this.isStopped||this._next(n)},e.prototype.error=function(n){this.isStopped||(this.isStopped=!0,this._error(n))},e.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},e.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this),this.destination=null)},e.prototype._next=function(n){this.destination.next(n)},e.prototype._error=function(n){try{this.destination.error(n)}finally{this.unsubscribe()}},e.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},e}(At),gl=function(){function t(e){this.partialObserver=e}return t.prototype.next=function(e){var n=this.partialObserver;if(n.next)try{n.next(e)}catch(s){Gt(s)}},t.prototype.error=function(e){var n=this.partialObserver;if(n.error)try{n.error(e)}catch(s){Gt(s)}else Gt(e)},t.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(n){Gt(n)}},t}(),wt=function(t){Te(e,t);function e(n,s,i){var r=t.call(this)||this,a;return F(n)||!n?a={next:n??void 0,error:s??void 0,complete:i??void 0}:a=n,r.destination=new gl(a),r}return e}(qs);function Gt(t){Da(t)}function bl(t){throw t}var yl={closed:!0,next:Ji,error:bl,complete:Ji},Hs=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function Dt(t){return t}function Sl(t){return t.length===0?Dt:t.length===1?t[0]:function(n){return t.reduce(function(s,i){return i(s)},n)}}var ne=function(){function t(e){e&&(this._subscribe=e)}return t.prototype.lift=function(e){var n=new t;return n.source=this,n.operator=e,n},t.prototype.subscribe=function(e,n,s){var i=this,r=vl(e)?e:new wt(e,n,s);return on(function(){var a=i,o=a.operator,d=a.source;r.add(o?o.call(r,d):d?i._subscribe(r):i._trySubscribe(r))}),r},t.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(n){e.error(n)}},t.prototype.forEach=function(e,n){var s=this;return n=Ki(n),new n(function(i,r){var a=new wt({next:function(o){try{e(o)}catch(d){r(d),a.unsubscribe()}},error:r,complete:i});s.subscribe(a)})},t.prototype._subscribe=function(e){var n;return(n=this.source)===null||n===void 0?void 0:n.subscribe(e)},t.prototype[Hs]=function(){return this},t.prototype.pipe=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return Sl(e)(this)},t.prototype.toPromise=function(e){var n=this;return e=Ki(e),new e(function(s,i){var r;n.subscribe(function(a){return r=a},function(a){return i(a)},function(){return s(r)})})},t.create=function(e){return new t(e)},t}();function Ki(t){var e;return(e=t??ml.Promise)!==null&&e!==void 0?e:Promise}function Cl(t){return t&&F(t.next)&&F(t.error)&&F(t.complete)}function vl(t){return t&&t instanceof qs||Cl(t)&&Oa(t)}function Tl(t){return F(t==null?void 0:t.lift)}function we(t){return function(e){if(Tl(e))return e.lift(function(n){try{return t(n,this)}catch(s){this.error(s)}});throw new TypeError("Unable to lift unknown Observable type")}}function Pe(t,e,n,s,i){return new wl(t,e,n,s,i)}var wl=function(t){Te(e,t);function e(n,s,i,r,a,o){var d=t.call(this,n)||this;return d.onFinalize=a,d.shouldUnsubscribe=o,d._next=s?function(c){try{s(c)}catch(l){n.error(l)}}:t.prototype._next,d._error=r?function(c){try{r(c)}catch(l){n.error(l)}finally{this.unsubscribe()}}:t.prototype._error,d._complete=i?function(){try{i()}catch(c){n.error(c)}finally{this.unsubscribe()}}:t.prototype._complete,d}return e.prototype.unsubscribe=function(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var s=this.closed;t.prototype.unsubscribe.call(this),!s&&((n=this.onFinalize)===null||n===void 0||n.call(this))}},e}(qs),El=_a(function(t){return function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),Mn=function(t){Te(e,t);function e(){var n=t.call(this)||this;return n.closed=!1,n.currentObservers=null,n.observers=[],n.isStopped=!1,n.hasError=!1,n.thrownError=null,n}return e.prototype.lift=function(n){var s=new zi(this,this);return s.operator=n,s},e.prototype._throwIfClosed=function(){if(this.closed)throw new El},e.prototype.next=function(n){var s=this;on(function(){var i,r;if(s._throwIfClosed(),!s.isStopped){s.currentObservers||(s.currentObservers=Array.from(s.observers));try{for(var a=nt(s.currentObservers),o=a.next();!o.done;o=a.next()){var d=o.value;d.next(n)}}catch(c){i={error:c}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}}})},e.prototype.error=function(n){var s=this;on(function(){if(s._throwIfClosed(),!s.isStopped){s.hasError=s.isStopped=!0,s.thrownError=n;for(var i=s.observers;i.length;)i.shift().error(n)}})},e.prototype.complete=function(){var n=this;on(function(){if(n._throwIfClosed(),!n.isStopped){n.isStopped=!0;for(var s=n.observers;s.length;)s.shift().complete()}})},e.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(e.prototype,"observed",{get:function(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0},enumerable:!1,configurable:!0}),e.prototype._trySubscribe=function(n){return this._throwIfClosed(),t.prototype._trySubscribe.call(this,n)},e.prototype._subscribe=function(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)},e.prototype._innerSubscribe=function(n){var s=this,i=this,r=i.hasError,a=i.isStopped,o=i.observers;return r||a?Pa:(this.currentObservers=null,o.push(n),new At(function(){s.currentObservers=null,gn(o,n)}))},e.prototype._checkFinalizedStatuses=function(n){var s=this,i=s.hasError,r=s.thrownError,a=s.isStopped;i?n.error(r):a&&n.complete()},e.prototype.asObservable=function(){var n=new ne;return n.source=this,n},e.create=function(n,s){return new zi(n,s)},e}(ne),zi=function(t){Te(e,t);function e(n,s){var i=t.call(this)||this;return i.destination=n,i.source=s,i}return e.prototype.next=function(n){var s,i;(i=(s=this.destination)===null||s===void 0?void 0:s.next)===null||i===void 0||i.call(s,n)},e.prototype.error=function(n){var s,i;(i=(s=this.destination)===null||s===void 0?void 0:s.error)===null||i===void 0||i.call(s,n)},e.prototype.complete=function(){var n,s;(s=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||s===void 0||s.call(n)},e.prototype._subscribe=function(n){var s,i;return(i=(s=this.source)===null||s===void 0?void 0:s.subscribe(n))!==null&&i!==void 0?i:Pa},e}(Mn),A=function(t){Te(e,t);function e(n){var s=t.call(this)||this;return s._value=n,s}return Object.defineProperty(e.prototype,"value",{get:function(){return this.getValue()},enumerable:!1,configurable:!0}),e.prototype._subscribe=function(n){var s=t.prototype._subscribe.call(this,n);return!s.closed&&n.next(this._value),s},e.prototype.getValue=function(){var n=this,s=n.hasError,i=n.thrownError,r=n._value;if(s)throw i;return this._throwIfClosed(),r},e.prototype.next=function(n){t.prototype.next.call(this,this._value=n)},e}(Mn),Gs={now:function(){return(Gs.delegate||Date).now()},delegate:void 0},Is=function(t){Te(e,t);function e(n,s,i){n===void 0&&(n=1/0),s===void 0&&(s=1/0),i===void 0&&(i=Gs);var r=t.call(this)||this;return r._bufferSize=n,r._windowTime=s,r._timestampProvider=i,r._buffer=[],r._infiniteTimeWindow=!0,r._infiniteTimeWindow=s===1/0,r._bufferSize=Math.max(1,n),r._windowTime=Math.max(1,s),r}return e.prototype.next=function(n){var s=this,i=s.isStopped,r=s._buffer,a=s._infiniteTimeWindow,o=s._timestampProvider,d=s._windowTime;i||(r.push(n),!a&&r.push(o.now()+d)),this._trimBuffer(),t.prototype.next.call(this,n)},e.prototype._subscribe=function(n){this._throwIfClosed(),this._trimBuffer();for(var s=this._innerSubscribe(n),i=this,r=i._infiniteTimeWindow,a=i._buffer,o=a.slice(),d=0;d<o.length&&!n.closed;d+=r?1:2)n.next(o[d]);return this._checkFinalizedStatuses(n),s},e.prototype._trimBuffer=function(){var n=this,s=n._bufferSize,i=n._timestampProvider,r=n._buffer,a=n._infiniteTimeWindow,o=(a?1:2)*s;if(s<1/0&&o<r.length&&r.splice(0,r.length-o),!a){for(var d=i.now(),c=0,l=1;l<r.length&&r[l]<=d;l+=2)c=l;c&&r.splice(0,c+1)}},e}(Mn),kl=function(t){Te(e,t);function e(n,s){return t.call(this)||this}return e.prototype.schedule=function(n,s){return this},e}(At),Yi={setInterval:function(t,e){for(var n=[],s=2;s<arguments.length;s++)n[s-2]=arguments[s];return setInterval.apply(void 0,st([t,e],$e(n)))},clearInterval:function(t){return clearInterval(t)},delegate:void 0},Rl=function(t){Te(e,t);function e(n,s){var i=t.call(this,n,s)||this;return i.scheduler=n,i.work=s,i.pending=!1,i}return e.prototype.schedule=function(n,s){var i;if(s===void 0&&(s=0),this.closed)return this;this.state=n;var r=this.id,a=this.scheduler;return r!=null&&(this.id=this.recycleAsyncId(a,r,s)),this.pending=!0,this.delay=s,this.id=(i=this.id)!==null&&i!==void 0?i:this.requestAsyncId(a,this.id,s),this},e.prototype.requestAsyncId=function(n,s,i){return i===void 0&&(i=0),Yi.setInterval(n.flush.bind(n,this),i)},e.prototype.recycleAsyncId=function(n,s,i){if(i===void 0&&(i=0),i!=null&&this.delay===i&&this.pending===!1)return s;s!=null&&Yi.clearInterval(s)},e.prototype.execute=function(n,s){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var i=this._execute(n,s);if(i)return i;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},e.prototype._execute=function(n,s){var i=!1,r;try{this.work(n)}catch(a){i=!0,r=a||new Error("Scheduled action threw falsy error")}if(i)return this.unsubscribe(),r},e.prototype.unsubscribe=function(){if(!this.closed){var n=this,s=n.id,i=n.scheduler,r=i.actions;this.work=this.state=this.scheduler=null,this.pending=!1,gn(r,this),s!=null&&(this.id=this.recycleAsyncId(i,s,null)),this.delay=null,t.prototype.unsubscribe.call(this)}},e}(kl),Xi=function(){function t(e,n){n===void 0&&(n=t.now),this.schedulerActionCtor=e,this.now=n}return t.prototype.schedule=function(e,n,s){return n===void 0&&(n=0),new this.schedulerActionCtor(this,e).schedule(s,n)},t.now=Gs.now,t}(),Il=function(t){Te(e,t);function e(n,s){s===void 0&&(s=Xi.now);var i=t.call(this,n,s)||this;return i.actions=[],i._active=!1,i}return e.prototype.flush=function(n){var s=this.actions;if(this._active){s.push(n);return}var i;this._active=!0;do if(i=n.execute(n.state,n.delay))break;while(n=s.shift());if(this._active=!1,i){for(;n=s.shift();)n.unsubscribe();throw i}},e}(Xi),_l=new Il(Rl),Pl=new ne(function(t){return t.complete()});function Ol(t){return t&&F(t.schedule)}function Ws(t){return t[t.length-1]}function Al(t){return F(Ws(t))?t.pop():void 0}function Nt(t){return Ol(Ws(t))?t.pop():void 0}function Dl(t,e){return typeof Ws(t)=="number"?t.pop():e}var Js=function(t){return t&&typeof t.length=="number"&&typeof t!="function"};function Na(t){return F(t==null?void 0:t.then)}function xa(t){return F(t[Hs])}function La(t){return Symbol.asyncIterator&&F(t==null?void 0:t[Symbol.asyncIterator])}function Ua(t){return new TypeError("You provided "+(t!==null&&typeof t=="object"?"an invalid object":"'"+t+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}function Nl(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Ma=Nl();function Fa(t){return F(t==null?void 0:t[Ma])}function ja(t){return fl(this,arguments,function(){var n,s,i,r;return Ia(this,function(a){switch(a.label){case 0:n=t.getReader(),a.label=1;case 1:a.trys.push([1,,9,10]),a.label=2;case 2:return[4,Qe(n.read())];case 3:return s=a.sent(),i=s.value,r=s.done,r?[4,Qe(void 0)]:[3,5];case 4:return[2,a.sent()];case 5:return[4,Qe(i)];case 6:return[4,a.sent()];case 7:return a.sent(),[3,2];case 8:return[3,10];case 9:return n.releaseLock(),[7];case 10:return[2]}})})}function Va(t){return F(t==null?void 0:t.getReader)}function Ae(t){if(t instanceof ne)return t;if(t!=null){if(xa(t))return xl(t);if(Js(t))return Ll(t);if(Na(t))return Ul(t);if(La(t))return $a(t);if(Fa(t))return Ml(t);if(Va(t))return Fl(t)}throw Ua(t)}function xl(t){return new ne(function(e){var n=t[Hs]();if(F(n.subscribe))return n.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Ll(t){return new ne(function(e){for(var n=0;n<t.length&&!e.closed;n++)e.next(t[n]);e.complete()})}function Ul(t){return new ne(function(e){t.then(function(n){e.closed||(e.next(n),e.complete())},function(n){return e.error(n)}).then(null,Da)})}function Ml(t){return new ne(function(e){var n,s;try{for(var i=nt(t),r=i.next();!r.done;r=i.next()){var a=r.value;if(e.next(a),e.closed)return}}catch(o){n={error:o}}finally{try{r&&!r.done&&(s=i.return)&&s.call(i)}finally{if(n)throw n.error}}e.complete()})}function $a(t){return new ne(function(e){jl(t,e).catch(function(n){return e.error(n)})})}function Fl(t){return $a(ja(t))}function jl(t,e){var n,s,i,r;return hl(this,void 0,void 0,function(){var a,o;return Ia(this,function(d){switch(d.label){case 0:d.trys.push([0,5,6,11]),n=pl(t),d.label=1;case 1:return[4,n.next()];case 2:if(s=d.sent(),!!s.done)return[3,4];if(a=s.value,e.next(a),e.closed)return[2];d.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return o=d.sent(),i={error:o},[3,11];case 6:return d.trys.push([6,,9,10]),s&&!s.done&&(r=n.return)?[4,r.call(n)]:[3,8];case 7:d.sent(),d.label=8;case 8:return[3,10];case 9:if(i)throw i.error;return[7];case 10:return[7];case 11:return e.complete(),[2]}})})}function De(t,e,n,s,i){s===void 0&&(s=0),i===void 0&&(i=!1);var r=e.schedule(function(){n(),i?t.add(this.schedule(null,s)):this.unsubscribe()},s);if(t.add(r),!i)return r}function Ba(t,e){return e===void 0&&(e=0),we(function(n,s){n.subscribe(Pe(s,function(i){return De(s,t,function(){return s.next(i)},e)},function(){return De(s,t,function(){return s.complete()},e)},function(i){return De(s,t,function(){return s.error(i)},e)}))})}function qa(t,e){return e===void 0&&(e=0),we(function(n,s){s.add(t.schedule(function(){return n.subscribe(s)},e))})}function Vl(t,e){return Ae(t).pipe(qa(e),Ba(e))}function $l(t,e){return Ae(t).pipe(qa(e),Ba(e))}function Bl(t,e){return new ne(function(n){var s=0;return e.schedule(function(){s===t.length?n.complete():(n.next(t[s++]),n.closed||this.schedule())})})}function ql(t,e){return new ne(function(n){var s;return De(n,e,function(){s=t[Ma](),De(n,e,function(){var i,r,a;try{i=s.next(),r=i.value,a=i.done}catch(o){n.error(o);return}a?n.complete():n.next(r)},0,!0)}),function(){return F(s==null?void 0:s.return)&&s.return()}})}function Ha(t,e){if(!t)throw new Error("Iterable cannot be null");return new ne(function(n){De(n,e,function(){var s=t[Symbol.asyncIterator]();De(n,e,function(){s.next().then(function(i){i.done?n.complete():n.next(i.value)})},0,!0)})})}function Hl(t,e){return Ha(ja(t),e)}function Gl(t,e){if(t!=null){if(xa(t))return Vl(t,e);if(Js(t))return Bl(t,e);if(Na(t))return $l(t,e);if(La(t))return Ha(t,e);if(Fa(t))return ql(t,e);if(Va(t))return Hl(t,e)}throw Ua(t)}function Ge(t,e){return e?Gl(t,e):Ae(t)}function Ga(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=Nt(t);return Ge(t,n)}function Z(t,e){return we(function(n,s){var i=0;n.subscribe(Pe(s,function(r){s.next(t.call(e,r,i++))}))})}var Wl=Array.isArray;function Jl(t,e){return Wl(e)?t.apply(void 0,st([],$e(e))):t(e)}function Wa(t){return Z(function(e){return Jl(t,e)})}var Kl=Array.isArray,zl=Object.getPrototypeOf,Yl=Object.prototype,Xl=Object.keys;function Zl(t){if(t.length===1){var e=t[0];if(Kl(e))return{args:e,keys:null};if(Ql(e)){var n=Xl(e);return{args:n.map(function(s){return e[s]}),keys:n}}}return{args:t,keys:null}}function Ql(t){return t&&typeof t=="object"&&zl(t)===Yl}function ed(t,e){return t.reduce(function(n,s,i){return n[s]=e[i],n},{})}function Be(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=Nt(t),s=Al(t),i=Zl(t),r=i.args,a=i.keys;if(r.length===0)return Ge([],n);var o=new ne(td(r,n,a?function(d){return ed(a,d)}:Dt));return s?o.pipe(Wa(s)):o}function td(t,e,n){return n===void 0&&(n=Dt),function(s){Zi(e,function(){for(var i=t.length,r=new Array(i),a=i,o=i,d=function(l){Zi(e,function(){var h=Ge(t[l],e),u=!1;h.subscribe(Pe(s,function(f){r[l]=f,u||(u=!0,o--),o||s.next(n(r.slice()))},function(){--a||s.complete()}))},s)},c=0;c<i;c++)d(c)},s)}}function Zi(t,e,n){t?De(n,t,e):e()}function nd(t,e,n,s,i,r,a,o){var d=[],c=0,l=0,h=!1,u=function(){h&&!d.length&&!c&&e.complete()},f=function(m){return c<s?p(m):d.push(m)},p=function(m){c++;var g=!1;Ae(n(m,l++)).subscribe(Pe(e,function(b){e.next(b)},function(){g=!0},void 0,function(){if(g)try{c--;for(var b=function(){var E=d.shift();a||p(E)};d.length&&c<s;)b();u()}catch(E){e.error(E)}}))};return t.subscribe(Pe(e,f,function(){h=!0,u()})),function(){}}function Et(t,e,n){return n===void 0&&(n=1/0),F(e)?Et(function(s,i){return Z(function(r,a){return e(s,r,i,a)})(Ae(t(s,i)))},n):(typeof e=="number"&&(n=e),we(function(s,i){return nd(s,i,t,n)}))}function Ja(t){return t===void 0&&(t=1/0),Et(Dt,t)}function sd(){return Ja(1)}function Qi(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return sd()(Ge(t,Nt(t)))}var id=["addListener","removeListener"],rd=["addEventListener","removeEventListener"],ad=["on","off"];function _s(t,e,n,s){if(F(n)&&(s=n,n=void 0),s)return _s(t,e,n).pipe(Wa(s));var i=$e(ud(t)?rd.map(function(o){return function(d){return t[o](e,d,n)}}):od(t)?id.map(er(t,e)):cd(t)?ad.map(er(t,e)):[],2),r=i[0],a=i[1];if(!r&&Js(t))return Et(function(o){return _s(o,e,n)})(Ae(t));if(!r)throw new TypeError("Invalid event target");return new ne(function(o){var d=function(){for(var c=[],l=0;l<arguments.length;l++)c[l]=arguments[l];return o.next(1<c.length?c:c[0])};return r(d),function(){return a(d)}})}function er(t,e){return function(n){return function(s){return t[n](e,s)}}}function od(t){return F(t.addListener)&&F(t.removeListener)}function cd(t){return F(t.on)&&F(t.off)}function ud(t){return F(t.addEventListener)&&F(t.removeEventListener)}function ld(t,e,n){return new ne(function(s){var i=function(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];return s.next(a.length===1?a[0]:a)},r=t(i);return F(e)?function(){return e(i,r)}:void 0})}function Ks(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=Nt(t),s=Dl(t,1/0),i=t;return i.length?i.length===1?Ae(i[0]):Ja(s)(Ge(i,n)):Pl}function Fn(t,e){return F(e)?Et(t,e,1):Et(t,1)}function dd(t,e){return e===void 0&&(e=_l),we(function(n,s){var i=null,r=null,a=null,o=function(){if(i){i.unsubscribe(),i=null;var c=r;r=null,s.next(c)}};function d(){var c=a+t,l=e.now();if(l<c){i=this.schedule(void 0,c-l),s.add(i);return}o()}n.subscribe(Pe(s,function(c){r=c,a=e.now(),i||(i=e.schedule(d,t),s.add(i))},function(){o(),s.complete()},void 0,function(){r=i=null}))})}function Q(t,e){return e===void 0&&(e=Dt),t=t??hd,we(function(n,s){var i,r=!0;n.subscribe(Pe(s,function(a){var o=e(a);(r||!t(i,o))&&(r=!1,i=o,s.next(a))}))})}function hd(t,e){return t===e}function Wt(t,e){return Q(function(n,s){return n[t]===s[t]})}function fd(){return we(function(t,e){var n,s=!1;t.subscribe(Pe(e,function(i){var r=n;n=i,s&&e.next([r,i]),s=!0}))})}function pd(t){t===void 0&&(t={});var e=t.connector,n=e===void 0?function(){return new Mn}:e,s=t.resetOnError,i=s===void 0?!0:s,r=t.resetOnComplete,a=r===void 0?!0:r,o=t.resetOnRefCountZero,d=o===void 0?!0:o;return function(c){var l,h,u,f=0,p=!1,m=!1,g=function(){h==null||h.unsubscribe(),h=void 0},b=function(){g(),l=u=void 0,p=m=!1},E=function(){var C=l;b(),C==null||C.unsubscribe()};return we(function(C,w){f++,!m&&!p&&g();var k=u=u??n();w.add(function(){f--,f===0&&!m&&!p&&(h=ns(E,d))}),k.subscribe(w),!l&&f>0&&(l=new wt({next:function(R){return k.next(R)},error:function(R){m=!0,g(),h=ns(b,i,R),k.error(R)},complete:function(){p=!0,g(),h=ns(b,a),k.complete()}}),Ae(C).subscribe(l))})(c)}}function ns(t,e){for(var n=[],s=2;s<arguments.length;s++)n[s-2]=arguments[s];if(e===!0){t();return}if(e!==!1){var i=new wt({next:function(){i.unsubscribe(),t()}});return Ae(e.apply(void 0,st([],$e(n)))).subscribe(i)}}function re(t,e,n){var s,i,r,a,o=!1;return t&&typeof t=="object"?(s=t.bufferSize,a=s===void 0?1/0:s,i=t.windowTime,e=i===void 0?1/0:i,r=t.refCount,o=r===void 0?!1:r,n=t.scheduler):a=t??1/0,pd({connector:function(){return new Is(a,e,n)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function jn(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=Nt(t);return we(function(s,i){(n?Qi(t,s,n):Qi(t,s)).subscribe(i)})}function Ps(t,e){return e===void 0&&(e=!1),we(function(n,s){var i=0;n.subscribe(Pe(s,function(r){var a=t(r,i++);(a||e)&&s.next(r),!a&&s.complete()}))})}var Ka={},za={exports:{}},tr=za.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(t){return t.encoding?"rtpmap:%d %s/%s/%s":t.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(t){return t.address!=null?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(t){return t.subtype!=null?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(t){return"extmap:%d"+(t.direction?"/%s":"%v")+(t["encrypt-uri"]?" %s":"%v")+" %s"+(t.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(t){return t.sessionConfig!=null?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(t){var e="candidate:%s %d %s %d %s %d typ %s";return e+=t.raddr!=null?" raddr %s rport %d":"%v%v",e+=t.tcptype!=null?" tcptype %s":"%v",t.generation!=null&&(e+=" generation %d"),e+=t["network-id"]!=null?" network-id %d":"%v",e+=t["network-cost"]!=null?" network-cost %d":"%v",e}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(t){var e="ssrc:%d";return t.attribute!=null&&(e+=" %s",t.value!=null&&(e+=":%s")),e}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(t){return t.maxMessageSize!=null?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(t){return t.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(t){return"imageattr:%s %s %s"+(t.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(t){return"simulcast:%s %s"+(t.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(t){return"ts-refclk:%s"+(t.clksrcExt!=null?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(t){var e="mediaclk:";return e+=t.id!=null?"id=%s %s":"%v%s",e+=t.mediaClockValue!=null?"=%s":"",e+=t.rateNumerator!=null?" rate=%s":"",e+=t.rateDenominator!=null?"/%s":"",e}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};Object.keys(tr).forEach(function(t){var e=tr[t];e.forEach(function(n){n.reg||(n.reg=/(.*)/),n.format||(n.format="%s")})});var Ya=za.exports;(function(t){var e=function(o){return String(Number(o))===o?Number(o):o},n=function(o,d,c,l){if(l&&!c)d[l]=e(o[1]);else for(var h=0;h<c.length;h+=1)o[h+1]!=null&&(d[c[h]]=e(o[h+1]))},s=function(o,d,c){var l=o.name&&o.names;o.push&&!d[o.push]?d[o.push]=[]:l&&!d[o.name]&&(d[o.name]={});var h=o.push?{}:l?d[o.name]:d;n(c.match(o.reg),h,o.names,o.name),o.push&&d[o.push].push(h)},i=Ya,r=RegExp.prototype.test.bind(/^([a-z])=(.*)/);t.parse=function(o){var d={},c=[],l=d;return o.split(/(\r\n|\r|\n)/).filter(r).forEach(function(h){var u=h[0],f=h.slice(2);u==="m"&&(c.push({rtp:[],fmtp:[]}),l=c[c.length-1]);for(var p=0;p<(i[u]||[]).length;p+=1){var m=i[u][p];if(m.reg.test(f))return s(m,l,f)}}),d.media=c,d};var a=function(o,d){var c=d.split(/=(.+)/,2);return c.length===2?o[c[0]]=e(c[1]):c.length===1&&d.length>1&&(o[c[0]]=void 0),o};t.parseParams=function(o){return o.split(/;\s?/).reduce(a,{})},t.parseFmtpConfig=t.parseParams,t.parsePayloads=function(o){return o.toString().split(" ").map(Number)},t.parseRemoteCandidates=function(o){for(var d=[],c=o.split(" ").map(e),l=0;l<c.length;l+=3)d.push({component:c[l],ip:c[l+1],port:c[l+2]});return d},t.parseImageAttributes=function(o){return o.split(" ").map(function(d){return d.substring(1,d.length-1).split(",").reduce(a,{})})},t.parseSimulcastStreamList=function(o){return o.split(";").map(function(d){return d.split(",").map(function(c){var l,h=!1;return c[0]!=="~"?l=e(c):(l=e(c.substring(1,c.length)),h=!0),{scid:l,paused:h}})})}})(Ka);var ss=Ya,md=/%[sdv%]/g,gd=function(t){var e=1,n=arguments,s=n.length;return t.replace(md,function(i){if(e>=s)return i;var r=n[e];switch(e+=1,i){case"%%":return"%";case"%s":return String(r);case"%d":return Number(r);case"%v":return""}})},mt=function(t,e,n){var s=e.format instanceof Function?e.format(e.push?n:n[e.name]):e.format,i=[t+"="+s];if(e.names)for(var r=0;r<e.names.length;r+=1){var a=e.names[r];e.name?i.push(n[e.name][a]):i.push(n[e.names[r]])}else i.push(n[e.name]);return gd.apply(null,i)},bd=["v","o","s","i","u","e","p","c","b","t","r","z","a"],yd=["i","c","b","a"],Sd=function(t,e){e=e||{},t.version==null&&(t.version=0),t.name==null&&(t.name=" "),t.media.forEach(function(r){r.payloads==null&&(r.payloads="")});var n=e.outerOrder||bd,s=e.innerOrder||yd,i=[];return n.forEach(function(r){ss[r].forEach(function(a){a.name in t&&t[a.name]!=null?i.push(mt(r,a,t)):a.push in t&&t[a.push]!=null&&t[a.push].forEach(function(o){i.push(mt(r,a,o))})})}),t.media.forEach(function(r){i.push(mt("m",ss.m[0],r)),s.forEach(function(a){ss[a].forEach(function(o){o.name in r&&r[o.name]!=null?i.push(mt(a,o,r)):o.push in r&&r[o.push]!=null&&r[o.push].forEach(function(d){i.push(mt(a,o,d))})})})}),i.join(`\r
`)+`\r
`},We=Ka,Cd=Sd,Xa=Cd,zs=We.parse;We.parseParams;We.parseFmtpConfig;We.parsePayloads;We.parseRemoteCandidates;We.parseImageAttributes;We.parseSimulcastStreamList;const is={AVAILABLE:"available",DISABLED:"disabled",AUTO_ON:"auto-on"},z={BLOCK_USERS:"block-users",CHANGE_MAX_DURATION:"change-max-duration",CREATE_CALL:"create-call",CREATE_REACTION:"create-reaction",ENABLE_NOISE_CANCELLATION:"enable-noise-cancellation",END_CALL:"end-call",JOIN_BACKSTAGE:"join-backstage",JOIN_CALL:"join-call",JOIN_ENDED_CALL:"join-ended-call",MUTE_USERS:"mute-users",PIN_FOR_EVERYONE:"pin-for-everyone",READ_CALL:"read-call",REMOVE_CALL_MEMBER:"remove-call-member",SCREENSHARE:"screenshare",SEND_AUDIO:"send-audio",SEND_VIDEO:"send-video",START_BROADCAST_CALL:"start-broadcast-call",START_CLOSED_CAPTIONS_CALL:"start-closed-captions-call",START_RECORD_CALL:"start-record-call",START_TRANSCRIPTION_CALL:"start-transcription-call",STOP_BROADCAST_CALL:"stop-broadcast-call",STOP_CLOSED_CAPTIONS_CALL:"stop-closed-captions-call",STOP_RECORD_CALL:"stop-record-call",STOP_TRANSCRIPTION_CALL:"stop-transcription-call",UPDATE_CALL:"update-call",UPDATE_CALL_MEMBER:"update-call-member",UPDATE_CALL_PERMISSIONS:"update-call-permissions",UPDATE_CALL_SETTINGS:"update-call-settings"};class Za extends Error{}var bn;(function(t){t[t.NULL_VALUE=0]="NULL_VALUE"})(bn||(bn={}));class vd extends T{constructor(){super("google.protobuf.Struct",[{no:1,name:"fields",kind:"map",K:9,V:{kind:"message",T:()=>et}}])}internalJsonWrite(e,n){let s={};for(let[i,r]of Object.entries(e.fields))s[i]=et.toJson(r);return s}internalJsonRead(e,n,s){if(!cn(e))throw new globalThis.Error("Unable to parse message "+this.typeName+" from JSON "+rt(e)+".");s||(s=this.create());for(let[i,r]of globalThis.Object.entries(e))s.fields[i]=et.fromJson(r);return s}}const yn=new vd;class Td extends T{constructor(){super("google.protobuf.Value",[{no:1,name:"null_value",kind:"enum",oneof:"kind",T:()=>["google.protobuf.NullValue",bn]},{no:2,name:"number_value",kind:"scalar",oneof:"kind",T:1},{no:3,name:"string_value",kind:"scalar",oneof:"kind",T:9},{no:4,name:"bool_value",kind:"scalar",oneof:"kind",T:8},{no:5,name:"struct_value",kind:"message",oneof:"kind",T:()=>yn},{no:6,name:"list_value",kind:"message",oneof:"kind",T:()=>nr}])}internalJsonWrite(e,n){if(e.kind.oneofKind===void 0)throw new globalThis.Error;switch(e.kind.oneofKind){case void 0:throw new globalThis.Error;case"boolValue":return e.kind.boolValue;case"nullValue":return null;case"numberValue":let s=e.kind.numberValue;if(typeof s=="number"&&!Number.isFinite(s))throw new globalThis.Error;return s;case"stringValue":return e.kind.stringValue;case"listValue":let i=this.fields.find(a=>a.no===6);if((i==null?void 0:i.kind)!=="message")throw new globalThis.Error;return i.T().toJson(e.kind.listValue);case"structValue":let r=this.fields.find(a=>a.no===5);if((r==null?void 0:r.kind)!=="message")throw new globalThis.Error;return r.T().toJson(e.kind.structValue)}}internalJsonRead(e,n,s){switch(s||(s=this.create()),typeof e){case"number":s.kind={oneofKind:"numberValue",numberValue:e};break;case"string":s.kind={oneofKind:"stringValue",stringValue:e};break;case"boolean":s.kind={oneofKind:"boolValue",boolValue:e};break;case"object":e===null?s.kind={oneofKind:"nullValue",nullValue:bn.NULL_VALUE}:globalThis.Array.isArray(e)?s.kind={oneofKind:"listValue",listValue:nr.fromJson(e)}:s.kind={oneofKind:"structValue",structValue:yn.fromJson(e)};break;default:throw new globalThis.Error("Unable to parse "+this.typeName+" from JSON "+rt(e))}return s}}const et=new Td;class wd extends T{constructor(){super("google.protobuf.ListValue",[{no:1,name:"values",kind:"message",repeat:1,T:()=>et}])}internalJsonWrite(e,n){return e.values.map(s=>et.toJson(s))}internalJsonRead(e,n,s){if(!globalThis.Array.isArray(e))throw new globalThis.Error("Unable to parse "+this.typeName+" from JSON "+rt(e));s||(s=this.create());let i=e.map(r=>et.fromJson(r));return s.values.push(...i),s}}const nr=new wd;class Ed extends T{constructor(){super("google.protobuf.Timestamp",[{no:1,name:"seconds",kind:"scalar",T:3},{no:2,name:"nanos",kind:"scalar",T:5}])}now(){const e=this.create(),n=Date.now();return e.seconds=B.from(Math.floor(n/1e3)).toString(),e.nanos=n%1e3*1e6,e}toDate(e){return new Date(B.from(e.seconds).toNumber()*1e3+Math.ceil(e.nanos/1e6))}fromDate(e){const n=this.create(),s=e.getTime();return n.seconds=B.from(Math.floor(s/1e3)).toString(),n.nanos=s%1e3*1e6,n}internalJsonWrite(e,n){let s=B.from(e.seconds).toNumber()*1e3;if(s<Date.parse("0001-01-01T00:00:00Z")||s>Date.parse("9999-12-31T23:59:59Z"))throw new Error("Unable to encode Timestamp to JSON. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.");if(e.nanos<0)throw new Error("Unable to encode invalid Timestamp to JSON. Nanos must not be negative.");let i="Z";if(e.nanos>0){let r=(e.nanos+1e9).toString().substring(1);r.substring(3)==="000000"?i="."+r.substring(0,3)+"Z":r.substring(6)==="000"?i="."+r.substring(0,6)+"Z":i="."+r+"Z"}return new Date(s).toISOString().replace(".000Z",i)}internalJsonRead(e,n,s){if(typeof e!="string")throw new Error("Unable to parse Timestamp from JSON "+rt(e)+".");let i=e.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:Z|\.([0-9]{3,9})Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!i)throw new Error("Unable to parse Timestamp from JSON. Invalid format.");let r=Date.parse(i[1]+"-"+i[2]+"-"+i[3]+"T"+i[4]+":"+i[5]+":"+i[6]+(i[8]?i[8]:"Z"));if(Number.isNaN(r))throw new Error("Unable to parse Timestamp from JSON. Invalid value.");if(r<Date.parse("0001-01-01T00:00:00Z")||r>Date.parse("9999-12-31T23:59:59Z"))throw new globalThis.Error("Unable to parse Timestamp from JSON. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.");return s||(s=this.create()),s.seconds=B.from(r/1e3).toString(),s.nanos=0,i[7]&&(s.nanos=parseInt("1"+i[7]+"0".repeat(9-i[7].length))-1e9),s}}const kt=new Ed;var ee;(function(t){t[t.PUBLISHER_UNSPECIFIED=0]="PUBLISHER_UNSPECIFIED",t[t.SUBSCRIBER=1]="SUBSCRIBER"})(ee||(ee={}));var Sn;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.POOR=1]="POOR",t[t.GOOD=2]="GOOD",t[t.EXCELLENT=3]="EXCELLENT"})(Sn||(Sn={}));var je;(function(t){t[t.LOW_UNSPECIFIED=0]="LOW_UNSPECIFIED",t[t.MID=1]="MID",t[t.HIGH=2]="HIGH",t[t.OFF=3]="OFF"})(je||(je={}));var v;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.AUDIO=1]="AUDIO",t[t.VIDEO=2]="VIDEO",t[t.SCREEN_SHARE=3]="SCREEN_SHARE",t[t.SCREEN_SHARE_AUDIO=4]="SCREEN_SHARE_AUDIO"})(v||(v={}));var Rt;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.PUBLISH_TRACK_NOT_FOUND=100]="PUBLISH_TRACK_NOT_FOUND",t[t.PUBLISH_TRACKS_MISMATCH=101]="PUBLISH_TRACKS_MISMATCH",t[t.PUBLISH_TRACK_OUT_OF_ORDER=102]="PUBLISH_TRACK_OUT_OF_ORDER",t[t.PUBLISH_TRACK_VIDEO_LAYER_NOT_FOUND=103]="PUBLISH_TRACK_VIDEO_LAYER_NOT_FOUND",t[t.LIVE_ENDED=104]="LIVE_ENDED",t[t.PARTICIPANT_NOT_FOUND=200]="PARTICIPANT_NOT_FOUND",t[t.PARTICIPANT_MIGRATING_OUT=201]="PARTICIPANT_MIGRATING_OUT",t[t.PARTICIPANT_MIGRATION_FAILED=202]="PARTICIPANT_MIGRATION_FAILED",t[t.PARTICIPANT_MIGRATING=203]="PARTICIPANT_MIGRATING",t[t.PARTICIPANT_RECONNECT_FAILED=204]="PARTICIPANT_RECONNECT_FAILED",t[t.PARTICIPANT_MEDIA_TRANSPORT_FAILURE=205]="PARTICIPANT_MEDIA_TRANSPORT_FAILURE",t[t.CALL_NOT_FOUND=300]="CALL_NOT_FOUND",t[t.REQUEST_VALIDATION_FAILED=400]="REQUEST_VALIDATION_FAILED",t[t.UNAUTHENTICATED=401]="UNAUTHENTICATED",t[t.PERMISSION_DENIED=403]="PERMISSION_DENIED",t[t.TOO_MANY_REQUESTS=429]="TOO_MANY_REQUESTS",t[t.INTERNAL_SERVER_ERROR=500]="INTERNAL_SERVER_ERROR",t[t.SFU_SHUTTING_DOWN=600]="SFU_SHUTTING_DOWN",t[t.SFU_FULL=700]="SFU_FULL"})(Rt||(Rt={}));var qe;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.REACT=1]="REACT",t[t.ANGULAR=2]="ANGULAR",t[t.ANDROID=3]="ANDROID",t[t.IOS=4]="IOS",t[t.FLUTTER=5]="FLUTTER",t[t.REACT_NATIVE=6]="REACT_NATIVE",t[t.UNITY=7]="UNITY",t[t.GO=8]="GO",t[t.PLAIN_JAVASCRIPT=9]="PLAIN_JAVASCRIPT"})(qe||(qe={}));var Cn;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.USER_MUTED=1]="USER_MUTED",t[t.PERMISSION_REVOKED=2]="PERMISSION_REVOKED",t[t.MODERATION=3]="MODERATION"})(Cn||(Cn={}));var Os;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.SHUTTING_DOWN=1]="SHUTTING_DOWN",t[t.REBALANCE=2]="REBALANCE"})(Os||(Os={}));var vn;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.ENDED=1]="ENDED",t[t.LIVE_ENDED=2]="LIVE_ENDED",t[t.KICKED=3]="KICKED",t[t.SESSION_ENDED=4]="SESSION_ENDED"})(vn||(vn={}));var D;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.DISCONNECT=1]="DISCONNECT",t[t.FAST=2]="FAST",t[t.REJOIN=3]="REJOIN",t[t.MIGRATE=4]="MIGRATE"})(D||(D={}));var As;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.NONE=1]="NONE",t[t.LIGHT=2]="LIGHT",t[t.MODERATE=3]="MODERATE",t[t.SEVERE=4]="SEVERE",t[t.CRITICAL=5]="CRITICAL",t[t.EMERGENCY=6]="EMERGENCY",t[t.SHUTDOWN=7]="SHUTDOWN"})(As||(As={}));var Ds;(function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.NOMINAL=1]="NOMINAL",t[t.FAIR=2]="FAIR",t[t.SERIOUS=3]="SERIOUS",t[t.CRITICAL=4]="CRITICAL"})(Ds||(Ds={}));class kd extends T{constructor(){super("stream.video.sfu.models.CallState",[{no:1,name:"participants",kind:"message",repeat:1,T:()=>ct},{no:2,name:"started_at",kind:"message",T:()=>kt},{no:3,name:"participant_count",kind:"message",T:()=>Qa},{no:4,name:"pins",kind:"message",repeat:1,T:()=>eo}])}}const Rd=new kd;class Id extends T{constructor(){super("stream.video.sfu.models.ParticipantCount",[{no:1,name:"total",kind:"scalar",T:13},{no:2,name:"anonymous",kind:"scalar",T:13}])}}const Qa=new Id;class _d extends T{constructor(){super("stream.video.sfu.models.Pin",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9}])}}const eo=new _d;class Pd extends T{constructor(){super("stream.video.sfu.models.Participant",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"published_tracks",kind:"enum",repeat:1,T:()=>["stream.video.sfu.models.TrackType",v,"TRACK_TYPE_"]},{no:4,name:"joined_at",kind:"message",T:()=>kt},{no:5,name:"track_lookup_prefix",kind:"scalar",T:9},{no:6,name:"connection_quality",kind:"enum",T:()=>["stream.video.sfu.models.ConnectionQuality",Sn,"CONNECTION_QUALITY_"]},{no:7,name:"is_speaking",kind:"scalar",T:8},{no:8,name:"is_dominant_speaker",kind:"scalar",T:8},{no:9,name:"audio_level",kind:"scalar",T:2},{no:10,name:"name",kind:"scalar",T:9},{no:11,name:"image",kind:"scalar",T:9},{no:12,name:"custom",kind:"message",T:()=>yn},{no:13,name:"roles",kind:"scalar",repeat:2,T:9}])}}const ct=new Pd;class Od extends T{constructor(){super("stream.video.sfu.models.StreamQuality",[{no:1,name:"video_quality",kind:"enum",T:()=>["stream.video.sfu.models.VideoQuality",je,"VIDEO_QUALITY_"]},{no:2,name:"user_id",kind:"scalar",T:9}])}}new Od;class Ad extends T{constructor(){super("stream.video.sfu.models.VideoDimension",[{no:1,name:"width",kind:"scalar",T:13},{no:2,name:"height",kind:"scalar",T:13}])}}const to=new Ad;class Dd extends T{constructor(){super("stream.video.sfu.models.VideoLayer",[{no:1,name:"rid",kind:"scalar",T:9},{no:2,name:"video_dimension",kind:"message",T:()=>to},{no:4,name:"bitrate",kind:"scalar",T:13},{no:5,name:"fps",kind:"scalar",T:13},{no:6,name:"quality",kind:"enum",T:()=>["stream.video.sfu.models.VideoQuality",je,"VIDEO_QUALITY_"]}])}}const Nd=new Dd;class xd extends T{constructor(){super("stream.video.sfu.models.PublishOptions",[{no:1,name:"codecs",kind:"message",repeat:1,T:()=>no}])}}const Ld=new xd;class Ud extends T{constructor(){super("stream.video.sfu.models.PublishOption",[{no:1,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",v,"TRACK_TYPE_"]},{no:2,name:"codec",kind:"message",T:()=>Vn},{no:3,name:"bitrate",kind:"scalar",T:5},{no:4,name:"fps",kind:"scalar",T:5},{no:5,name:"max_spatial_layers",kind:"scalar",T:5},{no:6,name:"max_temporal_layers",kind:"scalar",T:5}])}}const no=new Ud;class Md extends T{constructor(){super("stream.video.sfu.models.Codec",[{no:11,name:"payload_type",kind:"scalar",T:13},{no:10,name:"name",kind:"scalar",T:9},{no:14,name:"clock_rate",kind:"scalar",T:13},{no:13,name:"encoding_parameters",kind:"scalar",T:9},{no:12,name:"fmtp",kind:"scalar",T:9}])}}const Vn=new Md;let Fd=class extends T{constructor(){super("stream.video.sfu.models.ICETrickle",[{no:1,name:"peer_type",kind:"enum",T:()=>["stream.video.sfu.models.PeerType",ee,"PEER_TYPE_"]},{no:2,name:"ice_candidate",kind:"scalar",T:9},{no:3,name:"session_id",kind:"scalar",T:9}])}};const so=new Fd;class jd extends T{constructor(){super("stream.video.sfu.models.TrackInfo",[{no:1,name:"track_id",kind:"scalar",T:9},{no:2,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",v,"TRACK_TYPE_"]},{no:5,name:"layers",kind:"message",repeat:1,T:()=>Nd},{no:6,name:"mid",kind:"scalar",T:9},{no:7,name:"dtx",kind:"scalar",T:8},{no:8,name:"stereo",kind:"scalar",T:8},{no:9,name:"red",kind:"scalar",T:8},{no:10,name:"muted",kind:"scalar",T:8}])}}const Ys=new jd;let Vd=class extends T{constructor(){super("stream.video.sfu.models.Error",[{no:1,name:"code",kind:"enum",T:()=>["stream.video.sfu.models.ErrorCode",Rt,"ERROR_CODE_"]},{no:2,name:"message",kind:"scalar",T:9},{no:3,name:"should_retry",kind:"scalar",T:8}])}};const Ee=new Vd;class $d extends T{constructor(){super("stream.video.sfu.models.ClientDetails",[{no:1,name:"sdk",kind:"message",T:()=>Hd},{no:2,name:"os",kind:"message",T:()=>Wd},{no:3,name:"browser",kind:"message",T:()=>Kd},{no:4,name:"device",kind:"message",T:()=>Yd}])}}const Bd=new $d;class qd extends T{constructor(){super("stream.video.sfu.models.Sdk",[{no:1,name:"type",kind:"enum",T:()=>["stream.video.sfu.models.SdkType",qe,"SDK_TYPE_"]},{no:2,name:"major",kind:"scalar",T:9},{no:3,name:"minor",kind:"scalar",T:9},{no:4,name:"patch",kind:"scalar",T:9}])}}const Hd=new qd;class Gd extends T{constructor(){super("stream.video.sfu.models.OS",[{no:1,name:"name",kind:"scalar",T:9},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"architecture",kind:"scalar",T:9}])}}const Wd=new Gd;class Jd extends T{constructor(){super("stream.video.sfu.models.Browser",[{no:1,name:"name",kind:"scalar",T:9},{no:2,name:"version",kind:"scalar",T:9}])}}const Kd=new Jd;class zd extends T{constructor(){super("stream.video.sfu.models.Device",[{no:1,name:"name",kind:"scalar",T:9},{no:2,name:"version",kind:"scalar",T:9}])}}const Yd=new zd;class Xd extends T{constructor(){super("stream.video.sfu.models.Call",[{no:1,name:"type",kind:"scalar",T:9},{no:2,name:"id",kind:"scalar",T:9},{no:3,name:"created_by_user_id",kind:"scalar",T:9},{no:4,name:"host_user_id",kind:"scalar",T:9},{no:5,name:"custom",kind:"message",T:()=>yn},{no:6,name:"created_at",kind:"message",T:()=>kt},{no:7,name:"updated_at",kind:"message",T:()=>kt}])}}new Xd;class Zd extends T{constructor(){super("stream.video.sfu.models.CallGrants",[{no:1,name:"can_publish_audio",kind:"scalar",T:8},{no:2,name:"can_publish_video",kind:"scalar",T:8},{no:3,name:"can_screenshare",kind:"scalar",T:8}])}}const Qd=new Zd;class eh extends T{constructor(){super("stream.video.sfu.models.InputDevices",[{no:1,name:"available_devices",kind:"scalar",repeat:2,T:9},{no:2,name:"current_device",kind:"scalar",T:9},{no:3,name:"is_permitted",kind:"scalar",T:8}])}}const sr=new eh;class th extends T{constructor(){super("stream.video.sfu.models.AndroidState",[{no:1,name:"thermal_state",kind:"enum",T:()=>["stream.video.sfu.models.AndroidThermalState",As,"ANDROID_THERMAL_STATE_"]},{no:2,name:"is_power_saver_mode",kind:"scalar",T:8}])}}const nh=new th;class sh extends T{constructor(){super("stream.video.sfu.models.AppleState",[{no:1,name:"thermal_state",kind:"enum",T:()=>["stream.video.sfu.models.AppleThermalState",Ds,"APPLE_THERMAL_STATE_"]},{no:2,name:"is_low_power_mode_enabled",kind:"scalar",T:8}])}}const ih=new sh;class rh extends T{constructor(){super("stream.video.sfu.signal.StartNoiseCancellationRequest",[{no:1,name:"session_id",kind:"scalar",T:9}])}}const ah=new rh;class oh extends T{constructor(){super("stream.video.sfu.signal.StartNoiseCancellationResponse",[{no:1,name:"error",kind:"message",T:()=>Ee}])}}const ch=new oh;class uh extends T{constructor(){super("stream.video.sfu.signal.StopNoiseCancellationRequest",[{no:1,name:"session_id",kind:"scalar",T:9}])}}const lh=new uh;class dh extends T{constructor(){super("stream.video.sfu.signal.StopNoiseCancellationResponse",[{no:1,name:"error",kind:"message",T:()=>Ee}])}}const hh=new dh;class fh extends T{constructor(){super("stream.video.sfu.signal.Reconnection",[{no:1,name:"time_seconds",kind:"scalar",T:2},{no:2,name:"strategy",kind:"enum",T:()=>["stream.video.sfu.models.WebsocketReconnectStrategy",D,"WEBSOCKET_RECONNECT_STRATEGY_"]}])}}const ph=new fh;class mh extends T{constructor(){super("stream.video.sfu.signal.Telemetry",[{no:1,name:"connection_time_seconds",kind:"scalar",oneof:"data",T:2},{no:2,name:"reconnection",kind:"message",oneof:"data",T:()=>ph}])}}const gh=new mh;class bh extends T{constructor(){super("stream.video.sfu.signal.SendStatsRequest",[{no:1,name:"session_id",kind:"scalar",T:9},{no:2,name:"subscriber_stats",kind:"scalar",T:9},{no:3,name:"publisher_stats",kind:"scalar",T:9},{no:4,name:"webrtc_version",kind:"scalar",T:9},{no:5,name:"sdk",kind:"scalar",T:9},{no:6,name:"sdk_version",kind:"scalar",T:9},{no:7,name:"audio_devices",kind:"message",T:()=>sr},{no:8,name:"video_devices",kind:"message",T:()=>sr},{no:9,name:"android",kind:"message",oneof:"deviceState",T:()=>nh},{no:10,name:"apple",kind:"message",oneof:"deviceState",T:()=>ih},{no:11,name:"telemetry",kind:"message",T:()=>gh}])}}const yh=new bh;class Sh extends T{constructor(){super("stream.video.sfu.signal.SendStatsResponse",[{no:1,name:"error",kind:"message",T:()=>Ee}])}}const Ch=new Sh;class vh extends T{constructor(){super("stream.video.sfu.signal.ICERestartRequest",[{no:1,name:"session_id",kind:"scalar",T:9},{no:2,name:"peer_type",kind:"enum",T:()=>["stream.video.sfu.models.PeerType",ee,"PEER_TYPE_"]}])}}const Th=new vh;class wh extends T{constructor(){super("stream.video.sfu.signal.ICERestartResponse",[{no:1,name:"error",kind:"message",T:()=>Ee}])}}const Eh=new wh;class kh extends T{constructor(){super("stream.video.sfu.signal.UpdateMuteStatesRequest",[{no:1,name:"session_id",kind:"scalar",T:9},{no:3,name:"mute_states",kind:"message",repeat:1,T:()=>Oh}])}}const Rh=new kh;class Ih extends T{constructor(){super("stream.video.sfu.signal.UpdateMuteStatesResponse",[{no:4,name:"error",kind:"message",T:()=>Ee}])}}const _h=new Ih;class Ph extends T{constructor(){super("stream.video.sfu.signal.TrackMuteState",[{no:1,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",v,"TRACK_TYPE_"]},{no:2,name:"muted",kind:"scalar",T:8}])}}const Oh=new Ph;class Ah extends T{constructor(){super("stream.video.sfu.signal.AudioMuteChanged",[{no:1,name:"muted",kind:"scalar",T:8}])}}new Ah;class Dh extends T{constructor(){super("stream.video.sfu.signal.VideoMuteChanged",[{no:2,name:"muted",kind:"scalar",T:8}])}}new Dh;class Nh extends T{constructor(){super("stream.video.sfu.signal.UpdateSubscriptionsRequest",[{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"tracks",kind:"message",repeat:1,T:()=>Xs}])}}const xh=new Nh;class Lh extends T{constructor(){super("stream.video.sfu.signal.UpdateSubscriptionsResponse",[{no:4,name:"error",kind:"message",T:()=>Ee}])}}const Uh=new Lh;class Mh extends T{constructor(){super("stream.video.sfu.signal.TrackSubscriptionDetails",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"track_type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",v,"TRACK_TYPE_"]},{no:4,name:"dimension",kind:"message",T:()=>to}])}}const Xs=new Mh;class Fh extends T{constructor(){super("stream.video.sfu.signal.SendAnswerRequest",[{no:1,name:"peer_type",kind:"enum",T:()=>["stream.video.sfu.models.PeerType",ee,"PEER_TYPE_"]},{no:2,name:"sdp",kind:"scalar",T:9},{no:3,name:"session_id",kind:"scalar",T:9}])}}const jh=new Fh;class Vh extends T{constructor(){super("stream.video.sfu.signal.SendAnswerResponse",[{no:4,name:"error",kind:"message",T:()=>Ee}])}}const $h=new Vh;class Bh extends T{constructor(){super("stream.video.sfu.signal.ICETrickleResponse",[{no:4,name:"error",kind:"message",T:()=>Ee}])}}const qh=new Bh;class Hh extends T{constructor(){super("stream.video.sfu.signal.SetPublisherRequest",[{no:1,name:"sdp",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"tracks",kind:"message",repeat:1,T:()=>Ys}])}}const Gh=new Hh;class Wh extends T{constructor(){super("stream.video.sfu.signal.SetPublisherResponse",[{no:1,name:"sdp",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"ice_restart",kind:"scalar",T:8},{no:4,name:"error",kind:"message",T:()=>Ee}])}}const Jh=new Wh,rs=new _c("stream.video.sfu.signal.SignalServer",[{name:"SetPublisher",options:{},I:Gh,O:Jh},{name:"SendAnswer",options:{},I:jh,O:$h},{name:"IceTrickle",options:{},I:so,O:qh},{name:"UpdateSubscriptions",options:{},I:xh,O:Uh},{name:"UpdateMuteStates",options:{},I:Rh,O:_h},{name:"IceRestart",options:{},I:Th,O:Eh},{name:"SendStats",options:{},I:yh,O:Ch},{name:"StartNoiseCancellation",options:{},I:ah,O:ch},{name:"StopNoiseCancellation",options:{},I:lh,O:hh}]);class Kh extends T{constructor(){super("stream.video.sfu.event.SfuEvent",[{no:1,name:"subscriber_offer",kind:"message",oneof:"eventPayload",T:()=>xf},{no:2,name:"publisher_answer",kind:"message",oneof:"eventPayload",T:()=>Uf},{no:3,name:"connection_quality_changed",kind:"message",oneof:"eventPayload",T:()=>Ff},{no:4,name:"audio_level_changed",kind:"message",oneof:"eventPayload",T:()=>Wf},{no:5,name:"ice_trickle",kind:"message",oneof:"eventPayload",T:()=>so},{no:6,name:"change_publish_quality",kind:"message",oneof:"eventPayload",T:()=>ep},{no:10,name:"participant_joined",kind:"message",oneof:"eventPayload",T:()=>_f},{no:11,name:"participant_left",kind:"message",oneof:"eventPayload",T:()=>Of},{no:12,name:"dominant_speaker_changed",kind:"message",oneof:"eventPayload",T:()=>Bf},{no:13,name:"join_response",kind:"message",oneof:"eventPayload",T:()=>Rf},{no:14,name:"health_check_response",kind:"message",oneof:"eventPayload",T:()=>mf},{no:16,name:"track_published",kind:"message",oneof:"eventPayload",T:()=>bf},{no:17,name:"track_unpublished",kind:"message",oneof:"eventPayload",T:()=>Sf},{no:18,name:"error",kind:"message",oneof:"eventPayload",T:()=>rf},{no:19,name:"call_grants_updated",kind:"message",oneof:"eventPayload",T:()=>np},{no:20,name:"go_away",kind:"message",oneof:"eventPayload",T:()=>ip},{no:21,name:"ice_restart",kind:"message",oneof:"eventPayload",T:()=>cf},{no:22,name:"pins_updated",kind:"message",oneof:"eventPayload",T:()=>nf},{no:23,name:"call_ended",kind:"message",oneof:"eventPayload",T:()=>ap},{no:24,name:"participant_updated",kind:"message",oneof:"eventPayload",T:()=>Df},{no:25,name:"participant_migration_complete",kind:"message",oneof:"eventPayload",T:()=>ef},{no:26,name:"codec_negotiation_complete",kind:"message",oneof:"eventPayload",T:()=>Zh},{no:27,name:"change_publish_options",kind:"message",oneof:"eventPayload",T:()=>Yh}])}}const ir=new Kh;class zh extends T{constructor(){super("stream.video.sfu.event.ChangePublishOptions",[{no:1,name:"publish_option",kind:"message",T:()=>no}])}}const Yh=new zh;class Xh extends T{constructor(){super("stream.video.sfu.event.CodecNegotiationComplete",[])}}const Zh=new Xh;class Qh extends T{constructor(){super("stream.video.sfu.event.ParticipantMigrationComplete",[])}}const ef=new Qh;class tf extends T{constructor(){super("stream.video.sfu.event.PinsChanged",[{no:1,name:"pins",kind:"message",repeat:1,T:()=>eo}])}}const nf=new tf;class sf extends T{constructor(){super("stream.video.sfu.event.Error",[{no:4,name:"error",kind:"message",T:()=>Ee},{no:5,name:"reconnect_strategy",kind:"enum",T:()=>["stream.video.sfu.models.WebsocketReconnectStrategy",D,"WEBSOCKET_RECONNECT_STRATEGY_"]}])}}const rf=new sf;class af extends T{constructor(){super("stream.video.sfu.event.ICETrickle",[{no:1,name:"peer_type",kind:"enum",T:()=>["stream.video.sfu.models.PeerType",ee,"PEER_TYPE_"]},{no:2,name:"ice_candidate",kind:"scalar",T:9}])}}new af;class of extends T{constructor(){super("stream.video.sfu.event.ICERestart",[{no:1,name:"peer_type",kind:"enum",T:()=>["stream.video.sfu.models.PeerType",ee,"PEER_TYPE_"]}])}}const cf=new of;class uf extends T{constructor(){super("stream.video.sfu.event.SfuRequest",[{no:1,name:"join_request",kind:"message",oneof:"requestPayload",T:()=>io},{no:2,name:"health_check_request",kind:"message",oneof:"requestPayload",T:()=>ff},{no:3,name:"leave_call_request",kind:"message",oneof:"requestPayload",T:()=>df}])}}const gt=new uf;class lf extends T{constructor(){super("stream.video.sfu.event.LeaveCallRequest",[{no:1,name:"session_id",kind:"scalar",T:9},{no:2,name:"reason",kind:"scalar",T:9}])}}const df=new lf;class hf extends T{constructor(){super("stream.video.sfu.event.HealthCheckRequest",[])}}const ff=new hf;class pf extends T{constructor(){super("stream.video.sfu.event.HealthCheckResponse",[{no:1,name:"participant_count",kind:"message",T:()=>Qa}])}}const mf=new pf;class gf extends T{constructor(){super("stream.video.sfu.event.TrackPublished",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",v,"TRACK_TYPE_"]},{no:4,name:"participant",kind:"message",T:()=>ct}])}}const bf=new gf;class yf extends T{constructor(){super("stream.video.sfu.event.TrackUnpublished",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"type",kind:"enum",T:()=>["stream.video.sfu.models.TrackType",v,"TRACK_TYPE_"]},{no:4,name:"cause",kind:"enum",T:()=>["stream.video.sfu.models.TrackUnpublishReason",Cn,"TRACK_UNPUBLISH_REASON_"]},{no:5,name:"participant",kind:"message",T:()=>ct}])}}const Sf=new yf;class Cf extends T{constructor(){super("stream.video.sfu.event.JoinRequest",[{no:1,name:"token",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"subscriber_sdp",kind:"scalar",T:9},{no:8,name:"publisher_sdp",kind:"scalar",T:9},{no:4,name:"client_details",kind:"message",T:()=>Bd},{no:5,name:"migration",kind:"message",T:()=>Ef},{no:6,name:"fast_reconnect",kind:"scalar",T:8},{no:7,name:"reconnect_details",kind:"message",T:()=>Tf}])}}const io=new Cf;class vf extends T{constructor(){super("stream.video.sfu.event.ReconnectDetails",[{no:1,name:"strategy",kind:"enum",T:()=>["stream.video.sfu.models.WebsocketReconnectStrategy",D,"WEBSOCKET_RECONNECT_STRATEGY_"]},{no:3,name:"announced_tracks",kind:"message",repeat:1,T:()=>Ys},{no:4,name:"subscriptions",kind:"message",repeat:1,T:()=>Xs},{no:5,name:"reconnect_attempt",kind:"scalar",T:13},{no:6,name:"from_sfu_id",kind:"scalar",T:9},{no:7,name:"previous_session_id",kind:"scalar",T:9}])}}const Tf=new vf;class wf extends T{constructor(){super("stream.video.sfu.event.Migration",[{no:1,name:"from_sfu_id",kind:"scalar",T:9},{no:2,name:"announced_tracks",kind:"message",repeat:1,T:()=>Ys},{no:3,name:"subscriptions",kind:"message",repeat:1,T:()=>Xs}])}}const Ef=new wf;class kf extends T{constructor(){super("stream.video.sfu.event.JoinResponse",[{no:1,name:"call_state",kind:"message",T:()=>Rd},{no:2,name:"reconnected",kind:"scalar",T:8},{no:3,name:"fast_reconnect_deadline_seconds",kind:"scalar",T:5},{no:4,name:"publish_options",kind:"message",T:()=>Ld}])}}const Rf=new kf;class If extends T{constructor(){super("stream.video.sfu.event.ParticipantJoined",[{no:1,name:"call_cid",kind:"scalar",T:9},{no:2,name:"participant",kind:"message",T:()=>ct}])}}const _f=new If;class Pf extends T{constructor(){super("stream.video.sfu.event.ParticipantLeft",[{no:1,name:"call_cid",kind:"scalar",T:9},{no:2,name:"participant",kind:"message",T:()=>ct}])}}const Of=new Pf;class Af extends T{constructor(){super("stream.video.sfu.event.ParticipantUpdated",[{no:1,name:"call_cid",kind:"scalar",T:9},{no:2,name:"participant",kind:"message",T:()=>ct}])}}const Df=new Af;class Nf extends T{constructor(){super("stream.video.sfu.event.SubscriberOffer",[{no:1,name:"ice_restart",kind:"scalar",T:8},{no:2,name:"sdp",kind:"scalar",T:9}])}}const xf=new Nf;class Lf extends T{constructor(){super("stream.video.sfu.event.PublisherAnswer",[{no:1,name:"sdp",kind:"scalar",T:9}])}}const Uf=new Lf;class Mf extends T{constructor(){super("stream.video.sfu.event.ConnectionQualityChanged",[{no:1,name:"connection_quality_updates",kind:"message",repeat:1,T:()=>Vf}])}}const Ff=new Mf;class jf extends T{constructor(){super("stream.video.sfu.event.ConnectionQualityInfo",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"connection_quality",kind:"enum",T:()=>["stream.video.sfu.models.ConnectionQuality",Sn,"CONNECTION_QUALITY_"]}])}}const Vf=new jf;class $f extends T{constructor(){super("stream.video.sfu.event.DominantSpeakerChanged",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9}])}}const Bf=new $f;class qf extends T{constructor(){super("stream.video.sfu.event.AudioLevel",[{no:1,name:"user_id",kind:"scalar",T:9},{no:2,name:"session_id",kind:"scalar",T:9},{no:3,name:"level",kind:"scalar",T:2},{no:4,name:"is_speaking",kind:"scalar",T:8}])}}const Hf=new qf;class Gf extends T{constructor(){super("stream.video.sfu.event.AudioLevelChanged",[{no:1,name:"audio_levels",kind:"message",repeat:1,T:()=>Hf}])}}const Wf=new Gf;class Jf extends T{constructor(){super("stream.video.sfu.event.AudioSender",[{no:2,name:"codec",kind:"message",T:()=>Vn}])}}const Kf=new Jf;class zf extends T{constructor(){super("stream.video.sfu.event.VideoLayerSetting",[{no:1,name:"name",kind:"scalar",T:9},{no:2,name:"active",kind:"scalar",T:8},{no:3,name:"max_bitrate",kind:"scalar",T:5},{no:4,name:"scale_resolution_down_by",kind:"scalar",T:2},{no:6,name:"codec",kind:"message",T:()=>Vn},{no:7,name:"max_framerate",kind:"scalar",T:13},{no:8,name:"scalability_mode",kind:"scalar",T:9}])}}const Yf=new zf;class Xf extends T{constructor(){super("stream.video.sfu.event.VideoSender",[{no:2,name:"codec",kind:"message",T:()=>Vn},{no:3,name:"layers",kind:"message",repeat:1,T:()=>Yf}])}}const Zf=new Xf;class Qf extends T{constructor(){super("stream.video.sfu.event.ChangePublishQuality",[{no:1,name:"audio_senders",kind:"message",repeat:1,T:()=>Kf},{no:2,name:"video_senders",kind:"message",repeat:1,T:()=>Zf}])}}const ep=new Qf;class tp extends T{constructor(){super("stream.video.sfu.event.CallGrantsUpdated",[{no:1,name:"current_grants",kind:"message",T:()=>Qd},{no:2,name:"message",kind:"scalar",T:9}])}}const np=new tp;class sp extends T{constructor(){super("stream.video.sfu.event.GoAway",[{no:1,name:"reason",kind:"enum",T:()=>["stream.video.sfu.models.GoAwayReason",Os,"GO_AWAY_REASON_"]}])}}const ip=new sp;class rp extends T{constructor(){super("stream.video.sfu.event.CallEnded",[{no:1,name:"reason",kind:"enum",T:()=>["stream.video.sfu.models.CallEndedReason",vn,"CALL_ENDED_REASON_"]}])}}const ap=new rp;var K;(function(t){t.UNKNOWN="UNKNOWN",t.VISIBLE="VISIBLE",t.INVISIBLE="INVISIBLE"})(K||(K={}));var Se;(function(t){t[t.IMMEDIATE=20]="IMMEDIATE",t[t.FAST=100]="FAST",t[t.MEDIUM=600]="MEDIUM",t[t.SLOW=1200]="SLOW"})(Se||(Se={}));class op{constructor(e){this._transport=e,this.typeName=rs.typeName,this.methods=rs.methods,this.options=rs.options}setPublisher(e,n){const s=this.methods[0],i=this._transport.mergeOptions(n);return Re("unary",this._transport,s,i,e)}sendAnswer(e,n){const s=this.methods[1],i=this._transport.mergeOptions(n);return Re("unary",this._transport,s,i,e)}iceTrickle(e,n){const s=this.methods[2],i=this._transport.mergeOptions(n);return Re("unary",this._transport,s,i,e)}updateSubscriptions(e,n){const s=this.methods[3],i=this._transport.mergeOptions(n);return Re("unary",this._transport,s,i,e)}updateMuteStates(e,n){const s=this.methods[4],i=this._transport.mergeOptions(n);return Re("unary",this._transport,s,i,e)}iceRestart(e,n){const s=this.methods[5],i=this._transport.mergeOptions(n);return Re("unary",this._transport,s,i,e)}sendStats(e,n){const s=this.methods[6],i=this._transport.mergeOptions(n);return Re("unary",this._transport,s,i,e)}startNoiseCancellation(e,n){const s=this.methods[7],i=this._transport.mergeOptions(n);return Re("unary",this._transport,s,i,e)}stopNoiseCancellation(e,n){const s=this.methods[8],i=this._transport.mergeOptions(n);return Re("unary",this._transport,s,i,e)}}const cp={baseUrl:"",sendJson:!0,timeout:5*1e3,jsonOptions:{ignoreUnknownFields:!0}},up=t=>({interceptUnary(e,n,s,i){return i.meta={...i.meta,...t},e(n,s,i)}}),lp=(t,e)=>({interceptUnary:(n,s,i,r)=>(t(e,`Calling SFU RPC method ${s.name}`,{input:i,options:r}),n(s,i,r))}),dp=t=>{const e=new ll({...cp,...t});return new op(e)},tt=t=>new Promise(e=>setTimeout(e,t));function Ns(t){return t&&(Object.prototype.toString.call(t)==="[object Function]"||typeof t=="function"||t instanceof Function)}const Ye={TOKEN_EXPIRED:40,WS_CLOSED_SUCCESS:1e3,WS_CLOSED_ABRUPTLY:1006,WS_POLICY_VIOLATION:1008};function Zs(t){const e=Math.min(500+t*2e3,5e3),n=Math.min(Math.max(250,(t-1)*2e3),5e3);return Math.floor(Math.random()*(e-n)+n)}function xs(){return ro()}function bt(t){let e="";for(let n=0;n<t.length;n++)e+=t[n].toString(16).padStart(2,"0");return e}function ro(){const t=pp(16);return t[6]=t[6]&15|64,t[8]=t[8]&191|128,bt(t.subarray(0,4))+"-"+bt(t.subarray(4,6))+"-"+bt(t.subarray(6,8))+"-"+bt(t.subarray(8,10))+"-"+bt(t.subarray(10,16))}function hp(t){const e=Math.pow(2,8*t.byteLength/t.length);for(let n=0;n<t.length;n++)t[n]=Math.random()*e}const fp=typeof crypto<"u"&&typeof(crypto==null?void 0:crypto.getRandomValues)<"u"?crypto.getRandomValues.bind(crypto):typeof msCrypto<"u"?msCrypto.getRandomValues.bind(msCrypto):hp;function pp(t){const e=new Uint8Array(t);return fp(e),e}function Ls(t){typeof window<"u"&&window.addEventListener&&(window.addEventListener("offline",t),window.addEventListener("online",t))}function ao(t){typeof window<"u"&&window.removeEventListener&&(window.removeEventListener("offline",t),window.removeEventListener("online",t))}function mp(t){return!t.status||t.status<200||300<=t.status}function gp(t){return t.code!==void 0}const J=()=>{var t;return typeof navigator>"u"?!1:((t=navigator.product)==null?void 0:t.toLowerCase())==="reactnative"},rr=Object.freeze({trace:0,debug:1,info:2,warn:3,error:4});let oo,Qs="info";const co=(t,e,...n)=>{let s;switch(t){case"error":if(J()){e=`ERROR: ${e}`,s=console.info;break}s=console.error;break;case"warn":if(J()){e=`WARN: ${e}`,s=console.info;break}s=console.warn;break;case"info":s=console.info;break;case"trace":s=console.trace;break;default:s=console.log;break}s(e,...n)},bp=(t,e)=>{oo=t,e&&yp(e)},yp=t=>{Qs=t},Sp=()=>Qs,V=t=>{const e=oo||co,n=(t||[]).filter(Boolean).join(":");return(i,r,...a)=>{rr[i]>=rr[Qs]&&e(i,`[${n}]: ${r}`,...a)}},Ie=async(t,e)=>{var i;let n=0,s;do{n>0&&await tt(Zs(n));try{s=await t()}catch(r){const a=r instanceof ie&&r.code===q[q.cancelled],o=(e==null?void 0:e.aborted)??!1;if(a||o)throw r;V(["sfu-client","rpc"])("debug",`rpc failed (${n})`,r),n++}}while(!s||(i=s.response.error)!=null&&i.shouldRetry);return s},Cp="1.14.0",[vp,Tp,wp]=Cp.split(".");let Ep={type:qe.PLAIN_JAVASCRIPT,major:vp,minor:Tp,patch:wp},kp,Rp,Ip={oneofKind:void 0};const Us=()=>Ep,_p=()=>kp,Pp=()=>Rp,Op=()=>Ip,ar=()=>{if(J())return{sdk:Us(),os:_p(),device:Pp()};const t=new dl.UAParser(navigator.userAgent),{browser:e,os:n,device:s,cpu:i}=t.getResult();return{sdk:Us(),browser:{name:e.name||navigator.userAgent,version:e.version||""},os:{name:n.name||"",version:n.version||"",architecture:i.architecture||""},device:{name:[s.vendor,s.model,s.type].filter(Boolean).join(" "),version:""}}},ei=()=>typeof navigator>"u"?!1:/^((?!chrome|android).)*safari/i.test(navigator.userAgent||""),xt=()=>{var t;return typeof navigator>"u"?!1:(t=navigator.userAgent)==null?void 0:t.includes("Firefox")},or=(t,e,n,s)=>{const i=s==="receiver"?RTCRtpReceiver:RTCRtpSender;if(!("getCapabilities"in i))return;const r=i.getCapabilities(t);if(!r)return;const a=[],o=[],d=[],c=`${t}/${e.toLowerCase()}`,l=n&&`${t}/${n.toLowerCase()}`;for(const h of r.codecs){const u=h.mimeType.toLowerCase();if(u===l)continue;if(!(u===c)){d.push(h);continue}if(u!=="video/h264"){a.push(h);continue}const m=h.sdpFmtpLine;if(!m||!m.includes("profile-level-id=42")){o.push(h);continue}m.includes("packetization-mode=1")?a.unshift(h):a.push(h)}return[...a,...o,...d]},Ap=async t=>{const e=new RTCPeerConnection;e.addTransceiver("video",{direction:t}),e.addTransceiver("audio",{direction:t});const s=(await e.createOffer()).sdp??"";return e.getTransceivers().forEach(i=>{var r;(r=i.stop)==null||r.call(i)}),e.close(),s},as=t=>J()?cr(t,"h264"):ei()?"h264":xt()?"vp8":cr(t,"vp8"),cr=(t,e)=>{if(!t||!("getCapabilities"in RTCRtpSender))return e;const n=RTCRtpSender.getCapabilities("video");if(!n||Tn(t)&&(ei()||xt()))return e;const{codecs:s}=n,i=`video/${t}`.toLowerCase();return s.some(r=>r.mimeType.toLowerCase()===i)?t:e},Tn=t=>t?(t=t.toLowerCase(),t==="vp9"||t==="av1"||t==="video/vp9"||t==="video/av1"):!1,Dp={subscriberOffer:void 0,publisherAnswer:void 0,connectionQualityChanged:void 0,audioLevelChanged:void 0,iceTrickle:void 0,changePublishQuality:void 0,participantJoined:void 0,participantLeft:void 0,dominantSpeakerChanged:void 0,joinResponse:void 0,healthCheckResponse:void 0,trackPublished:void 0,trackUnpublished:void 0,error:void 0,callGrantsUpdated:void 0,goAway:void 0,iceRestart:void 0,pinsUpdated:void 0,callEnded:void 0,participantUpdated:void 0,participantMigrationComplete:void 0,codecNegotiationComplete:void 0,changePublishOptions:void 0},ur=t=>Object.prototype.hasOwnProperty.call(Dp,t);class Np{constructor(){this.logger=V(["Dispatcher"]),this.subscribers={},this.dispatch=(e,n="0")=>{const s=e.eventPayload.oneofKind;if(!s)return;const i=e.eventPayload[s];this.logger("debug",`Dispatching ${s}, tag=${n}`,i);const r=this.subscribers[s];if(r)for(const a of r)try{a(i)}catch(o){this.logger("warn","Listener failed with error",o)}},this.on=(e,n)=>{var s;return((s=this.subscribers)[e]??(s[e]=[])).push(n),()=>{this.off(e,n)}},this.off=(e,n)=>{this.subscribers[e]=(this.subscribers[e]||[]).filter(s=>s!==n)},this.offAll=e=>{e?this.subscribers[e]=[]:this.subscribers={}}}}class xp{constructor(){this.subscriberCandidates=new Is,this.publisherCandidates=new Is,this.push=e=>{e.peerType===ee.SUBSCRIBER?this.subscriberCandidates.next(e):e.peerType===ee.PUBLISHER_UNSPECIFIED?this.publisherCandidates.next(e):V(["sfu-client"])("warn","ICETrickle, Unknown peer type",e)}}}function uo(t){if(t.usernameFragment)return JSON.stringify(t.toJSON());{const e=t.candidate.split(" "),n=e.findIndex(i=>i==="ufrag")+1,s=e[n];return JSON.stringify({...t,usernameFragment:s})}}const Lp={h264:{2160:5e6,1440:3e6,1080:2e6,720:125e4,540:75e4,360:4e5,default:125e4},vp8:{2160:5e6,1440:275e4,1080:2e6,720:125e4,540:6e5,360:35e4,default:125e4},vp9:{2160:3e6,1440:2e6,1080:15e5,720:125e4,540:5e5,360:275e3,default:125e4},av1:{2160:2e6,1440:155e4,1080:1e6,720:6e5,540:35e4,360:2e5,default:6e5}},Up=(t,e)=>{const n=Lp[t];if(!n)throw new Error(`Unknown codec: ${t}`);let s=n[e];if(!s){const r=Object.keys(n).map(Number).reduce((a,o)=>Math.abs(o-e)<Math.abs(a-e)?o:a);s=n[r]}return s??n.default},lo=125e4,Mp={bitrate:lo,width:1280,height:720},Fp={q:3e5,h:75e4,f:lo},jp=t=>t==null?void 0:t.filter(e=>e.rid==="f").map(e=>({...e,rid:"q"})),Vp=t=>t==="q"?je.LOW_UNSPECIFIED:t==="h"?je.MID:je.HIGH,$p=(t,e=Mp,n,s)=>{const i=[],r=t.getSettings(),{width:a=0,height:o=0}=r,{scalabilityMode:d,bitrateDownscaleFactor:c=2,maxSimulcastLayers:l=3}=s||{},h=Bp(e,a,o,n,s);let u=1,f=1;const p=Tn(n),m=p?3:Math.min(3,l);for(const g of["f","h","q"].slice(0,m)){const b={active:!0,rid:g,width:Math.round(a/u),height:Math.round(o/u),maxBitrate:Math.round(h/f)||Fp[g],maxFramerate:30};p?b.scalabilityMode=d||"L3T2_KEY":b.scaleResolutionDownBy=u,u*=2,f*=c,i.unshift(b)}return qp(r,i)},Bp=(t,e,n,s,i)=>{const{width:r,height:a,bitrate:o}=t,{preferredBitrate:d}=i||{},c=e>n?n:e,l=d||(s?Up(s,c):o);if(e<r||n<a){const h=e*n,u=r*a,f=h/u;return Math.round(l*f)}return l},qp=(t,e)=>{let n;const s=Math.max(t.width||0,t.height||0);s<=320?n=e.filter(r=>r.rid==="f"):s<=640?n=e.filter(r=>r.rid!=="h"):n=e;const i=["q","h","f"];return n.map((r,a)=>({...r,rid:i[a]}))},Hp=(t,e,n=3e6)=>{const{screenShareSettings:s}=e||{},i=t.getSettings();return[{active:!0,rid:"q",width:i.width||0,height:i.height||0,scaleResolutionDownBy:1,maxBitrate:(s==null?void 0:s.maxBitrate)??n,maxFramerate:(s==null?void 0:s.maxFramerate)??30}]},wn=(t,e)=>{V(["helpers"])("warn",e,t)},ti=t=>{switch(t){case v.SCREEN_SHARE:return"screenShareStream";case v.SCREEN_SHARE_AUDIO:return"screenShareAudioStream";case v.VIDEO:return"videoStream";case v.AUDIO:return"audioStream";case v.UNSPECIFIED:throw new Error("Track type is unspecified");default:wn(t,"Unknown track type")}},Gp=t=>{switch(t){case"audio":return v.AUDIO;case"video":return v.VIDEO;case"screenshare":return v.SCREEN_SHARE;case"screenshare_audio":return v.SCREEN_SHARE_AUDIO;default:wn(t,"Unknown mute type")}},Wp=t=>{switch(t){case"TRACK_TYPE_AUDIO":return v.AUDIO;case"TRACK_TYPE_VIDEO":return v.VIDEO;case"TRACK_TYPE_SCREEN_SHARE":return v.SCREEN_SHARE;case"TRACK_TYPE_SCREEN_SHARE_AUDIO":return v.SCREEN_SHARE_AUDIO;default:return}},te=ho(Kp),os=ho(zp),It=new Map;async function Jp(t){var e;await((e=It.get(t))==null?void 0:e.promise)}function ho(t){return function(n,s){const{cb:i,onContinued:r}=t(n,s),a=It.get(n);a==null||a.onContinued();const o=a?a.promise.then(i,i):i();return It.set(n,{promise:o,onContinued:r}),o}}function Kp(t,e){let n=!1;return{cb:()=>e().finally(()=>{n||It.delete(t)}),onContinued:()=>n=!0}}function zp(t,e){const n=new AbortController;return{cb:()=>n.signal.aborted?Promise.resolve("canceled"):e(n.signal).finally(()=>{n.signal.aborted||It.delete(t)}),onContinued:()=>n.abort()}}const Yp=t=>typeof t=="function",fe=t=>{let e,n;if(Be([t]).subscribe({next:([s])=>{e=s},error:s=>{n=s}}).unsubscribe(),n)throw n;return e},Oe=(t,e)=>{const n=Yp(e)?e(fe(t)):e;return t.next(n),n},Xp=(t,e)=>{const n=t.getValue(),s=Oe(t,e);return{lastValue:n,value:s,rollback:()=>Oe(t,n)}},ue=(t,e,n=s=>V(["RxUtils"])("warn","An observable emitted an error",s))=>{const s=t.subscribe({next:e,error:n});return()=>{s.unsubscribe()}},Zp=(t,e)=>{const n=Symbol();return ue(t,s=>{te(n,()=>e(s))})};var I;(function(t){t.UNKNOWN="unknown",t.IDLE="idle",t.RINGING="ringing",t.JOINING="joining",t.JOINED="joined",t.LEFT="left",t.RECONNECTING="reconnecting",t.MIGRATING="migrating",t.RECONNECTING_FAILED="reconnecting-failed",t.OFFLINE="offline"})(I||(I={}));class Qp{constructor(){this.connectedUserSubject=new A(void 0),this.callsSubject=new A([]),this.setConnectedUser=e=>Oe(this.connectedUserSubject,e),this.setCalls=e=>Oe(this.callsSubject,e),this.registerCall=e=>{this.calls.find(n=>n.cid===e.cid)||this.setCalls(n=>[...n,e])},this.unregisterCall=e=>this.setCalls(n=>n.filter(s=>s!==e)),this.findCall=(e,n)=>this.calls.find(s=>s.type===e&&s.id===n),this.connectedUserSubject.subscribe(async e=>{if(!e){const n=V(["client-state"]);for(const s of this.calls)s.state.callingState!==I.LEFT&&(n("info",`User disconnected, leaving call: ${s.cid}`),await s.leave({reason:"client.disconnectUser() called"}).catch(i=>{n("error",`Error leaving call: ${s.cid}`,i)}))}})}get connectedUser(){return fe(this.connectedUserSubject)}get calls(){return fe(this.callsSubject)}}class em{constructor(e){this.getCurrentValue=fe,this.connectedUser$=e.connectedUserSubject.asObservable(),this.calls$=e.callsSubject.asObservable()}get connectedUser(){return fe(this.connectedUser$)}get calls(){return fe(this.calls$)}}const En=(...t)=>(e,n)=>{for(const s of t){const i=s(e,n);if(i!==0)return i}return 0},tm=t=>e=>(n,s)=>t(n,s)?e(n,s):0,Xe=t=>t.publishedTracks.includes(v.VIDEO),Jt=t=>t.publishedTracks.includes(v.AUDIO),Me=t=>t.publishedTracks.includes(v.SCREEN_SHARE),nm=t=>t.publishedTracks.includes(v.SCREEN_SHARE_AUDIO),fo=(t,e)=>t.isDominantSpeaker&&!e.isDominantSpeaker?-1:!t.isDominantSpeaker&&e.isDominantSpeaker?1:0,po=(t,e)=>t.isSpeaking&&!e.isSpeaking?-1:!t.isSpeaking&&e.isSpeaking?1:0,sm=(t,e)=>Me(t)&&!Me(e)?-1:!Me(t)&&Me(e)?1:0,mo=(t,e)=>Xe(t)&&!Xe(e)?-1:!Xe(t)&&Xe(e)?1:0,go=(t,e)=>Jt(t)&&!Jt(e)?-1:!Jt(t)&&Jt(e)?1:0,im=(t,e)=>{if(t.pin&&e.pin){if(!t.pin.isLocalPin&&e.pin.isLocalPin)return-1;if(t.pin.isLocalPin&&!e.pin.isLocalPin)return 1;if(t.pin.pinnedAt>e.pin.pinnedAt)return-1;if(t.pin.pinnedAt<e.pin.pinnedAt)return 1}return t.pin&&!e.pin?-1:!t.pin&&e.pin?1:0},bo=t=>(e,n)=>{var s,i,r,a;return((s=e.reaction)==null?void 0:s.type)===t&&((i=n.reaction)==null?void 0:i.type)!==t?-1:((r=e.reaction)==null?void 0:r.type)!==t&&((a=n.reaction)==null?void 0:a.type)===t?1:0},rm=(...t)=>(e,n)=>Kt(e,t)&&!Kt(n,t)?-1:!Kt(e,t)&&Kt(n,t)?1:0,Kt=(t,e)=>(t.roles||[]).some(n=>e.includes(n)),yo=tm((t,e)=>{var n,s;return((n=t.viewportVisibilityState)==null?void 0:n.videoTrack)===K.INVISIBLE||((s=e.viewportVisibilityState)==null?void 0:s.videoTrack)===K.INVISIBLE}),kn=En(im,sm,yo(En(fo,po,bo("raised-hand"),mo,go))),lr=En(yo(En(fo,po,bo("raised-hand"),mo,go)),rm("admin","host","speaker")),cs={broadcasting:!1,hls:{playlist_url:""},rtmps:[]};class am{constructor(){this.backstageSubject=new A(!0),this.blockedUserIdsSubject=new A([]),this.createdAtSubject=new A(new Date),this.endedAtSubject=new A(void 0),this.startsAtSubject=new A(void 0),this.updatedAtSubject=new A(new Date),this.createdBySubject=new A(void 0),this.customSubject=new A({}),this.egressSubject=new A(void 0),this.ingressSubject=new A(void 0),this.recordingSubject=new A(!1),this.sessionSubject=new A(void 0),this.settingsSubject=new A(void 0),this.transcribingSubject=new A(!1),this.captioningSubject=new A(!1),this.endedBySubject=new A(void 0),this.thumbnailsSubject=new A(void 0),this.membersSubject=new A([]),this.ownCapabilitiesSubject=new A([]),this.callingStateSubject=new A(I.UNKNOWN),this.startedAtSubject=new A(void 0),this.participantCountSubject=new A(0),this.anonymousParticipantCountSubject=new A(0),this.participantsSubject=new A([]),this.callStatsReportSubject=new A(void 0),this.closedCaptionsSubject=new A([]),this.orphanedTracks=[],this.logger=V(["CallState"]),this.sortParticipantsBy=kn,this.closedCaptionsTasks=new Map,this.dispose=()=>{for(const[s,i]of this.closedCaptionsTasks.entries())clearTimeout(i),this.closedCaptionsTasks.delete(s)},this.setSortParticipantsBy=s=>{this.sortParticipantsBy=s,this.setCurrentValue(this.participantsSubject,i=>i)},this.getCurrentValue=fe,this.setCurrentValue=Oe,this.setParticipantCount=s=>this.setCurrentValue(this.participantCountSubject,s),this.setStartedAt=s=>this.setCurrentValue(this.startedAtSubject,s),this.setCaptioning=s=>Xp(this.captioningSubject,s),this.setAnonymousParticipantCount=s=>this.setCurrentValue(this.anonymousParticipantCountSubject,s),this.setParticipants=s=>this.setCurrentValue(this.participantsSubject,s),this.setCallingState=s=>this.setCurrentValue(this.callingStateSubject,s),this.setCallStatsReport=s=>this.setCurrentValue(this.callStatsReportSubject,s),this.setMembers=s=>{this.setCurrentValue(this.membersSubject,s)},this.setOwnCapabilities=s=>this.setCurrentValue(this.ownCapabilitiesSubject,s),this.setEndedAt=s=>this.setCurrentValue(this.endedAtSubject,s),this.findParticipantBySessionId=s=>this.participants.find(i=>i.sessionId===s),this.getParticipantLookupBySessionId=()=>this.participants.reduce((s,i)=>(s[i.sessionId]=i,s),{}),this.updateParticipant=(s,i)=>{const r=this.findParticipantBySessionId(s);if(!r){this.logger("warn",`Participant with sessionId ${s} not found`);return}const a=typeof i=="function"?i(r):i,o={...r,...a};return this.setParticipants(d=>d.map(c=>c.sessionId===s?o:c))},this.updateOrAddParticipant=(s,i)=>this.setParticipants(r=>{let a=!0;const o=r.map(d=>d.sessionId===s?(a=!1,{...d,...i}):d);return a&&o.push(i),o}),this.updateParticipants=s=>Object.keys(s).length===0?this.participants:this.setParticipants(i=>i.map(r=>{const a=s[r.sessionId];return a?{...r,...a}:r})),this.updateParticipantTracks=(s,i)=>this.updateParticipants(Object.entries(i).reduce((r,[a,o])=>{o.dimension&&(o.dimension.height=Math.ceil(o.dimension.height),o.dimension.width=Math.ceil(o.dimension.width));const d=s==="videoTrack"?"videoDimension":s==="screenShareTrack"?"screenShareDimension":void 0;return d&&(r[a]={[d]:o.dimension}),r},{})),this.updateFromEvent=s=>{const i=this.eventHandlers[s.type];i&&i(s)},this.setServerSidePins=s=>{const i=s.reduce((r,a)=>(r[a.sessionId]=Date.now(),r),{});return this.setParticipants(r=>r.map(a=>{const o=i[a.sessionId];return o?{...a,pin:{isLocalPin:!1,pinnedAt:o}}:a.pin&&!a.pin.isLocalPin?{...a,pin:void 0}:a}))},this.registerOrphanedTrack=s=>{this.orphanedTracks.push(s)},this.removeOrphanedTrack=s=>{this.orphanedTracks=this.orphanedTracks.filter(i=>i.id!==s)},this.takeOrphanedTracks=s=>{const i=this.orphanedTracks.filter(r=>r.trackLookupPrefix===s);return i.length>0&&(this.orphanedTracks=this.orphanedTracks.filter(r=>r.trackLookupPrefix!==s)),i},this.updateClosedCaptionSettings=s=>{this.closedCaptionsSettings={...this.closedCaptionsSettings,...s}},this.updateFromCallResponse=s=>{this.setCurrentValue(this.backstageSubject,s.backstage),this.setCurrentValue(this.blockedUserIdsSubject,s.blocked_user_ids),this.setCurrentValue(this.createdAtSubject,new Date(s.created_at)),this.setCurrentValue(this.updatedAtSubject,new Date(s.updated_at)),this.setCurrentValue(this.startsAtSubject,s.starts_at?new Date(s.starts_at):void 0),this.setEndedAt(s.ended_at?new Date(s.ended_at):void 0),this.setCurrentValue(this.createdBySubject,s.created_by),this.setCurrentValue(this.customSubject,s.custom),this.setCurrentValue(this.egressSubject,s.egress),this.setCurrentValue(this.ingressSubject,s.ingress),this.setCurrentValue(this.recordingSubject,s.recording);const i=this.setCurrentValue(this.sessionSubject,s.session);this.updateParticipantCountFromSession(i),this.setCurrentValue(this.settingsSubject,s.settings),this.setCurrentValue(this.transcribingSubject,s.transcribing),this.setCurrentValue(this.captioningSubject,s.captioning),this.setCurrentValue(this.thumbnailsSubject,s.thumbnails)},this.updateFromSfuCallState=(s,i,r)=>{const{participants:a,participantCount:o,startedAt:d,pins:c}=s,l=(r==null?void 0:r.announcedTracks.map(h=>h.trackType))??[];this.setParticipants(()=>{const h=this.getParticipantLookupBySessionId();return a.map(u=>{const f=h[u.sessionId],p=u.sessionId===i;return Object.assign({},f,u,{isLocalParticipant:p,publishedTracks:p?l:u.publishedTracks,viewportVisibilityState:(f==null?void 0:f.viewportVisibilityState)??{videoTrack:K.UNKNOWN,screenShareTrack:K.UNKNOWN}})})}),this.setParticipantCount((o==null?void 0:o.total)||0),this.setAnonymousParticipantCount((o==null?void 0:o.anonymous)||0),this.setStartedAt(d?kt.toDate(d):new Date),this.setServerSidePins(c)},this.updateFromMemberRemoved=s=>{this.updateFromCallResponse(s.call),this.setCurrentValue(this.membersSubject,i=>i.filter(r=>s.members.indexOf(r.user_id)===-1))},this.updateFromMemberAdded=s=>{this.updateFromCallResponse(s.call),this.setCurrentValue(this.membersSubject,i=>[...i,...s.members])},this.updateFromHLSBroadcastStopped=()=>{this.setCurrentValue(this.egressSubject,(s=cs)=>({...s,broadcasting:!1}))},this.updateFromHLSBroadcastingFailed=()=>{this.setCurrentValue(this.egressSubject,(s=cs)=>({...s,broadcasting:!1}))},this.updateFromHLSBroadcastStarted=s=>{this.setCurrentValue(this.egressSubject,(i=cs)=>({...i,broadcasting:!0,hls:{...i.hls,playlist_url:s.hls_playlist_url}}))},this.updateParticipantCountFromSession=s=>{if(!s||this.callingState===I.JOINED)return;const i=Object.values(s.participants_count_by_role).reduce((a,o)=>a+o,0),r=Math.max(i,s.participants.length);this.setParticipantCount(r),this.setAnonymousParticipantCount(s.anonymous_participant_count||0)},this.updateFromSessionParticipantCountUpdate=s=>{const i=this.setCurrentValue(this.sessionSubject,r=>r&&{...r,anonymous_participant_count:s.anonymous_participant_count,participants_count_by_role:s.participants_count_by_role});this.updateParticipantCountFromSession(i)},this.updateFromSessionParticipantLeft=s=>{const i=this.setCurrentValue(this.sessionSubject,r=>{if(!r)return r;const{participants:a,participants_count_by_role:o}=r,{user:d,user_session_id:c}=s.participant;return{...r,participants:a.filter(l=>l.user_session_id!==c),participants_count_by_role:{...o,[d.role]:Math.max(0,(o[d.role]||0)-1)}}});this.updateParticipantCountFromSession(i)},this.updateFromSessionParticipantJoined=s=>{const i=this.setCurrentValue(this.sessionSubject,r=>{if(!r)return r;const{participants:a,participants_count_by_role:o}=r,{user:d,user_session_id:c}=s.participant;let l=!0;const h=a.map(f=>f.user_session_id===c?(l=!1,s.participant):f);l&&h.push(s.participant);const u=l?1:0;return{...r,participants:h,participants_count_by_role:{...o,[d.role]:(o[d.role]||0)+u}}});this.updateParticipantCountFromSession(i)},this.updateMembers=s=>{this.updateFromCallResponse(s.call),this.setCurrentValue(this.membersSubject,i=>i.map(r=>{const a=s.members.find(o=>o.user_id===r.user_id);return a||r}))},this.updateParticipantReaction=s=>{const{user:i,custom:r,type:a,emoji_code:o}=s.reaction;this.setParticipants(d=>d.map(c=>c.userId!==i.id?c:{...c,reaction:{type:a,emoji_code:o,custom:r}}))},this.unblockUser=s=>{this.setCurrentValue(this.blockedUserIdsSubject,i=>i&&i.filter(r=>r!==s.user.id))},this.blockUser=s=>{this.setCurrentValue(this.blockedUserIdsSubject,i=>[...i||[],s.user.id])},this.updateOwnCapabilities=s=>{var i;s.user.id===((i=this.localParticipant)==null?void 0:i.userId)&&this.setCurrentValue(this.ownCapabilitiesSubject,s.own_capabilities)},this.updateFromClosedCaptions=s=>{this.setCurrentValue(this.closedCaptionsSubject,i=>{const{closed_caption:r}=s,a=u=>`${u.speaker_id}/${u.start_time}`,o=a(r);if(i.some(u=>a(u)===o))return i;const c=[...i,r],{visibilityDurationMs:l=2700,maxVisibleCaptions:h=2}=this.closedCaptionsSettings||{};if(l>0){const u=setTimeout(()=>{this.setCurrentValue(this.closedCaptionsSubject,f=>f.filter(p=>p!==r)),this.closedCaptionsTasks.delete(o)},l);this.closedCaptionsTasks.set(o,u);for(let f=0;f<c.length-h;f++){const p=a(c[f]),m=this.closedCaptionsTasks.get(p);clearTimeout(m),this.closedCaptionsTasks.delete(p)}}return c.slice(-h)})},this.participants$=this.participantsSubject.asObservable().pipe(Z(s=>s.sort(this.sortParticipantsBy)),re({bufferSize:1,refCount:!0})),this.localParticipant$=this.participants$.pipe(Z(s=>s.find(i=>i.isLocalParticipant)),re({bufferSize:1,refCount:!0})),this.remoteParticipants$=this.participants$.pipe(Z(s=>s.filter(i=>!i.isLocalParticipant)),re({bufferSize:1,refCount:!0})),this.pinnedParticipants$=this.participants$.pipe(Z(s=>s.filter(i=>!!i.pin)),re({bufferSize:1,refCount:!0})),this.dominantSpeaker$=this.participants$.pipe(Z(s=>s.find(i=>i.isDominantSpeaker)),re({bufferSize:1,refCount:!0})),this.hasOngoingScreenShare$=this.participants$.pipe(Z(s=>s.some(i=>Me(i))),Q(),re({bufferSize:1,refCount:!0})),this.createdAt$=this.createdAtSubject.asObservable(),this.endedAt$=this.endedAtSubject.asObservable(),this.startsAt$=this.startsAtSubject.asObservable(),this.startedAt$=this.startedAtSubject.asObservable(),this.updatedAt$=this.updatedAtSubject.asObservable(),this.callStatsReport$=this.callStatsReportSubject.asObservable(),this.members$=this.membersSubject.asObservable(),this.createdBy$=this.createdBySubject.asObservable(),this.custom$=this.customSubject.asObservable(),this.egress$=this.egressSubject.asObservable(),this.ingress$=this.ingressSubject.asObservable(),this.session$=this.sessionSubject.asObservable(),this.settings$=this.settingsSubject.asObservable(),this.endedBy$=this.endedBySubject.asObservable(),this.thumbnails$=this.thumbnailsSubject.asObservable(),this.closedCaptions$=this.closedCaptionsSubject.asObservable();const e=(s,i)=>{if(s.length!==i.length)return!1;for(const r of s)if(!i.includes(r))return!1;for(const r of i)if(!s.includes(r))return!1;return!0},n=(s,i)=>s.asObservable().pipe(Q(i));this.anonymousParticipantCount$=n(this.anonymousParticipantCountSubject),this.blockedUserIds$=n(this.blockedUserIdsSubject,e),this.backstage$=n(this.backstageSubject),this.callingState$=n(this.callingStateSubject),this.ownCapabilities$=n(this.ownCapabilitiesSubject,e),this.participantCount$=n(this.participantCountSubject),this.recording$=n(this.recordingSubject),this.transcribing$=n(this.transcribingSubject),this.captioning$=n(this.captioningSubject),this.eventHandlers={"call.deleted":void 0,"call.permission_request":void 0,"call.recording_ready":void 0,"call.transcription_ready":void 0,"call.user_muted":void 0,"connection.error":void 0,"connection.ok":void 0,"health.check":void 0,"user.banned":void 0,"user.deactivated":void 0,"user.deleted":void 0,"user.muted":void 0,"user.presence.changed":void 0,"user.reactivated":void 0,"user.unbanned":void 0,"user.updated":void 0,custom:void 0,"call.accepted":s=>this.updateFromCallResponse(s.call),"call.blocked_user":this.blockUser,"call.closed_caption":this.updateFromClosedCaptions,"call.closed_captions_failed":()=>{this.setCurrentValue(this.captioningSubject,!1)},"call.closed_captions_started":()=>{this.setCurrentValue(this.captioningSubject,!0)},"call.closed_captions_stopped":()=>{this.setCurrentValue(this.captioningSubject,!1)},"call.created":s=>this.updateFromCallResponse(s.call),"call.ended":s=>{this.updateFromCallResponse(s.call),this.setCurrentValue(this.endedBySubject,s.user)},"call.hls_broadcasting_failed":this.updateFromHLSBroadcastingFailed,"call.hls_broadcasting_started":this.updateFromHLSBroadcastStarted,"call.hls_broadcasting_stopped":this.updateFromHLSBroadcastStopped,"call.live_started":s=>this.updateFromCallResponse(s.call),"call.member_added":this.updateFromMemberAdded,"call.member_removed":this.updateFromMemberRemoved,"call.member_updated_permission":this.updateMembers,"call.member_updated":this.updateMembers,"call.notification":s=>{this.updateFromCallResponse(s.call),this.setMembers(s.members)},"call.permissions_updated":this.updateOwnCapabilities,"call.reaction_new":this.updateParticipantReaction,"call.recording_started":()=>this.setCurrentValue(this.recordingSubject,!0),"call.recording_stopped":()=>this.setCurrentValue(this.recordingSubject,!1),"call.recording_failed":()=>this.setCurrentValue(this.recordingSubject,!1),"call.rejected":s=>this.updateFromCallResponse(s.call),"call.ring":s=>this.updateFromCallResponse(s.call),"call.missed":s=>this.updateFromCallResponse(s.call),"call.session_ended":s=>this.updateFromCallResponse(s.call),"call.session_participant_count_updated":this.updateFromSessionParticipantCountUpdate,"call.session_participant_joined":this.updateFromSessionParticipantJoined,"call.session_participant_left":this.updateFromSessionParticipantLeft,"call.session_started":s=>this.updateFromCallResponse(s.call),"call.transcription_started":()=>{this.setCurrentValue(this.transcribingSubject,!0)},"call.transcription_stopped":()=>{this.setCurrentValue(this.transcribingSubject,!1)},"call.transcription_failed":()=>{this.setCurrentValue(this.transcribingSubject,!1)},"call.unblocked_user":this.unblockUser,"call.updated":s=>this.updateFromCallResponse(s.call)}}get participantCount(){return this.getCurrentValue(this.participantCount$)}get startedAt(){return this.getCurrentValue(this.startedAt$)}get captioning(){return this.getCurrentValue(this.captioning$)}get anonymousParticipantCount(){return this.getCurrentValue(this.anonymousParticipantCount$)}get participants(){return this.getCurrentValue(this.participants$)}get localParticipant(){return this.getCurrentValue(this.localParticipant$)}get remoteParticipants(){return this.getCurrentValue(this.remoteParticipants$)}get dominantSpeaker(){return this.getCurrentValue(this.dominantSpeaker$)}get pinnedParticipants(){return this.getCurrentValue(this.pinnedParticipants$)}get hasOngoingScreenShare(){return this.getCurrentValue(this.hasOngoingScreenShare$)}get callingState(){return this.getCurrentValue(this.callingState$)}get callStatsReport(){return this.getCurrentValue(this.callStatsReport$)}get members(){return this.getCurrentValue(this.members$)}get ownCapabilities(){return this.getCurrentValue(this.ownCapabilities$)}get backstage(){return this.getCurrentValue(this.backstage$)}get blockedUserIds(){return this.getCurrentValue(this.blockedUserIds$)}get createdAt(){return this.getCurrentValue(this.createdAt$)}get endedAt(){return this.getCurrentValue(this.endedAt$)}get startsAt(){return this.getCurrentValue(this.startsAt$)}get updatedAt(){return this.getCurrentValue(this.updatedAt$)}get createdBy(){return this.getCurrentValue(this.createdBy$)}get custom(){return this.getCurrentValue(this.custom$)}get egress(){return this.getCurrentValue(this.egress$)}get ingress(){return this.getCurrentValue(this.ingress$)}get recording(){return this.getCurrentValue(this.recording$)}get session(){return this.getCurrentValue(this.session$)}get settings(){return this.getCurrentValue(this.settings$)}get transcribing(){return this.getCurrentValue(this.transcribing$)}get endedBy(){return this.getCurrentValue(this.endedBy$)}get thumbnails(){return this.getCurrentValue(this.thumbnails$)}get closedCaptions(){return this.getCurrentValue(this.closedCaptions$)}}const om=t=>{const n=/^a=rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/.exec(t);if(n)return{original:n[0],payload:n[1],codec:n[2]}},cm=t=>{const n=/^a=fmtp:(\d*) (.*)/.exec(t);if(n)return{original:n[0],payload:n[1],config:n[2]}},um=(t,e)=>{const s=new RegExp(`(m=${e} \\d+ [\\w/]+) ([\\d\\s]+)`).exec(t);if(s)return{original:s[0],mediaWithPorts:s[1],codecOrder:s[2]}},lm=(t,e)=>{let n;const s=[],i=[];let r=!1;if(t.split(/(\r\n|\r|\n)/).forEach(a=>{if(!/^([a-z])=(.*)/.test(a))return;const d=a[0];if(d==="m"){const c=um(a,e);r=!!c,c&&(n=c)}else if(r&&d==="a"){const c=om(a),l=cm(a);c?s.push(c):l&&i.push(l)}}),n)return{media:n,rtpMap:s,fmtp:i}},dm=t=>{const e=lm(t,"audio"),n=e==null?void 0:e.rtpMap.find(i=>i.codec.toLowerCase()==="opus"),s=n==null?void 0:n.payload;if(s)return e==null?void 0:e.fmtp.find(i=>i.payload===s)},hm=(t,e)=>{const n=dm(t);if(!n)return t;const s=/usedtx=(\d)/.exec(n.config),i=`usedtx=${e?"1":"0"}`,r=s?n.original.replace(/usedtx=(\d)/,i):`${n.original};${i}`;return t.replace(n.original,r)},fm=(t,e,n)=>{var c;const[s,i]=n.mimeType.toLowerCase().split("/"),r=l=>new Set(l.split(";").map(h=>h.trim().toLowerCase())),a=(l,h)=>{if(l.size!==h.size)return!1;for(const u of l)if(!h.has(u))return!1;return!0},o=r(n.sdpFmtpLine||""),d=zs(t);for(const l of d.media){if(l.type!==s||String(l.mid)!==e)continue;const h=new Set;for(const u of l.rtp){if(u.codec.toLowerCase()!==i)continue;(i==="vp8"?!0:l.fmtp.some(p=>p.payload===u.payload&&a(r(p.config),o)))&&h.add(u.payload)}for(const u of l.fmtp){const f=u.config.match(/(apt)=(\d+)/);if(!f)continue;const[,,p]=f;h.has(Number(p))&&h.add(u.payload)}l.rtp=l.rtp.filter(u=>h.has(u.payload)),l.fmtp=l.fmtp.filter(u=>h.has(u.payload)),l.rtcpFb=(c=l.rtcpFb)==null?void 0:c.filter(u=>h.has(u.payload)),l.payloads=Array.from(h).join(" ")}return Xa(d)},pm=(t,e,n=51e4)=>{n=Math.max(Math.min(n,51e4),96e3);const s=zs(t),i=s.media.find(o=>o.type==="audio"&&String(o.mid)===e);if(!i)return t;const r=i.rtp.find(o=>o.codec==="opus");if(!r)return t;const a=i.fmtp.find(o=>o.payload===r.payload);return a?(a.config.match(/stereo=(\d)/)?a.config=a.config.replace(/stereo=(\d)/,"stereo=1"):a.config=`${a.config};stereo=1`,a.config.match(/maxaveragebitrate=(\d*)/)?a.config=a.config.replace(/maxaveragebitrate=(\d*)/,`maxaveragebitrate=${n}`):a.config=`${a.config};maxaveragebitrate=${n}`,Xa(s)):t},us=(t,e,n)=>{if(t.mid)return t.mid;if(!n)return"";const s=t.sender.track,r=zs(n).media.find(a=>{var o;return a.type===s.kind&&(((o=a.msid)==null?void 0:o.includes(s.id))??!0)});return typeof(r==null?void 0:r.mid)<"u"?String(r.mid):e===-1?"":String(e)};class mm{constructor({connectionConfig:e,sfuClient:n,dispatcher:s,state:i,isDtxEnabled:r,isRedEnabled:a,onUnrecoverableError:o,logTag:d}){this.transceiverCache=new Map,this.trackLayersCache=new Map,this.publishOptsForTrack=new Map,this.transceiverInitOrder=[],this.isIceRestarting=!1,this.createPeerConnection=c=>{const l=new RTCPeerConnection(c);return l.addEventListener("icecandidate",this.onIceCandidate),l.addEventListener("negotiationneeded",this.onNegotiationNeeded),l.addEventListener("icecandidateerror",this.onIceCandidateError),l.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),l.addEventListener("icegatheringstatechange",this.onIceGatheringStateChange),l.addEventListener("signalingstatechange",this.onSignalingStateChange),l},this.close=({stopTracks:c})=>{c&&(this.stopPublishing(),this.transceiverCache.clear(),this.trackLayersCache.clear()),this.detachEventHandlers(),this.pc.close()},this.detachEventHandlers=()=>{this.unsubscribeOnIceRestart(),this.unsubscribeChangePublishQuality(),this.pc.removeEventListener("icecandidate",this.onIceCandidate),this.pc.removeEventListener("negotiationneeded",this.onNegotiationNeeded),this.pc.removeEventListener("icecandidateerror",this.onIceCandidateError),this.pc.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.pc.removeEventListener("icegatheringstatechange",this.onIceGatheringStateChange),this.pc.removeEventListener("signalingstatechange",this.onSignalingStateChange)},this.publishStream=async(c,l,h,u={})=>{if(l.readyState==="ended")throw new Error("Can't publish a track that has ended already.");l.enabled||(l.enabled=!0);const f=this.transceiverCache.get(h);if(!f||!f.sender.track){const p=()=>{this.logger("info",`Track ${v[h]} has ended abruptly`),l.removeEventListener("ended",p),this.notifyTrackMuteStateChanged(c,h,!0).catch(m=>this.logger("warn","Couldn't notify track mute state",m))};l.addEventListener("ended",p),this.addTransceiver(h,l,u,c)}else await this.updateTransceiver(f,l);await this.notifyTrackMuteStateChanged(c,h,!1)},this.addTransceiver=(c,l,h,u)=>{const{forceCodec:f,preferredCodec:p}=h,m=f||as(p),g=this.computeLayers(c,l,h),b=this.pc.addTransceiver(l,{direction:"sendonly",streams:c===v.VIDEO||c===v.SCREEN_SHARE?[u]:void 0,sendEncodings:Tn(m)?jp(g):g});if(this.logger("debug",`Added ${v[c]} transceiver`),this.transceiverInitOrder.push(c),this.transceiverCache.set(c,b),this.publishOptsForTrack.set(c,h),!("setCodecPreferences"in b))return;const E=this.getCodecPreferences(c,c===v.VIDEO?m:void 0,"receiver");if(E)try{this.logger("info",`Setting ${v[c]} codec preferences`,E),b.setCodecPreferences(E)}catch(C){this.logger("warn","Couldn't set codec preferences",C)}},this.updateTransceiver=async(c,l)=>{const h=c.sender.track;h&&h!==l&&h.stop(),await c.sender.replaceTrack(l)},this.unpublishStream=async(c,l)=>{var u;const h=this.transceiverCache.get(c);h&&h.sender.track&&(l?h.sender.track.readyState==="live":h.sender.track.enabled)&&(l?h.sender.track.stop():h.sender.track.enabled=!1,(u=this.state.localParticipant)!=null&&u.publishedTracks.includes(c)&&await this.notifyTrackMuteStateChanged(void 0,c,!0))},this.isPublishing=c=>{const l=this.transceiverCache.get(c);if(!l||!l.sender)return!1;const h=l.sender.track;return!!h&&h.readyState==="live"&&h.enabled},this.notifyTrackMuteStateChanged=async(c,l,h)=>{await this.sfuClient.updateMuteState(l,h);const u=ti(l);u&&(h?this.state.updateParticipant(this.sfuClient.sessionId,f=>({publishedTracks:f.publishedTracks.filter(p=>p!==l),[u]:void 0})):this.state.updateParticipant(this.sfuClient.sessionId,f=>({publishedTracks:f.publishedTracks.includes(l)?f.publishedTracks:[...f.publishedTracks,l],[u]:c})))},this.stopPublishing=()=>{this.logger("debug","Stopping publishing all tracks"),this.pc.getSenders().forEach(c=>{var l;(l=c.track)==null||l.stop(),this.pc.signalingState!=="closed"&&this.pc.removeTrack(c)})},this.changePublishQuality=async c=>{var g;this.logger("info","Update publish quality, requested layers by SFU:",c);const l=(g=this.transceiverCache.get(v.VIDEO))==null?void 0:g.sender;if(!l){this.logger("warn","Update publish quality, no video sender found.");return}const h=l.getParameters();if(h.encodings.length===0){this.logger("warn","Update publish quality, No suitable video encoding quality found");return}const[u]=h.codecs,f=u&&Tn(u.mimeType);let p=!1;for(const b of h.encodings){const E=f?c[0]:c.find(H=>H.name===b.rid)??(h.encodings.length===1?c[0]:void 0),C=!!(E!=null&&E.active);if(C!==b.active&&(b.active=C,p=!0),!E)continue;const{maxFramerate:w,scaleResolutionDownBy:k,maxBitrate:R,scalabilityMode:x}=E;k>=1&&k!==b.scaleResolutionDownBy&&(b.scaleResolutionDownBy=k,p=!0),R>0&&R!==b.maxBitrate&&(b.maxBitrate=R,p=!0),w>0&&w!==b.maxFramerate&&(b.maxFramerate=w,p=!0),x&&x!==b.scalabilityMode&&(b.scalabilityMode=x,p=!0)}const m=h.encodings.filter(b=>b.active);if(!p){this.logger("info","Update publish quality, no change:",m);return}await l.setParameters(h),this.logger("info","Update publish quality, enabled rids:",m)},this.getStats=c=>this.pc.getStats(c),this.getCodecPreferences=(c,l,h)=>{if(c===v.VIDEO)return or("video",l||"vp8",void 0,h);if(c===v.AUDIO){const u=this.isRedEnabled?"red":"opus",f=this.isRedEnabled?void 0:"red";return or("audio",l??u,f,h)}},this.onIceCandidate=c=>{const{candidate:l}=c;if(!l){this.logger("debug","null ice candidate");return}this.sfuClient.iceTrickle({iceCandidate:uo(l),peerType:ee.PUBLISHER_UNSPECIFIED}).catch(h=>{this.logger("warn","ICETrickle failed",h)})},this.setSfuClient=c=>{this.sfuClient=c},this.restartIce=async()=>{this.logger("debug","Restarting ICE connection");const c=this.pc.signalingState;if(this.isIceRestarting||c==="have-local-offer"){this.logger("debug","ICE restart is already in progress");return}await this.negotiate({iceRestart:!0})},this.onNegotiationNeeded=()=>{this.negotiate().catch(c=>{var l;this.logger("error","Negotiation failed.",c),(l=this.onUnrecoverableError)==null||l.call(this)})},this.negotiate=async c=>{const l=await this.pc.createOffer(c);l.sdp&&(l.sdp=hm(l.sdp,this.isDtxEnabled),this.isPublishing(v.SCREEN_SHARE_AUDIO)&&(l.sdp=this.enableHighQualityAudio(l.sdp)),this.isPublishing(v.VIDEO)&&(l.sdp=this.removeUnpreferredCodecs(l.sdp,v.VIDEO)));const h=this.getAnnouncedTracks(l.sdp);if(h.length===0)throw new Error("Can't negotiate without announcing any tracks");try{this.isIceRestarting=(c==null?void 0:c.iceRestart)??!1,await this.pc.setLocalDescription(l);const{response:u}=await this.sfuClient.setPublisher({sdp:l.sdp||"",tracks:h});if(u.error)throw new Error(u.error.message);await this.pc.setRemoteDescription({type:"answer",sdp:u.sdp})}finally{this.isIceRestarting=!1}this.sfuClient.iceTrickleBuffer.publisherCandidates.subscribe(async u=>{try{const f=JSON.parse(u.iceCandidate);await this.pc.addIceCandidate(f)}catch(f){this.logger("warn","ICE candidate error",f,u)}})},this.enableHighQualityAudio=c=>{const l=this.transceiverCache.get(v.SCREEN_SHARE_AUDIO);if(!l)return c;const h=this.transceiverInitOrder.indexOf(v.SCREEN_SHARE_AUDIO),u=us(l,h,c);return pm(c,u)},this.getAnnouncedTracks=c=>{var l;return c=c||((l=this.pc.localDescription)==null?void 0:l.sdp),this.pc.getTransceivers().filter(h=>h.direction==="sendonly"&&h.sender.track).map(h=>{let u;this.transceiverCache.forEach((k,R)=>{k===h&&(u=R)});const f=h.sender.track;let p;const m=f.readyState==="live";m?(p=this.computeLayers(u,f)||[],this.trackLayersCache.set(u,p)):(p=this.trackLayersCache.get(u)||[],this.logger("debug",`Track ${v[u]} is ended. Announcing last known optimal layers`,p));const g=p.map(k=>({rid:k.rid||"",bitrate:k.maxBitrate||0,fps:k.maxFramerate||0,quality:Vp(k.rid||""),videoDimension:{width:k.width,height:k.height}})),b=[v.AUDIO,v.SCREEN_SHARE_AUDIO].includes(u),E=f.getSettings(),C=b&&E.channelCount===2,w=this.transceiverInitOrder.indexOf(u);return{trackId:f.id,layers:g,trackType:u,mid:us(h,w,c),stereo:C,dtx:b&&this.isDtxEnabled,red:b&&this.isRedEnabled,muted:!m}})},this.computeLayers=(c,l,h)=>{var b;const{settings:u}=this.state,f=u==null?void 0:u.video.target_resolution,p=(b=u==null?void 0:u.screensharing.target_resolution)==null?void 0:b.bitrate,m=h||this.publishOptsForTrack.get(c),g=(h==null?void 0:h.forceCodec)||as(h==null?void 0:h.preferredCodec);return c===v.VIDEO?$p(l,f,g,m):c===v.SCREEN_SHARE?Hp(l,m,p):void 0},this.onIceCandidateError=c=>{const l=c instanceof RTCPeerConnectionIceErrorEvent&&`${c.errorCode}: ${c.errorText}`,h=this.pc.iceConnectionState,u=h==="connected"||h==="checking"?"debug":"warn";this.logger(u,"ICE Candidate error",l)},this.onIceConnectionStateChange=()=>{const c=this.pc.iceConnectionState;this.logger("debug","ICE Connection state changed to",c),this.state.callingState!==I.RECONNECTING&&(c==="failed"||c==="disconnected")&&(this.logger("debug","Attempting to restart ICE"),this.restartIce().catch(l=>{var h;this.logger("error","ICE restart error",l),(h=this.onUnrecoverableError)==null||h.call(this)}))},this.onIceGatheringStateChange=()=>{this.logger("debug","ICE Gathering State",this.pc.iceGatheringState)},this.onSignalingStateChange=()=>{this.logger("debug","Signaling state changed",this.pc.signalingState)},this.logger=V(["Publisher",d]),this.pc=this.createPeerConnection(e),this.sfuClient=n,this.state=i,this.isDtxEnabled=r,this.isRedEnabled=a,this.onUnrecoverableError=o,this.unsubscribeOnIceRestart=s.on("iceRestart",c=>{c.peerType===ee.PUBLISHER_UNSPECIFIED&&this.restartIce().catch(l=>{var h;this.logger("warn","ICERestart failed",l),(h=this.onUnrecoverableError)==null||h.call(this)})}),this.unsubscribeChangePublishQuality=s.on("changePublishQuality",({videoSenders:c})=>{te("publisher.changePublishQuality",async()=>{for(const l of c){const{layers:h}=l,u=h.filter(f=>f.active);await this.changePublishQuality(u)}}).catch(l=>{this.logger("warn","Failed to change publish quality",l)})})}removeUnpreferredCodecs(e,n){const s=this.publishOptsForTrack.get(n),i=!!(s!=null&&s.forceSingleCodec)||J()||xt();if(!s||!i)return e;const r=s.forceCodec||as(s.preferredCodec),a=this.getCodecPreferences(n,r,"sender");if(!a||a.length===0)return e;const o=this.transceiverCache.get(n);if(!o)return e;const d=this.transceiverInitOrder.indexOf(n),c=us(o,d,e),[l]=a;return fm(e,c,l)}}class gm{constructor({sfuClient:e,dispatcher:n,state:s,connectionConfig:i,onUnrecoverableError:r,logTag:a}){this.isIceRestarting=!1,this.createPeerConnection=c=>{const l=new RTCPeerConnection(c);return l.addEventListener("icecandidate",this.onIceCandidate),l.addEventListener("track",this.handleOnTrack),l.addEventListener("icecandidateerror",this.onIceCandidateError),l.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),l.addEventListener("icegatheringstatechange",this.onIceGatheringStateChange),l},this.close=()=>{this.detachEventHandlers(),this.pc.close()},this.detachEventHandlers=()=>{this.unregisterOnSubscriberOffer(),this.unregisterOnIceRestart(),this.pc.removeEventListener("icecandidate",this.onIceCandidate),this.pc.removeEventListener("track",this.handleOnTrack),this.pc.removeEventListener("icecandidateerror",this.onIceCandidateError),this.pc.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.pc.removeEventListener("icegatheringstatechange",this.onIceGatheringStateChange)},this.getStats=c=>this.pc.getStats(c),this.setSfuClient=c=>{this.sfuClient=c},this.restartIce=async()=>{if(this.logger("debug","Restarting ICE connection"),this.pc.signalingState==="have-remote-offer"){this.logger("debug","ICE restart is already in progress");return}if(this.pc.connectionState==="new"){this.logger("debug","ICE connection is not yet established, skipping restart.");return}const c=this.isIceRestarting;try{this.isIceRestarting=!0,await this.sfuClient.iceRestart({peerType:ee.SUBSCRIBER})}catch(l){throw this.isIceRestarting=c,l}},this.handleOnTrack=c=>{const[l]=c.streams,[h,u]=l.id.split(":"),f=this.state.participants.find(E=>E.trackLookupPrefix===h);this.logger("debug",`[onTrack]: Got remote ${u} track for userId: ${f==null?void 0:f.userId}`,c.track.id,c.track);const p=`${f==null?void 0:f.userId} ${u}:${h}`;c.track.addEventListener("mute",()=>{this.logger("info",`[onTrack]: Track muted: ${p}`)}),c.track.addEventListener("unmute",()=>{this.logger("info",`[onTrack]: Track unmuted: ${p}`)}),c.track.addEventListener("ended",()=>{this.logger("info",`[onTrack]: Track ended: ${p}`),this.state.removeOrphanedTrack(l.id)});const m=Wp(u);if(!m)return this.logger("error",`Unknown track type: ${u}`);if(!f){this.logger("warn",`[onTrack]: Received track for unknown participant: ${h}`,c),this.state.registerOrphanedTrack({id:l.id,trackLookupPrefix:h,track:l,trackType:m});return}const g=ti(m);if(!g){this.logger("error",`Unknown track type: ${u}`);return}const b=f[g];b&&(this.logger("info",`[onTrack]: Cleaning up previous remote ${c.track.kind} tracks for userId: ${f.userId}`),b.getTracks().forEach(E=>{E.stop(),b.removeTrack(E)})),this.state.updateParticipant(f.sessionId,{[g]:l})},this.onIceCandidate=c=>{const{candidate:l}=c;if(!l){this.logger("debug","null ice candidate");return}this.sfuClient.iceTrickle({iceCandidate:uo(l),peerType:ee.SUBSCRIBER}).catch(h=>{this.logger("warn","ICETrickle failed",h)})},this.negotiate=async c=>{this.logger("info","Received subscriberOffer",c),await this.pc.setRemoteDescription({type:"offer",sdp:c.sdp}),this.sfuClient.iceTrickleBuffer.subscriberCandidates.subscribe(async h=>{try{const u=JSON.parse(h.iceCandidate);await this.pc.addIceCandidate(u)}catch(u){this.logger("warn","ICE candidate error",[u,h])}});const l=await this.pc.createAnswer();await this.pc.setLocalDescription(l),await this.sfuClient.sendAnswer({peerType:ee.SUBSCRIBER,sdp:l.sdp||""}),this.isIceRestarting=!1},this.onIceConnectionStateChange=()=>{const c=this.pc.iceConnectionState;this.logger("debug","ICE connection state changed",c),this.state.callingState!==I.RECONNECTING&&(this.isIceRestarting||(c==="failed"||c==="disconnected")&&(this.logger("debug","Attempting to restart ICE"),this.restartIce().catch(l=>{var h;this.logger("error","ICE restart failed",l),(h=this.onUnrecoverableError)==null||h.call(this)})))},this.onIceGatheringStateChange=()=>{this.logger("debug","ICE gathering state changed",this.pc.iceGatheringState)},this.onIceCandidateError=c=>{const l=c instanceof RTCPeerConnectionIceErrorEvent&&`${c.errorCode}: ${c.errorText}`,h=this.pc.iceConnectionState,u=h==="connected"||h==="checking"?"debug":"warn";this.logger(u,"ICE Candidate error",l)},this.logger=V(["Subscriber",a]),this.sfuClient=e,this.state=s,this.onUnrecoverableError=r,this.pc=this.createPeerConnection(i);const o=Symbol("subscriberOffer");this.unregisterOnSubscriberOffer=n.on("subscriberOffer",c=>{te(o,()=>this.negotiate(c)).catch(l=>{this.logger("error","Negotiation failed.",l)})});const d=Symbol("iceRestart");this.unregisterOnIceRestart=n.on("iceRestart",c=>{te(d,async()=>{c.peerType===ee.SUBSCRIBER&&await this.restartIce()}).catch(l=>{var h;this.logger("error","ICERestart failed",l),(h=this.onUnrecoverableError)==null||h.call(this)})})}}const bm=t=>{const{endpoint:e,onMessage:n,logTag:s}=t,i=V(["SfuClientWS",s]),r=new WebSocket(e);return r.binaryType="arraybuffer",r.addEventListener("error",a=>{i("error","Signaling WS channel error",a)}),r.addEventListener("close",a=>{i("info","Signaling WS channel is closed",a)}),r.addEventListener("open",a=>{i("info","Signaling WS channel is open",a)}),r.addEventListener("message",a=>{try{const o=a.data instanceof ArrayBuffer?ir.fromBinary(new Uint8Array(a.data)):ir.fromJsonString(a.data.toString());n(o)}catch(o){i("error","Failed to decode a message. Check whether the Proto models match.",{event:a,error:o})}}),r};function _t(t){let e=!0;const n=t.then(i=>({status:"resolved",result:i}),i=>({status:"rejected",error:i})).finally(()=>e=!1),s=()=>n.then(i=>{if(i.status==="rejected")throw i.error;return i.result});return s.checkPending=()=>e,s}const St=()=>{let t,e;const n=new Promise((o,d)=>{t=o,e=d});let s=!1,i=!1;return{promise:n,resolve:o=>{s=!0,t(o)},reject:o=>{i=!0,e(o)},isResolved:s,isRejected:i}},dr=Symbol("uninitialized");function Je(t){let e=dr;return()=>(e===dr&&(e=t()),e)}const ym={src:`const timerIdMapping = new Map();
self.addEventListener('message', (event) => {
    const request = event.data;
    switch (request.type) {
        case 'setTimeout':
        case 'setInterval':
            timerIdMapping.set(request.id, (request.type === 'setTimeout' ? setTimeout : setInterval)(() => {
                tick(request.id);
                if (request.type === 'setTimeout') {
                    timerIdMapping.delete(request.id);
                }
            }, request.timeout));
            break;
        case 'clearTimeout':
        case 'clearInterval':
            (request.type === 'clearTimeout' ? clearTimeout : clearInterval)(timerIdMapping.get(request.id));
            timerIdMapping.delete(request.id);
            break;
    }
});
function tick(id) {
    const message = { type: 'tick', id };
    self.postMessage(message);
}`};class Sm{constructor(){this.currentTimerId=1,this.callbacks=new Map,this.fallback=!1}setup({useTimerWorker:e=!0}={}){if(!e){this.fallback=!0;return}try{const n=ym.src,s=new Blob([n],{type:"application/javascript; charset=utf-8"}),i=URL.createObjectURL(s);this.worker=new Worker(i,{name:"str-timer-worker"}),this.worker.addEventListener("message",r=>{var d;const{type:a,id:o}=r.data;a==="tick"&&((d=this.callbacks.get(o))==null||d())})}catch(n){V(["timer-worker"])("error",n),this.fallback=!0}}destroy(){var e;this.callbacks.clear(),(e=this.worker)==null||e.terminate(),this.worker=void 0,this.fallback=!1}get ready(){return this.fallback||!!this.worker}setInterval(e,n){return this.setTimer("setInterval",e,n)}clearInterval(e){this.clearTimer("clearInterval",e)}setTimeout(e,n){return this.setTimer("setTimeout",e,n)}clearTimeout(e){this.clearTimer("clearTimeout",e)}setTimer(e,n,s){if(this.ready||this.setup(),this.fallback)return(e==="setTimeout"?setTimeout:setInterval)(n,s);const i=this.getTimerId();return this.callbacks.set(i,()=>{n(),e==="setTimeout"&&this.callbacks.delete(i)}),this.sendMessage({type:e,id:i,timeout:s}),i}clearTimer(e,n){if(n){if(this.ready||this.setup(),this.fallback){(e==="clearTimeout"?clearTimeout:clearInterval)(n);return}this.callbacks.delete(n),this.sendMessage({type:e,id:n})}}getTimerId(){return this.currentTimerId++}sendMessage(e){if(!this.worker)throw new Error("Cannot use timer worker before it's set up");this.worker.postMessage(e)}}let So=!1;const hr=()=>{So=!0},Rn=Je(()=>{const t=new Sm;return t.setup({useTimerWorker:So}),t});class ve{constructor({dispatcher:e,credentials:n,sessionId:s,logTag:i,joinResponseTimeout:r=5e3,onSignalClose:a,streamClient:o}){this.iceTrickleBuffer=new xp,this.isLeaving=!1,this.pingIntervalInMs=10*1e3,this.unhealthyTimeoutInMs=this.pingIntervalInMs+5*1e3,this.joinResponseTask=St(),this.abortController=new AbortController,this.createWebSocket=()=>{this.signalWs=bm({logTag:this.logTag,endpoint:`${this.credentials.server.ws_endpoint}?tag=${this.logTag}`,onMessage:l=>{this.lastMessageTimestamp=new Date,this.scheduleConnectionCheck(),this.dispatcher.dispatch(l,this.logTag)}}),this.signalWs.addEventListener("close",this.handleWebSocketClose),this.signalReady=_t(Promise.race([new Promise(l=>{const h=()=>{this.signalWs.removeEventListener("open",h),l(this.signalWs)};this.signalWs.addEventListener("open",h)}),new Promise((l,h)=>{setTimeout(()=>h(new Error("SFU WS connection timed out")),this.joinResponseTimeout)})]))},this.cleanUpWebSocket=()=>{this.signalWs.removeEventListener("close",this.handleWebSocketClose)},this.handleWebSocketClose=()=>{var l;this.signalWs.removeEventListener("close",this.handleWebSocketClose),Rn().clearInterval(this.keepAliveInterval),clearTimeout(this.connectionCheckTimeout),(l=this.onSignalClose)==null||l.call(this)},this.close=(l=ve.NORMAL_CLOSURE,h)=>{this.signalWs.readyState===WebSocket.OPEN&&(this.logger("debug",`Closing SFU WS connection: ${l} - ${h}`),this.signalWs.close(l,`js-client: ${h}`),this.cleanUpWebSocket()),this.dispose()},this.dispose=()=>{var l;this.logger("debug","Disposing SFU client"),this.unsubscribeIceTrickle(),this.unsubscribeNetworkChanged(),clearInterval(this.keepAliveInterval),clearTimeout(this.connectionCheckTimeout),clearTimeout(this.migrateAwayTimeout),this.abortController.abort(),(l=this.migrationTask)==null||l.resolve()},this.leaveAndClose=async l=>{await this.joinTask;try{this.isLeaving=!0,await this.notifyLeave(l)}catch(h){this.logger("debug","Error notifying SFU about leaving call",h)}this.close(ve.NORMAL_CLOSURE,l.substring(0,115))},this.updateSubscriptions=async l=>(await this.joinTask,Ie(()=>this.rpc.updateSubscriptions({sessionId:this.sessionId,tracks:l}),this.abortController.signal)),this.setPublisher=async l=>(await this.joinTask,Ie(()=>this.rpc.setPublisher({...l,sessionId:this.sessionId}),this.abortController.signal)),this.sendAnswer=async l=>(await this.joinTask,Ie(()=>this.rpc.sendAnswer({...l,sessionId:this.sessionId}),this.abortController.signal)),this.iceTrickle=async l=>(await this.joinTask,Ie(()=>this.rpc.iceTrickle({...l,sessionId:this.sessionId}),this.abortController.signal)),this.iceRestart=async l=>(await this.joinTask,Ie(()=>this.rpc.iceRestart({...l,sessionId:this.sessionId}),this.abortController.signal)),this.updateMuteState=async(l,h)=>(await this.joinTask,this.updateMuteStates({muteStates:[{trackType:l,muted:h}]})),this.updateMuteStates=async l=>(await this.joinTask,Ie(()=>this.rpc.updateMuteStates({...l,sessionId:this.sessionId}),this.abortController.signal)),this.sendStats=async l=>(await this.joinTask,Ie(()=>this.rpc.sendStats({...l,sessionId:this.sessionId}),this.abortController.signal)),this.startNoiseCancellation=async()=>(await this.joinTask,Ie(()=>this.rpc.startNoiseCancellation({sessionId:this.sessionId}),this.abortController.signal)),this.stopNoiseCancellation=async()=>(await this.joinTask,Ie(()=>this.rpc.stopNoiseCancellation({sessionId:this.sessionId}),this.abortController.signal)),this.enterMigration=async(l={})=>{var p;this.isLeaving=!0;const{timeout:h=7*1e3}=l;(p=this.migrationTask)==null||p.reject(new Error("Cancelled previous migration"));const u=this.migrationTask=St(),f=this.dispatcher.on("participantMigrationComplete",()=>{f(),clearTimeout(this.migrateAwayTimeout),u.resolve()});return this.migrateAwayTimeout=setTimeout(()=>{f(),u.reject(new Error(`Migration (${this.logTag}) failed to complete in ${h}ms`))},h),u.promise},this.join=async l=>{await this.signalReady(),(this.joinResponseTask.isResolved||this.joinResponseTask.isRejected)&&(this.joinResponseTask=St());const h=this.joinResponseTask;let u;const f=this.dispatcher.on("joinResponse",p=>{this.logger("debug","Received joinResponse",p),clearTimeout(u),f(),this.keepAlive(),h.resolve(p)});return u=setTimeout(()=>{f(),h.reject(new Error('Waiting for "joinResponse" has timed out'))},this.joinResponseTimeout),await this.send(gt.create({requestPayload:{oneofKind:"joinRequest",joinRequest:io.create({...l,sessionId:this.sessionId,token:this.credentials.token})}})),h.promise},this.ping=async()=>this.send(gt.create({requestPayload:{oneofKind:"healthCheckRequest",healthCheckRequest:{}}})),this.notifyLeave=async l=>this.send(gt.create({requestPayload:{oneofKind:"leaveCallRequest",leaveCallRequest:{sessionId:this.sessionId,reason:l}}})),this.send=async l=>{await this.signalReady();const h=gt.toJson(l);if(this.signalWs.readyState!==WebSocket.OPEN){this.logger("debug","Signal WS is not open. Skipping message",h);return}this.logger("debug",`Sending message to: ${this.edgeName}`,h),this.signalWs.send(gt.toBinary(l))},this.keepAlive=()=>{const l=Rn();l.clearInterval(this.keepAliveInterval),this.keepAliveInterval=l.setInterval(()=>{this.ping().catch(h=>{this.logger("error","Error sending healthCheckRequest to SFU",h)})},this.pingIntervalInMs)},this.scheduleConnectionCheck=()=>{clearTimeout(this.connectionCheckTimeout),this.connectionCheckTimeout=setTimeout(()=>{this.lastMessageTimestamp&&new Date().getTime()-this.lastMessageTimestamp.getTime()>this.unhealthyTimeoutInMs&&this.close(ve.ERROR_CONNECTION_UNHEALTHY,`SFU connection unhealthy. Didn't receive any message for ${this.unhealthyTimeoutInMs}ms`)},this.unhealthyTimeoutInMs)},this.dispatcher=e,this.sessionId=s||ro(),this.onSignalClose=a,this.credentials=n;const{server:d,token:c}=n;this.edgeName=d.edge_name,this.joinResponseTimeout=r,this.logTag=i,this.logger=V(["SfuClient",i]),this.rpc=dp({baseUrl:d.url,interceptors:[up({Authorization:`Bearer ${c}`}),Sp()==="trace"&&lp(this.logger,"trace")].filter(l=>!!l)}),this.unsubscribeIceTrickle=e.on("iceTrickle",l=>{this.iceTrickleBuffer.push(l)}),this.unsubscribeNetworkChanged=o.on("network.changed",l=>{var h;l.online?(h=this.networkAvailableTask)==null||h.resolve():this.networkAvailableTask=St()}),this.createWebSocket()}get isHealthy(){return this.signalWs.readyState===WebSocket.OPEN}get joinTask(){return this.joinResponseTask.promise}}ve.NORMAL_CLOSURE=1e3;ve.ERROR_CONNECTION_UNHEALTHY=4001;ve.DISPOSE_OLD_SOCKET=4002;const Cm=t=>({iceServers:t.map(e=>({urls:e.urls,username:e.username,credential:e.password}))}),vm=t=>async function(n){if(n.user.id===t.currentUserId)return;const{state:s}=t;n.call.created_by.id===t.currentUserId&&s.callingState===I.RINGING&&await t.join()},Tm=t=>async function(n){if(n.user.id===t.currentUserId)return;const{call:s}=n,{session:i}=s;if(!i){t.logger("warn","No call session provided. Ignoring call.rejected event.",n);return}const r=i.rejected_by,{members:a,callingState:o}=t.state;if(o!==I.RINGING){t.logger("info","Call is not in ringing mode (it is either accepted or rejected already). Ignoring call.rejected event.",n);return}t.isCreatedByMe?a.filter(c=>c.user_id!==t.currentUserId).every(c=>r[c.user_id])&&(t.logger("info","everyone rejected, leaving the call"),await t.leave({reason:"ring: everyone rejected"})):r[s.created_by.id]&&(t.logger("info","call creator rejected, leaving call"),await t.leave({reason:"ring: creator rejected"}))},wm=t=>function(){const{callingState:n}=t.state;n!==I.IDLE&&n!==I.LEFT&&t.leave({reason:"call.ended event received",reject:!1}).catch(s=>{t.logger("error","Failed to leave call after call.ended ",s)})},Em=t=>t.on("callEnded",async e=>{if(t.state.callingState!==I.LEFT)try{t.state.setEndedAt(new Date);const n=vn[e.reason];await t.leave({reason:`callEnded received: ${n}`})}catch(n){t.logger("error","Failed to leave call after being ended by the SFU",n)}}),km=t=>function(n){const{currentGrants:s}=n;if(s){const{canPublishAudio:i,canPublishVideo:r,canScreenshare:a}=s,o={[z.SEND_AUDIO]:i,[z.SEND_VIDEO]:r,[z.SCREENSHARE]:a},d=t.ownCapabilities.filter(c=>o[c]!==!1);Object.entries(o).forEach(([c,l])=>{l&&!d.includes(c)&&d.push(c)}),t.setOwnCapabilities(d)}},Rm=(t,e)=>t.on("connectionQualityChanged",n=>{const{connectionQualityUpdates:s}=n;s&&e.updateParticipants(s.reduce((i,r)=>{const{sessionId:a,connectionQuality:o}=r;return i[a]={connectionQuality:o},i},{}))}),Im=(t,e)=>t.on("healthCheckResponse",n=>{const{participantCount:s}=n;s&&(e.setParticipantCount(s.total),e.setAnonymousParticipantCount(s.anonymous))}),_m=(t,e)=>t.on("error",n=>{n.error&&n.error.code!==Rt.LIVE_ENDED||e.permissionsContext.hasPermission(z.JOIN_BACKSTAGE)||e.leave({reason:"live ended"}).catch(s=>{e.logger("error","Failed to leave call after live ended",s)})}),Pm=t=>t.on("error",e=>{if(!e.error)return;const n=V(["SfuClient"]),{error:s,reconnectStrategy:i}=e;n("error","SFU reported error",{code:Rt[s.code],reconnectStrategy:D[i],message:s.message,shouldRetry:s.shouldRetry})}),Om=t=>function(n){const{pins:s}=n;t.setServerSidePins(s)},Am=t=>t.on("trackUnpublished",async e=>{const{cause:n,type:s,sessionId:i}=e,{localParticipant:r}=t.state;if(n===Cn.MODERATION&&i===(r==null?void 0:r.sessionId)){const a=t.logger;a("info",`Local participant's ${v[s]} track is muted remotely`);try{s===v.VIDEO?await t.camera.disable():s===v.AUDIO?await t.microphone.disable():s===v.SCREEN_SHARE||s===v.SCREEN_SHARE_AUDIO?await t.screenShare.disable():a("warn","Unsupported track type to soft mute",v[s])}catch(o){a("error","Failed to stop publishing",o)}}}),Dm=t=>function(n){const{participant:s}=n;if(!s)return;const i=ni(t,s);t.updateOrAddParticipant(s.sessionId,Object.assign(s,i,{viewportVisibilityState:{videoTrack:K.UNKNOWN,screenShareTrack:K.UNKNOWN}}))},Nm=t=>function(n){const{participant:s}=n;s&&t.setParticipants(i=>i.filter(r=>r.sessionId!==s.sessionId))},xm=t=>function(n){const{participant:s}=n;s&&t.updateParticipant(s.sessionId,s)},Lm=t=>function(n){const{type:s,sessionId:i}=n;if(n.participant){const r=ni(t,n.participant),a=Object.assign(n.participant,r);t.updateOrAddParticipant(i,a)}else t.updateParticipant(i,r=>({publishedTracks:[...r.publishedTracks,s].filter(Mm)}))},Um=t=>function(n){const{type:s,sessionId:i}=n;if(n.participant){const r=ni(t,n.participant),a=Object.assign(n.participant,r);t.updateOrAddParticipant(i,a)}else t.updateParticipant(i,r=>({publishedTracks:r.publishedTracks.filter(a=>a!==s)}))},Mm=(t,e,n)=>n.indexOf(t)===e,ni=(t,e)=>{const n=t.takeOrphanedTracks(e.trackLookupPrefix);if(!n.length)return;const s={};for(const i of n){const r=ti(i.trackType);r&&(s[r]=i.track)}return s},Fm=(t,e)=>t.on("dominantSpeakerChanged",n=>{var i;const{sessionId:s}=n;s!==((i=e.dominantSpeaker)==null?void 0:i.sessionId)&&e.setParticipants(r=>r.map(a=>a.sessionId===s?{...a,isDominantSpeaker:!0}:a.isDominantSpeaker?{...a,isDominantSpeaker:!1}:a))}),jm=(t,e)=>t.on("audioLevelChanged",n=>{const{audioLevels:s}=n;e.updateParticipants(s.reduce((i,r)=>(i[r.sessionId]={audioLevel:r.level,isSpeaking:r.isSpeaking},i),{}))}),Vm=(t,e)=>{const n=t.state,s=[t.on("call.ended",wm(t)),Em(t),_m(e,t),Pm(e),Rm(e,n),Im(e,n),t.on("participantJoined",Dm(n)),t.on("participantLeft",Nm(n)),t.on("participantUpdated",xm(n)),t.on("trackPublished",Lm(n)),t.on("trackUnpublished",Um(n)),jm(e,n),Fm(e,n),t.on("callGrantsUpdated",km(n)),t.on("pinsUpdated",Om(n)),Am(t)];return t.ringing&&s.push(Co(t)),()=>{s.forEach(i=>i())}},Co=t=>{const e={"call.accepted":vm(t),"call.rejected":Tm(t)},n=Object.keys(e).map(s=>{const i=s;return t.on(i,e[i])});return()=>{n.forEach(s=>s())}},In=t=>{const e=[];return t.forEach(n=>{e.push(n)}),e},$m=t=>{const{sdk:e,...n}=t,s=vo(e),i=To(e);return{sdkName:s,sdkVersion:i,...n}},vo=t=>t&&t.type===qe.REACT?"stream-react":t&&t.type===qe.REACT_NATIVE?"stream-react-native":"stream-js",To=t=>t?`${t.major}.${t.minor}.${t.patch}`:"0.0.0-development",Bm=({subscriber:t,publisher:e,state:n,datacenter:s,pollingIntervalInMs:i=2e3})=>{const r=V(["stats"]),a=async(p,m)=>p==="subscriber"&&t?t.getStats(m):p==="publisher"&&e?e.getStats(m):void 0,o=async(p,m)=>{const g=p==="subscriber"?t:e;if(!g)return[];const b=[];for(let E of m.getTracks()){const C=await g.getStats(E),w=ls(C,{trackKind:E.kind,kind:p});b.push(w)}return b},d=p=>{l.add(p),h()},c=p=>{l.delete(p),h()},l=new Set,h=async()=>{var w,k;const p={},m=new Set(l);if(m.size>0)for(let R of n.participants){if(!m.has(R.sessionId))continue;const x=R.isLocalParticipant?"publisher":"subscriber";try{const H=new MediaStream([...((w=R.videoStream)==null?void 0:w.getVideoTracks())||[],...((k=R.audioStream)==null?void 0:k.getAudioTracks())||[]]);p[R.sessionId]=await o(x,H),H.getTracks().forEach(se=>{H.removeTrack(se)})}catch(H){r("error",`Failed to collect stats for ${x} of ${R.userId}`,H)}}const[g,b]=await Promise.all([t.getStats().then(R=>ls(R,{kind:"subscriber",trackKind:"video"})).then(fr),e?e.getStats().then(R=>ls(R,{kind:"publisher",trackKind:"video"})).then(fr):wo()]),[E,C]=await Promise.all([a("subscriber"),e?a("publisher"):void 0]);n.setCallStatsReport({datacenter:s,publisherStats:b,subscriberStats:g,subscriberRawStats:E,publisherRawStats:C,participants:p,timestamp:Date.now()})};let u;if(i>0){const p=async()=>{await h().catch(m=>{r("debug","Failed to collect stats",m)}),u=setTimeout(p,i)};p()}return{getRawStatsForTrack:a,getStatsForStream:o,startReportingStatsFor:d,stopReportingStatsFor:c,stop:()=>{u&&clearTimeout(u)}}},ls=(t,e)=>{const{trackKind:n,kind:s}=e,i=s==="subscriber"?"inbound-rtp":"outbound-rtp",r=In(t),a=r.filter(o=>o.type===i&&o.kind===n).map(o=>{const d=o,c=r.find(u=>u.type==="codec"&&u.id===d.codecId),l=r.find(u=>u.type==="transport"&&u.id===d.transportId);let h;if(l&&l.dtlsState==="connected"){const u=r.find(f=>f.type==="candidate-pair"&&f.id===l.selectedCandidatePairId);h=u==null?void 0:u.currentRoundTripTime}return{bytesSent:d.bytesSent,bytesReceived:d.bytesReceived,codec:c==null?void 0:c.mimeType,currentRoundTripTime:h,frameHeight:d.frameHeight,frameWidth:d.frameWidth,framesPerSecond:d.framesPerSecond,jitter:d.jitter,kind:d.kind,qualityLimitationReason:d.qualityLimitationReason,rid:d.rid,ssrc:d.ssrc}});return{rawStats:t,streams:a,timestamp:Date.now()}},wo=t=>({rawReport:t??{streams:[],timestamp:Date.now()},totalBytesSent:0,totalBytesReceived:0,averageJitterInMs:0,averageRoundTripTimeInMs:0,qualityLimitationReasons:"none",highestFrameWidth:0,highestFrameHeight:0,highestFramesPerSecond:0,codec:"",timestamp:Date.now()}),fr=t=>{const e=wo(t);let n=-1;const s=(d,c)=>d*c,i=new Set,r=t.streams,a=r.reduce((d,c)=>{d.totalBytesSent+=c.bytesSent||0,d.totalBytesReceived+=c.bytesReceived||0,d.averageJitterInMs+=c.jitter||0,d.averageRoundTripTimeInMs+=c.currentRoundTripTime||0;const l=s(c.frameWidth||0,c.frameHeight||0);return l>n&&(d.highestFrameWidth=c.frameWidth||0,d.highestFrameHeight=c.frameHeight||0,d.highestFramesPerSecond=c.framesPerSecond||0,n=l),i.add(c.qualityLimitationReason||""),d},e);r.length>0&&(a.averageJitterInMs=Math.round(a.averageJitterInMs/r.length*1e3),a.averageRoundTripTimeInMs=Math.round(a.averageRoundTripTimeInMs/r.length*1e3),a.codec=r[0].codec||"");const o=[i.has("cpu")&&"cpu",i.has("bandwidth")&&"bandwidth",i.has("other")&&"other"].filter(Boolean).join(", ");return o&&(a.qualityLimitationReasons=o),a};class qm{constructor(e,{options:n,clientDetails:s,subscriber:i,publisher:r,microphone:a,camera:o,state:d}){this.logger=V(["SfuStatsReporter"]),this.inputDevices=new Map,this.observeDevice=(h,u)=>{var p;const{hasBrowserPermission$:f}=h.state;(p=this.unsubscribeDevicePermissionsSubscription)==null||p.call(this),this.unsubscribeDevicePermissionsSubscription=ue(Be([f,this.state.ownCapabilities$]),([m,g])=>{var E;(E=this.unsubscribeListDevicesSubscription)==null||E.call(this);const b=u==="mic"?g.includes(z.SEND_AUDIO):g.includes(z.SEND_VIDEO);if(!m||!b){this.inputDevices.set(u,{currentDevice:"",availableDevices:[],isPermitted:!1});return}this.unsubscribeListDevicesSubscription=ue(Be([h.listDevices(),h.state.selectedDevice$]),([C,w])=>{const k=C.find(R=>R.deviceId===w);this.inputDevices.set(u,{currentDevice:(k==null?void 0:k.label)||w||"",availableDevices:C.map(R=>R.label),isPermitted:!0})})})},this.sendTelemetryData=async h=>this.run(h),this.run=async h=>{var p;const[u,f]=await Promise.all([this.subscriber.getStats().then(In).then(JSON.stringify),((p=this.publisher)==null?void 0:p.getStats().then(In).then(JSON.stringify))??"[]"]);await this.sfuClient.sendStats({sdk:this.sdkName,sdkVersion:this.sdkVersion,webrtcVersion:this.webRTCVersion,subscriberStats:u,publisherStats:f,audioDevices:this.inputDevices.get("mic"),videoDevices:this.inputDevices.get("camera"),deviceState:Op(),telemetry:h})},this.start=()=>{this.options.reporting_interval_ms<=0||(this.observeDevice(this.microphone,"mic"),this.observeDevice(this.camera,"camera"),clearInterval(this.intervalId),this.intervalId=setInterval(()=>{this.run().catch(h=>{this.logger("warn","Failed to report stats",h)})},this.options.reporting_interval_ms))},this.stop=()=>{var h,u;(h=this.unsubscribeDevicePermissionsSubscription)==null||h.call(this),this.unsubscribeDevicePermissionsSubscription=void 0,(u=this.unsubscribeListDevicesSubscription)==null||u.call(this),this.unsubscribeListDevicesSubscription=void 0,this.inputDevices.clear(),clearInterval(this.intervalId),this.intervalId=void 0},this.sfuClient=e,this.options=n,this.subscriber=i,this.publisher=r,this.microphone=a,this.camera=o,this.state=d;const{sdk:c,browser:l}=s;this.sdkName=vo(c),this.sdkVersion=To(c),this.webRTCVersion=`${(l==null?void 0:l.name)||""}-${(l==null?void 0:l.version)||""}`||"N/A"}}const Hm=.35;class Gm{constructor(){this.elementHandlerMap=new Map,this.observer=null,this.queueSet=new Set,this.setViewport=(e,n)=>{const s=()=>{var i;(i=this.observer)==null||i.disconnect(),this.observer=null,this.elementHandlerMap.clear()};return this.observer=new IntersectionObserver(i=>{i.forEach(r=>{const a=this.elementHandlerMap.get(r.target);a==null||a(r)})},{root:e,...n,threshold:(n==null?void 0:n.threshold)??Hm}),this.queueSet.size&&(this.queueSet.forEach(([i,r])=>{e.contains(i)&&(this.observer.observe(i),this.elementHandlerMap.set(i,r))}),this.queueSet.clear()),s},this.observe=(e,n)=>{const s=[e,n],i=()=>{var r;this.elementHandlerMap.delete(e),(r=this.observer)==null||r.unobserve(e),this.queueSet.delete(s)};return this.elementHandlerMap.has(e)?i:this.observer?(this.observer.root.contains(e)&&(this.elementHandlerMap.set(e,n),this.observer.observe(e)),i):(this.queueSet.add(s),i)}}}const pr={videoTrack:K.UNKNOWN,screenShareTrack:K.UNKNOWN},zt=Symbol("globalOverrideKey");class Wm{constructor(e,n){this.viewportTracker=new Gm,this.logger=V(["DynascaleManager"]),this.pendingSubscriptionsUpdate=null,this.videoTrackSubscriptionOverridesSubject=new A({}),this.videoTrackSubscriptionOverrides$=this.videoTrackSubscriptionOverridesSubject.asObservable(),this.incomingVideoSettings$=this.videoTrackSubscriptionOverrides$.pipe(Z(s=>{const{[zt]:i,...r}=s;return{enabled:(i==null?void 0:i.enabled)!==!1,preferredResolution:i!=null&&i.enabled?i.dimension:void 0,participants:Object.fromEntries(Object.entries(r).map(([a,o])=>[a,{enabled:(o==null?void 0:o.enabled)!==!1,preferredResolution:o!=null&&o.enabled?o.dimension:void 0}])),isParticipantVideoEnabled:a=>{var o,d;return((o=s[a])==null?void 0:o.enabled)??((d=s[zt])==null?void 0:d.enabled)??!0}}}),re(1)),this.setVideoTrackSubscriptionOverrides=(s,i)=>i?Oe(this.videoTrackSubscriptionOverridesSubject,r=>({...r,...Object.fromEntries(i.map(a=>[a,s]))})):Oe(this.videoTrackSubscriptionOverridesSubject,s?{[zt]:s}:{}),this.applyTrackSubscriptions=(s=Se.SLOW)=>{this.pendingSubscriptionsUpdate&&clearTimeout(this.pendingSubscriptionsUpdate);const i=()=>{var r;this.pendingSubscriptionsUpdate=null,(r=this.sfuClient)==null||r.updateSubscriptions(this.trackSubscriptions).catch(a=>{this.logger("debug","Failed to update track subscriptions",a)})};s?this.pendingSubscriptionsUpdate=setTimeout(i,s):i()},this.trackElementVisibility=(s,i,r)=>{const a=this.viewportTracker.observe(s,o=>{this.callState.updateParticipant(i,d=>{const c=d.viewportVisibilityState??pr,l=o.isIntersecting||document.fullscreenElement===s?K.VISIBLE:K.INVISIBLE;return{...d,viewportVisibilityState:{...c,[r]:l}}})});return()=>{a(),this.callState.updateParticipant(i,o=>{const d=o.viewportVisibilityState??pr;return{...o,viewportVisibilityState:{...d,[r]:K.UNKNOWN}}})}},this.setViewport=s=>this.viewportTracker.setViewport(s),this.bindVideoElement=(s,i,r)=>{const a=this.callState.findParticipantBySessionId(i);if(!a)return;const o=(m,g)=>{g&&(g.width===0||g.height===0)&&(this.logger("debug","Ignoring 0x0 dimension",a),g=void 0),this.callState.updateParticipantTracks(r,{[i]:{dimension:g}}),this.applyTrackSubscriptions(m)},d=this.callState.participants$.pipe(Z(m=>m.find(g=>g.sessionId===i)),Ps(m=>!!m),Q(),re({bufferSize:1,refCount:!0}));let c;const l=a.isLocalParticipant?null:d.pipe(Z(m=>{var g;return(g=m.viewportVisibilityState)==null?void 0:g[r]}),Q()).subscribe(m=>{if(!c){c=m??K.UNKNOWN;return}if(c=m??K.UNKNOWN,m===K.INVISIBLE)return o(Se.MEDIUM,void 0);o(Se.MEDIUM,{width:s.clientWidth,height:s.clientHeight})});let h;const u=a.isLocalParticipant?null:new ResizeObserver(()=>{const m={width:s.clientWidth,height:s.clientHeight};if(!h){h=m;return}if(h.width===m.width&&h.height===m.height||c===K.INVISIBLE)return;const b=Math.max(m.width/h.width,m.height/h.height)>1.2?Se.IMMEDIATE:Se.MEDIUM;o(b,{width:s.clientWidth,height:s.clientHeight}),h=m});u==null||u.observe(s);const f=a.isLocalParticipant?null:d.pipe(Wt("publishedTracks"),Z(m=>r==="videoTrack"?Xe(m):Me(m)),Q()).subscribe(m=>{m?o(Se.IMMEDIATE,{width:s.clientWidth,height:s.clientHeight}):o(Se.FAST,void 0)});s.autoplay=!0,s.playsInline=!0,s.muted=!0;const p=d.pipe(Wt(r==="videoTrack"?"videoStream":"screenShareStream")).subscribe(m=>{const g=r==="videoTrack"?m.videoStream:m.screenShareStream;s.srcObject!==g&&(s.srcObject=g??null,(ei()||xt())&&setTimeout(()=>{s.srcObject=g??null,s.play().catch(b=>{this.logger("warn","Failed to play stream",b)})},25))});return()=>{o(Se.FAST,void 0),l==null||l.unsubscribe(),f==null||f.unsubscribe(),p.unsubscribe(),u==null||u.disconnect()}},this.bindAudioElement=(s,i,r)=>{const a=this.callState.findParticipantBySessionId(i);if(!a||a.isLocalParticipant)return;const o=this.callState.participants$.pipe(Z(h=>h.find(u=>u.sessionId===i)),Ps(h=>!!h),Q(),re({bufferSize:1,refCount:!0})),d=o.pipe(Wt(r==="screenShareAudioTrack"?"screenShareAudioStream":"audioStream")).subscribe(h=>{const u=r==="screenShareAudioTrack"?h.screenShareAudioStream:h.audioStream;s.srcObject!==u&&setTimeout(()=>{if(s.srcObject=u??null,s.srcObject){s.play().catch(p=>{this.logger("warn","Failed to play stream",p)});const{selectedDevice:f}=this.speaker.state;f&&"setSinkId"in s&&s.setSinkId(f)}})}),c="setSinkId"in s?this.speaker.state.selectedDevice$.subscribe(h=>{h&&s.setSinkId(h)}):null,l=Be([this.speaker.state.volume$,o.pipe(Wt("audioVolume"))]).subscribe(([h,u])=>{s.volume=u.audioVolume??h});return s.autoplay=!0,()=>{c==null||c.unsubscribe(),l.unsubscribe(),d.unsubscribe()}},this.callState=e,this.speaker=n}setSfuClient(e){this.sfuClient=e}get trackSubscriptions(){const e=[];for(const n of this.callState.remoteParticipants){if(n.videoDimension&&Xe(n)){const s=this.videoTrackSubscriptionOverrides[n.sessionId]??this.videoTrackSubscriptionOverrides[zt];(s==null?void 0:s.enabled)!==!1&&e.push({userId:n.userId,sessionId:n.sessionId,trackType:v.VIDEO,dimension:(s==null?void 0:s.dimension)??n.videoDimension})}n.screenShareDimension&&Me(n)&&e.push({userId:n.userId,sessionId:n.sessionId,trackType:v.SCREEN_SHARE,dimension:n.screenShareDimension}),nm(n)&&e.push({userId:n.userId,sessionId:n.sessionId,trackType:v.SCREEN_SHARE_AUDIO})}return e}get videoTrackSubscriptionOverrides(){return fe(this.videoTrackSubscriptionOverrides$)}}class Jm{constructor(){this.permissions=[],this.setPermissions=e=>{this.permissions=e||[]},this.setCallSettings=e=>{this.settings=e},this.hasPermission=e=>this.permissions.includes(e),this.canRequest=(e,n=this.settings)=>{if(!n)return!1;const{audio:s,video:i,screensharing:r}=n;switch(e){case z.SEND_AUDIO:return s.access_request_enabled;case z.SEND_VIDEO:return i.access_request_enabled;case z.SCREENSHARE:return r.access_request_enabled;default:return!1}}}}class Ct{constructor(e,n={sortParticipantsBy:kn}){this.name=e,this.options=n}}class Km{constructor(e){this.register=n=>{this.callTypes[n.name]=n},this.unregister=n=>{delete this.callTypes[n]},this.get=n=>(this.callTypes[n]||this.register(new Ct(n)),this.callTypes[n]),this.callTypes=e.reduce((n,s)=>(n[s.name]=s,n),{})}}const zm=new Km([new Ct("default",{sortParticipantsBy:kn}),new Ct("development",{sortParticipantsBy:kn}),new Ct("livestream",{sortParticipantsBy:lr}),new Ct("audio_room",{sortParticipantsBy:lr})]);class Eo{constructor(e){this.permission=e,this.disposeController=new AbortController,this.wasPrompted=!1,this.listeners=new Set,this.logger=V(["permissions"]);const n=this.disposeController.signal;this.ready=(async()=>{const s=i=>{this.setState("prompt")};if(!Ym())return s();try{const i=await navigator.permissions.query({name:e.queryName});n.aborted||(this.setState(i.state),i.addEventListener("change",()=>this.setState(i.state),{signal:n}))}catch{s()}})()}dispose(){this.state=void 0,this.disposeController.abort()}async getState(){if(await this.ready,!this.state)throw new Error("BrowserPermission instance possibly disposed");return this.state}async prompt({forcePrompt:e=!1,throwOnNotAllowed:n=!1}={}){return await te(`permission-prompt-${this.permission.queryName}`,async()=>{if(await this.getState()!=="prompt"||this.wasPrompted&&!e){const s=this.state==="granted";if(!s&&n)throw new Error("Permission was not granted previously, and prompting again is not allowed");return s}try{this.wasPrompted=!0;const s=await navigator.mediaDevices.getUserMedia(this.permission.constraints);return ng(s),this.setState("granted"),!0}catch(s){if(s&&typeof s=="object"&&"name"in s&&(s.name==="NotAllowedError"||s.name==="SecurityError")){if(this.logger("info","Browser permission was not granted",{permission:this.permission}),this.setState("denied"),n)throw s;return!1}throw this.logger("error","Failed to getUserMedia",{error:s,permission:this.permission}),s}})}listen(e){return this.listeners.add(e),this.state&&e(this.state),()=>this.listeners.delete(e)}asObservable(){return ld(e=>this.listen(e),(e,n)=>n()).pipe(Z(e=>e!=="denied"))}setState(e){this.state!==e&&(this.state=e,this.listeners.forEach(n=>n(e)))}}function Ym(){var t;return!J()&&typeof navigator<"u"&&!!((t=navigator.permissions)!=null&&t.query)}const si=(t,e)=>Ge((async()=>{let n=await navigator.mediaDevices.enumerateDevices();return n.some(i=>i.kind===e&&i.label==="")&&await t.prompt()&&(n=await navigator.mediaDevices.enumerateDevices()),n.filter(i=>i.kind===e&&i.label!==""&&i.deviceId!=="default")})()),Xm=()=>typeof document>"u"?!1:"setSinkId"in document.createElement("audio"),ko={audio:{autoGainControl:!0,noiseSuppression:!0,echoCancellation:!0}},Ro={video:{width:1280,height:720}},it=Je(()=>new Eo({constraints:ko,queryName:"microphone"})),_n=Je(()=>new Eo({constraints:Ro,queryName:"camera"})),$n=Je(()=>navigator.mediaDevices.addEventListener?_s(navigator.mediaDevices,"devicechange").pipe(Z(()=>{}),dd(500)):Ge([])),Zm=Je(()=>Ks($n(),it().asObservable()).pipe(jn(void 0),Fn(()=>si(it(),"audioinput")),re(1))),Qm=Je(()=>Ks($n(),_n().asObservable()).pipe(jn(void 0),Fn(()=>si(_n(),"videoinput")),re(1))),eg=Je(()=>Ks($n(),it().asObservable()).pipe(jn(void 0),Fn(()=>si(it(),"audiooutput")),re(1))),Io=async t=>{const e=await navigator.mediaDevices.getUserMedia(t);return xt()&&navigator.mediaDevices.dispatchEvent(new Event("devicechange")),e};function _o(t){return t&&typeof t=="object"&&("name"in t&&t.name==="OverconstrainedError"||"message"in t&&typeof t.message=="string"&&t.message.startsWith("OverconstrainedError"))}const Po=async t=>{const e={audio:{...ko.audio,...t}};try{return await it().prompt({throwOnNotAllowed:!0,forcePrompt:!0}),await Io(e)}catch(n){if(_o(n)&&(t!=null&&t.deviceId)){const{deviceId:s,...i}=t;return V(["devices"])("warn","Failed to get audio stream, will try again with relaxed constraints",{error:n,constraints:e,relaxedConstraints:i}),Po(i)}throw V(["devices"])("error","Failed to get audio stream",{error:n,constraints:e}),n}},Oo=async t=>{const e={video:{...Ro.video,...t}};try{return await _n().prompt({throwOnNotAllowed:!0,forcePrompt:!0}),await Io(e)}catch(n){if(_o(n)&&(t!=null&&t.deviceId)){const{deviceId:s,...i}=t;return V(["devices"])("warn","Failed to get video stream, will try again with relaxed constraints",{error:n,constraints:e,relaxedConstraints:i}),Oo(i)}throw V(["devices"])("error","Failed to get video stream",{error:n,constraints:e}),n}},tg=async t=>{try{return await navigator.mediaDevices.getDisplayMedia({video:!0,audio:{channelCount:{ideal:2},echoCancellation:!1,autoGainControl:!1,noiseSuppression:!1},systemAudio:"include",...t})}catch(e){throw V(["devices"])("error","Failed to get screen share stream",e),e}},Pn=typeof navigator<"u"&&typeof navigator.mediaDevices<"u"?$n().pipe(jn(void 0),Fn(()=>navigator.mediaDevices.enumerateDevices()),re(1)):void 0,ng=t=>{t.active&&(t.getTracks().forEach(e=>{e.stop()}),typeof t.release=="function"&&t.release())};class ii{constructor(e,n,s){this.call=e,this.state=n,this.trackType=s,this.stopOnLeave=!0,this.subscriptions=[],this.isTrackStoppedDueToTrackEnd=!1,this.filters=[],this.statusChangeConcurrencyTag=Symbol("statusChangeConcurrencyTag"),this.filterRegistrationConcurrencyTag=Symbol("filterRegistrationConcurrencyTag"),this.dispose=()=>{this.subscriptions.forEach(i=>i())},this.logger=V([`${v[s].toLowerCase()} manager`]),Pn&&!J()&&(this.trackType===v.AUDIO||this.trackType===v.VIDEO)&&this.handleDisconnectedOrReplacedDevices()}listDevices(){return this.getDevices()}get enabled(){return this.state.status==="enabled"}async enable(){this.state.prevStatus=this.state.optimisticStatus,this.state.optimisticStatus!=="enabled"&&(this.state.setPendingStatus("enabled"),await os(this.statusChangeConcurrencyTag,async e=>{try{await this.unmuteStream(),this.state.setStatus("enabled")}finally{e.aborted||this.state.setPendingStatus(this.state.status)}}))}async disable(e=!1){this.state.prevStatus=this.state.optimisticStatus,!(!e&&this.state.optimisticStatus==="disabled")&&(this.state.setPendingStatus("disabled"),await os(this.statusChangeConcurrencyTag,async n=>{try{const s=e||this.state.disableMode==="stop-tracks";await this.muteStream(s),this.state.setStatus("disabled")}finally{n.aborted||this.state.setPendingStatus(this.state.status)}}))}async statusChangeSettled(){await Jp(this.statusChangeConcurrencyTag)}async resume(){this.state.prevStatus==="enabled"&&this.state.status!=="enabled"&&await this.enable()}async toggle(){return this.state.optimisticStatus==="enabled"?await this.disable():await this.enable()}registerFilter(e){const n={start:e,stop:void 0};return{registered:te(this.filterRegistrationConcurrencyTag,async()=>{this.filters.push(n),await this.applySettingsToStream()}),unregister:()=>te(this.filterRegistrationConcurrencyTag,async()=>{var i;(i=n.stop)==null||i.call(n),this.filters=this.filters.filter(r=>r!==n),await this.applySettingsToStream()})}}setDefaultConstraints(e){this.state.setDefaultConstraints(e)}async select(e){if(J())throw new Error("This method is not supported in React Native. Please visit https://getstream.io/video/docs/reactnative/core/camera-and-microphone/#speaker-management for reference.");const n=this.state.selectedDevice;if(e!==n)try{this.state.setDevice(e),await this.applySettingsToStream()}catch(s){throw this.state.setDevice(n),s}}async applySettingsToStream(){await os(this.statusChangeConcurrencyTag,async()=>{this.enabled&&(await this.muteStream(),await this.unmuteStream())})}getTracks(){var e;return((e=this.state.mediaStream)==null?void 0:e.getTracks())??[]}async muteStream(e=!0){if(!this.state.mediaStream)return;this.logger("debug",`${e?"Stopping":"Disabling"} stream`),this.call.state.callingState===I.JOINED&&await this.stopPublishStream(e),this.muteLocalStream(e),this.getTracks().every(s=>s.readyState==="ended")&&(this.state.mediaStream&&typeof this.state.mediaStream.release=="function"&&this.state.mediaStream.release(),this.state.setMediaStream(void 0,void 0),this.filters.forEach(s=>{var i;return(i=s.stop)==null?void 0:i.call(s)}))}muteTracks(){this.getTracks().forEach(e=>{e.enabled&&(e.enabled=!1)})}unmuteTracks(){this.getTracks().forEach(e=>{e.enabled||(e.enabled=!0)})}stopTracks(){this.getTracks().forEach(e=>{e.readyState==="live"&&e.stop()})}muteLocalStream(e){this.state.mediaStream&&(e?this.stopTracks():this.muteTracks())}async unmuteStream(){this.logger("debug","Starting stream");let e,n;if(this.state.mediaStream&&this.getTracks().every(s=>s.readyState==="live"))e=this.state.mediaStream,this.unmuteTracks();else{const i={...this.state.defaultConstraints,deviceId:this.state.selectedDevice?{exact:this.state.selectedDevice}:void 0},r=a=>async o=>{if(!a)return o;const d=await a;return o.getTracks().forEach(c=>{const l=c.stop;c.stop=function(){l.call(c),d.getTracks().forEach(u=>{u.kind===c.kind&&u.stop()})}}),d.getTracks().forEach(c=>{const l=()=>{o.getTracks().forEach(h=>{c.kind===h.kind&&(h.stop(),h.dispatchEvent(new Event("ended")))})};c.addEventListener("ended",l),this.subscriptions.push(()=>{c.removeEventListener("ended",l)})}),o};n=this.getStream(i),e=await this.filters.reduce((a,o)=>a.then(d=>{const{stop:c,output:l}=o.start(d);return o.stop=c,l}).then(r(a),d=>(this.logger("warn","Filter failed to start and will be ignored",d),a)),n)}if(this.call.state.callingState===I.JOINED&&await this.publishStream(e),this.state.mediaStream!==e){this.state.setMediaStream(e,await n);const s=async()=>{await this.statusChangeSettled(),this.enabled&&(this.isTrackStoppedDueToTrackEnd=!0,setTimeout(()=>{this.isTrackStoppedDueToTrackEnd=!1},2e3),await this.disable())};this.getTracks().forEach(i=>{i.addEventListener("ended",s),this.subscriptions.push(()=>i.removeEventListener("ended",s))})}}get mediaDeviceKind(){return this.trackType===v.AUDIO?"audioinput":this.trackType===v.VIDEO?"videoinput":""}handleDisconnectedOrReplacedDevices(){this.subscriptions.push(ue(Be([Pn.pipe(fd()),this.state.selectedDevice$]),async([[e,n],s])=>{try{if(!s)return;await this.statusChangeSettled();let i=!1,r=!1;const a=this.findDeviceInList(n,s),o=this.findDeviceInList(e,s);!a&&o?i=!0:a&&o&&a.deviceId===o.deviceId&&a.groupId!==o.groupId&&(r=!0),i&&(await this.disable(),await this.select(void 0)),r&&(this.isTrackStoppedDueToTrackEnd&&this.state.status==="disabled"?(await this.enable(),this.isTrackStoppedDueToTrackEnd=!1):await this.applySettingsToStream())}catch(i){this.logger("warn","Unexpected error while handling disconnected or replaced device",i)}}))}findDeviceInList(e,n){return e.find(s=>s.deviceId===n&&s.kind===this.mediaDeviceKind)}}class ri{constructor(e="stop-tracks",n){this.disableMode=e,this.statusSubject=new A(void 0),this.optimisticStatusSubject=new A(void 0),this.mediaStreamSubject=new A(void 0),this.selectedDeviceSubject=new A(void 0),this.defaultConstraintsSubject=new A(void 0),this.mediaStream$=this.mediaStreamSubject.asObservable(),this.selectedDevice$=this.selectedDeviceSubject.asObservable().pipe(Q()),this.status$=this.statusSubject.asObservable().pipe(Q()),this.optimisticStatus$=this.optimisticStatusSubject.asObservable().pipe(Q()),this.defaultConstraints$=this.defaultConstraintsSubject.asObservable(),this.getCurrentValue=fe,this.setCurrentValue=Oe,this.hasBrowserPermission$=n?n.asObservable().pipe(re(1)):Ga(!0)}get status(){return this.getCurrentValue(this.status$)}get optimisticStatus(){return this.getCurrentValue(this.optimisticStatus$)}get selectedDevice(){return this.getCurrentValue(this.selectedDevice$)}get mediaStream(){return this.getCurrentValue(this.mediaStream$)}setStatus(e){this.setCurrentValue(this.statusSubject,e)}setPendingStatus(e){this.setCurrentValue(this.optimisticStatusSubject,e)}setMediaStream(e,n){this.setCurrentValue(this.mediaStreamSubject,e),n&&this.setDevice(this.getDeviceIdFromStream(n))}setDevice(e){this.setCurrentValue(this.selectedDeviceSubject,e)}get defaultConstraints(){return this.getCurrentValue(this.defaultConstraints$)}setDefaultConstraints(e){this.setCurrentValue(this.defaultConstraintsSubject,e)}}class sg extends ri{constructor(){super("stop-tracks",_n()),this.directionSubject=new A(void 0),this.direction$=this.directionSubject.asObservable().pipe(Q())}get direction(){return this.getCurrentValue(this.direction$)}setDirection(e){this.setCurrentValue(this.directionSubject,e)}setMediaStream(e,n){var s;if(super.setMediaStream(e,n),e){const i=J()?this.direction:((s=e.getVideoTracks()[0])==null?void 0:s.getSettings().facingMode)==="environment"?"back":"front";this.setDirection(i)}}getDeviceIdFromStream(e){const[n]=e.getVideoTracks();return n==null?void 0:n.getSettings().deviceId}}const ig=()=>/Mobi/i.test(navigator.userAgent);class rg extends ii{constructor(e){super(e,new sg,v.VIDEO),this.targetResolution={width:1280,height:720}}isDirectionSupportedByDevice(){return J()||ig()}async selectDirection(e){this.isDirectionSupportedByDevice()?(this.state.setDirection(e),this.state.setDevice(void 0),await this.applySettingsToStream()):this.logger("warn","Camera direction ignored for desktop devices")}async flip(){const e=this.state.direction==="front"?"back":"front";await this.selectDirection(e)}async selectTargetResolution(e){if(this.targetResolution.height=e.height,this.targetResolution.width=e.width,this.state.optimisticStatus==="enabled")try{await this.statusChangeSettled()}catch(n){this.logger("warn","could not apply target resolution",n)}if(this.enabled&&this.state.mediaStream){const[n]=this.state.mediaStream.getVideoTracks();if(!n)return;const{width:s,height:i}=n.getSettings();(s!==this.targetResolution.width||i!==this.targetResolution.height)&&(await this.applySettingsToStream(),this.logger("debug",`${s}x${i} target resolution applied to media stream`))}}setPreferredCodec(e){this.call.updatePublishOptions({preferredCodec:e})}getDevices(){return Qm()}getStream(e){return e.width=this.targetResolution.width,e.height=this.targetResolution.height,!e.deviceId&&this.state.direction&&this.isDirectionSupportedByDevice()&&(e.facingMode=this.state.direction==="front"?"user":"environment"),Oo(e)}publishStream(e){return this.call.publishVideoStream(e)}stopPublishStream(e){return this.call.stopPublish(v.VIDEO,e)}}class ag extends ri{constructor(e){super(e,it()),this.speakingWhileMutedSubject=new A(!1),this.speakingWhileMuted$=this.speakingWhileMutedSubject.asObservable().pipe(Q())}get speakingWhileMuted(){return this.getCurrentValue(this.speakingWhileMuted$)}setSpeakingWhileMuted(e){this.setCurrentValue(this.speakingWhileMutedSubject,e)}getDeviceIdFromStream(e){const[n]=e.getAudioTracks();return n==null?void 0:n.getSettings().deviceId}}const og=500,cg=150,ug=128,lg=(t,e,n={})=>{const{detectionFrequencyInMs:s=og,audioLevelThreshold:i=cg,fftSize:r=ug,destroyStreamOnStop:a=!0}=n,o=new AudioContext,d=o.createAnalyser();d.fftSize=r;const c=o.createMediaStreamSource(t);c.connect(d);const l=setInterval(()=>{var m;const h=new Uint8Array(d.frequencyBinCount);d.getByteFrequencyData(h);const u=h.some(g=>g>=i),f=h.reduce((g,b)=>g+b,0)/h.length,p=f>i?100:Math.round(f/i*100);(m=t.getAudioTracks()[0])!=null&&m.enabled?e({isSoundDetected:u,audioLevel:p}):e({isSoundDetected:!1,audioLevel:0})},s);return async function(){clearInterval(l),c.disconnect(),d.disconnect(),o.state!=="closed"&&await o.close(),a&&t.getTracks().forEach(u=>{u.stop(),t.removeTrack(u)})}},dg=.2;class hg{constructor(){this.pc1=new RTCPeerConnection({}),this.pc2=new RTCPeerConnection({})}async start(){try{this.cleanupAudioStream();const e=await navigator.mediaDevices.getUserMedia({audio:!0});this.audioStream=e,this.pc1.addEventListener("icecandidate",async r=>{await this.pc2.addIceCandidate(r.candidate)}),this.pc2.addEventListener("icecandidate",async r=>{await this.pc1.addIceCandidate(r.candidate)}),e.getTracks().forEach(r=>this.pc1.addTrack(r,e));const n=await this.pc1.createOffer({});await this.pc2.setRemoteDescription(n),await this.pc1.setLocalDescription(n);const s=await this.pc2.createAnswer();await this.pc1.setRemoteDescription(s),await this.pc2.setLocalDescription(s),e.getAudioTracks().forEach(r=>r.enabled=!1)}catch(e){console.error("Error connecting and negotiating between PeerConnections:",e)}}stop(){this.pc1.close(),this.pc2.close(),this.cleanupAudioStream(),this.intervalId&&clearInterval(this.intervalId)}onSpeakingDetectedStateChange(e){return this.intervalId=setInterval(async()=>{const n=await this.pc1.getStats(),i=In(n).find(r=>r.type==="media-source"&&r.kind==="audio");if(i){const{audioLevel:r}=i;r&&(r>=dg?e({isSoundDetected:!0,audioLevel:r}):e({isSoundDetected:!1,audioLevel:0}))}},1e3),()=>{clearInterval(this.intervalId)}}cleanupAudioStream(){this.audioStream&&(this.audioStream.getTracks().forEach(e=>e.stop()),typeof this.audioStream.release=="function"&&this.audioStream.release())}}class fg extends ii{constructor(e,n=J()?"disable-tracks":"stop-tracks"){super(e,new ag(n),v.AUDIO),this.speakingWhileMutedNotificationEnabled=!0,this.soundDetectorConcurrencyTag=Symbol("soundDetectorConcurrencyTag"),this.subscriptions.push(ue(Be([this.call.state.callingState$,this.call.state.ownCapabilities$,this.state.selectedDevice$,this.state.status$]),async([s,i,r,a])=>{try{if(s===I.LEFT&&await this.stopSpeakingWhileMutedDetection(),s!==I.JOINED||!this.speakingWhileMutedNotificationEnabled)return;i.includes(z.SEND_AUDIO)?a==="disabled"?await this.startSpeakingWhileMutedDetection(r):await this.stopSpeakingWhileMutedDetection():await this.stopSpeakingWhileMutedDetection()}catch(o){this.logger("warn","Could not enable speaking while muted",o)}})),this.subscriptions.push(ue(this.call.state.callingState$,s=>{var r,a;if(!this.noiseCancellationRegistration||!this.noiseCancellation)return;((a=(r=this.call.state.settings)==null?void 0:r.audio.noise_cancellation)==null?void 0:a.mode)===is.AUTO_ON&&s===I.JOINED?this.noiseCancellationRegistration.then(()=>{var o;return(o=this.noiseCancellation)==null?void 0:o.enable()}).catch(o=>(this.logger("warn","Failed to enable noise cancellation",o),this.call.notifyNoiseCancellationStopped())):s===I.LEFT&&this.noiseCancellationRegistration.then(()=>{var o;return(o=this.noiseCancellation)==null?void 0:o.disable()}).catch(o=>{this.logger("warn","Failed to disable noise cancellation",o)})}))}async enableNoiseCancellation(e){if(J())throw new Error("Noise cancellation is not supported in React Native");const{ownCapabilities:n,settings:s}=this.call.state;if(!n.includes(z.ENABLE_NOISE_CANCELLATION))throw new Error("Noise cancellation is not available.");const r=s==null?void 0:s.audio.noise_cancellation;if(!r||r.mode===is.DISABLED)throw new Error("Noise cancellation is disabled for this call type.");try{this.noiseCancellation=e,this.noiseCancellationChangeUnsubscribe=this.noiseCancellation.on("change",o=>{o?this.call.notifyNoiseCancellationStarting().catch(d=>{this.logger("warn","notifyNoiseCancellationStart failed",d)}):this.call.notifyNoiseCancellationStopped().catch(d=>{this.logger("warn","notifyNoiseCancellationStop failed",d)})});const a=this.registerFilter(e.toFilter());this.noiseCancellationRegistration=a.registered,this.unregisterNoiseCancellation=a.unregister,await this.noiseCancellationRegistration,r.mode===is.AUTO_ON&&this.call.state.callingState===I.JOINED&&e.enable()}catch(a){this.logger("warn","Failed to enable noise cancellation",a),await this.disableNoiseCancellation().catch(o=>{this.logger("warn","Failed to disable noise cancellation",o)})}}async disableNoiseCancellation(){var e;if(J())throw new Error("Noise cancellation is not supported in React Native");await(((e=this.unregisterNoiseCancellation)==null?void 0:e.call(this))??Promise.resolve()).then(()=>{var n;return(n=this.noiseCancellation)==null?void 0:n.disable()}).then(()=>{var n;return(n=this.noiseCancellationChangeUnsubscribe)==null?void 0:n.call(this)}).catch(n=>{this.logger("warn","Failed to unregister noise cancellation",n)}),await this.call.notifyNoiseCancellationStopped()}async enableSpeakingWhileMutedNotification(){this.speakingWhileMutedNotificationEnabled=!0,this.state.status==="disabled"&&await this.startSpeakingWhileMutedDetection(this.state.selectedDevice)}async disableSpeakingWhileMutedNotification(){this.speakingWhileMutedNotificationEnabled=!1,await this.stopSpeakingWhileMutedDetection()}getDevices(){return Zm()}getStream(e){return Po(e)}publishStream(e){return this.call.publishAudioStream(e)}stopPublishStream(e){return this.call.stopPublish(v.AUDIO,e)}async startSpeakingWhileMutedDetection(e){await te(this.soundDetectorConcurrencyTag,async()=>{var n;if(await this.stopSpeakingWhileMutedDetection(),J()){this.rnSpeechDetector=new hg,await this.rnSpeechDetector.start();const s=(n=this.rnSpeechDetector)==null?void 0:n.onSpeakingDetectedStateChange(i=>{this.state.setSpeakingWhileMuted(i.isSoundDetected)});this.soundDetectorCleanup=()=>{var i;s(),(i=this.rnSpeechDetector)==null||i.stop(),this.rnSpeechDetector=void 0}}else{const s=await this.getStream({deviceId:e});this.soundDetectorCleanup=lg(s,i=>{this.state.setSpeakingWhileMuted(i.isSoundDetected)})}})}async stopSpeakingWhileMutedDetection(){await te(this.soundDetectorConcurrencyTag,async()=>{if(!this.soundDetectorCleanup)return;const e=this.soundDetectorCleanup;this.soundDetectorCleanup=void 0,this.state.setSpeakingWhileMuted(!1),await e()})}}class pg extends ri{constructor(){super(...arguments),this.audioEnabledSubject=new A(!0),this.settingsSubject=new A(void 0),this.audioEnabled$=this.audioEnabledSubject.asObservable().pipe(Q()),this.settings$=this.settingsSubject.asObservable(),this.getDeviceIdFromStream=e=>{const[n]=e.getTracks();return n==null?void 0:n.getSettings().deviceId}}get audioEnabled(){return this.getCurrentValue(this.audioEnabled$)}setAudioEnabled(e){this.setCurrentValue(this.audioEnabledSubject,e)}get settings(){return this.getCurrentValue(this.settings$)}setSettings(e){this.setCurrentValue(this.settingsSubject,e)}}class mg extends ii{constructor(e){super(e,new pg,v.SCREEN_SHARE),this.subscriptions.push(ue(e.state.settings$,n=>{const s=n==null?void 0:n.screensharing.target_resolution;s&&this.setDefaultConstraints({video:{width:s.width,height:s.height}})}))}enableScreenShareAudio(){this.state.setAudioEnabled(!0)}async disableScreenShareAudio(){var e;this.state.setAudioEnabled(!1),(e=this.call.publisher)!=null&&e.isPublishing(v.SCREEN_SHARE_AUDIO)&&await this.call.stopPublish(v.SCREEN_SHARE_AUDIO,!0)}getSettings(){return this.state.settings}setSettings(e){this.state.setSettings(e)}getDevices(){return Ga([])}getStream(e){return this.state.audioEnabled||(e.audio=!1),tg(e)}publishStream(e){return this.call.publishScreenShareStream(e)}async stopPublishStream(e){await this.call.stopPublish(v.SCREEN_SHARE,e),await this.call.stopPublish(v.SCREEN_SHARE_AUDIO,e)}async select(e){throw new Error("This method is not supported in for Screen Share")}}class gg{constructor(){this.selectedDeviceSubject=new A(""),this.volumeSubject=new A(1),this.isDeviceSelectionSupported=Xm(),this.getCurrentValue=fe,this.setCurrentValue=Oe,this.selectedDevice$=this.selectedDeviceSubject.asObservable().pipe(Q()),this.volume$=this.volumeSubject.asObservable().pipe(Q())}get selectedDevice(){return this.getCurrentValue(this.selectedDevice$)}get volume(){return this.getCurrentValue(this.volume$)}setDevice(e){this.setCurrentValue(this.selectedDeviceSubject,e)}setVolume(e){this.setCurrentValue(this.volumeSubject,e)}}class bg{constructor(e){this.state=new gg,this.subscriptions=[],this.dispose=()=>{this.subscriptions.forEach(n=>n.unsubscribe())},this.call=e,Pn&&!J()&&this.subscriptions.push(Be([Pn,this.state.selectedDevice$]).subscribe(([n,s])=>{if(!s)return;n.find(r=>r.deviceId===s&&r.kind==="audiooutput")||this.select("")}))}listDevices(){if(J())throw new Error("This feature is not supported in React Native. Please visit https://getstream.io/video/docs/reactnative/core/camera-and-microphone/#speaker-management for more details");return eg()}select(e){if(J())throw new Error("This feature is not supported in React Native. Please visit https://getstream.io/video/docs/reactnative/core/camera-and-microphone/#speaker-management for more details");this.state.setDevice(e)}setVolume(e){if(J())throw new Error("This feature is not supported in React Native. Please visit https://getstream.io/video/docs/reactnative/core/camera-and-microphone/#speaker-management for more details");if(e&&(e<0||e>1))throw new Error("Volume must be between 0 and 1");this.state.setVolume(e)}setParticipantVolume(e,n){if(J())throw new Error("This feature is not supported in React Native. Please visit https://getstream.io/video/docs/reactnative/core/camera-and-microphone/#speaker-management for more details");if(n&&(n<0||n>1))throw new Error("Volume must be between 0 and 1, or undefined");this.call.state.updateParticipant(e,{audioVolume:n})}}class yt{constructor({type:e,id:n,streamClient:s,members:i,ownCapabilities:r,sortParticipantsBy:a,clientStore:o,ringing:d=!1,watching:c=!1}){this.state=new am,this.permissionsContext=new Jm,this.dispatcher=new Np,this.sfuClientTag=0,this.reconnectConcurrencyTag=Symbol("reconnectConcurrencyTag"),this.reconnectAttempts=0,this.reconnectStrategy=D.UNSPECIFIED,this.fastReconnectDeadlineSeconds=0,this.disconnectionTimeoutSeconds=0,this.lastOfflineTimestamp=0,this.trackPublishOrder=[],this.hasJoinedOnce=!1,this.deviceSettingsAppliedOnce=!1,this.initialized=!1,this.joinLeaveConcurrencyTag=Symbol("joinLeaveConcurrencyTag"),this.leaveCallHooks=new Set,this.streamClientEventHandlers=new Map,this.handleOwnCapabilitiesUpdated=async u=>{if(this.permissionsContext.setPermissions(u),!this.publisher)return;const f={[z.SEND_AUDIO]:v.AUDIO,[z.SEND_VIDEO]:v.VIDEO,[z.SCREENSHARE]:v.SCREEN_SHARE};for(const[p,m]of Object.entries(f))if(!this.permissionsContext.hasPermission(p))try{switch(m){case v.AUDIO:this.microphone.enabled&&await this.microphone.disable();break;case v.VIDEO:this.camera.enabled&&await this.camera.disable();break;case v.SCREEN_SHARE:this.screenShare.enabled&&await this.screenShare.disable();break}}catch(b){this.logger("error","Can't disable mic/camera/screenshare after revoked permissions",b)}},this.on=(u,f)=>{if(ur(u))return this.dispatcher.on(u,f);const p=this.streamClient.on(u,m=>{const g=m;g.call_cid&&g.call_cid===this.cid&&f(g)});return this.streamClientEventHandlers.set(f,p),()=>{this.off(u,f)}},this.off=(u,f)=>{if(ur(u))return this.dispatcher.off(u,f);const p=this.streamClientEventHandlers.get(f);p&&p()},this.leave=async({reject:u,reason:f="user is leaving the call"}={})=>{await te(this.joinLeaveConcurrencyTag,async()=>{var g,b,E,C,w;const p=this.state.callingState;if(p===I.LEFT)throw new Error("Cannot leave call that has already been left.");if(p===I.JOINING&&await new Promise(R=>{this.state.callingState$.pipe(Ps(x=>x!==I.JOINED,!0)).subscribe(()=>R())}),p===I.RINGING&&u!==!1)if(u)await this.reject(f);else{const k=this.state.remoteParticipants.length>0;this.isCreatedByMe&&!k&&await this.reject("cancel")}(g=this.statsReporter)==null||g.stop(),this.statsReporter=void 0,(b=this.sfuStatsReporter)==null||b.stop(),this.sfuStatsReporter=void 0,(E=this.subscriber)==null||E.close(),this.subscriber=void 0,(C=this.publisher)==null||C.close({stopTracks:!0}),this.publisher=void 0,await((w=this.sfuClient)==null?void 0:w.leaveAndClose(f)),this.sfuClient=void 0,this.dynascaleManager.setSfuClient(void 0),this.state.setCallingState(I.LEFT),this.state.dispose(),this.leaveCallHooks.forEach(k=>k()),this.initialized=!1,this.hasJoinedOnce=!1,this.clientStore.unregisterCall(this),this.camera.dispose(),this.microphone.dispose(),this.screenShare.dispose(),this.speaker.dispose();const m=[];this.camera.stopOnLeave&&m.push(this.camera.disable(!0)),this.microphone.stopOnLeave&&m.push(this.microphone.disable(!0)),this.screenShare.stopOnLeave&&m.push(this.screenShare.disable(!0)),await Promise.all(m)})},this.updateFromRingingEvent=async u=>{await this.setup();const f=this.state.members.find(m=>m.user.id===u.call.created_by.id);f?this.state.setMembers([f,...u.members]):this.state.setMembers(u.members),this.state.updateFromCallResponse(u.call),this.watching=!0,this.ringingSubject.next(!0);const p=this.clientStore.calls.filter(m=>m.cid!==this.cid);this.clientStore.setCalls([this,...p]),await this.applyDeviceConfig(!1)},this.get=async u=>{await this.setup();const f=await this.streamClient.get(this.streamClientBasePath,u);return this.state.updateFromCallResponse(f.call),this.state.setMembers(f.members),this.state.setOwnCapabilities(f.own_capabilities),(u!=null&&u.ring||this.ringing)&&this.ringingSubject.next(!0),this.streamClient._hasConnectionID()&&(this.watching=!0,this.clientStore.registerCall(this)),await this.applyDeviceConfig(!1),f},this.getOrCreate=async u=>{await this.setup();const f=await this.streamClient.post(this.streamClientBasePath,u);return this.state.updateFromCallResponse(f.call),this.state.setMembers(f.members),this.state.setOwnCapabilities(f.own_capabilities),(u!=null&&u.ring||this.ringing)&&this.ringingSubject.next(!0),this.streamClient._hasConnectionID()&&(this.watching=!0,this.clientStore.registerCall(this)),await this.applyDeviceConfig(!1),f},this.create=async u=>this.getOrCreate(u),this.ring=async()=>await this.get({ring:!0}),this.notify=async()=>await this.get({notify:!0}),this.accept=async()=>this.streamClient.post(`${this.streamClientBasePath}/accept`),this.reject=async u=>this.streamClient.post(`${this.streamClientBasePath}/reject`,{reason:u}),this.join=async u=>{var H,se,me,ut;const f=Date.now();await this.setup();const p=this.state.callingState;if([I.JOINED,I.JOINING].includes(p))throw new Error("Illegal State: call.join() shall be called only once");this.joinCallData=u,this.logger("debug","Starting join flow"),this.state.setCallingState(I.JOINING);const m=this.reconnectStrategy===D.MIGRATE,g=this.reconnectStrategy===D.REJOIN,b=this.reconnectStrategy===D.FAST;let E=(H=this.sfuStatsReporter)==null?void 0:H.options;if(!this.credentials||!E||g||m)try{const oe=await this.doJoinRequest(u);this.credentials=oe.credentials,E=oe.stats_options}catch(oe){throw this.state.setCallingState(p),oe}const C=this.sfuClient,w=C==null?void 0:C.sessionId,k=!!(C!=null&&C.isHealthy),R=g||m||!k?new ve({logTag:String(this.sfuClientTag++),dispatcher:this.dispatcher,credentials:this.credentials,streamClient:this.streamClient,sessionId:g?void 0:w,onSignalClose:()=>this.handleSfuSignalClose(R)}):C;this.sfuClient=R,this.dynascaleManager.setSfuClient(R);const x=ar();if(C!==R){const oe=await Ap("recvonly"),Ne=this.reconnectStrategy!==D.UNSPECIFIED?this.getReconnectDetails(u==null?void 0:u.migrating_from,w):void 0,{callState:xe,fastReconnectDeadlineSeconds:Bn}=await R.join({subscriberSdp:oe,publisherSdp:"",clientDetails:x,fastReconnect:b,reconnectDetails:Ne});this.fastReconnectDeadlineSeconds=Bn,xe&&this.state.updateFromSfuCallState(xe,R.sessionId,Ne)}if(m||this.state.setCallingState(I.JOINED),this.hasJoinedOnce=!0,b)await this.restoreICE(R,{includeSubscriber:!1});else{const oe=Cm(this.credentials.ice_servers);this.initPublisherAndSubscriber({sfuClient:R,connectionConfig:oe,clientDetails:x,statsOptions:E,closePreviousInstances:!m})}if(!g&&!b&&!m&&((se=this.sfuStatsReporter)==null||se.sendTelemetryData({data:{oneofKind:"connectionTimeSeconds",connectionTimeSeconds:(Date.now()-f)/1e3}})),g){const oe=D[this.reconnectStrategy];await(C==null?void 0:C.leaveAndClose(`Closing previous WS after reconnect with strategy: ${oe}`))}else k||C==null||C.close(ve.DISPOSE_OLD_SOCKET,"Closing unhealthy WS after reconnect");this.deviceSettingsAppliedOnce||(await this.applyDeviceConfig(!0),this.deviceSettingsAppliedOnce=!0),(me=this.joinCallData)==null||delete me.ring,(ut=this.joinCallData)==null||delete ut.notify,this.logger("info",`Joined call ${this.cid}`)},this.getReconnectDetails=(u,f)=>{var b;const p=this.reconnectStrategy,m=p===D.REJOIN,g=((b=this.publisher)==null?void 0:b.getAnnouncedTracks())||[];return{strategy:p,announcedTracks:g,subscriptions:this.dynascaleManager.trackSubscriptions,reconnectAttempt:this.reconnectAttempts,fromSfuId:u||"",previousSessionId:m&&f||""}},this.restoreICE=async(u,f={})=>{const{includeSubscriber:p=!0,includePublisher:m=!0}=f;this.subscriber&&(this.subscriber.setSfuClient(u),p&&await this.subscriber.restartIce()),this.publisher&&(this.publisher.setSfuClient(u),m&&await this.publisher.restartIce())},this.initPublisherAndSubscriber=u=>{var C,w,k,R;const{sfuClient:f,connectionConfig:p,clientDetails:m,statsOptions:g,closePreviousInstances:b}=u;if(b&&this.subscriber&&this.subscriber.close(),this.subscriber=new gm({sfuClient:f,dispatcher:this.dispatcher,state:this.state,connectionConfig:p,logTag:String(this.sfuClientTag),onUnrecoverableError:()=>{this.reconnect(D.REJOIN).catch(x=>{this.logger("warn","[Reconnect] Error reconnecting after a subscriber error",x)})}}),!(((C=this.streamClient.user)==null?void 0:C.type)==="anonymous")){b&&this.publisher&&this.publisher.close({stopTracks:!1});const x=(w=this.state.settings)==null?void 0:w.audio,H=!!(x!=null&&x.opus_dtx_enabled),se=!!(x!=null&&x.redundant_coding_enabled);this.publisher=new mm({sfuClient:f,dispatcher:this.dispatcher,state:this.state,connectionConfig:p,isDtxEnabled:H,isRedEnabled:se,logTag:String(this.sfuClientTag),onUnrecoverableError:()=>{this.reconnect(D.REJOIN).catch(me=>{this.logger("warn","[Reconnect] Error reconnecting after a publisher error",me)})}})}(k=this.statsReporter)==null||k.stop(),this.statsReporter=Bm({subscriber:this.subscriber,publisher:this.publisher,state:this.state,datacenter:f.edgeName}),(R=this.sfuStatsReporter)==null||R.stop(),(g==null?void 0:g.reporting_interval_ms)>0&&(this.sfuStatsReporter=new qm(f,{clientDetails:m,options:g,subscriber:this.subscriber,publisher:this.publisher,microphone:this.microphone,camera:this.camera,state:this.state}),this.sfuStatsReporter.start())},this.doJoinRequest=async u=>{const f=await this.streamClient.getLocationHint(),p={...u,location:f},m=await this.streamClient.post(`${this.streamClientBasePath}/join`,p);return this.state.updateFromCallResponse(m.call),this.state.setMembers(m.members),this.state.setOwnCapabilities(m.own_capabilities),u!=null&&u.ring&&!this.ringing&&this.ringingSubject.next(!0),this.ringing&&!this.isCreatedByMe&&await this.accept(),this.streamClient._hasConnectionID()&&(this.watching=!0,this.clientStore.registerCall(this)),m},this.handleSfuSignalClose=u=>{this.logger("debug","[Reconnect] SFU signal connection closed"),this.state.callingState!==I.JOINING&&(u.isLeaving||this.reconnect(D.REJOIN).catch(f=>{this.logger("warn","[Reconnect] Error reconnecting",f)}))},this.reconnect=async u=>{if(!(this.state.callingState===I.RECONNECTING||this.state.callingState===I.RECONNECTING_FAILED))return te(this.reconnectConcurrencyTag,async()=>{var p;this.logger("info",`[Reconnect] Reconnecting with strategy ${D[u]}`);let f=Date.now();this.reconnectStrategy=u;do{if(this.disconnectionTimeoutSeconds>0&&(Date.now()-f)/1e3>this.disconnectionTimeoutSeconds){this.logger("warn","[Reconnect] Stopping reconnection attempts after reaching disconnection timeout"),this.state.setCallingState(I.RECONNECTING_FAILED);return}this.reconnectStrategy!==D.FAST&&this.reconnectAttempts++;const m=D[this.reconnectStrategy];try{switch(await((p=this.networkAvailableTask)==null?void 0:p.promise),this.reconnectStrategy){case D.UNSPECIFIED:case D.DISCONNECT:this.logger("debug",`[Reconnect] No-op strategy ${m}`);break;case D.FAST:await this.reconnectFast();break;case D.REJOIN:await this.reconnectRejoin();break;case D.MIGRATE:await this.reconnectMigrate();break;default:wn(this.reconnectStrategy,"Unknown reconnection strategy");break}break}catch(g){if(g instanceof Za&&g.unrecoverable){this.logger("warn","[Reconnect] Can't reconnect due to coordinator unrecoverable error",g),this.state.setCallingState(I.RECONNECTING_FAILED);return}this.logger("warn",`[Reconnect] ${m} (${this.reconnectAttempts}) failed. Attempting with REJOIN`,g),await tt(500),this.reconnectStrategy=D.REJOIN}}while(this.state.callingState!==I.JOINED&&this.state.callingState!==I.RECONNECTING_FAILED&&this.state.callingState!==I.LEFT)})},this.reconnectFast=async()=>{var f;let u=Date.now();this.reconnectStrategy=D.FAST,this.state.setCallingState(I.RECONNECTING),await this.join(this.joinCallData),(f=this.sfuStatsReporter)==null||f.sendTelemetryData({data:{oneofKind:"reconnection",reconnection:{timeSeconds:(Date.now()-u)/1e3,strategy:D.FAST}}})},this.reconnectRejoin=async()=>{var f;let u=Date.now();this.reconnectStrategy=D.REJOIN,this.state.setCallingState(I.RECONNECTING),await this.join(this.joinCallData),await this.restorePublishedTracks(),this.restoreSubscribedTracks(),(f=this.sfuStatsReporter)==null||f.sendTelemetryData({data:{oneofKind:"reconnection",reconnection:{timeSeconds:(Date.now()-u)/1e3,strategy:D.REJOIN}}})},this.reconnectMigrate=async()=>{var b,E;let u=Date.now();const f=this.sfuClient;if(!f)throw new Error("Cannot migrate without an active SFU client");this.reconnectStrategy=D.MIGRATE,this.state.setCallingState(I.MIGRATING);const p=this.subscriber,m=this.publisher;p==null||p.detachEventHandlers(),m==null||m.detachEventHandlers();const g=_t(f.enterMigration());try{const C=f.edgeName;await this.join({...this.joinCallData,migrating_from:C})}finally{(b=this.joinCallData)==null||delete b.migrating_from}await this.restorePublishedTracks(),this.restoreSubscribedTracks();try{await g(),this.state.setCallingState(I.JOINED)}finally{p==null||p.close(),m==null||m.close({stopTracks:!1}),f.close()}(E=this.sfuStatsReporter)==null||E.sendTelemetryData({data:{oneofKind:"reconnection",reconnection:{timeSeconds:(Date.now()-u)/1e3,strategy:D.MIGRATE}}})},this.registerReconnectHandlers=()=>{const u=this.on("goAway",()=>{this.reconnect(D.MIGRATE).catch(m=>{this.logger("warn","[Reconnect] Error reconnecting",m)})}),f=this.on("error",m=>{const{reconnectStrategy:g}=m;g!==D.UNSPECIFIED&&(g===D.DISCONNECT?this.leave({reason:"SFU instructed to disconnect"}).catch(b=>{this.logger("warn","Can't leave call after disconnect request",b)}):this.reconnect(g).catch(b=>{this.logger("warn","[Reconnect] Error reconnecting",b)}))}),p=this.streamClient.on("network.changed",m=>{var g,b,E,C;if(m.online)this.logger("debug","[Reconnect] Going online"),(b=this.sfuClient)==null||b.close(ve.DISPOSE_OLD_SOCKET,"Closing WS to reconnect after going online"),(E=this.networkAvailableTask)==null||E.resolve(),this.networkAvailableTask=void 0,(C=this.sfuStatsReporter)==null||C.start();else{if(this.logger("debug","[Reconnect] Going offline"),!this.hasJoinedOnce)return;this.lastOfflineTimestamp=Date.now();const w=St();w.promise.then(()=>{let k=D.FAST;this.lastOfflineTimestamp&&(Date.now()-this.lastOfflineTimestamp)/1e3>this.fastReconnectDeadlineSeconds&&(k=D.REJOIN),this.reconnect(k).catch(R=>{this.logger("warn","[Reconnect] Error reconnecting after going online",R)})}),this.networkAvailableTask=w,(g=this.sfuStatsReporter)==null||g.stop(),this.state.setCallingState(I.OFFLINE)}});this.leaveCallHooks.add(u),this.leaveCallHooks.add(f),this.leaveCallHooks.add(p)},this.restorePublishedTracks=async()=>{for(const u of this.trackPublishOrder)switch(u){case v.AUDIO:const f=this.microphone.state.mediaStream;f&&await this.publishAudioStream(f);break;case v.VIDEO:const p=this.camera.state.mediaStream;p&&await this.publishVideoStream(p);break;case v.SCREEN_SHARE:const m=this.screenShare.state.mediaStream;m&&await this.publishScreenShareStream(m);break;case v.SCREEN_SHARE_AUDIO:case v.UNSPECIFIED:break;default:wn(u,"Unknown track type");break}},this.restoreSubscribedTracks=()=>{const{remoteParticipants:u}=this.state;u.length<=0||this.dynascaleManager.applyTrackSubscriptions(void 0)},this.publishVideoStream=async u=>{if(!this.sfuClient)throw new Error("Call not joined yet.");if(await this.sfuClient.joinTask,!this.permissionsContext.hasPermission(z.SEND_VIDEO))throw new Error("No permission to publish video");if(!this.publisher)throw new Error("Publisher is not initialized");const[f]=u.getVideoTracks();if(!f)throw new Error("There is no video track in the stream");this.trackPublishOrder.includes(v.VIDEO)||this.trackPublishOrder.push(v.VIDEO),await this.publisher.publishStream(u,f,v.VIDEO,this.publishOptions)},this.publishAudioStream=async u=>{if(!this.sfuClient)throw new Error("Call not joined yet.");if(await this.sfuClient.joinTask,!this.permissionsContext.hasPermission(z.SEND_AUDIO))throw new Error("No permission to publish audio");if(!this.publisher)throw new Error("Publisher is not initialized");const[f]=u.getAudioTracks();if(!f)throw new Error("There is no audio track in the stream");this.trackPublishOrder.includes(v.AUDIO)||this.trackPublishOrder.push(v.AUDIO),await this.publisher.publishStream(u,f,v.AUDIO)},this.publishScreenShareStream=async u=>{if(!this.sfuClient)throw new Error("Call not joined yet.");if(await this.sfuClient.joinTask,!this.permissionsContext.hasPermission(z.SCREENSHARE))throw new Error("No permission to publish screen share");if(!this.publisher)throw new Error("Publisher is not initialized");const[f]=u.getVideoTracks();if(!f)throw new Error("There is no screen share track in the stream");this.trackPublishOrder.includes(v.SCREEN_SHARE)||this.trackPublishOrder.push(v.SCREEN_SHARE);const p={screenShareSettings:this.screenShare.getSettings()};await this.publisher.publishStream(u,f,v.SCREEN_SHARE,p);const[m]=u.getAudioTracks();m&&(this.trackPublishOrder.includes(v.SCREEN_SHARE_AUDIO)||this.trackPublishOrder.push(v.SCREEN_SHARE_AUDIO),await this.publisher.publishStream(u,m,v.SCREEN_SHARE_AUDIO,p))},this.stopPublish=async(u,f=!0)=>{var p;this.logger("info",`stopPublish ${v[u]}, stop tracks: ${f}`),await((p=this.publisher)==null?void 0:p.unpublishStream(u,f))},this.notifyNoiseCancellationStarting=async()=>{var u;return(u=this.sfuClient)==null?void 0:u.startNoiseCancellation().catch(f=>{this.logger("warn","Failed to notify start of noise cancellation",f)})},this.notifyNoiseCancellationStopped=async()=>{var u;return(u=this.sfuClient)==null?void 0:u.stopNoiseCancellation().catch(f=>{this.logger("warn","Failed to notify stop of noise cancellation",f)})},this.startReportingStatsFor=u=>{var f;return(f=this.statsReporter)==null?void 0:f.startReportingStatsFor(u)},this.stopReportingStatsFor=u=>{var f;return(f=this.statsReporter)==null?void 0:f.stopReportingStatsFor(u)},this.resetReaction=u=>{this.state.updateParticipant(u,{reaction:void 0})},this.setSortParticipantsBy=u=>this.state.setSortParticipantsBy(u),this.sendReaction=async u=>this.streamClient.post(`${this.streamClientBasePath}/reaction`,u),this.blockUser=async u=>this.streamClient.post(`${this.streamClientBasePath}/block`,{user_id:u}),this.unblockUser=async u=>this.streamClient.post(`${this.streamClientBasePath}/unblock`,{user_id:u}),this.muteSelf=u=>{const f=this.currentUserId;if(f)return this.muteUser(f,u)},this.muteOthers=u=>{const f=Gp(u);if(!f)return;const p=[];for(const m of this.state.remoteParticipants)m.publishedTracks.includes(f)&&p.push(m.userId);if(p.length>0)return this.muteUser(p,u)},this.muteUser=(u,f)=>this.streamClient.post(`${this.streamClientBasePath}/mute_users`,{user_ids:Array.isArray(u)?u:[u],[f]:!0}),this.muteAllUsers=u=>this.streamClient.post(`${this.streamClientBasePath}/mute_users`,{mute_all_users:!0,[u]:!0}),this.startRecording=async u=>this.streamClient.post(`${this.streamClientBasePath}/start_recording`,u||{}),this.stopRecording=async()=>this.streamClient.post(`${this.streamClientBasePath}/stop_recording`,{}),this.startTranscription=async u=>this.streamClient.post(`${this.streamClientBasePath}/start_transcription`,u),this.stopTranscription=async()=>this.streamClient.post(`${this.streamClientBasePath}/stop_transcription`),this.startClosedCaptions=async u=>{const f=this.state.setCaptioning(!0);try{return await this.streamClient.post(`${this.streamClientBasePath}/start_closed_captions`,u)}catch(p){throw f.rollback(),p}},this.stopClosedCaptions=async u=>{const f=this.state.setCaptioning(!1);try{return await this.streamClient.post(`${this.streamClientBasePath}/stop_closed_captions`,u)}catch(p){throw f.rollback(),p}},this.updateClosedCaptionSettings=u=>{this.state.updateClosedCaptionSettings(u)},this.requestPermissions=async u=>{const{permissions:f}=u;if(!f.every(m=>this.permissionsContext.canRequest(m)))throw new Error(`You are not allowed to request permissions: ${f.join(", ")}`);return this.streamClient.post(`${this.streamClientBasePath}/request_permission`,u)},this.grantPermissions=async(u,f)=>this.updateUserPermissions({user_id:u,grant_permissions:f}),this.revokePermissions=async(u,f)=>this.updateUserPermissions({user_id:u,revoke_permissions:f}),this.updateUserPermissions=async u=>this.streamClient.post(`${this.streamClientBasePath}/user_permissions`,u),this.goLive=async(u={},f)=>this.streamClient.post(`${this.streamClientBasePath}/go_live`,u,f),this.stopLive=async()=>this.streamClient.post(`${this.streamClientBasePath}/stop_live`,{}),this.startHLS=async()=>this.streamClient.post(`${this.streamClientBasePath}/start_broadcasting`,{}),this.stopHLS=async()=>this.streamClient.post(`${this.streamClientBasePath}/stop_broadcasting`,{}),this.update=async u=>{const f=await this.streamClient.patch(`${this.streamClientBasePath}`,u),{call:p,members:m,own_capabilities:g}=f;return this.state.updateFromCallResponse(p),this.state.setMembers(m),this.state.setOwnCapabilities(g),f},this.endCall=async()=>this.streamClient.post(`${this.streamClientBasePath}/mark_ended`),this.pin=u=>{this.state.updateParticipant(u,{pin:{isLocalPin:!0,pinnedAt:Date.now()}})},this.unpin=u=>{this.state.updateParticipant(u,{pin:void 0})},this.pinForEveryone=async u=>this.streamClient.post(`${this.streamClientBasePath}/pin`,u),this.unpinForEveryone=async u=>this.streamClient.post(`${this.streamClientBasePath}/unpin`,u),this.queryMembers=u=>this.streamClient.post("/call/members",{...u||{},id:this.id,type:this.type}),this.updateCallMembers=async u=>this.streamClient.post(`${this.streamClientBasePath}/members`,u),this.scheduleAutoDrop=()=>{this.cancelAutoDrop();const u=this.state.settings;if(!u||this.state.callingState!==I.RINGING)return;const f=this.isCreatedByMe?u.ring.auto_cancel_timeout_ms:u.ring.incoming_call_timeout_ms;f<=0||(this.dropTimeout=setTimeout(()=>{this.state.callingState===I.RINGING&&this.leave({reject:!0,reason:"timeout"}).catch(p=>{this.logger("error","Failed to drop call",p)})},f))},this.cancelAutoDrop=()=>{clearTimeout(this.dropTimeout),this.dropTimeout=void 0},this.queryRecordings=async u=>{let f=this.streamClientBasePath;return u&&(f=`${f}/${u}`),this.streamClient.get(`${f}/recordings`)},this.queryTranscriptions=async()=>this.streamClient.get(`${this.streamClientBasePath}/transcriptions`),this.getCallStats=async u=>{const f=`${this.streamClientBasePath}/stats/${u}`;return this.streamClient.get(f)},this.submitFeedback=async(u,{reason:f,custom:p}={})=>{var k,R;if(u<1||u>5)throw new Error("Rating must be between 1 and 5");const m=(k=this.state.session)==null?void 0:k.id;if(!m)throw new Error("Feedback can be submitted only in the context of a call session");const{sdkName:g,sdkVersion:b,...E}=$m(ar()),C=((R=this.sfuClient)==null?void 0:R.sessionId)??"N/A",w=`${this.streamClientBasePath}/feedback/${m}`;return this.streamClient.post(w,{rating:u,reason:f,user_session_id:C,sdk:g,sdk_version:b,custom:{...p,"x-stream-platform-data":E}})},this.sendCustomEvent=async u=>this.streamClient.post(`${this.streamClientBasePath}/event`,{custom:u}),this.applyDeviceConfig=async u=>{await this.initCamera({setStatus:u}).catch(f=>{this.logger("warn","Camera init failed",f)}),await this.initMic({setStatus:u}).catch(f=>{this.logger("warn","Mic init failed",f)})},this.initCamera=async u=>{var p,m,g,b,E;if(await this.camera.statusChangeSettled(),(p=this.state.localParticipant)!=null&&p.videoStream||!this.permissionsContext.hasPermission("send-video"))return;if(!this.camera.state.direction&&!this.camera.state.selectedDevice){let C="front";const w=(m=this.state.settings)==null?void 0:m.video.camera_facing;w&&(C=w==="front"?"front":"back"),this.camera.state.setDirection(C)}const f=(g=this.state.settings)==null?void 0:g.video.target_resolution;f&&await this.camera.selectTargetResolution(f),u.setStatus&&(this.camera.enabled&&this.camera.state.mediaStream&&!((b=this.publisher)!=null&&b.isPublishing(v.VIDEO))&&await this.publishVideoStream(this.camera.state.mediaStream),this.camera.state.status===void 0&&((E=this.state.settings)!=null&&E.video.camera_default_on)&&await this.camera.enable())},this.initMic=async u=>{var f,p,m;await this.microphone.statusChangeSettled(),!((f=this.state.localParticipant)!=null&&f.audioStream||!this.permissionsContext.hasPermission("send-audio"))&&u.setStatus&&(this.microphone.enabled&&this.microphone.state.mediaStream&&!((p=this.publisher)!=null&&p.isPublishing(v.AUDIO))&&await this.publishAudioStream(this.microphone.state.mediaStream),this.microphone.state.status===void 0&&((m=this.state.settings)!=null&&m.audio.mic_default_on)&&await this.microphone.enable())},this.trackElementVisibility=(u,f,p)=>this.dynascaleManager.trackElementVisibility(u,f,p),this.setViewport=u=>this.dynascaleManager.setViewport(u),this.bindVideoElement=(u,f,p)=>{const m=this.dynascaleManager.bindVideoElement(u,f,p);if(m)return this.leaveCallHooks.add(m),()=>{this.leaveCallHooks.delete(m),m()}},this.bindAudioElement=(u,f,p="audioTrack")=>{const m=this.dynascaleManager.bindAudioElement(u,f,p);if(m)return this.leaveCallHooks.add(m),()=>{this.leaveCallHooks.delete(m),m()}},this.bindCallThumbnailElement=(u,f={})=>{const p=()=>{u.src=f.fallbackImageSource||"https://getstream.io/random_svg/?name=x&id=x"},m=ue(this.state.thumbnails$,g=>{if(!g)return;u.addEventListener("error",p);const b=new URL(g.image_url);b.searchParams.set("w",String(u.clientWidth)),b.searchParams.set("h",String(u.clientHeight)),u.src=b.toString()});return()=>{m(),u.removeEventListener("error",p)}},this.setPreferredIncomingVideoResolution=(u,f)=>{this.dynascaleManager.setVideoTrackSubscriptionOverrides(u?{enabled:!0,dimension:u}:void 0,f),this.dynascaleManager.applyTrackSubscriptions()},this.setIncomingVideoEnabled=u=>{this.dynascaleManager.setVideoTrackSubscriptionOverrides(u?void 0:{enabled:!1}),this.dynascaleManager.applyTrackSubscriptions()},this.setDisconnectionTimeout=u=>{this.disconnectionTimeoutSeconds=u},this.type=e,this.id=n,this.cid=`${e}:${n}`,this.ringingSubject=new A(d),this.watching=c,this.streamClient=s,this.clientStore=o,this.streamClientBasePath=`/call/${this.type}/${this.id}`,this.logger=V(["Call"]);const l=zm.get(e),h=a||l.options.sortParticipantsBy;h&&this.state.setSortParticipantsBy(h),this.state.setMembers(i||[]),this.state.setOwnCapabilities(r||[]),this.state.setCallingState(d?I.RINGING:I.IDLE),this.camera=new rg(this),this.microphone=new fg(this),this.speaker=new bg(this),this.screenShare=new mg(this),this.dynascaleManager=new Wm(this.state,this.speaker)}async setup(){await te(this.joinLeaveConcurrencyTag,async()=>{this.initialized||(this.leaveCallHooks.add(this.on("all",e=>{this.state.updateFromEvent(e)})),this.leaveCallHooks.add(Vm(this,this.dispatcher)),this.registerEffects(),this.registerReconnectHandlers(),this.state.callingState===I.LEFT&&this.state.setCallingState(I.IDLE),this.initialized=!0)})}registerEffects(){this.leaveCallHooks.add(ue(this.state.settings$,e=>{e&&this.permissionsContext.setCallSettings(e)})),this.leaveCallHooks.add(Zp(this.state.ownCapabilities$,this.handleOwnCapabilitiesUpdated)),this.leaveCallHooks.add(ue(this.state.blockedUserIds$,async e=>{if(!e||e.length===0)return;const n=this.currentUserId;n&&e.includes(n)&&(this.logger("info","Leaving call because of being blocked"),await this.leave({reason:"user blocked"}).catch(s=>{this.logger("error","Error leaving call after being blocked",s)}))})),this.leaveCallHooks.add(ue(this.state.session$,e=>{var r;if(!this.ringing)return;const n=(r=this.clientStore.connectedUser)==null?void 0:r.id;if(!n)return;const s=!!(e!=null&&e.accepted_by[n]),i=!!(e!=null&&e.rejected_by[n]);(s||i)&&this.cancelAutoDrop()})),this.leaveCallHooks.add(ue(this.ringingSubject,e=>{var c,l;if(!e)return;const n=this.state.session,s=(c=this.clientStore.connectedUser)==null?void 0:c.id,i=n==null?void 0:n.ended_at,r=(l=this.state.createdBy)==null?void 0:l.id,a=n==null?void 0:n.rejected_by,o=n==null?void 0:n.accepted_by;let d=!1;i?d=!0:r&&a?a[r]&&(d=!0):s&&a?a[s]&&(d=!0):s&&o&&o[s]&&(d=!0),d?this.state.callingState!==I.IDLE&&this.state.setCallingState(I.IDLE):(this.state.callingState===I.IDLE&&this.state.setCallingState(I.RINGING),this.scheduleAutoDrop(),this.leaveCallHooks.add(Co(this)))}))}get ringing(){return fe(this.ringingSubject)}get currentUserId(){var e;return(e=this.clientStore.connectedUser)==null?void 0:e.id}get isCreatedByMe(){var e;return((e=this.state.createdBy)==null?void 0:e.id)===this.currentUserId}updatePublishOptions(e){this.publishOptions={...this.publishOptions,...e}}}var yg=null;class Sg{constructor(e){this._log=(n,s={},i="info")=>{this.client.logger(i,"connection:"+n,{...s})},this.setClient=n=>{this.client=n},this._buildUrl=()=>{const n=new URLSearchParams;return n.set("api_key",this.client.key),n.set("stream-auth-type",this.client.getAuthType()),n.set("X-Stream-Client",this.client.getUserAgent()),`${this.client.wsBaseURL}/connect?${n.toString()}`},this.onlineStatusChanged=n=>{n.type==="offline"?(this._log("onlineStatusChanged() - Status changing to offline"),this._setHealth(!1,!0)):n.type==="online"&&(this._log(`onlineStatusChanged() - Status changing to online. isHealthy: ${this.isHealthy}`),this.isHealthy||this._reconnect({interval:10}))},this.onopen=n=>{var a;if(this.wsID!==n)return;const s=this.client.user;if(!s){this.client.logger("error","User not set, can't connect to WS");return}const i=this.client._getToken();if(!i){this.client.logger("error","Token not set, can't connect authenticate");return}const r={token:i,user_details:{id:s.id,name:s.name,image:s.image,custom:s.custom}};(a=this.ws)==null||a.send(JSON.stringify(r)),this._log("onopen() - onopen callback",{wsID:n})},this.onmessage=(n,s)=>{var r,a;if(this.wsID!==n)return;this._log("onmessage() - onmessage callback",{event:s,wsID:n});const i=typeof s.data=="string"?JSON.parse(s.data):null;if(!this.isResolved&&i&&i.type==="connection.error"&&(this.isResolved=!0,i.error)){(r=this.rejectPromise)==null||r.call(this,this._errorFromWSEvent(i,!1));return}if(this.lastEvent=new Date,i&&(i.type==="health.check"||i.type==="connection.ok")&&this.scheduleNextPing(),i&&i.type==="connection.ok"&&((a=this.resolvePromise)==null||a.call(this,i),this._setHealth(!0)),i&&i.type==="connection.error"&&i.error){const{code:o}=i.error;this.isHealthy=!1,this.isConnecting=!1,this.consecutiveFailures+=1,o===Ye.TOKEN_EXPIRED&&!this.client.tokenManager.isStatic()&&(clearTimeout(this.connectionCheckTimeoutRef),this._log("connect() - WS failure due to expired token, so going to try to reload token and reconnect"),this._reconnect({refreshToken:!0}))}i&&(i.received_at=new Date,this.client.dispatchEvent(i)),this.scheduleConnectionCheck()},this.onclose=(n,s)=>{var i,r;if(this.wsID===n)if(this._log("onclose() - onclose callback - "+s.code,{event:s,wsID:n}),s.code===Ye.WS_CLOSED_SUCCESS){const a=new Error(`WS connection reject with error ${s.reason}`);a.reason=s.reason,a.code=s.code,a.wasClean=s.wasClean,a.target=s.target,(i=this.rejectPromise)==null||i.call(this,a),this._log(`onclose() - WS connection reject with error ${s.reason}`,{event:s})}else this.consecutiveFailures+=1,this.totalFailures+=1,this._setHealth(!1),this.isConnecting=!1,(r=this.rejectPromise)==null||r.call(this,this._errorFromWSEvent(s)),this._log("onclose() - WS connection closed. Calling reconnect ...",{event:s}),this._reconnect()},this.onerror=(n,s)=>{var i;this.wsID===n&&(this.consecutiveFailures+=1,this.totalFailures+=1,this._setHealth(!1),this.isConnecting=!1,(i=this.rejectPromise)==null||i.call(this,new Error(`WebSocket error: ${s}`)),this._log("onerror() - WS connection resulted into error",{event:s}),this._reconnect())},this._setHealth=(n,s=!1)=>{if(n!==this.isHealthy){if(this.isHealthy=n,this.isHealthy||s){this.client.dispatchEvent({type:"connection.changed",online:this.isHealthy});return}setTimeout(()=>{this.isHealthy||this.client.dispatchEvent({type:"connection.changed",online:this.isHealthy})},5e3)}},this._errorFromWSEvent=(n,s=!0)=>{let i,r,a;if(gp(n))i=n.code,a=n.reason,r=0;else{const{error:c}=n;i=c.code,a=c.message,r=c.StatusCode}const o=`WS failed with code: ${i} and reason: ${a}`;this._log(o,{event:n},"warn");const d=new Error(o);return d.code=i,d.StatusCode=r,d.isWSFailure=s,d},this._setupConnectionPromise=()=>{this.isResolved=!1,this.connectionOpenSafe=_t(new Promise((n,s)=>{this.resolvePromise=n,this.rejectPromise=s}))},this.scheduleNextPing=()=>{const n=Rn();this.healthCheckTimeoutRef&&n.clearTimeout(this.healthCheckTimeoutRef),this.healthCheckTimeoutRef=n.setTimeout(()=>{var i;const s=[{type:"health.check",client_id:this.client.clientID}];try{(i=this.ws)==null||i.send(JSON.stringify(s))}catch{}},this.pingInterval)},this.scheduleConnectionCheck=()=>{clearTimeout(this.connectionCheckTimeoutRef),this.connectionCheckTimeoutRef=setTimeout(()=>{const n=new Date;this.lastEvent&&n.getTime()-this.lastEvent.getTime()>this.connectionCheckTimeout&&(this._log("scheduleConnectionCheck - going to reconnect"),this._setHealth(!1),this._reconnect())},this.connectionCheckTimeout)},this.client=e,this.consecutiveFailures=0,this.totalFailures=0,this.isConnecting=!1,this.isDisconnected=!1,this.isResolved=!1,this.isHealthy=!1,this.wsID=1,this.lastEvent=null,this.pingInterval=25*1e3,this.connectionCheckTimeout=this.pingInterval+10*1e3,Ls(this.onlineStatusChanged)}async connect(e=15e3){if(this.isConnecting)throw Error("You've called connect twice, can only attempt 1 connection at the time");this.isDisconnected=!1;try{const n=await this._connect();this.consecutiveFailures=0,this._log(`connect() - Established ws connection with healthcheck: ${n}`)}catch(n){if(this.isHealthy=!1,this.consecutiveFailures+=1,n.code===Ye.TOKEN_EXPIRED&&!this.client.tokenManager.isStatic())this._log("connect() - WS failure due to expired token, so going to try to reload token and reconnect"),this._reconnect({refreshToken:!0});else if(!n.isWSFailure)throw new Error(JSON.stringify({code:n.code,StatusCode:n.StatusCode,message:n.message,isWSFailure:n.isWSFailure}))}return await this._waitForHealthy(e)}async _waitForHealthy(e=15e3){return Promise.race([(async()=>{for(let s=0;s<=e;s+=50)try{return await this.connectionOpen}catch(i){if(s===e)throw new Error(JSON.stringify({code:i.code,StatusCode:i.StatusCode,message:i.message,isWSFailure:i.isWSFailure}));await tt(50)}})(),(async()=>{throw await tt(e),this.isConnecting=!1,new Error(JSON.stringify({code:"",StatusCode:"",message:"initial WS connection could not be established",isWSFailure:!0}))})()])}disconnect(e){this._log(`disconnect() - Closing the websocket connection for wsID ${this.wsID}`),this.wsID+=1,this.isConnecting=!1,this.isDisconnected=!0,this.healthCheckTimeoutRef&&Rn().clearInterval(this.healthCheckTimeoutRef),this.connectionCheckTimeoutRef&&clearInterval(this.connectionCheckTimeoutRef),ao(this.onlineStatusChanged),this.isHealthy=!1;let n;const{ws:s}=this;return s&&s.close&&s.readyState===s.OPEN?(n=new Promise(i=>{const r=a=>{this._log(`disconnect() - resolving isClosedPromise ${a?"with":"without"} close frame`,{event:a}),i()};s.onclose=r,setTimeout(r,e??1e3)}),this._log("disconnect() - Manually closed connection by calling client.disconnect()"),s.close(Ye.WS_CLOSED_SUCCESS,"Manually closed connection by calling client.disconnect()")):(this._log("disconnect() - ws connection doesn't exist or it is already closed."),n=Promise.resolve()),delete this.ws,n}async _connect(){var n,s,i,r;if(this.isConnecting)return;this.isConnecting=!0,this.requestID=xs();let e=!1;try{this._log("_connect() - waiting for token"),await this.client.tokenManager.tokenReady(),e=!0}catch{}try{e||(this._log("_connect() - tokenProvider failed before, so going to retry"),await this.client.tokenManager.loadToken()),this.client.isConnectionIsPromisePending||this.client._setupConnectionIdPromise(),this._setupConnectionPromise();const a=this._buildUrl();this._log(`_connect() - Connecting to ${a}`,{wsURL:a,requestID:this.requestID});const o=this.client.options.WebSocketImpl??WebSocket;this.ws=new o(a),this.ws.onopen=this.onopen.bind(this,this.wsID),this.ws.onclose=this.onclose.bind(this,this.wsID),this.ws.onerror=this.onerror.bind(this,this.wsID),this.ws.onmessage=this.onmessage.bind(this,this.wsID);const d=await this.connectionOpen;if(this.isConnecting=!1,d)return this.connectionID=d.connection_id,(s=(n=this.client).resolveConnectionId)==null||s.call(n,this.connectionID),d}catch(a){throw await this.client._setupConnectionIdPromise(),this.isConnecting=!1,this._log("_connect() - Error - ",a),(r=(i=this.client).rejectConnectionId)==null||r.call(i,a),a}}async _reconnect(e={}){if(this._log("_reconnect() - Initiating the reconnect"),this.isConnecting||this.isHealthy){this._log("_reconnect() - Abort (1) since already connecting or healthy");return}let n=e.interval;if(n||(n=Zs(this.consecutiveFailures)),await tt(n),this.isConnecting||this.isHealthy){this._log("_reconnect() - Abort (2) since already connecting or healthy");return}if(this.isDisconnected){this._log("_reconnect() - Abort (3) since disconnect() is called");return}this._log("_reconnect() - Destroying current WS connection"),this._destroyCurrentWSConnection(),e.refreshToken&&await this.client.tokenManager.loadToken();try{await this._connect(),this._log("_reconnect() - Waiting for recoverCallBack"),this._log("_reconnect() - Finished recoverCallBack"),this.consecutiveFailures=0}catch(s){if(this.isHealthy=!1,this.consecutiveFailures+=1,s.code===Ye.TOKEN_EXPIRED&&!this.client.tokenManager.isStatic())return this._log("_reconnect() - WS failure due to expired token, so going to try to reload token and reconnect"),this._reconnect({refreshToken:!0});s.isWSFailure&&(this._log("_reconnect() - WS failure, so going to try to reconnect"),this._reconnect())}this._log("_reconnect() - == END ==")}_destroyCurrentWSConnection(){var e;this.wsID+=1;try{(e=this==null?void 0:this.ws)==null||e.close()}catch{}}get connectionOpen(){var e;return(e=this.connectionOpenSafe)==null?void 0:e.call(this)}}function Cg(t){const e=t.split(".");if(e.length!==3)return"";const n=e[1],s=vg(n);return JSON.parse(s).user_id}const vg=t=>{const e={},n=String.fromCharCode,s=t.length;let i,r=0,a,o,d=0,c,l="";const h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(i=0;i<64;i++)e[h.charAt(i)]=i;for(o=0;o<s;o++)for(a=e[t.charAt(o)],r=(r<<6)+a,d+=6;d>=8;)((c=r>>>(d-=8)&255)||o<s-2)&&(l+=n(c));return l};class Tg{constructor(e){this.setTokenOrProvider=async(n,s,i)=>{this.validateToken(n,s,i),this.user=s,Ns(n)&&(this.tokenProvider=n,this.type="provider"),typeof n=="string"&&(this.token=n,this.type="static"),await this.loadToken()},this.reset=()=>{this.token=void 0,this.tokenProvider=void 0,this.type="static",this.user=void 0,this.loadTokenPromise=null},this.validateToken=(n,s,i)=>{if(!(s&&i&&!n)){if(!this.secret&&!n)throw new Error("UserWithId token can not be empty");if(n&&typeof n!="string"&&!Ns(n))throw new Error("user token should either be a string or a function");if(typeof n=="string"){if(i&&n==="")return;const r=Cg(n);if(n!=null&&(r==null||r===""||!i&&r!==s.id))throw new Error("userToken does not have a user_id or is not matching with user.id")}}},this.tokenReady=()=>this.loadTokenPromise,this.loadToken=()=>(this.loadTokenPromise=new Promise(async(n,s)=>{if(this.type==="static")return n(this.token);if(this.tokenProvider&&typeof this.tokenProvider!="string"){try{this.token=await this.tokenProvider()}catch(i){return s(new Error(`Call to tokenProvider failed with message: ${i}`))}n(this.token)}}),this.loadTokenPromise),this.getToken=()=>{if(this.token)return this.token;if(this.user&&!this.token)return this.token;throw new Error("User token is not set. Either client.connectUser wasn't called or client.disconnect was called")},this.isStatic=()=>this.type==="static",this.loadTokenPromise=null,this.secret=e,this.type="static"}}const mr=async(t="https://hint.stream-io-video.com/",e=2e3,n=3)=>{const s=V(["location-hint"]);let i=0,r="ERR";do{const a=new AbortController,o=setTimeout(()=>a.abort(),e);try{const c=(await fetch(t,{method:"HEAD",signal:a.signal})).headers.get("x-amz-cf-pop")||"ERR";s("debug",`Location header: ${c}`),r=c.substring(0,3)}catch(d){s("warn",`Failed to get location hint from ${t}`,d),r="ERR"}finally{clearTimeout(o)}}while(r==="ERR"&&++i<n);return r};class gr{constructor(e,n){var i;this.listeners={},this.getAuthType=()=>this.anonymous?"anonymous":"jwt",this.setBaseURL=r=>{this.baseURL=r,this.wsBaseURL=this.baseURL.replace("http","ws").replace(":3030",":8800")},this.getLocationHint=async(r,a)=>{const o=await this.locationHint;return!o||o==="ERR"?(this.locationHint=mr(r??this.options.locationHintUrl,a??this.options.locationHintTimeout),this.locationHint):o},this._getConnectionID=()=>{var r;return(r=this.wsConnection)==null?void 0:r.connectionID},this._hasConnectionID=()=>!!this._getConnectionID(),this.connectUser=async(r,a)=>{if(!r.id)throw new Error('The "id" field on the user is missing');if(this.userID===r.id&&this.setUserPromise)return this.logger("warn","Consecutive calls to connectUser is detected, ideally you should only call this function once in your app."),this.setUserPromise;if(this.userID)throw new Error("Use client.disconnect() before trying to connect as a different user. connectUser was called twice.");(this._isUsingServerAuth()||this.node)&&!this.options.allowServerSideConnect&&this.logger("warn","Please do not use connectUser server side. Use our @stream-io/node-sdk instead: https://getstream.io/video/docs/api/"),this.userID=r.id,this.anonymous=!1;const o=this._setToken(r,a,this.anonymous);this._setUser(r);const d=this.openConnection();this.setUserPromise=Promise.all([o,d]).then(c=>c[1]);try{return Ls(this.updateNetworkConnectionStatus),await this.setUserPromise}catch(c){throw this.persistUserOnConnectionFailure?this.closeConnection():this.disconnectUser(),c}},this._setToken=(r,a,o)=>this.tokenManager.setTokenOrProvider(a,r,o),this._setUser=r=>{this.user=r,this.userID=r.id,this._user={...r}},this.closeConnection=async r=>{var a;await((a=this.wsConnection)==null?void 0:a.disconnect(r))},this.openConnection=async()=>{var o,d,c;if(!this.userID)throw Error("UserWithId is not set on client, use client.connectUser or client.connectAnonymousUser instead");const r=(o=this.wsPromiseSafe)==null?void 0:o.call(this);if((d=this.wsConnection)!=null&&d.isConnecting&&r)return this.logger("info","client:openConnection() - connection already in progress"),await r;if((c=this.wsConnection)!=null&&c.isHealthy&&this._hasConnectionID()){this.logger("info","client:openConnection() - openConnection called twice, healthy connection already exists");return}await this._setupConnectionIdPromise(),this.clientID=`${this.userID}--${xs()}`;const a=this.connect();return this.wsPromiseSafe=_t(a),await a},this.disconnectUser=async r=>{this.logger("info","client:disconnect() - Disconnecting the client"),delete this.user,delete this._user,delete this.userID,this.anonymous=!1,await this.closeConnection(r),ao(this.updateNetworkConnectionStatus),this.tokenManager.reset(),this.connectionIdPromiseSafe=void 0,this.rejectConnectionId=void 0,this.resolveConnectionId=void 0},this.connectGuestUser=async r=>{this.guestUserCreatePromise=this.doAxiosRequest("post","/guest",{user:r},{publicEndpoint:!0});const a=await this.guestUserCreatePromise;return this.guestUserCreatePromise.finally(()=>this.guestUserCreatePromise=void 0),this.connectUser(a.user,a.access_token)},this.connectAnonymousUser=async(r,a)=>{var o;Ls(this.updateNetworkConnectionStatus),await this._setupConnectionIdPromise(),this.anonymous=!0,await this._setToken(r,a,this.anonymous),this._setUser(r),(o=this.resolveConnectionId)==null||o.call(this)},this.on=(r,a)=>{var o;return this.listeners[r]||(this.listeners[r]=[]),this.logger("debug",`Adding listener for ${r} event`),(o=this.listeners[r])==null||o.push(a),()=>{this.off(r,a)}},this.off=(r,a)=>{var o;this.listeners[r]||(this.listeners[r]=[]),this.logger("debug",`Removing listener for ${r} event`),this.listeners[r]=(o=this.listeners[r])==null?void 0:o.filter(d=>d!==a)},this._setupConnectionIdPromise=()=>{this.connectionIdPromiseSafe=_t(new Promise((r,a)=>{this.resolveConnectionId=r,this.rejectConnectionId=a}))},this._logApiRequest=(r,a,o,d)=>{this.logger("trace",`client: ${r} - Request - ${a}`,{payload:o,config:d})},this._logApiResponse=(r,a,o)=>{this.logger("trace",`client:${r} - Response - url: ${a} > status ${o.status}`,{response:o})},this._logApiError=(r,a,o)=>{this.logger("error",`client:${r} - Error - url: ${a}`,{url:a,error:o})},this.doAxiosRequest=async(r,a,o,d={})=>{var l,h;if(!d.publicEndpoint){await Promise.all([this.tokenManager.tokenReady(),this.guestUserCreatePromise]);try{await this.connectionIdPromise}catch{await((l=this.wsConnection)==null?void 0:l._waitForHealthy()),await this.connectionIdPromise}}const c=this._enrichAxiosOptions(d);try{let u;switch(this._logApiRequest(r,a,o,c),r){case"get":u=await this.axiosInstance.get(a,c);break;case"delete":u=await this.axiosInstance.delete(a,c);break;case"post":u=await this.axiosInstance.post(a,o,c);break;case"put":u=await this.axiosInstance.put(a,o,c);break;case"patch":u=await this.axiosInstance.patch(a,o,c);break;case"options":u=await this.axiosInstance.options(a,c);break;default:throw new Error("Invalid request type")}return this._logApiResponse(r,a,u),this.consecutiveFailures=0,this.handleResponse(u)}catch(u){if(u.client_request_id=(h=c.headers)==null?void 0:h["x-client-request-id"],this.consecutiveFailures+=1,u.response)return this._logApiError(r,a,u.response),u.response.data.code===Ye.TOKEN_EXPIRED&&!this.tokenManager.isStatic()?(this.consecutiveFailures>1&&await tt(Zs(this.consecutiveFailures)),await this.tokenManager.loadToken(),await this.doAxiosRequest(r,a,o,d)):this.handleResponse(u.response);throw this._logApiError(r,a,u),u}},this.get=(r,a)=>this.doAxiosRequest("get",r,null,{params:a}),this.put=(r,a,o)=>this.doAxiosRequest("put",r,a,{params:o}),this.post=(r,a,o)=>this.doAxiosRequest("post",r,a,{params:o}),this.patch=(r,a,o)=>this.doAxiosRequest("patch",r,a,{params:o}),this.delete=(r,a)=>this.doAxiosRequest("delete",r,null,{params:a}),this.errorFromResponse=r=>{const{data:a,status:o}=r,d=new Za;return d.message=`Stream error code ${a.code}: ${a.message}`,d.code=a.code,d.unrecoverable=a.unrecoverable,d.response=r,d.status=o,d},this.handleResponse=r=>{const a=r.data;if(mp(r))throw this.errorFromResponse(r);return a},this.dispatchEvent=r=>{if(this.logger("debug",`Dispatching event: ${r.type}`,r),!!this.listeners){for(const a of this.listeners.all||[])a(r);for(const a of this.listeners[r.type]||[])a(r)}},this.connect=async()=>{if(!this.userID||!this._user)throw Error("Call connectUser or connectAnonymousUser before starting the connection");if(!this.wsBaseURL)throw Error("Websocket base url not set");if(!this.clientID)throw Error("clientID is not set");return this.wsConnection=new Sg(this),this.logger("info","StreamClient.connect: this.wsConnection.connect()"),await this.wsConnection.connect(this.defaultWSTimeout)},this.getUserAgent=()=>this.userAgent||`stream-video-javascript-client-${this.node?"node":"browser"}-1.14.0`,this.setUserAgent=r=>{this.userAgent=r},this._isUsingServerAuth=()=>!!this.secret,this._enrichAxiosOptions=(r={params:{},headers:{},config:{}})=>{var d;const a=r.publicEndpoint&&!this.user?void 0:this._getToken(),o=a?{Authorization:a}:void 0;return(d=r.headers)!=null&&d["x-client-request-id"]||(r.headers={...r.headers,"x-client-request-id":xs()}),{params:{user_id:this.userID,connection_id:this._getConnectionID(),api_key:this.key,...r.params},headers:{...o,"stream-auth-type":r.publicEndpoint&&!this.user?"anonymous":this.getAuthType(),"X-Stream-Client":this.getUserAgent(),...r.headers},...r.config,...this.options.axiosRequestConfig}},this._getToken=()=>this.tokenManager?this.tokenManager.getToken():null,this.updateNetworkConnectionStatus=r=>{r.type==="offline"?(this.logger("debug","device went offline"),this.dispatchEvent({type:"network.changed",online:!1})):r.type==="online"&&(this.logger("debug","device went online"),this.dispatchEvent({type:"network.changed",online:!0}))},this.key=e,this.secret=n==null?void 0:n.secret;const s=n||{browser:typeof window<"u"};this.browser=s.browser||typeof window<"u",this.node=!this.browser,this.browser&&(this.locationHint=mr(n==null?void 0:n.locationHintUrl,n==null?void 0:n.locationHintTimeout,n==null?void 0:n.locationHintMaxAttempts)),this.options={timeout:5e3,withCredentials:!1,...s},this.node&&!this.options.httpsAgent&&(this.options.httpsAgent=new yg.Agent({keepAlive:!0,keepAliveMsecs:3e3})),this.setBaseURL(this.options.baseURL||"https://video.stream-io-api.com/video"),this.axiosInstance=W.create({...this.options,baseURL:this.baseURL}),this.wsConnection=null,this.wsPromiseSafe=null,this.setUserPromise=null,this.anonymous=!1,this.persistUserOnConnectionFailure=(i=this.options)==null?void 0:i.persistUserOnConnectionFailure,this.tokenManager=new Tg(this.secret),this.consecutiveFailures=0,this.defaultWSTimeout=15e3,this.logger=Ns(s.logger)?s.logger:()=>null}get connectionIdPromise(){var e;return(e=this.connectionIdPromiseSafe)==null?void 0:e.call(this)}get isConnectionIsPromisePending(){var e;return((e=this.connectionIdPromiseSafe)==null?void 0:e.checkPending())??!1}get wsPromise(){var e;return(e=this.wsPromiseSafe)==null?void 0:e.call(this)}}class Le{constructor(e,n){var a,o,d;this.logLevel="warn",this.eventHandlersToUnregister=[],this.connectionConcurrencyTag=Symbol("connectionConcurrencyTag"),this.connectUser=async(c,l)=>{if(c.type==="anonymous")return c.id="!anon",this.connectAnonymousUser(c,l);let h=()=>this.streamClient.connectUser(c,l);c.type==="guest"&&(h=async()=>this.streamClient.connectGuestUser(c));const u=await te(this.connectionConcurrencyTag,()=>h());return u!=null&&u.me&&this.writeableStateStore.setConnectedUser(u.me),this.eventHandlersToUnregister.push(this.on("connection.changed",f=>{if(f.online){const p=this.writeableStateStore.calls.filter(m=>m.watching).map(m=>m.cid);this.logger("info",`Rewatching calls after connection changed ${p.join(", ")}`),p.length>0&&this.queryCalls({watch:!0,filter_conditions:{cid:{$in:p}},sort:[{field:"cid",direction:1}]}).catch(m=>{this.logger("error","Failed to re-watch calls",m)})}})),this.eventHandlersToUnregister.push(this.on("call.created",f=>{const{call:p,members:m}=f;if(c.id===p.created_by.id){this.logger("warn","Received `call.created` sent by the current user");return}this.logger("info",`New call created and registered: ${p.cid}`);const g=new yt({streamClient:this.streamClient,type:p.type,id:p.id,members:m,clientStore:this.writeableStateStore});g.state.updateFromCallResponse(p),this.writeableStateStore.registerCall(g)})),this.eventHandlersToUnregister.push(this.on("call.ring",async f=>{const{call:p,members:m}=f;if(c.id===p.created_by.id){this.logger("debug","Received `call.ring` sent by the current user so ignoring the event");return}const g=this.writeableStateStore.findCall(p.type,p.id);g?await g.updateFromRingingEvent(f):await new yt({streamClient:this.streamClient,type:p.type,id:p.id,members:m,clientStore:this.writeableStateStore,ringing:!0}).get()})),u},this.disconnectUser=async c=>{var f;if(!this.streamClient.user)return;const l=(f=this.streamClient.user)==null?void 0:f.id,h=this.streamClient.key,u=()=>this.streamClient.disconnectUser(c);await te(this.connectionConcurrencyTag,()=>u()),l&&Le._instanceMap.delete(h+l),this.eventHandlersToUnregister.forEach(p=>p()),this.eventHandlersToUnregister=[],this.writeableStateStore.setConnectedUser(void 0)},this.on=(c,l)=>this.streamClient.on(c,l),this.off=(c,l)=>this.streamClient.off(c,l),this.call=(c,l)=>new yt({streamClient:this.streamClient,id:l,type:c,clientStore:this.writeableStateStore}),this.createGuestUser=async c=>this.streamClient.doAxiosRequest("post","/guest",c,{publicEndpoint:!0}),this.queryCalls=async(c={})=>{const l=await this.streamClient.post("/calls",c),h=[];for(const u of l.calls){const f=new yt({streamClient:this.streamClient,id:u.call.id,type:u.call.type,members:u.members,ownCapabilities:u.own_capabilities,watching:c.watch,clientStore:this.writeableStateStore});f.state.updateFromCallResponse(u.call),await f.applyDeviceConfig(!1),c.watch&&this.writeableStateStore.registerCall(f),h.push(f)}return{...l,calls:h}},this.queryCallStats=async(c={})=>this.streamClient.post("/call/stats",c),this.queryAggregateCallStats=async(c={})=>this.streamClient.post("/stats",c),this.edges=async()=>this.streamClient.get("/edges"),this.addDevice=async(c,l,h,u,f)=>await this.streamClient.post("/devices",{id:c,push_provider:l,voip_token:f,...u!=null?{user_id:u}:{},...h!=null?{push_provider_name:h}:{}}),this.addVoipDevice=async(c,l,h,u)=>await this.addDevice(c,l,h,u,!0),this.getDevices=async c=>await this.streamClient.get("/devices",c?{user_id:c}:{}),this.removeDevice=async(c,l)=>await this.streamClient.delete("/devices",{id:c,...l?{user_id:l}:{}}),this.onRingingCall=async c=>{let l=this.state.calls.find(h=>h.cid===c&&h.ringing);if(!l){const[h,u]=c.split(":");l=new yt({streamClient:this.streamClient,type:h,id:u,clientStore:this.writeableStateStore,ringing:!0}),await l.get()}return l},this.connectAnonymousUser=async(c,l)=>{const h=()=>this.streamClient.connectAnonymousUser(c,l);return await te(this.connectionConcurrencyTag,()=>h())};let s=co,i="warn";typeof e=="string"?(i=(n==null?void 0:n.logLevel)||i,s=(n==null?void 0:n.logger)||s,n!=null&&n.enableTimerWorker&&hr()):(i=((a=e.options)==null?void 0:a.logLevel)||i,s=((o=e.options)==null?void 0:o.logger)||s,(d=e.options)!=null&&d.enableTimerWorker&&hr()),bp(s,i),this.logger=V(["client"]);const r=V(["coordinator"]);if(typeof e=="string")this.streamClient=new gr(e,{persistUserOnConnectionFailure:!0,...n,logLevel:i,logger:r});else{this.streamClient=new gr(e.apiKey,{persistUserOnConnectionFailure:!0,...e.options,logLevel:i,logger:r});const c=Us();{const l=qe[c.type].toLowerCase(),h=`${c.major}.${c.minor}.${c.patch}`,u=this.streamClient.getUserAgent();this.streamClient.setUserAgent(`${u}-video-${l}-sdk-${h}`)}}if(this.writeableStateStore=new Qp,this.readOnlyStateStore=new em(this.writeableStateStore),typeof e!="string"){const c=e.user,l=e.token||e.tokenProvider;if(c){let h=c.id;c.type==="anonymous"&&(h="!anon"),h&&(Le._instanceMap.has(e.apiKey+h)&&this.logger("warn",`A StreamVideoClient already exists for ${c.type==="anonymous"?"an anonymous user":h}; Prefer using getOrCreateInstance method`),c.id=h,Le._instanceMap.set(e.apiKey+h,this)),this.connectUser(c,l).catch(u=>{this.logger("error","Failed to connect",u)})}}}static getOrCreateInstance(e){const n=e.user;if(!n.id)if(e.user.type==="anonymous")n.id="!anon";else throw new Error("User ID is required for a non-anonymous user");if(!e.token&&!e.tokenProvider&&e.user.type!=="anonymous"&&e.user.type!=="guest")throw new Error("TokenProvider or token is required for a user that is not a guest or anonymous");let s=Le._instanceMap.get(e.apiKey+n.id);return s||(s=new Le({...e,user:n})),s}get state(){return this.readOnlyStateStore}}Le._instanceMap=new Map;var wg=Mo("<video></video>",2);function Dg(t,e){Fo(e,!0);let n=yi(null),s=yi(null);Bo(()=>{Vt(s)!==null&&bi(n,Vo(e.call.bindVideoElement(Vt(s),e.sessionId,"videoTrack")??null))}),qo(()=>{var r;(r=Vt(n))==null||r()});var i=wg();$o(i,r=>bi(s,r),()=>Vt(s)),Uo(t,i),jo()}export{Dg as P,Le as S};
