function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function c(e){if(Object.prototype.hasOwnProperty.call(e,"__esModule"))return e;var o=e.default;if(typeof o=="function"){var t=function r(){return this instanceof r?Reflect.construct(o,arguments,this.constructor):o.apply(this,arguments)};t.prototype=o.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var u=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,u.get?u:{enumerable:!0,get:function(){return e[r]}})}),t}export{n as a,c as g};
