import{ad as d,aC as v,ae as i,aD as h,aE as p,W as u,h as f,A as o,o as E,i as T}from"./DvS_9Yw_.js";function g(t){var r=document.createElement("template");return r.innerHTML=t.replaceAll("<!>","<!---->"),r.content}function a(t,r){var e=u;e.nodes_start===null&&(e.nodes_start=t,e.nodes_end=r)}function y(t,r){var e=(r&h)!==0,_=(r&p)!==0,n,c=!t.startsWith("<!>");return()=>{if(f)return a(o,null),o;n===void 0&&(n=g(c?t:"<!>"+t),e||(n=d(n)));var s=_||v?document.importNode(n,!0):n.cloneNode(!0);if(e){var m=d(s),l=s.lastChild;a(m,l)}else a(s,s);return s}}function A(t=""){if(!f){var r=i(t+"");return a(r,r),r}var e=o;return e.nodeType!==3&&(e.before(e=i()),E(e)),a(e,e),e}function M(){if(f)return a(o,null),o;var t=document.createDocumentFragment(),r=document.createComment(""),e=i();return t.append(r,e),a(r,e),t}function N(t,r){if(f){u.nodes_end=o,T();return}t!==null&&t.before(r)}export{N as a,a as b,M as c,y as f,A as t};
