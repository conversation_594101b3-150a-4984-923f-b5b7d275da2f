import"../chunks/CWj6FrbW.js";import"../chunks/BUgeVMx1.js";import{T as L}from"../chunks/BwY1cqmU.js";import{p as C,c as l,r as f,t as h,g as i,f as P,a as b,n as k}from"../chunks/DvS_9Yw_.js";import{s as $}from"../chunks/BGB6NhFz.js";import{f as s,a,c as B,t as I}from"../chunks/Ckyppc5t.js";import{a as T}from"../chunks/DZ8rvC1m.js";import{e as j}from"../chunks/hasoi-S8.js";import{s as N}from"../chunks/PoYD5o0_.js";import{i as R}from"../chunks/BTdX64hs.js";import{h as S}from"../chunks/CQqdknCc.js";import{g as U}from"../chunks/7czifHHs.js";import{B as q}from"../chunks/DF_v5tP2.js";var y=s("<div><!></div>"),z=s("<div> </div>"),A=s("<div>Loading ongoing livestreams</div>"),D=s("<now-live><!></now-live>",2);function E(m,n){C(n,!1);const c=o=>{U(`/watch?streamId=${encodeURIComponent(o)}`)};R();var e=D();N(e,1,"svelte-b6rghi");var u=l(e);T(u,()=>S({}),o=>{var r=A();a(o,r)},(o,r)=>{var t=B(),v=P(t);j(v,1,()=>i(r),d=>d.id,(d,_)=>{var p=y(),w=l(p);q(w,{onClick:()=>c(i(_).id),children:(x,F)=>{k();var g=I();h(()=>$(g,i(_).title)),a(x,g)},$$slots:{default:!0}}),f(p),a(d,p)}),a(o,t)},(o,r)=>{var t=z(),v=l(t);f(t),h(()=>$(v,`Could not load livestreams: ${i(r)??""}`)),a(o,t)}),f(e),a(m,e),b()}function to(m){L(m,{heading:"now live",children:(n,c)=>{E(n,{})}})}export{to as component};
