var fn=Array.isArray,un=Array.prototype.indexOf,Kn=Array.from,Zn=Object.defineProperty,K=Object.getOwnPropertyDescriptor,on=Object.getOwnPropertyDescriptors,_n=Object.prototype,cn=Array.prototype,St=Object.getPrototypeOf,Rt=Object.isExtensible;const $n=()=>{};function zn(t){return typeof(t==null?void 0:t.then)=="function"}function Wn(t){return t()}function Nt(t){for(var n=0;n<t.length;n++)t[n]()}const b=2,Ct=4,ft=8,wt=16,k=32,B=64,yt=128,T=256,rt=512,y=1024,D=2048,M=4096,j=8192,Et=16384,Pt=32768,Ft=65536,Xn=1<<17,vn=1<<19,Mt=1<<20,vt=1<<21,Z=Symbol("$state"),Jn=Symbol("legacy props"),Qn=Symbol("");function qt(t){return t===this.v}function hn(t,n){return t!=t?n==n:t!==n||t!==null&&typeof t=="object"||typeof t=="function"}function Lt(t){return!hn(t,this.v)}function pn(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function dn(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function wn(t){throw new Error("https://svelte.dev/e/effect_orphan")}function yn(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function te(){throw new Error("https://svelte.dev/e/hydration_failed")}function ne(t){throw new Error("https://svelte.dev/e/props_invalid_value")}function En(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function gn(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function Tn(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}let it=!1;function ee(){it=!0}const re=1,ae=2,le=4,se=8,fe=16,ie=1,ue=2,oe=4,_e=8,ce=16,ve=1,he=2,mn="[",An="[!",bn="]",gt={},E=Symbol(),pe="http://www.w3.org/1999/xhtml";let p=null;function Dt(t){p=t}function de(t,n=!1,e){var r=p={p,c:null,d:!1,e:null,m:!1,s:t,x:null,l:null};it&&!n&&(p.l={s:null,u:null,r1:[],r2:mt(!1)}),kn(()=>{r.d=!0})}function we(t){const n=p;if(n!==null){const u=n.e;if(u!==null){var e=h,r=v;n.e=null;try{for(var a=0;a<u.length;a++){var l=u[a];at(l.effect),H(l.reaction),Kt(l.fn)}}finally{at(e),H(r)}}p=n.p,n.m=!0}return{}}function ut(){return!it||p!==null&&p.l===null}function Y(t){if(typeof t!="object"||t===null||Z in t)return t;const n=St(t);if(n!==_n&&n!==cn)return t;var e=new Map,r=fn(t),a=O(0),l=v,u=i=>{var s=v;H(l);var f=i();return H(s),f};return r&&e.set("length",O(t.length)),new Proxy(t,{defineProperty(i,s,f){(!("value"in f)||f.configurable===!1||f.enumerable===!1||f.writable===!1)&&En();var _=e.get(s);return _===void 0?(_=u(()=>O(f.value)),e.set(s,_)):S(_,u(()=>Y(f.value))),!0},deleteProperty(i,s){var f=e.get(s);if(f===void 0)s in i&&(e.set(s,u(()=>O(E))),ct(a));else{if(r&&typeof s=="string"){var _=e.get("length"),o=Number(s);Number.isInteger(o)&&o<_.v&&S(_,o)}S(f,E),ct(a)}return!0},get(i,s,f){var A;if(s===Z)return t;var _=e.get(s),o=s in i;if(_===void 0&&(!o||(A=K(i,s))!=null&&A.writable)&&(_=u(()=>O(Y(o?i[s]:E))),e.set(s,_)),_!==void 0){var c=G(_);return c===E?void 0:c}return Reflect.get(i,s,f)},getOwnPropertyDescriptor(i,s){var f=Reflect.getOwnPropertyDescriptor(i,s);if(f&&"value"in f){var _=e.get(s);_&&(f.value=G(_))}else if(f===void 0){var o=e.get(s),c=o==null?void 0:o.v;if(o!==void 0&&c!==E)return{enumerable:!0,configurable:!0,value:c,writable:!0}}return f},has(i,s){var c;if(s===Z)return!0;var f=e.get(s),_=f!==void 0&&f.v!==E||Reflect.has(i,s);if(f!==void 0||h!==null&&(!_||(c=K(i,s))!=null&&c.writable)){f===void 0&&(f=u(()=>O(_?Y(i[s]):E)),e.set(s,f));var o=G(f);if(o===E)return!1}return _},set(i,s,f,_){var It;var o=e.get(s),c=s in i;if(r&&s==="length")for(var A=f;A<o.v;A+=1){var L=e.get(A+"");L!==void 0?S(L,E):A in i&&(L=u(()=>O(E)),e.set(A+"",L))}o===void 0?(!c||(It=K(i,s))!=null&&It.writable)&&(o=u(()=>O(void 0)),S(o,u(()=>Y(f))),e.set(s,o)):(c=o.v!==E,S(o,u(()=>Y(f))));var et=Reflect.getOwnPropertyDescriptor(i,s);if(et!=null&&et.set&&et.set.call(_,f),!c){if(r&&typeof s=="string"){var xt=e.get("length"),_t=Number(s);Number.isInteger(_t)&&_t>=xt.v&&S(xt,_t+1)}ct(a)}return!0},ownKeys(i){G(a);var s=Reflect.ownKeys(i).filter(o=>{var c=e.get(o);return c===void 0||c.v!==E});for(var[f,_]of e)_.v!==E&&!(f in i)&&s.push(f);return s},setPrototypeOf(){gn()}})}function ct(t,n=1){S(t,t.v+n)}function Tt(t){var n=b|D,e=v!==null&&(v.f&b)!==0?v:null;return h===null||e!==null&&(e.f&T)!==0?n|=T:h.f|=Mt,{ctx:p,deps:null,effects:null,equals:qt,f:n,fn:t,reactions:null,rv:0,v:null,wv:0,parent:e??h}}function ye(t){const n=Tt(t);return nn(n),n}function Ee(t){const n=Tt(t);return n.equals=Lt,n}function Yt(t){var n=t.effects;if(n!==null){t.effects=null;for(var e=0;e<n.length;e+=1)F(n[e])}}function xn(t){for(var n=t.parent;n!==null;){if((n.f&b)===0)return n;n=n.parent}return null}function jt(t){var n,e=h;at(xn(t));try{Yt(t),n=ln(t)}finally{at(e)}return n}function Ht(t){var n=jt(t);if(t.equals(n)||(t.v=n,t.wv=rn()),!V){var e=(N||(t.f&T)!==0)&&t.deps!==null?M:y;I(t,e)}}const z=new Map;function mt(t,n){var e={f:0,v:t,reactions:null,equals:qt,rv:0,wv:0};return e}function O(t,n){const e=mt(t);return nn(e),e}function ge(t,n=!1){var r;const e=mt(t);return n||(e.equals=Lt),it&&p!==null&&p.l!==null&&((r=p.l).s??(r.s=[])).push(e),e}function S(t,n,e=!1){v!==null&&!R&&ut()&&(v.f&(b|wt))!==0&&!(w!=null&&w.includes(t))&&Tn();let r=e?Y(n):n;return In(t,r)}function In(t,n){if(!t.equals(n)){var e=t.v;V?z.set(t,n):z.set(t,e),t.v=n,(t.f&b)!==0&&((t.f&D)!==0&&jt(t),I(t,(t.f&T)===0?y:M)),t.wv=rn(),Bt(t,D),ut()&&h!==null&&(h.f&y)!==0&&(h.f&(k|B))===0&&(m===null?Yn([t]):m.push(t))}return n}function Bt(t,n){var e=t.reactions;if(e!==null)for(var r=ut(),a=e.length,l=0;l<a;l++){var u=e[l],i=u.f;(i&D)===0&&(!r&&u===h||(I(u,n),(i&(y|T))!==0&&((i&b)!==0?Bt(u,M):ot(u))))}}function At(t){console.warn("https://svelte.dev/e/hydration_mismatch")}let P=!1;function Te(t){P=t}let x;function W(t){if(t===null)throw At(),gt;return x=t}function me(){return W(q(x))}function Ae(t){if(P){if(q(x)!==null)throw At(),gt;x=t}}function be(t=1){if(P){for(var n=t,e=x;n--;)e=q(e);x=e}}function xe(){for(var t=0,n=x;;){if(n.nodeType===8){var e=n.data;if(e===bn){if(t===0)return n;t-=1}else(e===mn||e===An)&&(t+=1)}var r=q(n);n.remove(),n=r}}function Ie(t){if(!t||t.nodeType!==8)throw At(),gt;return t.data}var kt,Rn,Ut,Vt;function Re(){if(kt===void 0){kt=window,Rn=/Firefox/.test(navigator.userAgent);var t=Element.prototype,n=Node.prototype,e=Text.prototype;Ut=K(n,"firstChild").get,Vt=K(n,"nextSibling").get,Rt(t)&&(t.__click=void 0,t.__className=void 0,t.__attributes=null,t.__style=void 0,t.__e=void 0),Rt(e)&&(e.__t=void 0)}}function ht(t=""){return document.createTextNode(t)}function pt(t){return Ut.call(t)}function q(t){return Vt.call(t)}function De(t,n){if(!P)return pt(t);var e=pt(x);if(e===null)e=x.appendChild(ht());else if(n&&e.nodeType!==3){var r=ht();return e==null||e.before(r),W(r),r}return W(e),e}function ke(t,n){if(!P){var e=pt(t);return e instanceof Comment&&e.data===""?q(e):e}return x}function Oe(t,n=1,e=!1){let r=P?x:t;for(var a;n--;)a=r,r=q(r);if(!P)return r;var l=r==null?void 0:r.nodeType;if(e&&l!==3){var u=ht();return r===null?a==null||a.after(u):r.before(u),W(u),u}return W(r),r}function Se(t){t.textContent=""}function Gt(t){h===null&&v===null&&wn(),v!==null&&(v.f&T)!==0&&h===null&&dn(),V&&pn()}function Dn(t,n){var e=n.last;e===null?n.last=n.first=t:(e.next=t,t.prev=e,n.last=t)}function U(t,n,e,r=!0){var a=h,l={ctx:p,deps:null,nodes_start:null,nodes_end:null,f:t|D,first:null,fn:n,last:null,next:null,parent:a,prev:null,teardown:null,transitions:null,wv:0};if(e)try{bt(l),l.f|=Pt}catch(s){throw F(l),s}else n!==null&&ot(l);var u=e&&l.deps===null&&l.first===null&&l.nodes_start===null&&l.teardown===null&&(l.f&(Mt|yt))===0;if(!u&&r&&(a!==null&&Dn(l,a),v!==null&&(v.f&b)!==0)){var i=v;(i.effects??(i.effects=[])).push(l)}return l}function kn(t){const n=U(ft,null,!1);return I(n,y),n.teardown=t,n}function Ne(t){Gt();var n=h!==null&&(h.f&k)!==0&&p!==null&&!p.m;if(n){var e=p;(e.e??(e.e=[])).push({fn:t,effect:h,reaction:v})}else{var r=Kt(t);return r}}function Ce(t){return Gt(),On(t)}function Pe(t){const n=U(B,t,!0);return(e={})=>new Promise(r=>{e.outro?Pn(n,()=>{F(n),r(void 0)}):(F(n),r(void 0))})}function Kt(t){return U(Ct,t,!1)}function On(t){return U(ft,t,!0)}function Fe(t,n=[],e=Tt){const r=n.map(e);return Sn(()=>t(...r.map(G)))}function Sn(t,n=0){return U(ft|wt|n,t,!0)}function Me(t,n=!0){return U(ft|k,t,!0,n)}function Zt(t){var n=t.teardown;if(n!==null){const e=V,r=v;Ot(!0),H(null);try{n.call(null)}finally{Ot(e),H(r)}}}function $t(t,n=!1){var e=t.first;for(t.first=t.last=null;e!==null;){var r=e.next;(e.f&B)!==0?e.parent=null:F(e,n),e=r}}function Nn(t){for(var n=t.first;n!==null;){var e=n.next;(n.f&k)===0&&F(n),n=e}}function F(t,n=!0){var e=!1;(n||(t.f&vn)!==0)&&t.nodes_start!==null&&t.nodes_end!==null&&(Cn(t.nodes_start,t.nodes_end),e=!0),$t(t,n&&!e),st(t,0),I(t,Et);var r=t.transitions;if(r!==null)for(const l of r)l.stop();Zt(t);var a=t.parent;a!==null&&a.first!==null&&zt(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=null}function Cn(t,n){for(;t!==null;){var e=t===n?null:q(t);t.remove(),t=e}}function zt(t){var n=t.parent,e=t.prev,r=t.next;e!==null&&(e.next=r),r!==null&&(r.prev=e),n!==null&&(n.first===t&&(n.first=r),n.last===t&&(n.last=e))}function Pn(t,n){var e=[];Wt(t,e,!0),Fn(e,()=>{F(t),n&&n()})}function Fn(t,n){var e=t.length;if(e>0){var r=()=>--e||n();for(var a of t)a.out(r)}else n()}function Wt(t,n,e){if((t.f&j)===0){if(t.f^=j,t.transitions!==null)for(const u of t.transitions)(u.is_global||e)&&n.push(u);for(var r=t.first;r!==null;){var a=r.next,l=(r.f&Ft)!==0||(r.f&k)!==0;Wt(r,n,l?e:!1),r=a}}}function qe(t){Xt(t,!0)}function Xt(t,n){if((t.f&j)!==0){t.f^=j,(t.f&y)===0&&(t.f^=y),nt(t)&&(I(t,D),ot(t));for(var e=t.first;e!==null;){var r=e.next,a=(e.f&Ft)!==0||(e.f&k)!==0;Xt(e,a?n:!1),e=r}if(t.transitions!==null)for(const l of t.transitions)(l.is_global||n)&&l.in()}}const Mn=typeof requestIdleCallback>"u"?t=>setTimeout(t,1):requestIdleCallback;let X=[],J=[];function Jt(){var t=X;X=[],Nt(t)}function Qt(){var t=J;J=[],Nt(t)}function Le(t){X.length===0&&queueMicrotask(Jt),X.push(t)}function Ye(t){J.length===0&&Mn(Qt),J.push(t)}function qn(){X.length>0&&Jt(),J.length>0&&Qt()}function Ln(t){var n=h;if((n.f&Pt)===0){if((n.f&yt)===0)throw t;n.fn(t)}else tn(t,n)}function tn(t,n){for(;n!==null;){if((n.f&yt)!==0)try{n.fn(t);return}catch{}n=n.parent}throw t}let Q=!1,tt=null,C=!1,V=!1;function Ot(t){V=t}let $=[];let v=null,R=!1;function H(t){v=t}let h=null;function at(t){h=t}let w=null;function nn(t){v!==null&&v.f&vt&&(w===null?w=[t]:w.push(t))}let d=null,g=0,m=null;function Yn(t){m=t}let en=1,lt=0,N=!1;function rn(){return++en}function nt(t){var o;var n=t.f;if((n&D)!==0)return!0;if((n&M)!==0){var e=t.deps,r=(n&T)!==0;if(e!==null){var a,l,u=(n&rt)!==0,i=r&&h!==null&&!N,s=e.length;if(u||i){var f=t,_=f.parent;for(a=0;a<s;a++)l=e[a],(u||!((o=l==null?void 0:l.reactions)!=null&&o.includes(f)))&&(l.reactions??(l.reactions=[])).push(f);u&&(f.f^=rt),i&&_!==null&&(_.f&T)===0&&(f.f^=T)}for(a=0;a<s;a++)if(l=e[a],nt(l)&&Ht(l),l.wv>t.wv)return!0}(!r||h!==null&&!N)&&I(t,y)}return!1}function an(t,n,e=!0){var r=t.reactions;if(r!==null)for(var a=0;a<r.length;a++){var l=r[a];w!=null&&w.includes(t)||((l.f&b)!==0?an(l,n,!1):n===l&&(e?I(l,D):(l.f&y)!==0&&I(l,M),ot(l)))}}function ln(t){var A;var n=d,e=g,r=m,a=v,l=N,u=w,i=p,s=R,f=t.f;d=null,g=0,m=null,N=(f&T)!==0&&(R||!C||v===null),v=(f&(k|B))===0?t:null,w=null,Dt(t.ctx),R=!1,lt++,t.f|=vt;try{var _=(0,t.fn)(),o=t.deps;if(d!==null){var c;if(st(t,g),o!==null&&g>0)for(o.length=g+d.length,c=0;c<d.length;c++)o[g+c]=d[c];else t.deps=o=d;if(!N)for(c=g;c<o.length;c++)((A=o[c]).reactions??(A.reactions=[])).push(t)}else o!==null&&g<o.length&&(st(t,g),o.length=g);if(ut()&&m!==null&&!R&&o!==null&&(t.f&(b|M|D))===0)for(c=0;c<m.length;c++)an(m[c],t);return a!==null&&a!==t&&(lt++,m!==null&&(r===null?r=m:r.push(...m))),_}catch(L){Ln(L)}finally{d=n,g=e,m=r,v=a,N=l,w=u,Dt(i),R=s,t.f^=vt}}function jn(t,n){let e=n.reactions;if(e!==null){var r=un.call(e,t);if(r!==-1){var a=e.length-1;a===0?e=n.reactions=null:(e[r]=e[a],e.pop())}}e===null&&(n.f&b)!==0&&(d===null||!d.includes(n))&&(I(n,M),(n.f&(T|rt))===0&&(n.f^=rt),Yt(n),st(n,0))}function st(t,n){var e=t.deps;if(e!==null)for(var r=n;r<e.length;r++)jn(t,e[r])}function bt(t){var n=t.f;if((n&Et)===0){I(t,y);var e=h,r=C;h=t,C=!0;try{(n&wt)!==0?Nn(t):$t(t),Zt(t);var a=ln(t);t.teardown=typeof a=="function"?a:null,t.wv=en;var l=t.deps,u}finally{C=r,h=e}}}function Hn(){try{yn()}catch(t){if(tt!==null)tn(t,tt);else throw t}}function sn(){var t=C;try{var n=0;for(C=!0;$.length>0;){n++>1e3&&Hn();var e=$,r=e.length;$=[];for(var a=0;a<r;a++){var l=Un(e[a]);Bn(l)}z.clear()}}finally{Q=!1,C=t,tt=null}}function Bn(t){var n=t.length;if(n!==0)for(var e=0;e<n;e++){var r=t[e];(r.f&(Et|j))===0&&nt(r)&&(bt(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?zt(r):r.fn=null))}}function ot(t){Q||(Q=!0,queueMicrotask(sn));for(var n=tt=t;n.parent!==null;){n=n.parent;var e=n.f;if((e&(B|k))!==0){if((e&y)===0)return;n.f^=y}}$.push(n)}function Un(t){for(var n=[],e=t;e!==null;){var r=e.f,a=(r&(k|B))!==0,l=a&&(r&y)!==0;if(!l&&(r&j)===0){(r&Ct)!==0?n.push(e):a?e.f^=y:nt(e)&&bt(e);var u=e.first;if(u!==null){e=u;continue}}var i=e.parent;for(e=e.next;e===null&&i!==null;)e=i.next,i=i.parent}return n}function Vn(t){for(var n;;){if(qn(),$.length===0)return Q=!1,tt=null,n;Q=!0,sn()}}async function je(){await Promise.resolve(),Vn()}function G(t){var n=t.f,e=(n&b)!==0;if(v!==null&&!R){if(!(w!=null&&w.includes(t))){var r=v.deps;t.rv<lt&&(t.rv=lt,d===null&&r!==null&&r[g]===t?g++:d===null?d=[t]:(!N||!d.includes(t))&&d.push(t))}}else if(e&&t.deps===null&&t.effects===null){var a=t,l=a.parent;l!==null&&(l.f&T)===0&&(a.f^=T)}return e&&(a=t,nt(a)&&Ht(a)),V&&z.has(t)?z.get(t):t.v}function He(t){var n=R;try{return R=!0,t()}finally{R=n}}const Gn=-7169;function I(t,n){t.f=t.f&Gn|n}function Be(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if(Z in t)dt(t);else if(!Array.isArray(t))for(let n in t){const e=t[n];typeof e=="object"&&e&&Z in e&&dt(e)}}}function dt(t,n=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!n.has(t)){n.add(t),t instanceof Date&&t.getTime();for(let r in t)try{dt(t[r],n)}catch{}const e=St(t);if(e!==Object.prototype&&e!==Array.prototype&&e!==Map.prototype&&e!==Set.prototype&&e!==Date.prototype){const r=on(e);for(let a in r){const l=r[a].get;if(l)try{l.call(t)}catch{}}}}}export{Qn as $,x as A,at as B,H as C,Dt as D,p as E,qe as F,Pn as G,An as H,Vn as I,Ce as J,Ne as K,Nt as L,He as M,Wn as N,Be as O,Tt as P,ee as Q,Kt as R,On as S,Z as T,E as U,v as V,h as W,St as X,pe as Y,on as Z,Ye as _,we as a,Ee as a0,Ie as a1,bn as a2,j as a3,Kn as a4,ae as a5,Wt as a6,Se as a7,Fn as a8,F as a9,ie as aA,$n as aB,Rn as aC,ve as aD,he as aE,Zn as aF,kn as aG,je as aH,hn as aI,re as aa,fe as ab,q as ac,pt as ad,ht as ae,fn as af,le as ag,se as ah,Ft as ai,mn as aj,vn as ak,Re as al,gt as am,At as an,te as ao,Pe as ap,K as aq,ne as ar,Xn as as,oe as at,Lt as au,_e as av,Jn as aw,ce as ax,it as ay,ue as az,Y as b,De as c,O as d,S as e,ke as f,G as g,P as h,me as i,ut as j,Sn as k,zn as l,xe as m,be as n,W as o,de as p,Te as q,Ae as r,Oe as s,Fe as t,ye as u,In as v,mt as w,ge as x,Me as y,Le as z};
