import{o as y,v as n,w as A,E as I,H as N,x as p,y as R,z as o,A as _,B as u,C as h,D as g,U as x}from"./runtime.BoVB3PJd.js";function S(d,v,E=!1){n&&A();var e=d,a=null,s=null,f=x,T=E?I:0,r=!1;const b=(l,t=!0)=>{r=!0,i(t,l)},i=(l,t)=>{if(f===(f=l))return;let c=!1;if(n){const m=e.data===N;!!f===m&&(e=p(),R(e),o(!1),c=!0)}f?(a?_(a):t&&(a=u(()=>t(e))),s&&h(s,()=>{s=null})):(s?_(s):t&&(s=u(()=>t(e))),a&&h(a,()=>{a=null})),c&&o(!0)};y(()=>{r=!1,v(b),r||i(null,null)},T),n&&(e=g)}export{S as i};
