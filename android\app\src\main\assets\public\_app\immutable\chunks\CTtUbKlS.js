import{C as f,B as l,V as y,W as b,aF as P,af as S,aG as T,z as W}from"./DvS_9Yw_.js";let w=!1;function q(){w||(w=!0,document.addEventListener("reset",r=>{Promise.resolve().then(()=>{var t;if(!r.defaultPrevented)for(const a of r.target.elements)(t=a.__on_r)==null||t.call(a)})},{capture:!0}))}function E(r){var t=y,a=b;f(null),l(null);try{return r()}finally{f(t),l(a)}}function D(r,t,a,n=a){r.addEventListener(t,()=>E(a));const i=r.__on_r;i?r.__on_r=()=>{i(),n(!0)}:r.__on_r=()=>n(!0),q()}const x=new Set,O=new Set;function m(r,t,a,n={}){function i(e){if(n.capture||z.call(t,e),!e.cancelBubble)return E(()=>a==null?void 0:a.call(this,e))}return r.startsWith("pointer")||r.startsWith("touch")||r==="wheel"?W(()=>{t.addEventListener(r,i,n)}):t.addEventListener(r,i,n),i}function F(r,t,a,n,i){var e={capture:n,passive:i},o=m(r,t,a,e);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&T(()=>{t.removeEventListener(r,o,e)})}function G(r){for(var t=0;t<r.length;t++)x.add(r[t]);for(var a of O)a(r)}function z(r){var g;var t=this,a=t.ownerDocument,n=r.type,i=((g=r.composedPath)==null?void 0:g.call(r))||[],e=i[0]||r.target,o=0,v=r.__root;if(v){var _=i.indexOf(v);if(_!==-1&&(t===document||t===window)){r.__root=t;return}var p=i.indexOf(t);if(p===-1)return;_<=p&&(o=_)}if(e=i[o]||r.target,e!==t){P(r,"currentTarget",{configurable:!0,get(){return e||a}});var L=y,k=b;f(null),l(null);try{for(var s,h=[];e!==null;){var d=e.assignedSlot||e.parentNode||e.host||null;try{var u=e["__"+n];if(u!=null&&(!e.disabled||r.target===e))if(S(u)){var[B,...M]=u;B.apply(e,[r,...M])}else u.call(e,r)}catch(c){s?h.push(c):s=c}if(r.cancelBubble||d===t||d===null)break;e=d}if(s){for(let c of h)queueMicrotask(()=>{throw c});throw s}}finally{r.__root=t,delete r.currentTarget,f(L),l(k)}}}export{q as a,x as b,G as d,F as e,z as h,D as l,O as r};
